package com.yhl.scp.dfp.delivery.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>DeliveryPlanPublishedDTO</code>
 * <p>
 * 发货计划发布表DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-17 15:59:50
 */
@ApiModel(value = "发货计划发布表DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DeliveryPlanPublishedDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 360095691518228675L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 发货计划发布唯一ID
     */
    @ApiModelProperty(value = "发货计划发布唯一ID")
    private Integer KID;
    /**
     * 发货计划版本id
     */
    @ApiModelProperty(value = "发货计划版本id")
    private String deliveryVersionId;
    /**
     * 主表id
     */
    @ApiModelProperty(value = "主表id")
    private String deliveryPlanDataId;
    /**
     * 需求类型
     */
    @ApiModelProperty(value = "需求类型")
    private String demandCategory;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    private String oemCode;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    private String productCode;
    /**
     * 发货日期
     */
    @ApiModelProperty(value = "发货日期")
    private Date demandTime;
    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    private Integer demandQuantity;
    /**
     * 箱数
     */
    @ApiModelProperty(value = "箱数")
    private Integer boxQuantity;
    /**
     * 发版人
     */
    @ApiModelProperty(value = "发版人")
    private String publisher;
    /**
     * 发版时间
     */
    @ApiModelProperty(value = "发版时间")
    private Date publishTime;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;

}
