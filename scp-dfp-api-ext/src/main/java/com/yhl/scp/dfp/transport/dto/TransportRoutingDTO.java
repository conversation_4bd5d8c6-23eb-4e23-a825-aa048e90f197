package com.yhl.scp.dfp.transport.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>TransportRoutingDTO</code>
 * <p>
 * 运输路径DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-20 22:19:32
 */
@ApiModel(value = "运输路径DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class TransportRoutingDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -53533653121638977L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 运输路径编码
     */
    @ApiModelProperty(value = "运输路径编码")
    private String routingCode;
    /**
     * 运输路径名称
     */
    @ApiModelProperty(value = "运输路径名称")
    private String routingName;
    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String transportType;
    /**
     * 事业部
     */
    @ApiModelProperty(value = "事业部")
    private String businessUnit;
    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码")
    private String customerCode;
    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerName;
    /**
     * 客户地点
     */
    @ApiModelProperty(value = "客户地点")
    private String customerLocation;
    /**
     * 国家
     */
    @ApiModelProperty(value = "国家")
    private String transportCountry;
    /**
     * 城市
     */
    @ApiModelProperty(value = "城市")
    private String transportCity;
    /**
     * 运输方式
     */
    @ApiModelProperty(value = "运输方式")
    private String transportMode;
    /**
     * 发货地
     */
    @ApiModelProperty(value = "发货地")
    private String deliverAddress;
    /**
     * 收货地
     */
    @ApiModelProperty(value = "收货地")
    private String receiverAddress;
    /**
     * 总运输距离
     */
    @ApiModelProperty(value = "总运输距离")
    private Double totalTransportDistance;
    /**
     * 总运输时间
     */
    @ApiModelProperty(value = "总运输时间")
    private Double totalTransportTime;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private Integer versionValue;
    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间")
    private Date lastUpdateTime;
    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private Integer priority;

}
