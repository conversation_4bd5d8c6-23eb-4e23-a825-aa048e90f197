package com.yhl.scp.dfp.deliveryLog.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.dfp.deliveryLog.dto.DeliveryPlanPublishedLogDTO;
import com.yhl.scp.dfp.deliveryLog.vo.DeliveryPlanPublishedLogVO;

import java.util.List;

/**
 * <code>DeliveryPlanPublishedLogService</code>
 * <p>
 * 发货计划发布追踪表应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-20 17:23:13
 */
public interface DeliveryPlanPublishedLogService extends BaseService<DeliveryPlanPublishedLogDTO, DeliveryPlanPublishedLogVO> {

    /**
     * 查询所有
     *
     * @return list {@link DeliveryPlanPublishedLogVO}
     */
    List<DeliveryPlanPublishedLogVO> selectAll();

}
