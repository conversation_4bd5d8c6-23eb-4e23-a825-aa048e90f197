package com.yhl.scp.dfp.calendar.service;



import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.dfp.calendar.dto.CalendarRuleDTO;
import com.yhl.scp.dfp.calendar.dto.ResourceCalendarDTO;
import com.yhl.scp.dfp.calendar.vo.ResourceCalendarVO;
import com.yhl.scp.dfp.calendar.vo.ResourceWorkHourStatisticsVO;
import com.yhl.scp.dfp.calendar.vo.WorkHourStatisticsVO;
import com.yhl.scp.mds.extension.feign.dto.ResourceCalendarParamDTO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <code>ResourceCalendarService</code>
 * <p>
 * 资源日历应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-08-07 16:07:22
 */
public interface DfpResourceCalendarService extends BaseService<ResourceCalendarDTO, ResourceCalendarVO> {

    /**
     * 查询所有
     *
     * @return list {@link ResourceCalendarVO}
     */
    List<ResourceCalendarVO> selectAll();

    /**
     * 根据resourceIds刷新对应的资源日历
     *
     * @param calendarRuleDTO     日历规则
     * @param organizationId      生产组织ID
     * @param standardResourceIds 标准资源ids
     * @param physicalResourceIds 理资源ids
     * @param startDate           开始日期
     * @param endDate             结束日期
     */
    void refreshResourceCalendar(
            CalendarRuleDTO calendarRuleDTO,
            String organizationId,
            List<String> standardResourceIds,
            List<String> physicalResourceIds,
            Date startDate,
            Date endDate);


    /**
     * 刷新资源日历
     */
    void refreshResourceCalendar();

    /**
     * 根据条件获取-资源日历
     *
     * @param paramDTO 条件
     * @return 资源日历
     */
    List<ResourceCalendarVO> selectResourceCalendarList(ResourceCalendarParamDTO paramDTO);

    /**
     * 按照资源，开始日期，结束日期查询日历
     */
    List<ResourceCalendarVO> doSearch(String standardResourceId, String physicalResourceId, Date startDate, Date endDate);


    /**
     * 批量查询标准资源下物理资源日历集合
     *
     * @param standardResourceIds 标准资源id集合
     * @return Map<String, List < ResourceCalendarVO>>  标准资源id与其物理资源日历集合关系
     */
    Map<String, List<ResourceCalendarVO>> selectByStandardResourceIds(List<String> standardResourceIds);

    /**
     * 统计工时
     *
     * @param standardResourceIds  标准资源id
     * @param physicalResourceIds 理资源ids
     * @param timePeriodGroupId   时间序列组id
     * @param startDate           开始日期
     * @param endDate             结束日期
     * @return {@link WorkHourStatisticsVO}
     */
    List<WorkHourStatisticsVO> calWorkHour(List<String> standardResourceIds,
                                           List<String> physicalResourceIds,
                                           String timePeriodGroupId,
                                           Date startDate,
                                           Date endDate,
                                           List<String> timePeriodIds);


    /**
     * 计算[startDate,endDate]这一段时间内的总工时
     *
     * @param standardResourceIds 标准资源ids
     * @param startDate           开始时间 (只精确到天)
     * @param endDate             结束时间 (只精确到天)
     * @param timePeriodIds       一组时段组
     * @return list {@link ResourceWorkHourStatisticsVO}
     */
    List<ResourceWorkHourStatisticsVO> calWorkHourByStandardResourceIds(List<String> standardResourceIds,
                                                                        Date startDate,
                                                                        Date endDate,
                                                                        List<String> timePeriodIds);


    /**
     * @param standardResourceId 标准资源id
     * @param physicalResourceId 物理资源id
     * @param date               日期
     */
    void deleteByDateByResourceId(List<String> standardResourceId, List<String> physicalResourceId, Date date);

    /**
     * 根据物理资源ids获取物理资源日历集合
     *
     * @param physicalResourceIds
     * @return
     */
    List<ResourceCalendarVO> selectByPhysicalResourceIds(List<String> physicalResourceIds);

    List<ResourceCalendarVO> selectResourceByStandardResourceIds(List<String> standardResourceIds);

    List<ResourceCalendarVO> getLatestMonths(List<String> oemCodes, List<String> months);

    List<ResourceCalendarVO> getLastestMonthsByVehicle(List<String> vehicles, List<String> months);

    public List<ResourceCalendarVO> getMonthsByOemByVehicle(List<String> oemCodes,List<String> vehicles,
                                                            List<String> months);

    Map<String,Map<String,List<Date>>> getMonthMapByOemByVehicle(List<String> oemCodes,
                                                                               List<String> vehicles,
                                                                               List<String> months);
    Map<String,List<ResourceCalendarVO>> getResourceByOem(List<String> oemCodes, List<Date> dateList,
    		Boolean holidayWeekendFlag);

    List<ResourceCalendarVO> getMonthFillResourceCalendar(String month);

    List<ResourceCalendarVO> getResourceByOem(String oemCode);
}
