package com.yhl.scp.dfp.stock.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>CurrentBatchQuantityDTO</code>
 * <p>
 * 批次现有量数据DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-29 11:25:43
 */
@ApiModel(value = "批次现有量数据DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CurrentBatchQuantityDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 710642708355707874L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 组织
     */
    @ApiModelProperty(value = "组织")
    private String organization;
    /**
     * 本厂编号
     */
    @ApiModelProperty(value = "本厂编号")
    private String productCode;
    /**
     * 本厂编号说明
     */
    @ApiModelProperty(value = "本厂编号说明")
    private String productCodeDesc;
    /**
     * 子库存
     */
    @ApiModelProperty(value = "子库存")
    private String subInventory;
    /**
     * 子库存说明
     */
    @ApiModelProperty(value = "子库存说明")
    private String subInventoryDesc;
    /**
     * 货位
     */
    @ApiModelProperty(value = "货位")
    private String location;
    /**
     * 货位说明
     */
    @ApiModelProperty(value = "货位说明")
    private String locationDesc;
    /**
     * 条码号
     */
    @ApiModelProperty(value = "条码号")
    private String barcode;
    /**
     * 批次
     */
    @ApiModelProperty(value = "批次")
    private String batchNumber;
    /**
     * 现有量
     */
    @ApiModelProperty(value = "现有量")
    private Integer quantity;
    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;
    /**
     * 面积(m²)
     */
    @ApiModelProperty(value = "面积(m²)")
    private BigDecimal area;
    /**
     * 重量
     */
    @ApiModelProperty(value = "重量")
    private BigDecimal weight;
    /**
     * 每卷/每箱数量
     */
    @ApiModelProperty(value = "每卷/每箱数量")
    private BigDecimal perRollOrBoxQuantity;
    /**
     * 批次有效期
     */
    @ApiModelProperty(value = "批次有效期")
    private String batchExpirationDate;
    /**
     * 分类编码
     */
    @ApiModelProperty(value = "分类编码")
    private String classificationCode;
    /**
     * 分类说明
     */
    @ApiModelProperty(value = "分类说明")
    private String classificationDesc;
    /**
     * 入库时间
     */
    @ApiModelProperty(value = "入库时间")
    private Date storageTime;
    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间")
    private Date lastUpdateTime;
    /**
     * 目录编号
     */
    @ApiModelProperty(value = "目录编号")
    private String catalogNumber;
    /**
     * 采购分类
     */
    @ApiModelProperty(value = "采购分类")
    private String purchaseClassification;
    /**
     * 零件号
     */
    @ApiModelProperty(value = "零件号")
    private String partNumber;
    /**
     * 客户号
     */
    @ApiModelProperty(value = "客户号")
    private String customerNumber;
    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerName;
    /**
     * 库龄
     */
    @ApiModelProperty(value = "库龄")
    private String storageAge;
    /**
     * 库龄天数
     */
    @ApiModelProperty(value = "库龄天数")
    private BigDecimal storageAgeDays;
    /**
     * 保质期
     */
    @ApiModelProperty(value = "保质期")
    private BigDecimal shelfLife;
    /**
     * 距离失效天数
     */
    @ApiModelProperty(value = "距离失效天数")
    private BigDecimal daysToExpiry;
    /**
     * 特性说明
     */
    @ApiModelProperty(value = "特性说明")
    private String featureDesc;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;

}
