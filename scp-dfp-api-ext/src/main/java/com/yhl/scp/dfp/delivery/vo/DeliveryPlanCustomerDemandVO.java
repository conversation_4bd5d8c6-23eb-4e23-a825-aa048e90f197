package com.yhl.scp.dfp.delivery.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <code>DeliveryPlanCustomerDemandVO</code>
 * <p>
 * 发货计划-客户需求量
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-16 13:51:11
 */
@ApiModel(value = "发货计划表VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryPlanCustomerDemandVO implements Serializable {

    private static final long serialVersionUID = -13251316386759664L;

    /**
     * 背景颜色
     */
    @ApiModelProperty(value = "背景颜色")
    @FieldInterpretation(value = "背景颜色")
    private String backColor;

    /**
     * 客户需求量
     */
    @ApiModelProperty(value = "版本编码")
    @FieldInterpretation(value = "版本编码")
    private Integer customerDemand;
    
    /**
     * 计划日期
     */
    @ApiModelProperty(value = "计划日期")
    @FieldInterpretation(value = "计划日期")
    private Date plannedDate;
    

}
