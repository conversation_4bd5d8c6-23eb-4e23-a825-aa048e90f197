package com.yhl.scp.dfp.deliveryLog.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>DeliveryPlanPublishedLogVO</code>
 * <p>
 * 发货计划发布追踪表VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-20 17:23:11
 */
@ApiModel(value = "发货计划发布追踪表VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryPlanPublishedLogVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 975020966195536643L;

    /**
     * 发货计划版本ID
     */
    @ApiModelProperty(value = "发货计划版本ID")
    @FieldInterpretation(value = "发货计划版本ID")
    private String deliveryVersionId;
    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @FieldInterpretation(value = "主表ID")
    private String deliveryPlanDataId;
    /**
     * 需求类型
     */
    @ApiModelProperty(value = "需求类型")
    @FieldInterpretation(value = "需求类型")
    private String demandCategory;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    @FieldInterpretation(value = "主机厂编码")
    private String oemCode;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @FieldInterpretation(value = "本厂编码")
    private String productCode;
    /**
     * 发货日期
     */
    @ApiModelProperty(value = "发货日期")
    @FieldInterpretation(value = "发货日期")
    private Date demandTime;
    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    @FieldInterpretation(value = "需求数量")
    private Integer demandQuantity;
    /**
     * 箱数
     */
    @ApiModelProperty(value = "箱数")
    @FieldInterpretation(value = "箱数")
    private Integer boxQuantity;
    /**
     * 发版人
     */
    @ApiModelProperty(value = "发版人")
    @FieldInterpretation(value = "发版人")
    private String publisher;
    /**
     * 发版时间
     */
    @ApiModelProperty(value = "发版时间")
    @FieldInterpretation(value = "发版时间")
    private Date publishTime;

    @Override
    public void clean() {

    }

}
