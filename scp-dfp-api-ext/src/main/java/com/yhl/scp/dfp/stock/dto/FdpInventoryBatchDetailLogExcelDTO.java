package com.yhl.scp.dfp.stock.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * <code>FdpInventoryBatchDetailLogDTO</code>
 * <p>
 * DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-18 22:56:27
 */
@ApiModel(value = "DTO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FdpInventoryBatchDetailLogExcelDTO  implements Serializable {

    private static final long serialVersionUID = 952106910500341482L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 库存点代码
     */
    @ApiModelProperty(value = "库存点代码")
    @ExcelProperty(value = "库存点代码")
    private String stockPointCode;
    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    @ExcelProperty(value = "物料编码")
    private String productCode;
    /**
     * 子库存
     */
    @ApiModelProperty(value = "子库存")
    @ExcelProperty(value = "子库存")
    private String subinventory;
    /**
     * 子库存描述
     */
    @ApiModelProperty(value = "子库存描述")
    @ExcelProperty(value = "子库存描述")
    private String subinventoryDescription;
    /**
     * 货位
     */
    @ApiModelProperty(value = "货位")
    @ExcelProperty(value = "货位")
    private String freightSpace;
    /**
     * 货位描述
     */
    @ApiModelProperty(value = "货位描述")
    @ExcelProperty(value = "货位描述")
    private String freightSpaceDescription;
    /**
     * 批次
     */
    @ApiModelProperty(value = "批次")
    @ExcelProperty(value = "批次")
    private String batch;
    /**
     * 条码号
     */
    @ApiModelProperty(value = "条码号")
    @ExcelProperty(value = "条码号")
    private String barCode;
    /**
     * 现有量
     */
    @ApiModelProperty(value = "现有量")
    @ExcelProperty(value = "现有量")
    private String currentQuantity;
    /**
     * 客户号
     */
    @ApiModelProperty(value = "客户号")
    @ExcelProperty(value = "客户号")
    private String customerNum;
    /**
     * 零件号
     */
    @ApiModelProperty(value = "零件号")
    @ExcelProperty(value = "零件号")
    private String partNum;
    /**
     * 入库时间
     */
    @ApiModelProperty(value = "入库时间")
    @ExcelProperty(value = "入库时间")
    private String assignedTime;
    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间")
    @ExcelProperty(value = "最后更新时间")
    private String lastUpdateDate;
    /**
     * 库龄
     */
    @ApiModelProperty(value = "库龄")
    private String stockAge;
    /**
     * 库龄天数
     */
    @ApiModelProperty(value = "库龄天数")
    private String stockAgeDay;
    /**
     * 保质期
     */
    @ApiModelProperty(value = "保质期")
    private String warrantyDate;
    /**
     * 距离失效时间
     */
    @ApiModelProperty(value = "距离失效时间")
    private String distanceEnableDate;
    /**
     * 数据来源（ERP/MES）
     */
    @ApiModelProperty(value = "数据来源（ERP/MES）")
    @ExcelProperty(value = "数据来源")
    private String sourceType;
    /**
     * 原始报文组织ID
     */
    @ApiModelProperty(value = "原始报文组织ID")
    @ExcelProperty(value = "组织ID")
    private String originalOrgId;
    /**
     * 分配状态
     */
    @ApiModelProperty(value = "分配状态")
    private String allocationStatus;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private Integer versionValue;

}
