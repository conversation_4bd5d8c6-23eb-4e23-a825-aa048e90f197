package com.yhl.scp.dfp.report.service;

import com.yhl.platform.common.Pagination;
import com.yhl.scp.dfp.report.vo.HighRiskProductOrderVO;

import java.util.List;
import java.util.Map;

/**
 * <code>HighRiskProductService</code>
 * <p>
 * HighRiskProductService
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-14 23:18:06
 */
public interface HighRiskProductOrderService {

    List<HighRiskProductOrderVO> selectByVersionId(String versionId);

    List<HighRiskProductOrderVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam,
                                              String organizationCode);

    List<HighRiskProductOrderVO> selectByCondition(String sortParam, String queryCriteriaParam,
                                                   String organizationCode);

    List<HighRiskProductOrderVO> selectByVOParams(Map<String, Object> params);
}
