package com.yhl.scp.dfp.report.service;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.Pagination;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVersionVO;
import com.yhl.scp.dfp.report.dto.DemandDeliveryProductionDetailDTO;
import com.yhl.scp.dfp.report.vo.DemandDeliveryProductionVO;

import java.util.List;

/**
 * <code>DemandDeliveryProductionService</code>
 * <p>
 * 需求发货生产报表服务接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-15 10:49:11
 */
public interface DemandDeliveryProductionService {

    /**
     * 查询需求发货生产报表数据（原方法，保持兼容性）
     * @param dto 查询条件
     * @return 报表数据列表
     */
    List<DemandDeliveryProductionVO> queryDemandDeliveryProductionReport(DemandDeliveryProductionDetailDTO dto);

    /**
     * 分页查询需求发货生产报表数据（新增优化方法）
     * @param dto 查询条件（包含分页参数）
     * @return 分页报表数据
     */
    PageInfo<DemandDeliveryProductionVO> queryDemandDeliveryProductionReportWithPagination(DemandDeliveryProductionDetailDTO dto);
}
