package com.yhl.scp.dfp.delivery.vo;

import java.io.Serializable;
import java.util.List;

import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanPublishedDTO;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <code>DeliveryPlanDoPublishVO</code>
 * <p>
 * 发货计划明细VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-25 13:51:52
 */
@ApiModel(value = "发货计划明细VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryPlanDoPublishVO implements Serializable {

    private static final long serialVersionUID = 671304281721020917L;



    private BaseResponse<List<String>> syncMesResp;
    
    private List<DeliveryPlanPublishedDTO> oldDeliveryPlanPublishedDTOS;




}
