package com.yhl.scp.ips.log.convertor;

import com.yhl.scp.ips.log.domain.entity.DataChangeRecordDO;
import com.yhl.scp.ips.log.dto.DataChangeRecordDTO;
import com.yhl.scp.ips.log.infrastructure.po.DataChangeRecordPO;
import com.yhl.scp.ips.log.vo.DataChangeRecordVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DataChangeRecordConvertor {

    DataChangeRecordConvertor INSTANCE = Mappers.getMapper(DataChangeRecordConvertor.class);

    DataChangeRecordDO dto2Do(DataChangeRecordDTO obj);

    DataChangeRecordDTO do2Dto(DataChangeRecordDO obj);

    List<DataChangeRecordDO> dto2Dos(List<DataChangeRecordDTO> list);

    List<DataChangeRecordDTO> do2Dtos(List<DataChangeRecordDO> list);

    DataChangeRecordVO do2Vo(DataChangeRecordDO obj);

    DataChangeRecordVO po2Vo(DataChangeRecordPO obj);

    List<DataChangeRecordVO> po2Vos(List<DataChangeRecordPO> list);

    DataChangeRecordPO do2Po(DataChangeRecordDO obj);

    DataChangeRecordDO po2Do(DataChangeRecordPO obj);

    DataChangeRecordPO dto2Po(DataChangeRecordDTO obj);

    List<DataChangeRecordPO> dto2Pos(List<DataChangeRecordDTO> obj);

}
