<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.ips.log.infrastructure.dao.DataChangeRecordDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.ips.log.infrastructure.po.DataChangeRecordPO">
        <!--@Table log_data_change_record-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="data_table_name" jdbcType="VARCHAR" property="dataTableName"/>
        <result column="primary_id" jdbcType="VARCHAR" property="primaryId"/>
        <result column="before_data" jdbcType="VARCHAR" property="beforeData"/>
        <result column="after_data" jdbcType="VARCHAR" property="afterData"/>
        <result column="operate_user" jdbcType="VARCHAR" property="operateUser"/>
        <result column="operate_type" jdbcType="VARCHAR" property="operateType"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.ips.log.vo.DataChangeRecordVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id, data_table_name, primary_id, before_data, after_data, operate_user, operate_type, version_value, create_time, modify_time
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.dataTableName != null and params.dataTableName != ''">
                and data_table_name = #{params.dataTableName,jdbcType=VARCHAR}
            </if>
            <if test="params.primaryId != null and params.primaryId != ''">
                and primary_id = #{params.primaryId,jdbcType=VARCHAR}
            </if>
            <if test="params.beforeData != null and params.beforeData != ''">
                and before_data = #{params.beforeData,jdbcType=VARCHAR}
            </if>
            <if test="params.afterData != null and params.afterData != ''">
                and after_data = #{params.afterData,jdbcType=VARCHAR}
            </if>
            <if test="params.operateUser != null and params.operateUser != ''">
                and operate_user = #{params.operateUser,jdbcType=VARCHAR}
            </if>
            <if test="params.operateType != null and params.operateType != ''">
                and operate_type = #{params.operateType,jdbcType=VARCHAR}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from log_data_change_record
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from log_data_change_record
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from log_data_change_record
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from log_data_change_record
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.ips.log.infrastructure.po.DataChangeRecordPO">
        insert into log_data_change_record(id,
                                           data_table_name,
                                           primary_id,
                                           before_data,
                                           after_data,
                                           operate_user,
                                           operate_type,
                                           version_value,
                                           create_time,
                                           modify_time)
        values (#{id,jdbcType=VARCHAR},
                #{dataTableName,jdbcType=VARCHAR},
                #{primaryId,jdbcType=VARCHAR},
                #{beforeData,jdbcType=VARCHAR},
                #{afterData,jdbcType=VARCHAR},
                #{operateUser,jdbcType=VARCHAR},
                #{operateType,jdbcType=VARCHAR},
                #{versionValue,jdbcType=INTEGER},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.ips.log.infrastructure.po.DataChangeRecordPO">
        insert into log_data_change_record(id,
                                           data_table_name,
                                           primary_id,
                                           before_data,
                                           after_data,
                                           operate_user,
                                           operate_type,
                                           version_value,
                                           create_time,
                                           modify_time)
        values (#{id,jdbcType=VARCHAR},
                #{dataTableName,jdbcType=VARCHAR},
                #{primaryId,jdbcType=VARCHAR},
                #{beforeData,jdbcType=VARCHAR},
                #{afterData,jdbcType=VARCHAR},
                #{operateUser,jdbcType=VARCHAR},
                #{operateType,jdbcType=VARCHAR},
                #{versionValue,jdbcType=INTEGER},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into log_data_change_record(
        id,
        data_table_name,
        primary_id,
        before_data,
        after_data,
        operate_user,
        operate_type,
        version_value,
        create_time,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.id,jdbcType=VARCHAR},
            #{entity.dataTableName,jdbcType=VARCHAR},
            #{entity.primaryId,jdbcType=VARCHAR},
            #{entity.beforeData,jdbcType=VARCHAR},
            #{entity.afterData,jdbcType=VARCHAR},
            #{entity.operateUser,jdbcType=VARCHAR},
            #{entity.operateType,jdbcType=VARCHAR},
            #{entity.versionValue,jdbcType=INTEGER},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into log_data_change_record(
        id,
        data_table_name,
        primary_id,
        before_data,
        after_data,
        operate_user,
        operate_type,
        version_value,
        create_time,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.dataTableName,jdbcType=VARCHAR},
            #{entity.primaryId,jdbcType=VARCHAR},
            #{entity.beforeData,jdbcType=VARCHAR},
            #{entity.afterData,jdbcType=VARCHAR},
            #{entity.operateUser,jdbcType=VARCHAR},
            #{entity.operateType,jdbcType=VARCHAR},
            #{entity.versionValue,jdbcType=INTEGER},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.ips.log.infrastructure.po.DataChangeRecordPO">
        update log_data_change_record
        set data_table_name = #{dataTableName,jdbcType=VARCHAR},
            primary_id      = #{primaryId,jdbcType=VARCHAR},
            before_data     = #{beforeData,jdbcType=VARCHAR},
            after_data      = #{afterData,jdbcType=VARCHAR},
            operate_user    = #{operateUser,jdbcType=VARCHAR},
            operate_type    = #{operateType,jdbcType=VARCHAR},
            version_value   = #{versionValue,jdbcType=INTEGER},
            modify_time     = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.ips.log.infrastructure.po.DataChangeRecordPO">
        update log_data_change_record
        <set>
            <if test="item.dataTableName != null and item.dataTableName != ''">
                data_table_name = #{item.dataTableName,jdbcType=VARCHAR},
            </if>
            <if test="item.primaryId != null and item.primaryId != ''">
                primary_id = #{item.primaryId,jdbcType=VARCHAR},
            </if>
            <if test="item.beforeData != null and item.beforeData != ''">
                before_data = #{item.beforeData,jdbcType=VARCHAR},
            </if>
            <if test="item.afterData != null and item.afterData != ''">
                after_data = #{item.afterData,jdbcType=VARCHAR},
            </if>
            <if test="item.operateUser != null and item.operateUser != ''">
                operate_user = #{item.operateUser,jdbcType=VARCHAR},
            </if>
            <if test="item.operateType != null and item.operateType != ''">
                operate_type = #{item.operateType,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update log_data_change_record
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="data_table_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.dataTableName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="primary_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.primaryId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="before_data = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.beforeData,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="after_data = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.afterData,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="operate_user = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.operateUser,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="operate_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.operateType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update log_data_change_record
            <set>
                <if test="item.dataTableName != null and item.dataTableName != ''">
                    data_table_name = #{item.dataTableName,jdbcType=VARCHAR},
                </if>
                <if test="item.primaryId != null and item.primaryId != ''">
                    primary_id = #{item.primaryId,jdbcType=VARCHAR},
                </if>
                <if test="item.beforeData != null and item.beforeData != ''">
                    before_data = #{item.beforeData,jdbcType=VARCHAR},
                </if>
                <if test="item.afterData != null and item.afterData != ''">
                    after_data = #{item.afterData,jdbcType=VARCHAR},
                </if>
                <if test="item.operateUser != null and item.operateUser != ''">
                    operate_user = #{item.operateUser,jdbcType=VARCHAR},
                </if>
                <if test="item.operateType != null and item.operateType != ''">
                    operate_type = #{item.operateType,jdbcType=VARCHAR},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from log_data_change_record
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from log_data_change_record where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
