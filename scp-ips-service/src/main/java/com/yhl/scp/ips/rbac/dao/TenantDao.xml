<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.ips.rbac.dao.TenantDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.ips.rbac.entity.Tenant">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="tenant_name" jdbcType="VARCHAR" property="tenantName"/>
        <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode"/>
        <result column="company_code" jdbcType="VARCHAR" property="companyCode"/>
        <result column="tel" jdbcType="VARCHAR" property="tel"/>
        <result column="mail" jdbcType="VARCHAR" property="mail"/>
        <result column="start_time" jdbcType="DATE" property="startTime"/>
        <result column="end_time" jdbcType="DATE" property="endTime"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="creator_name" jdbcType="VARCHAR" property="creatorName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="modify_name" jdbcType="VARCHAR" property="modifyName"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
        company_code, tenant_name, tenant_code, tel, mail, start_time, end_time, creator, creator_name,
    create_time, modifier, modify_time, modify_name, enabled
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select 'false' as QUERYID,
        <include refid="Base_Column_List"/>
        from auth_rbac_tenant
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select 'false' as QUERYID,
        <include refid="Base_Column_List"/>
        from auth_rbac_tenant
    </select>
    <select id="selectByPage" parameterType="java.lang.String" resultMap="BaseResultMap">
        select 'false' as QUERYID,
        <include refid="Base_Column_List"/>
        from auth_rbac_tenant
        where 1=1
        <if test="queryCriteriaParam!=null and queryCriteriaParam!=''">
            ${queryCriteriaParam}
        </if>
        <if test="sortParam!=null and sortParam!=''">
            order by ${sortParam}
        </if>
    </select>
    <select id="selectByTenantName" parameterType="java.lang.String" resultMap="BaseResultMap">
        select 'false' as QUERYID,
        <include refid="Base_Column_List"/>
        from auth_rbac_tenant
        where tenant_name = #{tenantName,jdbcType=VARCHAR}
    </select>
    <select id="selectByUserId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select t.id,
               t.tenant_name,
               t.tenant_code,
               t.tel,
               t.mail,
               t.start_time,
               t.end_time,
               t.creator,
               t.creator_name,
               t.create_time,
               t.modifier,
               t.modify_time,
               t.modify_name,
               t.enabled
        from auth_rbac_tenant t
                 left join auth_rbac_tenant_user tu on t.id = tu.tenant_id
        where tu.user_id = #{userId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from auth_rbac_tenant
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.yhl.scp.ips.rbac.entity.Tenant">
        insert into auth_rbac_tenant (id, tenant_name, tenant_code,
                                      tel, mail, start_time,
                                      end_time, creator, creator_name,
                                      create_time, modifier, modify_time,
                                      modify_name, enabled)
        values (#{id,jdbcType=VARCHAR}, #{tenantName,jdbcType=VARCHAR}, #{tenantCode,jdbcType=VARCHAR},
                #{tel,jdbcType=VARCHAR}, #{mail,jdbcType=VARCHAR}, #{startTime,jdbcType=DATE},
                #{endTime,jdbcType=DATE}, #{creator,jdbcType=VARCHAR}, #{creatorName,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{modifier,jdbcType=VARCHAR}, #{modifyTime,jdbcType=TIMESTAMP},
                #{modifyName,jdbcType=VARCHAR}, #{enabled,jdbcType=VARCHAR})
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.yhl.scp.ips.rbac.entity.Tenant">
        update auth_rbac_tenant
        set tenant_name  = #{tenantName,jdbcType=VARCHAR},
            tenant_code  = #{tenantCode,jdbcType=VARCHAR},
            tel          = #{tel,jdbcType=VARCHAR},
            mail         = #{mail,jdbcType=VARCHAR},
            start_time   = #{startTime,jdbcType=DATE},
            end_time     = #{endTime,jdbcType=DATE},
            creator      = #{creator,jdbcType=VARCHAR},
            creator_name = #{creatorName,jdbcType=VARCHAR},
            create_time  = #{createTime,jdbcType=TIMESTAMP},
            modifier     = #{modifier,jdbcType=VARCHAR},
            modify_time  = #{modifyTime,jdbcType=TIMESTAMP},
            modify_name  = #{modifyName,jdbcType=VARCHAR},
            enabled      = #{enabled,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
</mapper>