package com.yhl.scp.ips.redisKeyManage.convertor;

import com.yhl.scp.ips.redisKeyManage.domain.entity.RedisKeyManageDO;
import com.yhl.scp.ips.redisKeyManage.dto.RedisKeyManageDTO;
import com.yhl.scp.ips.redisKeyManage.infrastructure.po.RedisKeyManagePO;
import com.yhl.scp.ips.redisKeyManage.vo.RedisKeyManageVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>RedisKeyManageConvertor</code>
 * <p>
 * redis key管理转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-10 16:16:30
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RedisKeyManageConvertor {

    RedisKeyManageConvertor INSTANCE = Mappers.getMapper(RedisKeyManageConvertor.class);

    RedisKeyManageDO dto2Do(RedisKeyManageDTO obj);

    RedisKeyManageDTO do2Dto(RedisKeyManageDO obj);

    List<RedisKeyManageDO> dto2Dos(List<RedisKeyManageDTO> list);

    List<RedisKeyManageDTO> do2Dtos(List<RedisKeyManageDO> list);

    RedisKeyManageVO do2Vo(RedisKeyManageDO obj);

    RedisKeyManageVO po2Vo(RedisKeyManagePO obj);

    List<RedisKeyManageVO> po2Vos(List<RedisKeyManagePO> list);

    RedisKeyManagePO do2Po(RedisKeyManageDO obj);

    RedisKeyManageDO po2Do(RedisKeyManagePO obj);

    RedisKeyManagePO dto2Po(RedisKeyManageDTO obj);

    List<RedisKeyManagePO> dto2Pos(List<RedisKeyManageDTO> obj);

}
