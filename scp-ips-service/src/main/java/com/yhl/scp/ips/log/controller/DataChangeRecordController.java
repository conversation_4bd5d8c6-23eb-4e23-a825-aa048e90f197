package com.yhl.scp.ips.log.controller;

import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.log.dto.DataChangeRecordDTO;
import com.yhl.scp.ips.log.dto.DataChangeRecordQueryDTO;
import com.yhl.scp.ips.log.service.DataChangeRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "控制器")
@RestController
@RequestMapping("dataChangeRecord")
public class DataChangeRecordController extends BaseController {

    @Resource
    private DataChangeRecordService dataChangeRecordService;

    @ApiOperation(value = "分页查询")
    @PostMapping(value = "page")
    public BaseResponse<Page<DataChangeRecordDTO>> page(@RequestBody DataChangeRecordQueryDTO params) {
        Page<DataChangeRecordDTO> changeRecords = dataChangeRecordService.selectByPage(getPagination(), params);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, changeRecords);
    }
}
