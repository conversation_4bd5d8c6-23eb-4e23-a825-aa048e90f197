package com.yhl.scp.ips.redisKeyManage.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * <code>RedisKeyManageDO</code>
 * <p>
 * redis key管理DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-10 16:16:17
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class RedisKeyManageDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 209836439097883615L;

    private String id;
    /**
     * 模块名称
     */
    private String moduleCode;
    /**
     * 场景名称
     */
    private String scenarioName;
    /**
     * redis key
     */
    private String configCode;
    /**
     * 名称
     */
    private String configName;
    /**
     * 责任人
     */
    private String responsibleUser;
    /**
     * 是否启动删除（YES/NO）
     */
    private String initiateDeletion;
    /**
     * 创建人
     */
    private String creatorName;
    /**
     * 更新人
     */
    private String modifyName;

}
