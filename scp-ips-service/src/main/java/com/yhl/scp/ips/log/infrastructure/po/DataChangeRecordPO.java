package com.yhl.scp.ips.log.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;

public class DataChangeRecordPO extends BasePO implements Serializable {

    private static final long serialVersionUID = -65875334596103494L;

    /**
     * 表名
     */
    private String dataTableName;
    /**
     * 数据ID
     */
    private String primaryId;
    /**
     * 修改前数据
     */
    private String beforeData;
    /**
     * 修改后数据
     */
    private String afterData;
    /**
     * 操作人
     */
    private String operateUser;
    /**
     * 操作类型
     */
    private String operateType;

    public String getDataTableName() {
        return dataTableName;
    }

    public void setDataTableName(String dataTableName) {
        this.dataTableName = dataTableName;
    }

    public String getPrimaryId() {
        return primaryId;
    }

    public void setPrimaryId(String primaryId) {
        this.primaryId = primaryId;
    }

    public String getBeforeData() {
        return beforeData;
    }

    public void setBeforeData(String beforeData) {
        this.beforeData = beforeData;
    }

    public String getAfterData() {
        return afterData;
    }

    public void setAfterData(String afterData) {
        this.afterData = afterData;
    }

    public String getOperateUser() {
        return operateUser;
    }

    public void setOperateUser(String operateUser) {
        this.operateUser = operateUser;
    }

    public String getOperateType() {
        return operateType;
    }

    public void setOperateType(String operateType) {
        this.operateType = operateType;
    }
}
