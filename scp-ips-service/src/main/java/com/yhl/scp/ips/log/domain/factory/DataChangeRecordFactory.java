package com.yhl.scp.ips.log.domain.factory;

import com.yhl.scp.ips.log.domain.entity.DataChangeRecordDO;
import com.yhl.scp.ips.log.dto.DataChangeRecordDTO;
import com.yhl.scp.ips.log.infrastructure.dao.DataChangeRecordDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class DataChangeRecordFactory {

    @Resource
    private DataChangeRecordDao dataChangeRecordDao;

    DataChangeRecordDO create(DataChangeRecordDTO dto) {
        // TODO
        return null;
    }

}
