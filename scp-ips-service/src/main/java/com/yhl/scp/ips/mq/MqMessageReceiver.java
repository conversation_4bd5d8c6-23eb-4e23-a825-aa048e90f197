package com.yhl.scp.ips.mq;

import com.yhl.scp.ips.constant.MqConstants;
import com.yhl.scp.ips.log.service.DataChangeRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class MqMessageReceiver {

    @Resource
    private DataChangeRecordService dataChangeRecordService;

    @RabbitListener(bindings = @QueueBinding(value = @Queue(value = MqConstants.DATA_RECORD_CHANGE_QUEUE, ignoreDeclarationExceptions = "true"), exchange = @Exchange(value = MqConstants.BPIM_EVENT_EXCHANGE, ignoreDeclarationExceptions = "true", type = ExchangeTypes.TOPIC), key = MqConstants.DATA_RECORD_CHANGE_ROUTING_KEY))
    public void receiveDataRecordChangeMessage(String data) {
        try {
            dataChangeRecordService.handle(data);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
}

