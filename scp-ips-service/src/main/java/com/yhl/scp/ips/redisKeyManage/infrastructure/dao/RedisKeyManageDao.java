package com.yhl.scp.ips.redisKeyManage.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.ips.redisKeyManage.infrastructure.po.RedisKeyManagePO;
import com.yhl.scp.ips.redisKeyManage.vo.RedisKeyManageVO;

/**
 * <code>RedisKeyManageDao</code>
 * <p>
 * redis key管理DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-10 16:16:11
 */
public interface RedisKeyManageDao extends BaseDao<RedisKeyManagePO, RedisKeyManageVO> {

}
