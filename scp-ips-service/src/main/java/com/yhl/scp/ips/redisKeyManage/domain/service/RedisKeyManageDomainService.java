package com.yhl.scp.ips.redisKeyManage.domain.service;

import com.yhl.scp.ips.redisKeyManage.domain.entity.RedisKeyManageDO;
import com.yhl.scp.ips.redisKeyManage.infrastructure.dao.RedisKeyManageDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <code>RedisKeyManageDomainService</code>
 * <p>
 * redis key管理领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-10 16:16:21
 */
@Service
public class RedisKeyManageDomainService {

    @Resource
    private RedisKeyManageDao redisKeyManageDao;

    /**
     * 数据校验
     *
     * @param redisKeyManageDO 领域对象
     */
    public void validation(RedisKeyManageDO redisKeyManageDO) {
        checkNotNull(redisKeyManageDO);
        checkUniqueCode(redisKeyManageDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param redisKeyManageDO 领域对象
     */
    private void checkNotNull(RedisKeyManageDO redisKeyManageDO) {

    }

    /**
     * 唯一性校验
     *
     * @param redisKeyManageDO 领域对象
     */
    private void checkUniqueCode(RedisKeyManageDO redisKeyManageDO) {

    }

}
