package com.yhl.scp.ips.bpm.controller;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import com.yhl.platform.common.PlatformUser;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.JacksonUtils;
import com.yhl.scp.ips.bpm.constants.ProcessVariableConstants;
import com.yhl.scp.ips.bpm.enums.ProcessStatusEnum;
import com.yhl.scp.ips.bpm.vo.PageResult;
import com.yhl.scp.ips.common.SystemHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.converter.BpmnXMLConverter;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.editor.constants.ModelDataJsonConstants;
import org.flowable.editor.language.json.converter.BpmnJsonConverter;
import org.flowable.engine.FormService;
import org.flowable.engine.IdentityService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.repository.Model;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.repository.ProcessDefinitionQuery;
import org.flowable.ui.task.model.runtime.ProcessDefinitionRepresentation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.xml.XMLConstants;
import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>BpmProcessController</code>
 * <p>
 * BPM流程定义管理
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021-11-22 14:34:00
 */
@Api(tags = "BPM流程定义管理")
@RestController
@RequestMapping(value = "bpmProcess")
@Slf4j
public class BpmProcessController extends BaseController {

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private IdentityService identityService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private FormService formService;

    @ApiOperation(value = "流程分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageResult<ProcessDefinitionRepresentation>> page(@RequestParam(value = "processDefinitionKey", required = false) String processDefinitionKey,
                                                                          @RequestParam(value = "processDefinitionName", required = false) String processDefinitionName,
                                                                          @RequestParam(value = "processDefinitionCategory", required = false) String processDefinitionCategory,
                                                                          @RequestParam(value = "processDefinitionResourceName", required = false) String processDefinitionResourceName) {
        try {
            log.info("process definition list query ...");
            // 组装查询条件
            ProcessDefinitionQuery processDefinitionQuery = assembleProcessDefinitionQuery(processDefinitionKey,
                    processDefinitionName, processDefinitionCategory, processDefinitionResourceName);
            long totalCount = processDefinitionQuery.count();
            int firstResult = PageResult.calcFirstResult(getPagination());
            List<ProcessDefinition> list = processDefinitionQuery.orderByDeploymentId().desc()
                    .listPage(firstResult, getPagination().getPageSize());
            List<ProcessDefinitionRepresentation> dataList = new ArrayList<>();
            // 组装结果集
            if (CollectionUtils.isNotEmpty(list)) {
                for (ProcessDefinition processDefinition : list) {
                    dataList.add(new ProcessDefinitionRepresentation(processDefinition));
                }
            }
            PageResult<ProcessDefinitionRepresentation> data = new PageResult<>(dataList, getPagination().getPageNum(),
                    getPagination().getPageSize(), totalCount);
            return new BaseResponse<>(Boolean.TRUE, data);
        } catch (Exception e) {
            log.error("Fail to query process page, ", e);
            return new BaseResponse<>("流程分页查询失败");
        }
    }

    /**
     * 组装查询条件
     *
     * @param processDefinitionKey          流程定义KEY
     * @param processDefinitionName         流程定义名
     * @param processDefinitionCategory     流程定义分类
     * @param processDefinitionResourceName 流程定义资源名
     * @return org.flowable.engine.repository.ProcessDefinitionQuery
     */
    private ProcessDefinitionQuery assembleProcessDefinitionQuery(String processDefinitionKey, String processDefinitionName,
                                                                  String processDefinitionCategory, String processDefinitionResourceName) {
        ProcessDefinitionQuery processDefinitionQuery = repositoryService.createProcessDefinitionQuery();
        if (StringUtils.isNotBlank(processDefinitionKey)) {
            processDefinitionQuery.processDefinitionKey(processDefinitionKey);
        }
        if (StringUtils.isNotBlank(processDefinitionName)) {
            processDefinitionQuery.processDefinitionName("%" + processDefinitionName + "%");
        }
        if (StringUtils.isNotBlank(processDefinitionCategory)) {
            processDefinitionQuery.processDefinitionCategory(processDefinitionCategory);
        }
        if (StringUtils.isNotBlank(processDefinitionResourceName)) {
            processDefinitionQuery.processDefinitionResourceNameLike("%" + processDefinitionResourceName + "%");
        }
        return processDefinitionQuery;
    }

    /**
     * 组装通用流程变量
     *
     * @param variables 流程变量
     * @param user      用户
     */
    private void assembleCommonVariables(Map<String, Object> variables, PlatformUser user) {
        Date date = new Date();
        // 发起人
        variables.put(ProcessVariableConstants.INITIATOR, user.getStaffCode());
        // 发起人名
        variables.put(ProcessVariableConstants.INITIATOR_NAME, user.getCnName());
        // 发起时间
        variables.put(ProcessVariableConstants.INITIATE_TIME, date);
        // 编号
        variables.put(ProcessVariableConstants.SERIAL_NUMBER, DateUtils.dateToString(date, "yyyyMMddHHmmssSSS"));
        // 状态
        variables.put(ProcessVariableConstants.STATUS, ProcessStatusEnum.IN_PROGRESS.getCode());
        // 钉钉待办
        variables.put(ProcessVariableConstants.DING_DING_TODO, Lists.newArrayList());
    }

    @ApiOperation(value = "流程启动")
    @PostMapping(value = "start/{processDefinitionId}")
    public BaseResponse<Void> start(@PathVariable(value = "processDefinitionId") String processDefinitionId,
                                    @SuppressWarnings("unused") @RequestParam(value = "processDefinitionKey") String processDefinitionKey,
                                    @RequestBody(required = false) Map<String, Object> variables) {
        try {
            log.info("start process ...");
            PlatformUser user = SystemHolder.getUser();
            identityService.setAuthenticatedUserId(user.getStaffCode());
            if (null == variables) {
                variables = new HashMap<>(16);
            }
            // 1.组装通用流程变量
            assembleCommonVariables(variables, user);
            // 2.启动流程
            runtimeService.startProcessInstanceById(processDefinitionId, variables);
            return new BaseResponse<>();
        } catch (Exception e) {
            log.error("Fail to start process, ", e);
            return new BaseResponse<>("流程启动失败");
        }
    }

    /**
     * 组装流程各节点用户/用户组
     *
     * @param variables         流程变量
     * @param groupIds          组ID列表
     * @param translateGroupIds 需要翻译的组ID列表
     */
    @SuppressWarnings("unused")
    private void assembleAssigneeVariables(Map<String, Object> variables, List<String> groupIds, List<String> translateGroupIds) {
        List<String> involvedUserList = new ArrayList<>();

        String involvedGroupString = groupIds.stream().distinct().collect(Collectors.joining("#"));
        variables.put(ProcessVariableConstants.INVOLVED_GROUPS, involvedGroupString);

        for (String groupId : groupIds) {
            List<org.flowable.idm.api.User> userList = identityService.createUserQuery().memberOfGroup(groupId).list();
            if (CollectionUtils.isNotEmpty(userList)) {
                List<String> userIds = userList.stream().map(org.flowable.idm.api.User::getId).collect(Collectors.toList());
                involvedUserList.addAll(userIds);
                variables.put(groupId, userIds.size() == 1 ? userIds.get(0) : userIds);
                // 设置需要翻译用户
                if (translateGroupIds.contains(groupId)) {
                    List<String> collect1 = userList.stream().map(item -> item.getFirstName()
                            + (StringUtils.isNotBlank(item.getLastName()) ? item.getLastName() : "")).collect(Collectors.toList());
                    variables.put(groupId + "Translator", collect1);
                }
            }
        }

        String involvedUserString = involvedUserList.stream().distinct().collect(Collectors.joining("#"));
        variables.put(ProcessVariableConstants.INVOLVED_USERS, involvedUserString);
    }

    /**
     * 节点填充空数组
     *
     * @param variables          流程变量
     * @param taskDefinitionKeys 填充节点集合
     */
    @SuppressWarnings("unused")
    private void fillEmptyList(Map<String, Object> variables, List<String> taskDefinitionKeys) {
        for (String key : taskDefinitionKeys) {
            variables.put(key, new ArrayList<Map<String, Object>>());
        }
    }

    @ApiOperation(value = "流程启动1")
    @PostMapping(value = "start1/{processDefinitionId}")
    public BaseResponse<Void> start1(@PathVariable(value = "processDefinitionId") String processDefinitionId,
                                     @RequestBody(required = false) Map<String, String> variables) {
        try {
            log.info("start process ...");
            PlatformUser user = SystemHolder.getUser();
            identityService.setAuthenticatedUserId(user.getStaffCode());
            if (null == variables) {
                variables = new HashMap<>(16);
            }

            variables.put(ProcessVariableConstants.INITIATOR, user.getStaffCode());
            variables.put(ProcessVariableConstants.INITIATOR_NAME, user.getCnName());
            variables.put(ProcessVariableConstants.INITIATE_TIME,
                    DateUtils.dateToString(new Date(), DateUtils.COMMON_DATE_STR1));
            formService.submitStartFormData(processDefinitionId, variables);
            return new BaseResponse<>();
        } catch (Exception e) {
            log.error("Fail to start process, ", e);
            return new BaseResponse<>("流程启动失败");
        }
    }

    @ApiOperation(value = "流程转换模型")
    @PostMapping(value = "convert2Model/{processDefinitionId}")
    public BaseResponse<Void> convert2Model(@PathVariable("processDefinitionId") String processDefinitionId) {
        try {
            log.info("convert process definition to model ...");
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(processDefinitionId).singleResult();
            InputStream bpmnStream = repositoryService.getResourceAsStream(processDefinition.getDeploymentId(),
                    processDefinition.getResourceName());
            XMLInputFactory xif = XMLInputFactory.newInstance();

            // to be compliant, completely disable DOCTYPE declaration:
            xif.setProperty(XMLInputFactory.SUPPORT_DTD, false);
            // or completely disable external entities declarations:
            xif.setProperty(XMLInputFactory.IS_SUPPORTING_EXTERNAL_ENTITIES, Boolean.FALSE);
            // or prohibit the use of all protocols by external entities:
            xif.setProperty(XMLConstants.ACCESS_EXTERNAL_DTD, "");
            xif.setProperty(XMLConstants.ACCESS_EXTERNAL_SCHEMA, "");

            InputStreamReader in = new InputStreamReader(bpmnStream, StandardCharsets.UTF_8);
            XMLStreamReader xtr = xif.createXMLStreamReader(in);
            BpmnModel bpmnModel = new BpmnXMLConverter().convertToBpmnModel(xtr);

            BpmnJsonConverter converter = new BpmnJsonConverter();
            ObjectNode modelNode = converter.convertToJson(bpmnModel);
            Model modelData = repositoryService.newModel();
            modelData.setKey(processDefinition.getKey());
            modelData.setName(processDefinition.getResourceName());
            modelData.setCategory(processDefinition.getDeploymentId());

            ObjectNode modelObjectNode = JacksonUtils.createEmptyObjectNode();
            modelObjectNode.put(ModelDataJsonConstants.MODEL_NAME, processDefinition.getName());
            modelObjectNode.put(ModelDataJsonConstants.MODEL_REVISION, 1);
            modelObjectNode.put(ModelDataJsonConstants.MODEL_DESCRIPTION, processDefinition.getDescription());
            modelData.setMetaInfo(modelObjectNode.toString());

            repositoryService.saveModel(modelData);
            repositoryService.addModelEditorSource(modelData.getId(), modelNode.toString().getBytes(StandardCharsets.UTF_8));
            return new BaseResponse<>();
        } catch (Exception e) {
            log.error("Fail to convert process to model, ", e);
            return new BaseResponse<>("流程转换模型失败");
        }
    }

    @ApiOperation(value = "流程部署删除")
    @PostMapping(value = "delete/{deploymentId}")
    public BaseResponse<Void> delete(@PathVariable(value = "deploymentId") String deploymentId) {
        try {
            log.info("process definition delete ...");
            repositoryService.deleteDeployment(deploymentId, true);
            return new BaseResponse<>();
        } catch (Exception e) {
            log.error("Fail to delete process deployment, ", e);
            return new BaseResponse<>("流程部署删除失败");
        }
    }

    /**
     * IMAGE
     */
    public static final String PROCESS_DEFINITION_RESOURCE_TYPE_IMAGE = "IMAGE";

    /**
     * XML
     */
    public static final String PROCESS_DEFINITION_RESOURCE_TYPE_XML = "XML";

    /**
     * 输入流缓存大小
     */
    public static final int INPUT_STREAM_READ_BUFFER_SIZE = 1024;

    @ApiOperation(value = "图片|XML展示")
    @GetMapping(value = "resourceDiagram")
    public void resourceDiagram(
            @RequestParam(value = "processDefinitionId") String processDefinitionId,
            @ApiParam(allowableValues = "XML,IMAGE") @RequestParam("resourceType") String resourceType) {
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                .processDefinitionId(processDefinitionId).singleResult();
        String resourceName = "";
        if (PROCESS_DEFINITION_RESOURCE_TYPE_IMAGE.equals(resourceType)) {
            resourceName = processDefinition.getDiagramResourceName();
        } else if (PROCESS_DEFINITION_RESOURCE_TYPE_XML.equals(resourceType)) {
            resourceName = processDefinition.getResourceName();
        }
        try (InputStream inputStream = repositoryService.getResourceAsStream(processDefinition.getDeploymentId(),
                resourceName)) {
            byte[] b = new byte[1024];
            int length;
            while ((length = inputStream.read(b, 0, INPUT_STREAM_READ_BUFFER_SIZE)) != -1) {
                response.getOutputStream().write(b, 0, length);
            }
        } catch (Exception e) {
            log.error("Fail to show process, ", e);
            throw new BusinessException("流程文件展示失败", e);
        }
    }

}