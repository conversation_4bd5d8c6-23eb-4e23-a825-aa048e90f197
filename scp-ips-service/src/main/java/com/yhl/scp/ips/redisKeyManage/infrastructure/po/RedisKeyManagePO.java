package com.yhl.scp.ips.redisKeyManage.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>RedisKeyManagePO</code>
 * <p>
 * redis key管理PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-10 16:16:18
 */
public class RedisKeyManagePO extends BasePO implements Serializable {

    private static final long serialVersionUID = -82167871501148018L;

    /**
     * 模块名称
     */
    private String moduleCode;
    /**
     * 场景名称
     */
    private String scenarioName;
    /**
     * redis key
     */
    private String configCode;
    /**
     * 名称
     */
    private String configName;
    /**
     * 责任人
     */
    private String responsibleUser;
    /**
     * 是否启动删除（YES/NO）
     */
    private String initiateDeletion;
    /**
     * 创建人
     */
    private String creatorName;
    /**
     * 更新人
     */
    private String modifyName;

    public String getModuleCode() {
        return moduleCode;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public String getScenarioName() {
        return scenarioName;
    }

    public void setScenarioName(String scenarioName) {
        this.scenarioName = scenarioName;
    }

    public String getConfigCode() {
        return configCode;
    }

    public void setConfigCode(String configCode) {
        this.configCode = configCode;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public String getResponsibleUser() {
        return responsibleUser;
    }

    public void setResponsibleUser(String responsibleUser) {
        this.responsibleUser = responsibleUser;
    }

    public String getInitiateDeletion() {
        return initiateDeletion;
    }

    public void setInitiateDeletion(String initiateDeletion) {
        this.initiateDeletion = initiateDeletion;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getModifyName() {
        return modifyName;
    }

    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }

}
