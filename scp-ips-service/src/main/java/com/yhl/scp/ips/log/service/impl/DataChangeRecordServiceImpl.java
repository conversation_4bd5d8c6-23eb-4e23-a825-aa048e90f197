package com.yhl.scp.ips.log.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.log.convertor.DataChangeRecordConvertor;
import com.yhl.scp.ips.log.domain.entity.DataChangeRecordDO;
import com.yhl.scp.ips.log.domain.service.DataChangeRecordDomainService;
import com.yhl.scp.ips.log.dto.DataChangeRecordDTO;
import com.yhl.scp.ips.log.dto.DataChangeRecordQueryDTO;
import com.yhl.scp.ips.log.infrastructure.dao.DataChangeRecordDao;
import com.yhl.scp.ips.log.infrastructure.dao.DataChangeRecordRepository;
import com.yhl.scp.ips.log.infrastructure.po.DataChangeRecordPO;
import com.yhl.scp.ips.log.service.DataChangeRecordService;
import com.yhl.scp.ips.log.vo.DataChangeRecordVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DataChangeRecordServiceImpl extends AbstractService implements DataChangeRecordService {

    @Resource
    private DataChangeRecordDao dataChangeRecordDao;

    @Resource
    private DataChangeRecordDomainService dataChangeRecordDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private DataChangeRecordRepository dataChangeRecordRepository;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(DataChangeRecordDTO dataChangeRecordDTO) {
        // 0.数据转换
        DataChangeRecordDO dataChangeRecordDO = DataChangeRecordConvertor.INSTANCE.dto2Do(dataChangeRecordDTO);
        DataChangeRecordPO dataChangeRecordPO = DataChangeRecordConvertor.INSTANCE.dto2Po(dataChangeRecordDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        dataChangeRecordDomainService.validation(dataChangeRecordDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(dataChangeRecordPO);
        dataChangeRecordDao.insert(dataChangeRecordPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(DataChangeRecordDTO dataChangeRecordDTO) {
        // 0.数据转换
        DataChangeRecordDO dataChangeRecordDO = DataChangeRecordConvertor.INSTANCE.dto2Do(dataChangeRecordDTO);
        DataChangeRecordPO dataChangeRecordPO = DataChangeRecordConvertor.INSTANCE.dto2Po(dataChangeRecordDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        dataChangeRecordDomainService.validation(dataChangeRecordDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(dataChangeRecordPO);
        dataChangeRecordDao.update(dataChangeRecordPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<DataChangeRecordDTO> list) {
        List<DataChangeRecordPO> newList = DataChangeRecordConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        dataChangeRecordDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<DataChangeRecordDTO> list) {
        List<DataChangeRecordPO> newList = DataChangeRecordConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        dataChangeRecordDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return dataChangeRecordDao.deleteBatch(idList);
        }
        return dataChangeRecordDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public DataChangeRecordVO selectByPrimaryKey(String id) {
        DataChangeRecordPO po = dataChangeRecordDao.selectByPrimaryKey(id);
        return DataChangeRecordConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "DATA_CHANGE_RECORD")
    public List<DataChangeRecordVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "DATA_CHANGE_RECORD")
    public List<DataChangeRecordVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<DataChangeRecordVO> dataList = dataChangeRecordDao.selectByCondition(sortParam, queryCriteriaParam);
        DataChangeRecordServiceImpl target = springBeanUtils.getBean(DataChangeRecordServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<DataChangeRecordVO> selectByParams(Map<String, Object> params) {
        List<DataChangeRecordPO> list = dataChangeRecordDao.selectByParams(params);
        return DataChangeRecordConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<DataChangeRecordVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    /**
     * 处理数据
     *
     * @param data
     */
    @Override
    public void handle(String data) {
        DataChangeRecordDTO recordDTO = JSONObject.parseObject(data, DataChangeRecordDTO.class);
        dataChangeRecordRepository.save(recordDTO);
    }

    @Override
    public Page<DataChangeRecordDTO> selectByPage(Pagination pagination, DataChangeRecordQueryDTO queryDTO) {
        Pageable pageable = PageRequest.of(pagination.getPageNum() - 1, pagination.getPageSize(), Sort.by(Sort.Direction.DESC, "createTime"));
        DataChangeRecordDTO example = new DataChangeRecordDTO();
        ExampleMatcher matcher = ExampleMatcher.matching()
                .withIgnoreCase()
                .withStringMatcher(ExampleMatcher.StringMatcher.CONTAINING);

        if (Objects.nonNull(queryDTO)) {
            if (StringUtils.isNotBlank(queryDTO.getDataTableName())) {
                example.setDataTableName(queryDTO.getDataTableName());
                matcher.withMatcher("dataTableName", ExampleMatcher.GenericPropertyMatchers.contains());
            }
            if (StringUtils.isNotBlank(queryDTO.getPrimaryId())) {
                example.setPrimaryId(queryDTO.getPrimaryId());
                matcher.withMatcher("primaryId", ExampleMatcher.GenericPropertyMatchers.contains());
            }
        }
        return dataChangeRecordRepository.findAll(Example.of(example, matcher), pageable);
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<DataChangeRecordVO> invocation(List<DataChangeRecordVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
