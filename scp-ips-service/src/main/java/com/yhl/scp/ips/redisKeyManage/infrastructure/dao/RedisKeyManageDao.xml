<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.ips.redisKeyManage.infrastructure.dao.RedisKeyManageDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.ips.redisKeyManage.infrastructure.po.RedisKeyManagePO">
        <!--@Table conf_redis_key_manage-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="module_code" jdbcType="VARCHAR" property="moduleCode"/>
        <result column="scenario_name" jdbcType="VARCHAR" property="scenarioName"/>
        <result column="config_code" jdbcType="VARCHAR" property="configCode"/>
        <result column="config_name" jdbcType="VARCHAR" property="configName"/>
        <result column="responsible_user" jdbcType="VARCHAR" property="responsibleUser"/>
        <result column="initiate_deletion" jdbcType="VARCHAR" property="initiateDeletion"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="creator_name" jdbcType="VARCHAR" property="creatorName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_name" jdbcType="VARCHAR" property="modifyName"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.ips.redisKeyManage.vo.RedisKeyManageVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id,module_code,scenario_name,config_code,config_name,responsible_user,initiate_deletion,enabled,creator,creator_name,create_time,modifier,modify_name,modify_time
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.moduleCode != null and params.moduleCode != ''">
                and module_code = #{params.moduleCode,jdbcType=VARCHAR}
            </if>
            <if test="params.moduleCodes != null and params.moduleCodes.size() > 0">
                and module_code in
                <foreach collection="params.moduleCodes" item="item" index="index" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.scenarioName != null and params.scenarioName != ''">
                and scenario_name = #{params.scenarioName,jdbcType=VARCHAR}
            </if>
            <if test="params.configCode != null and params.configCode != ''">
                and config_code = #{params.configCode,jdbcType=VARCHAR}
            </if>
            <if test="params.configName != null and params.configName != ''">
                and config_name = #{params.configName,jdbcType=VARCHAR}
            </if>
            <if test="params.responsibleUser != null and params.responsibleUser != ''">
                and responsible_user = #{params.responsibleUser,jdbcType=VARCHAR}
            </if>
            <if test="params.initiateDeletion != null and params.initiateDeletion != ''">
                and initiate_deletion = #{params.initiateDeletion,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.creatorName != null and params.creatorName != ''">
                and creator_name = #{params.creatorName,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyName != null and params.modifyName != ''">
                and modify_name = #{params.modifyName,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from conf_redis_key_manage
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from conf_redis_key_manage
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from conf_redis_key_manage
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from conf_redis_key_manage
        <include refid="Base_Where_Condition"/>
    </select>

    <select id="selectVOByParams" resultMap="VOResultMap">
        select
        <include refid="Base_Column_List"/>
        from conf_redis_key_manage
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.ips.redisKeyManage.infrastructure.po.RedisKeyManagePO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into conf_redis_key_manage(
        id,
        module_code,
        scenario_name,
        config_code,
        config_name,
        responsible_user,
        initiate_deletion,
        enabled,
        creator,
        creator_name,
        create_time,
        modifier,
        modify_name,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{moduleCode,jdbcType=VARCHAR},
        #{scenarioName,jdbcType=VARCHAR},
        #{configCode,jdbcType=VARCHAR},
        #{configName,jdbcType=VARCHAR},
        #{responsibleUser,jdbcType=VARCHAR},
        #{initiateDeletion,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{creatorName,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyName,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.ips.redisKeyManage.infrastructure.po.RedisKeyManagePO">
        insert into conf_redis_key_manage(id,
                                          module_code,
                                          scenario_name,
                                          config_code,
                                          config_name,
                                          responsible_user,
                                          initiate_deletion,
                                          enabled,
                                          creator,
                                          creator_name,
                                          create_time,
                                          modifier,
                                          modify_name,
                                          modify_time)
        values (#{id,jdbcType=VARCHAR},
                #{moduleCode,jdbcType=VARCHAR},
                #{scenarioName,jdbcType=VARCHAR},
                #{configCode,jdbcType=VARCHAR},
                #{configName,jdbcType=VARCHAR},
                #{responsibleUser,jdbcType=VARCHAR},
                #{initiateDeletion,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{creatorName,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyName,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into conf_redis_key_manage(
        id,
        module_code,
        scenario_name,
        config_code,
        config_name,
        responsible_user,
        initiate_deletion,
        enabled,
        creator,
        creator_name,
        create_time,
        modifier,
        modify_name,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.moduleCode,jdbcType=VARCHAR},
            #{entity.scenarioName,jdbcType=VARCHAR},
            #{entity.configCode,jdbcType=VARCHAR},
            #{entity.configName,jdbcType=VARCHAR},
            #{entity.responsibleUser,jdbcType=VARCHAR},
            #{entity.initiateDeletion,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.creatorName,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyName,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into conf_redis_key_manage(
        id,
        module_code,
        scenario_name,
        config_code,
        config_name,
        responsible_user,
        initiate_deletion,
        enabled,
        creator,
        creator_name,
        create_time,
        modifier,
        modify_name,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.moduleCode,jdbcType=VARCHAR},
            #{entity.scenarioName,jdbcType=VARCHAR},
            #{entity.configCode,jdbcType=VARCHAR},
            #{entity.configName,jdbcType=VARCHAR},
            #{entity.responsibleUser,jdbcType=VARCHAR},
            #{entity.initiateDeletion,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.creatorName,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyName,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.ips.redisKeyManage.infrastructure.po.RedisKeyManagePO">
        update conf_redis_key_manage
        set module_code       = #{moduleCode,jdbcType=VARCHAR},
            scenario_name     = #{scenarioName,jdbcType=VARCHAR},
            config_code       = #{configCode,jdbcType=VARCHAR},
            config_name       = #{configName,jdbcType=VARCHAR},
            responsible_user  = #{responsibleUser,jdbcType=VARCHAR},
            initiate_deletion = #{initiateDeletion,jdbcType=VARCHAR},
            enabled           = #{enabled,jdbcType=VARCHAR},
            creator_name      = #{creatorName,jdbcType=VARCHAR},
            modifier          = #{modifier,jdbcType=VARCHAR},
            modify_name       = #{modifyName,jdbcType=VARCHAR},
            modify_time       = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.ips.redisKeyManage.infrastructure.po.RedisKeyManagePO">
        update conf_redis_key_manage
        <set>
            <if test="item.moduleCode != null and item.moduleCode != ''">
                module_code = #{item.moduleCode,jdbcType=VARCHAR},
            </if>
            <if test="item.scenarioName != null and item.scenarioName != ''">
                scenario_name = #{item.scenarioName,jdbcType=VARCHAR},
            </if>
            <if test="item.configCode != null and item.configCode != ''">
                config_code = #{item.configCode,jdbcType=VARCHAR},
            </if>
            <if test="item.configName != null and item.configName != ''">
                config_name = #{item.configName,jdbcType=VARCHAR},
            </if>
            <if test="item.responsibleUser != null and item.responsibleUser != ''">
                responsible_user = #{item.responsibleUser,jdbcType=VARCHAR},
            </if>
            <if test="item.initiateDeletion != null and item.initiateDeletion != ''">
                initiate_deletion = #{item.initiateDeletion,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.creatorName != null and item.creatorName != ''">
                creator_name = #{item.creatorName,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyName != null and item.modifyName != ''">
                modify_name = #{item.modifyName,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update conf_redis_key_manage
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="module_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.moduleCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="scenario_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.scenarioName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="config_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.configCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="config_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.configName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="responsible_user = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.responsibleUser,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="initiate_deletion = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.initiateDeletion,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creatorName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update conf_redis_key_manage
            <set>
                <if test="item.moduleCode != null and item.moduleCode != ''">
                    module_code = #{item.moduleCode,jdbcType=VARCHAR},
                </if>
                <if test="item.scenarioName != null and item.scenarioName != ''">
                    scenario_name = #{item.scenarioName,jdbcType=VARCHAR},
                </if>
                <if test="item.configCode != null and item.configCode != ''">
                    config_code = #{item.configCode,jdbcType=VARCHAR},
                </if>
                <if test="item.configName != null and item.configName != ''">
                    config_name = #{item.configName,jdbcType=VARCHAR},
                </if>
                <if test="item.responsibleUser != null and item.responsibleUser != ''">
                    responsible_user = #{item.responsibleUser,jdbcType=VARCHAR},
                </if>
                <if test="item.initiateDeletion != null and item.initiateDeletion != ''">
                    initiate_deletion = #{item.initiateDeletion,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.creatorName != null and item.creatorName != ''">
                    creator_name = #{item.creatorName,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyName != null and item.modifyName != ''">
                    modify_name = #{item.modifyName,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from conf_redis_key_manage
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from conf_redis_key_manage where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
