package com.yhl.scp.ips.config;

import cn.hutool.core.map.MapUtil;
import com.google.common.collect.Lists;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.redisKeyManage.infrastructure.dao.RedisKeyManageDao;
import com.yhl.scp.ips.redisKeyManage.vo.RedisKeyManageVO;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.ips.system.service.ScenarioService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 初始化字典表信息至缓存
 */
@Component
@Slf4j
public class RedisKeyManageInit implements ApplicationRunner {

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private RedisKeyManageDao redisKeyManageDao;

    @Resource
    private ScenarioService scenarioService;

    @Resource
    private CollectionCacheInit collectionCacheInit;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("开始清理redis key信息");
        BaseResponse<List<CollectionValueVO>> redisKeyModuleResponse = collectionCacheInit.getByCollectionCode("REDIS_KEY_MODULE");
        List<CollectionValueVO> collectionValues = redisKeyModuleResponse.getData();
        List<String> moduleCodes = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(collectionValues)) {
            moduleCodes.addAll(collectionValues.stream().map(CollectionValueVO::getCollectionValue).distinct().collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(moduleCodes)) {
            moduleCodes.addAll(Lists.newArrayList("IPS", "MDS", "MPS", "MRP", "DFP", "DCP"));
        }
        List<Scenario> scenarios = scenarioService.selectAll();
        if (CollectionUtils.isEmpty(scenarios)) {
            log.error("清理redis key，未查询到场景信息");
            return;
        }
        List<String> patternKeys = Lists.newArrayList();
        for (Scenario scenario : scenarios) {
            Map<String, Object> params = MapUtil.newHashMap();
            params.put("scenarioName", scenario.getDataBaseName());
            params.put("moduleCodes", moduleCodes);
            params.put("enabled", YesOrNoEnum.YES.getCode());
            List<RedisKeyManageVO> redisKeyManageVOS = redisKeyManageDao.selectVOByParams(params);
            if (CollectionUtils.isEmpty(redisKeyManageVOS)) {
                continue;
            }
            for (RedisKeyManageVO redisKeyManageVO : redisKeyManageVOS) {
                String configCode = redisKeyManageVO.getConfigCode();
                String initiateDeletion = redisKeyManageVO.getInitiateDeletion();
                if (StringUtils.isBlank(initiateDeletion) || !YesOrNoEnum.YES.getCode().equals(initiateDeletion)) {
                    continue;
                }
                if (StringUtils.isBlank(configCode)) {
                    continue;
                }
                int index = configCode.indexOf("{tenantId}");
                if (index > 0) {
                    configCode = configCode.substring(0, index);
                }
                index = configCode.indexOf("{userId}");
                if (index > 0) {
                    configCode = configCode.substring(0, index);
                }
                String patternKey = String.format(configCode, "*");
                patternKeys.add(patternKey);
            }
        }
        if (CollectionUtils.isEmpty(patternKeys)) {
            log.info("redis key信息清理完成，没有需要删除的key");
            return;
        }
        for (String patternKey : patternKeys) {
            Set<String> keys = redisUtil.keys(patternKey);
            if (CollectionUtils.isEmpty(keys)) {
                continue;
            }
            for (String key : keys) {
                if (redisUtil.hasKey(key)) {
                    redisUtil.delete(key);
                }
            }
        }
        log.info("redis key信息清理完成");
    }
}
