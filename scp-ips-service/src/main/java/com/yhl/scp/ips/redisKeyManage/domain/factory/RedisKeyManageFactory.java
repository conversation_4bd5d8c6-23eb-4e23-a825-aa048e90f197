package com.yhl.scp.ips.redisKeyManage.domain.factory;

import com.yhl.scp.ips.redisKeyManage.domain.entity.RedisKeyManageDO;
import com.yhl.scp.ips.redisKeyManage.dto.RedisKeyManageDTO;
import com.yhl.scp.ips.redisKeyManage.infrastructure.dao.RedisKeyManageDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>RedisKeyManageFactory</code>
 * <p>
 * redis key管理领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-10 16:16:31
 */
@Component
public class RedisKeyManageFactory {

    @Resource
    private RedisKeyManageDao redisKeyManageDao;

    RedisKeyManageDO create(RedisKeyManageDTO dto) {
        // TODO
        return null;
    }

}
