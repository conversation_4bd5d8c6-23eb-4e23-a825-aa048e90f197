<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.ips.warning.infrastructure.dao.WarningSqlSettingDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.ips.warning.infrastructure.po.WarningSqlSettingPO">
        <!--@Table warning_sql_setting-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="warning_code" jdbcType="VARCHAR" property="warningCode"/>
        <result column="warning_description" jdbcType="VARCHAR" property="warningDescription"/>
        <result column="receiver" jdbcType="VARCHAR" property="receiver"/>
        <result column="carbon_copy" jdbcType="VARCHAR" property="carbonCopy"/>
        <result column="blind_carbon_copy" jdbcType="VARCHAR" property="blindCarbonCopy"/>
        <result column="sql_context" jdbcType="VARCHAR" property="sqlContext"/>
        <result column="database_tenant" jdbcType="VARCHAR" property="databaseTenant"/>
        <result column="polling_time" jdbcType="VARCHAR" property="pollingTime"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="activation_date" jdbcType="TIMESTAMP" property="activationDate"/>
        <result column="invalid_date" jdbcType="TIMESTAMP" property="invalidDate"/>
        <result column="text_content" jdbcType="VARCHAR" property="textContent"/>
        <result column="send_way" jdbcType="VARCHAR" property="sendWay"/>
        <result column="staff_code" jdbcType="VARCHAR" property="staffCode"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="data_source" jdbcType="VARCHAR" property="dataSource"/>
        <result column="interface_setting" jdbcType="VARCHAR" property="interfaceSetting"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.ips.warning.vo.WarningSqlSettingVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id,warning_code,warning_description,receiver,carbon_copy,blind_carbon_copy,sql_context,database_tenant,polling_time,enabled,creator,create_time,modifier,modify_time,version_value,remark,activation_date,invalid_date,text_content,send_way,staff_code,phone,data_source,interface_setting
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.warningCode != null and params.warningCode != ''">
                and warning_code = #{params.warningCode,jdbcType=VARCHAR}
            </if>
            <if test="params.warningDescription != null and params.warningDescription != ''">
                and warning_description = #{params.warningDescription,jdbcType=VARCHAR}
            </if>
            <if test="params.receiver != null and params.receiver != ''">
                and receiver = #{params.receiver,jdbcType=VARCHAR}
            </if>
            <if test="params.carbonCopy != null and params.carbonCopy != ''">
                and carbon_copy = #{params.carbonCopy,jdbcType=VARCHAR}
            </if>
            <if test="params.blindCarbonCopy != null and params.blindCarbonCopy != ''">
                and blind_carbon_copy = #{params.blindCarbonCopy,jdbcType=VARCHAR}
            </if>
            <if test="params.sqlContext != null and params.sqlContext != ''">
                and sql_context = #{params.sqlContext,jdbcType=VARCHAR}
            </if>
            <if test="params.databaseTenant != null and params.databaseTenant != ''">
                and database_tenant = #{params.databaseTenant,jdbcType=VARCHAR}
            </if>
            <if test="params.pollingTime != null and params.pollingTime != ''">
                and polling_time = #{params.pollingTime,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.activationDate != null">
                and activation_date = #{params.activationDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.invalidDate != null">
                and invalid_date = #{params.invalidDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.textContent != null and params.textContent != ''">
                and text_content = #{params.textContent,jdbcType=VARCHAR}
            </if>
            <if test="params.sendWay != null and params.sendWay != ''">
                and send_way = #{params.sendWay,jdbcType=VARCHAR}
            </if>
            <if test="params.staffCode != null and params.staffCode != ''">
                and staff_code = #{params.staffCode,jdbcType=VARCHAR}
            </if>
            <if test="params.phone != null and params.phone != ''">
                and phone = #{params.phone,jdbcType=VARCHAR}
            </if>
            <if test="params.dataSource != null and params.dataSource != ''">
                and data_source = #{params.dataSource,jdbcType=VARCHAR}
            </if>
            <if test="params.interfaceSetting != null and params.interfaceSetting != ''">
                and interface_setting = #{params.interfaceSetting,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from warning_sql_setting
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from warning_sql_setting
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from warning_sql_setting
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from warning_sql_setting
        <include refid="Base_Where_Condition" />
    </select>
    <!--    -->
    <select id="selectByManualSql" resultType="java.util.LinkedHashMap">
        ${sqlContext}
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.ips.warning.infrastructure.po.WarningSqlSettingPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into warning_sql_setting(
        id,
        warning_code,
        warning_description,
        receiver,
        carbon_copy,
        blind_carbon_copy,
        sql_context,
        database_tenant,
        polling_time,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        remark,
        activation_date,
        invalid_date,
        text_content,
        send_way,
        staff_code,
        phone,
        data_source,
        interface_setting)
        values (
        #{id,jdbcType=VARCHAR},
        #{warningCode,jdbcType=VARCHAR},
        #{warningDescription,jdbcType=VARCHAR},
        #{receiver,jdbcType=VARCHAR},
        #{carbonCopy,jdbcType=VARCHAR},
        #{blindCarbonCopy,jdbcType=VARCHAR},
        #{sqlContext,jdbcType=VARCHAR},
        #{databaseTenant,jdbcType=VARCHAR},
        #{pollingTime,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{remark,jdbcType=VARCHAR},
        #{activationDate,jdbcType=TIMESTAMP},
        #{invalidDate,jdbcType=TIMESTAMP},
        #{textContent,jdbcType=VARCHAR},
        #{sendWay,jdbcType=VARCHAR},
        #{staffCode,jdbcType=VARCHAR},
        #{phone,jdbcType=VARCHAR},
        #{dataSource,jdbcType=VARCHAR},
        #{interfaceSetting,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.ips.warning.infrastructure.po.WarningSqlSettingPO">
        insert into warning_sql_setting(
        id,
        warning_code,
        warning_description,
        receiver,
        carbon_copy,
        blind_carbon_copy,
        sql_context,
        database_tenant,
        polling_time,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        remark,
        activation_date,
        invalid_date,
        text_content,
        send_way,
        staff_code,
        phone,
        data_source,
        interface_setting)
        values (
        #{id,jdbcType=VARCHAR},
        #{warningCode,jdbcType=VARCHAR},
        #{warningDescription,jdbcType=VARCHAR},
        #{receiver,jdbcType=VARCHAR},
        #{carbonCopy,jdbcType=VARCHAR},
        #{blindCarbonCopy,jdbcType=VARCHAR},
        #{sqlContext,jdbcType=VARCHAR},
        #{databaseTenant,jdbcType=VARCHAR},
        #{pollingTime,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{remark,jdbcType=VARCHAR},
        #{activationDate,jdbcType=TIMESTAMP},
        #{invalidDate,jdbcType=TIMESTAMP},
        #{textContent,jdbcType=VARCHAR},
        #{sendWay,jdbcType=VARCHAR},
        #{staffCode,jdbcType=VARCHAR},
        #{phone,jdbcType=VARCHAR},
        #{dataSource,jdbcType=VARCHAR},
        #{interfaceSetting,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into warning_sql_setting(
        id,
        warning_code,
        warning_description,
        receiver,
        carbon_copy,
        blind_carbon_copy,
        sql_context,
        database_tenant,
        polling_time,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        remark,
        activation_date,
        invalid_date,
        text_content,
        send_way,
        staff_code,
        phone,
        data_source,
        interface_setting)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.warningCode,jdbcType=VARCHAR},
        #{entity.warningDescription,jdbcType=VARCHAR},
        #{entity.receiver,jdbcType=VARCHAR},
        #{entity.carbonCopy,jdbcType=VARCHAR},
        #{entity.blindCarbonCopy,jdbcType=VARCHAR},
        #{entity.sqlContext,jdbcType=VARCHAR},
        #{entity.databaseTenant,jdbcType=VARCHAR},
        #{entity.pollingTime,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.activationDate,jdbcType=TIMESTAMP},
        #{entity.invalidDate,jdbcType=TIMESTAMP},
        #{entity.textContent,jdbcType=VARCHAR},
        #{entity.sendWay,jdbcType=VARCHAR},
        #{entity.staffCode,jdbcType=VARCHAR},
        #{entity.phone,jdbcType=VARCHAR},
        #{entity.dataSource,jdbcType=VARCHAR},
        #{entity.interfaceSetting,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into warning_sql_setting(
        id,
        warning_code,
        warning_description,
        receiver,
        carbon_copy,
        blind_carbon_copy,
        sql_context,
        database_tenant,
        polling_time,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        remark,
        activation_date,
        invalid_date,
        text_content,
        send_way,
        staff_code,
        phone,
        data_source,
        interface_setting)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.warningCode,jdbcType=VARCHAR},
        #{entity.warningDescription,jdbcType=VARCHAR},
        #{entity.receiver,jdbcType=VARCHAR},
        #{entity.carbonCopy,jdbcType=VARCHAR},
        #{entity.blindCarbonCopy,jdbcType=VARCHAR},
        #{entity.sqlContext,jdbcType=VARCHAR},
        #{entity.databaseTenant,jdbcType=VARCHAR},
        #{entity.pollingTime,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.activationDate,jdbcType=TIMESTAMP},
        #{entity.invalidDate,jdbcType=TIMESTAMP},
        #{entity.textContent,jdbcType=VARCHAR},
        #{entity.sendWay,jdbcType=VARCHAR},
        #{entity.staffCode,jdbcType=VARCHAR},
        #{entity.phone,jdbcType=VARCHAR},
        #{entity.dataSource,jdbcType=VARCHAR},
        #{entity.interfaceSetting,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.ips.warning.infrastructure.po.WarningSqlSettingPO">
        update warning_sql_setting set
        warning_code = #{warningCode,jdbcType=VARCHAR},
        warning_description = #{warningDescription,jdbcType=VARCHAR},
        receiver = #{receiver,jdbcType=VARCHAR},
        carbon_copy = #{carbonCopy,jdbcType=VARCHAR},
        blind_carbon_copy = #{blindCarbonCopy,jdbcType=VARCHAR},
        sql_context = #{sqlContext,jdbcType=VARCHAR},
        database_tenant = #{databaseTenant,jdbcType=VARCHAR},
        polling_time = #{pollingTime,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        version_value = #{versionValue,jdbcType=INTEGER},
        remark = #{remark,jdbcType=VARCHAR},
        activation_date = #{activationDate,jdbcType=TIMESTAMP},
        invalid_date = #{invalidDate,jdbcType=TIMESTAMP},
        text_content = #{textContent,jdbcType=VARCHAR},
        send_way = #{sendWay,jdbcType=VARCHAR},
        staff_code = #{staffCode,jdbcType=VARCHAR},
        phone = #{phone,jdbcType=VARCHAR},
        data_source = #{dataSource,jdbcType=VARCHAR},
        interface_setting = #{interfaceSetting,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.ips.warning.infrastructure.po.WarningSqlSettingPO">
        update warning_sql_setting
        <set>
            <if test="item.warningCode != null and item.warningCode != ''">
                warning_code = #{item.warningCode,jdbcType=VARCHAR},
            </if>
            <if test="item.warningDescription != null and item.warningDescription != ''">
                warning_description = #{item.warningDescription,jdbcType=VARCHAR},
            </if>
            <if test="item.receiver != null and item.receiver != ''">
                receiver = #{item.receiver,jdbcType=VARCHAR},
            </if>
            <if test="item.carbonCopy != null and item.carbonCopy != ''">
                carbon_copy = #{item.carbonCopy,jdbcType=VARCHAR},
            </if>
            <if test="item.blindCarbonCopy != null and item.blindCarbonCopy != ''">
                blind_carbon_copy = #{item.blindCarbonCopy,jdbcType=VARCHAR},
            </if>
            <if test="item.sqlContext != null and item.sqlContext != ''">
                sql_context = #{item.sqlContext,jdbcType=VARCHAR},
            </if>
            <if test="item.databaseTenant != null and item.databaseTenant != ''">
                database_tenant = #{item.databaseTenant,jdbcType=VARCHAR},
            </if>
            <if test="item.pollingTime != null and item.pollingTime != ''">
                polling_time = #{item.pollingTime,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.activationDate != null">
                activation_date = #{item.activationDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.invalidDate != null">
                invalid_date = #{item.invalidDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.textContent != null and item.textContent != ''">
                text_content = #{item.textContent,jdbcType=VARCHAR},
            </if>
            <if test="item.sendWay != null and item.sendWay != ''">
                send_way = #{item.sendWay,jdbcType=VARCHAR},
            </if>
            <if test="item.staffCode != null and item.staffCode != ''">
                staff_code = #{item.staffCode,jdbcType=VARCHAR},
            </if>
            <if test="item.phone != null and item.phone != ''">
                phone = #{item.phone,jdbcType=VARCHAR},
            </if>
            <if test="item.dataSource != null and item.dataSource != ''">
                data_source = #{item.dataSource,jdbcType=VARCHAR},
            </if>
            <if test="item.interfaceSetting != null and item.interfaceSetting != ''">
                interface_setting = #{item.interfaceSetting,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update warning_sql_setting
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="warning_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.warningCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="warning_description = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.warningDescription,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="receiver = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.receiver,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="carbon_copy = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.carbonCopy,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="blind_carbon_copy = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.blindCarbonCopy,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sql_context = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sqlContext,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="database_tenant = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.databaseTenant,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="polling_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.pollingTime,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="activation_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.activationDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="invalid_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.invalidDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="text_content = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.textContent,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="send_way = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sendWay,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="staff_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.staffCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="phone = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.phone,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="data_source = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.dataSource,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="interface_setting = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.interfaceSetting,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update warning_sql_setting 
        <set>
            <if test="item.warningCode != null and item.warningCode != ''">
                warning_code = #{item.warningCode,jdbcType=VARCHAR},
            </if>
            <if test="item.warningDescription != null and item.warningDescription != ''">
                warning_description = #{item.warningDescription,jdbcType=VARCHAR},
            </if>
            <if test="item.receiver != null and item.receiver != ''">
                receiver = #{item.receiver,jdbcType=VARCHAR},
            </if>
            <if test="item.carbonCopy != null and item.carbonCopy != ''">
                carbon_copy = #{item.carbonCopy,jdbcType=VARCHAR},
            </if>
            <if test="item.blindCarbonCopy != null and item.blindCarbonCopy != ''">
                blind_carbon_copy = #{item.blindCarbonCopy,jdbcType=VARCHAR},
            </if>
            <if test="item.sqlContext != null and item.sqlContext != ''">
                sql_context = #{item.sqlContext,jdbcType=VARCHAR},
            </if>
            <if test="item.databaseTenant != null and item.databaseTenant != ''">
                database_tenant = #{item.databaseTenant,jdbcType=VARCHAR},
            </if>
            <if test="item.pollingTime != null and item.pollingTime != ''">
                polling_time = #{item.pollingTime,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.activationDate != null">
                activation_date = #{item.activationDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.invalidDate != null">
                invalid_date = #{item.invalidDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.textContent != null and item.textContent != ''">
                text_content = #{item.textContent,jdbcType=VARCHAR},
            </if>
            <if test="item.sendWay != null and item.sendWay != ''">
                send_way = #{item.sendWay,jdbcType=VARCHAR},
            </if>
            <if test="item.staffCode != null and item.staffCode != ''">
                staff_code = #{item.staffCode,jdbcType=VARCHAR},
            </if>
            <if test="item.phone != null and item.phone != ''">
                phone = #{item.phone,jdbcType=VARCHAR},
            </if>
            <if test="item.dataSource != null and item.dataSource != ''">
                data_source = #{item.dataSource,jdbcType=VARCHAR},
            </if>
            <if test="item.interfaceSetting != null and item.interfaceSetting != ''">
                interface_setting = #{item.interfaceSetting,jdbcType=VARCHAR},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from warning_sql_setting where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from warning_sql_setting where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
