package com.yhl.scp.ips.log.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DataChangeRecordDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 834228382013760399L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 表名
     */
    private String dataTableName;
    /**
     * 数据ID
     */
    private String primaryId;
    /**
     * 修改前数据
     */
    private String beforeData;
    /**
     * 修改后数据
     */
    private String afterData;
    /**
     * 操作人
     */
    private String operateUser;
    /**
     * 操作类型
     */
    private String operateType;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date modifyTime;

}
