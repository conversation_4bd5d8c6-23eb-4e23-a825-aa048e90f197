package com.yhl.scp.das.service.impl;

import java.io.IOException;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import cn.hutool.core.collection.ListUtil;
import com.yhl.scp.biz.common.enums.ModuleCodeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.das.actuator.DfpActuator;
import com.yhl.scp.das.core.ActuatorStrategy;
import com.yhl.scp.das.core.TaskStatus;
import com.yhl.scp.das.service.AlgorithmService;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.system.entity.AlgorithmLog;

import lombok.extern.slf4j.Slf4j;

/**
 * <code>AlgorithmServiceImpl</code>
 * <p>
 * AlgorithmServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-12-11 13:49:40
 */
@Slf4j
@Service
public class AlgorithmServiceImpl implements AlgorithmService {
	
	@Resource
    private IpsFeign ipsFeign;
	
	@Autowired
    private ActuatorStrategy actuatorStrategy;

	@Override
	public BaseResponse<String> doStopAlgorithm(String id) throws Exception {
		log.info("开始执行算法运行程序终止：{}", id);
		//1.获取算法日志
		AlgorithmLog algorithmLog = ipsFeign.selectAlgorithmLogById(id).getData();
		if(!TaskStatus.RUNNING.equals(algorithmLog.getStatus())) {
			return BaseResponse.error("仅支持运行中的算法程序终止");
		}
		String moduleCode = algorithmLog.getModuleCode();
		List<String> moduleList = ListUtil.of(ModuleCodeEnum.DFP.getCode(), ModuleCodeEnum.MPS.getCode());
		if(!moduleList.contains(moduleCode)){
			algorithmLog.setStatus(TaskStatus.FAIL);
			algorithmLog.setEndTime(new Date());
			algorithmLog.setFailMsg("手动终止算法运行!");
			ipsFeign.updateAlgorithmLog(algorithmLog);
			return BaseResponse.success("操作成功!");
		}
		String executionNumber = algorithmLog.getExecutionNumber();
		//2.获取pid
		String pid = actuatorStrategy.getActuator(moduleCode).getPid(executionNumber);
		log.info("开始执行算法运行程序-获取算法运行程序PID：{}", pid);
        if(TaskStatus.WAITING.equals(pid)) {
        	//算法运行进程号文件尚未创建，等待创建
        	return BaseResponse.error("算法运行进程号文件尚未创建");
        }
		//3.执行kill -9 pid 中止算法运行
        try {
            //使用操作系统的命令来终止进程
            Process process = Runtime.getRuntime().exec("kill -9 " + pid);
            int exitCode = process.waitFor();
            log.info("开始执行算法运行程序-使用操作系统的命令来终止进程exitCode：{}", exitCode);
            if (exitCode == 0) {
            	//4.修改算法日志状态
            	algorithmLog.setStatus(TaskStatus.FAIL);
            	algorithmLog.setEndTime(new Date());
            	algorithmLog.setFailMsg("手动终止算法运行!");
            	ipsFeign.updateAlgorithmLog(algorithmLog);
                return BaseResponse.success("操作成功!");
            } else {//4.修改算法日志状态
				algorithmLog.setStatus(TaskStatus.FAIL);
				algorithmLog.setEndTime(new Date());
				algorithmLog.setFailMsg("异常手动终止算法运行!");
				ipsFeign.updateAlgorithmLog(algorithmLog);
                return BaseResponse.error("异常手动终止算法运行!");
            }
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
        return BaseResponse.success("操作成功!");
	}


}