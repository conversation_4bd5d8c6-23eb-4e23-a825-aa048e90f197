package com.yhl.scp.das.actuator;


import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.IOUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.das.common.constants.DasConstants;
import com.yhl.scp.das.core.*;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.mps.dispatch.ams.input.AmsAlgoInput;
import com.yhl.scp.mps.dispatch.annotation.AmsJsonAutoCreate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;

/**
 * <code>ApsActuator</code>
 * <p>
 * 规则式执行计划资源排程时间及排产数量，满足发货计划(APS/AMS)
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-12-11 13:40:35
 */
@Slf4j
@Component
public class ApsActuator extends IActuator {

    /**
     * 工作空间，主日计划共用一个路径
     */
    @Value("${aps.schedule.workspace}")
    private String workspace;

    @Resource
    private IpsFeign ipsFeign;

    @Resource
    AsynchronousCommandAlg asynchronousCommandAlg;

    @Override
    public AlgorithmResult create(InputBase inputBase) throws JsonProcessingException {
        String executionNumber = inputBase.getExecutionNumber();
        log.info("create a new actuator,executionNumber is {}", executionNumber);

        AlgorithmResult algorithmResult = new AlgorithmResult();

        algorithmResult.setTaskId(executionNumber);
        algorithmResult.setTaskStatus(TaskStatus.FAIL);

        if (StringUtils.isEmpty(executionNumber)) {
            algorithmResult.setMsg("executionNumber is empty");
            return algorithmResult;
        }

        if (null == inputBase.getInput()) {
            algorithmResult.setMsg("input is empty");
            return algorithmResult;
        }

        String dirPath = IOUtils.cleanseDir(false, workspace, executionNumber, "daily");
        String resultPath = IOUtils.cleanseDir(false, workspace, executionNumber, "daily", "output_inner");

        if (IOUtils.exists(dirPath)) {
            algorithmResult.setMsg("executionNumber used");
            log.warn("文件夹已经存在：{}", executionNumber);
            return algorithmResult;
        }

        IOUtils.create(dirPath);
        IOUtils.create(resultPath);

        log.info("工作基础路径：{},结果文件路径：{}", dirPath, resultPath);
        if (!IOUtils.exists(dirPath)) {
            algorithmResult.setMsg("create dir failed");
            return algorithmResult;
        }

        String amsInputFilePath = workspace + "/" + inputBase.getExecutionNumber() + "/daily/" + "input_inner/";
        log.info("ams file input path：{}", dirPath);

        AmsAlgoInput input = JSON.parseObject(inputBase.getInput().toString(), AmsAlgoInput.class);
        String confJsonFilePath = workspace + "/" + inputBase.getExecutionNumber() + "/daily/";
        log.info("conf.json生成路径：{}", confJsonFilePath);
        IOUtils.string2JSONFile(JSONUtil.toJsonStr(input.getConfigJson()), confJsonFilePath, "conf.json");
        // 获取类的所有字段
        Field[] fields = AmsAlgoInput.class.getDeclaredFields();
        for (Field field : fields) {
            // 检查字段是否有 AmsJsonAutoCreate 注解
            Annotation annotation = field.getAnnotation(AmsJsonAutoCreate.class);
            if (annotation != null) {
                // 获取 fileName 的值
                String fileName = ((AmsJsonAutoCreate) annotation).fileName();
                // 设置访问权限以便可以访问私有字段
                field.setAccessible(true);
                try {
                    // 获取字段值
                    Object fieldValue = field.get(input);
                    if (null == fieldValue) {
                        log.warn("数据为空，不生成文件：{}", fileName);
                        continue;
                    }
                    IOUtils.string2JSONFile(JSONUtil.toJsonStr(fieldValue), amsInputFilePath, fileName);
                    log.info("AMS创建文件路径：{}，文件名：{}", amsInputFilePath, fileName);
                } catch (Exception e) {
                    log.error("生成AMS文件失败：", e);
                    throw new BusinessException("AMS输入文件创建失败：" + e.getMessage());
                }
            }
        }


        try {
            // 调用C++
            log.info("C++ 调用工作空间：{}", dirPath);
            Object amsResult = asynchronousCommandAlg.syncExecute(DasConstants.ALGORITHM_TASK_TYPE_APS, dirPath, "log", resultPath);
            algorithmResult.setData(amsResult);
        } catch (Exception e) {
            log.error("C++ 算法程序调用异常", e);
            // fixme 调试不清除基础数据
            //            this.cleanFile(executionNumber);
            throw new BusinessException("C++程序调用异常：" + e.getMessage());
        }

        algorithmResult.setTaskStatus(TaskStatus.SUCCESS);
        algorithmResult.setMsg("create executed successfully");

        log.info("create a new actuator,executionNumber is {}", executionNumber);
        return algorithmResult;
    }

    private void cleanFile(String executionNumber) {
        String dirPath = IOUtils.cleanseDir(false, workspace, executionNumber, "daily");
        if (IOUtils.exists(dirPath)) {
            IOUtils.delete(dirPath);
            log.info("C++运行失败，文件已清除-路径：{}", dirPath);
        }
    }


    @Override
    public AlgorithmResult getResult(String executionNumber) {
        return null;
    }

    @Override
    public String getActuator() {
        return SystemModuleEnum.AMS.getCode();
    }

    @Override
    public String check(String executionNumber) throws Exception {
        return null;
    }

    @Override
    public String getPid(String executionNumber) throws Exception {
        return null;
    }

}
