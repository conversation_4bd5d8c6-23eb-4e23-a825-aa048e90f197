package com.yhl.scp.ips.rbac.service;

import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.rbac.dto.UserDTO;
import com.yhl.scp.ips.rbac.entity.User;

import java.util.List;
import java.util.Map;

/**
 * <code>UserService</code>
 * <p>
 * UserService
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-07-20 16:09:16
 */
public interface UserService {

    BaseResponse doCreate(User user);

    BaseResponse doUpdate(User user);

    User getUserByUserName(String userName);

    void doDelete(String userId);

    void doDisable(String userId);

    void doEnable(String userId);

    List<User> selectPage(Pagination pagination, String sortParam, String queryCriteriaParam);

    List<User> selectByParams(Map<String, Object> params);

    User selectById(String id);

    List<User> selectAll();

    void doUpdateById(User user);

    BaseResponse<Void> syncSyncUser(String startTime);

    BaseResponse<String> syncUserData(List<UserDTO> list);

	List<LabelValue<String>> selectOrderPlannerDropDown();

    void syncAllUser();

    BaseResponse<String> syncAllUserData(List<UserDTO> list);
}