package com.yhl.scp.ips.redisKeyManage.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * <code>RedisKeyManageDTO</code>
 * <p>
 * redis key管理DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-10 16:15:42
 */
@ApiModel(value = "redis key管理DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RedisKeyManageDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -58618988219097070L;

    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 模块名称
     */
    @ApiModelProperty(value = "模块名称")
    private String moduleCode;
    /**
     * 场景名称
     */
    @ApiModelProperty(value = "场景名称")
    private String scenarioName;
    /**
     * redis key
     */
    @ApiModelProperty(value = "redis key")
    private String configCode;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String configName;
    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    private String responsibleUser;
    /**
     * 是否启动删除（YES/NO）
     */
    @ApiModelProperty(value = "是否启动删除（YES/NO）")
    private String initiateDeletion;
    /**
     * 是否有效 YES/NO
     */
    @ApiModelProperty(value = "是否有效 YES/NO")
    private String enabled;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creatorName;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String modifyName;

}
