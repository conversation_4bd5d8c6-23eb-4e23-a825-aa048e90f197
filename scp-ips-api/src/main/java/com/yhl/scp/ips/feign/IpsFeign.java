package com.yhl.scp.ips.feign;

import com.yhl.platform.common.FeignConfig;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.common.constants.ServletContextConstants;
import com.yhl.scp.common.vo.LabelValueX;
import com.yhl.scp.ips.algorithm.dto.AlgorithmStepLogDTO;
import com.yhl.scp.ips.algorithm.vo.AlgorithmServerVO;
import com.yhl.scp.ips.bpm.dto.BpmVariablesSetDTO;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.log.dto.ExcelImportLogDTO;
import com.yhl.scp.ips.rbac.vo.WorkBenchResourceVO;
import com.yhl.scp.ips.redisKeyManage.vo.RedisKeyManageVO;
import com.yhl.scp.ips.system.entity.AlgorithmLog;
import com.yhl.scp.ips.system.entity.Scenario;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <code>IpsFeign</code>
 * <p>
 * 平台管理服务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-08-28 14:23:01
 */
@FeignClient(value = ServletContextConstants.IPS, path = "/", configuration = FeignConfig.class, url = "${ips.feign.url:}")
public interface IpsFeign {

    @PostMapping(value = "/api/algorithmlog/create")
    BaseResponse createAlgorithmLog(@RequestBody AlgorithmLog algorithmLog);

    @PostMapping(value = "/api/algorithmlog/createNew")
    BaseResponse createAlgorithmLogNew(@RequestBody AlgorithmLog algorithmLogMap);

    @PostMapping(value = "/api/algorithmlog/update")
    BaseResponse updateAlgorithmLog(@RequestBody AlgorithmLog algorithmLog);

    @GetMapping(value = "/api/algorithmlog/{id}")
    BaseResponse<AlgorithmLog> selectAlgorithmLogById(@PathVariable("id") String id);

    /**
     * 根据场景获取算法最后一次执行记录
     *
     * @param scenario 场景
     * @return
     */
    @GetMapping(value = "/api/algorithmlog/getLatestByScenario")
    AlgorithmLog getLatestByScenario(@RequestParam("scenario") String scenario);

    /**
     * 根据场景获取算法执行步骤集合
     *
     * @param scenario
     * @return
     */
    @GetMapping(value = "/api/algorithmlog/getScheduleLogsForGantt")
    List<AlgorithmLog> getScheduleLogsForGantt(@RequestParam("scenario") String scenario);

    @GetMapping(value = "/api/scenario/getByScenario")
    Scenario getByScenario(@RequestParam("moduleCode") String moduleCode, @RequestParam("scenario") String scenario);

    @PostMapping(value = "/api/scenario/getByScenarios")
    List<Scenario> getByScenarios(@RequestBody List<String> scenarios);

    @GetMapping(value = "/api/scenario/list")
    BaseResponse<List<LabelValueX<String>>> scenariosList(@RequestHeader("tenant") String tenant,
                                                          @RequestHeader("module") String module);

    @PostMapping(value = "/api/bpmVariables/set")
    void setBpmVariables(@RequestBody BpmVariablesSetDTO bpmVariablesSetDTO);

    @GetMapping(value = "/api/bpmVariables/get")
    Object getBpmVariables(@RequestParam(value = "processInstanceId") String processInstanceId,
                           @RequestParam(value = "taskId", required = false) String taskId,
                           @RequestParam(value = "variableName", required = false) String variableName);


    /**
     * 清理算法临时数据
     */
    @GetMapping(value = "/api/algorithmlog/clean")
    void cleanTempData();

    /**
     * Excel导入日志
     */
    @PostMapping(value = "/api/excelImportLog/create")
    void createExcelImportLog(@RequestBody List<ExcelImportLogDTO> list);


    @GetMapping(value = "/api/collectionValue/getByCollectionCode")
    List<CollectionValueVO> getByCollectionCode(@RequestParam("collection") String collection);


    @PostMapping(value = "/api/algorithmlog/saveKpiData")
    void saveKpiData(@RequestParam("logId") String logId, @RequestParam("kpiJson") String json);

    @PostMapping(value = "/api/algorithmServer/selectAll")
    List<AlgorithmServerVO> selectAllAlgorithmServerVOS();

    @PostMapping(value = "/api/algorithmlog/selectByStatus")
    List<AlgorithmLog> selectAlgorithmLogsByStatus(@RequestParam("status") String status);


    @GetMapping(value = "/api/algorithmlog/getLogById")
    AlgorithmLog getLogById(@RequestParam("logId") String logId);

    @PostMapping(value = "/api/algorithmLog/checkTask")
    boolean operationCheck(@RequestBody List<String> arrayList);

    @GetMapping(value = "/api/algorithmLog/adJustPlanCheck")
    boolean adJustPlanCheck(@RequestParam("userId") String userId);

    @PostMapping(value = "/api/algorithmLog/selectRunningProductionLineGroupResource")
    List<AlgorithmLog> selectTaskIsNotFail(@RequestBody List<String> moduleCodeList);

    @PostMapping(value = "/api/algorithmLog/handleRunningTask")
    void handleRunningTask();

    @PostMapping(value = "/api/stepLog/batchInsert")
    void batchSaveStepLog(@RequestBody List<AlgorithmStepLogDTO> list);

    @GetMapping(value = "/api/favorite/favoriteWorkBenchResource")
    List<WorkBenchResourceVO> selectWorkBenchResource(@RequestParam("userId") String userId);

    @GetMapping(value = "/api/redisKeyManage/selectByModuleCode")
    List<RedisKeyManageVO> selectRedisKeyManageByModuleCode(@RequestParam("moduleCode") String moduleCode);
}