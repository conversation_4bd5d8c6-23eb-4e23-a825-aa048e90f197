package com.yhl.scp.ips.redisKeyManage.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <code>RedisKeyManageVO</code>
 * <p>
 * redis key管理VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-10 16:15:43
 */
@ApiModel(value = "redis key管理VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RedisKeyManageVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 516383633292465752L;

    /**
     * 模块名称
     */
    @ApiModelProperty(value = "模块名称")
    @FieldInterpretation(value = "模块名称")
    private String moduleCode;
    /**
     * 场景名称
     */
    @ApiModelProperty(value = "场景名称")
    @FieldInterpretation(value = "场景名称")
    private String scenarioName;
    /**
     * redis key
     */
    @ApiModelProperty(value = "redis key")
    @FieldInterpretation(value = "redis key")
    private String configCode;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @FieldInterpretation(value = "名称")
    private String configName;
    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    @FieldInterpretation(value = "责任人")
    private String responsibleUser;
    /**
     * 是否启动删除（YES/NO）
     */
    @ApiModelProperty(value = "是否启动删除（YES/NO）")
    @FieldInterpretation(value = "是否启动删除（YES/NO）")
    private String initiateDeletion;

    @Override
    public void clean() {

    }
}
