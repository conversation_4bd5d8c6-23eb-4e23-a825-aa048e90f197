package com.yhl.scp.mrp.material.plan.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanInventoryShiftPO;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanInventoryShiftVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <code>MaterialPlanInventoryShiftDao</code>
 * <p>
 * 物料库存推移DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-31 21:18:24
 */
public interface MaterialPlanInventoryShiftDao extends BaseDao<MaterialPlanInventoryShiftPO, MaterialPlanInventoryShiftVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link MaterialPlanInventoryShiftVO}
     */
    List<MaterialPlanInventoryShiftVO> selectVOByParams(@Param("params") Map<String, Object> params);

    /**
     * 查询原片需求征询的物料库存推移数据
     * @param params
     * @return list {@link MaterialPlanInventoryShiftVO}
     */
	List<MaterialPlanInventoryShiftVO> selectForDemandCalculation(@Param("params") Map<String, Object> params);

    List<MaterialPlanInventoryShiftVO> selectGroupMaterialInfo(@Param("params") Map<String, Object> params);

    void deleteByVersionId(@Param("materialPlanVersionId") String materialPlanVersionId);

    void deleteByProductCodes(@Param("productCodeList") List<String> productCodeList);

    List<MaterialPlanInventoryShiftVO> selectMaterialInventoryShift(@Param("latestVersionId") String latestVersionId, @Param("productCodeList") List<String> productCodeList, @Param("scopeStart") String scopeStart, @Param("scopeEnd") String scopeEnd);

    List<MaterialPlanInventoryShiftVO> selectGroupGlassInfo(@Param("params") Map<String, Object> params);

    List<MaterialPlanInventoryShiftVO> selectGroupGlassInfoTotal(@Param("params") Map<String, Object> params);

    void deleteByVersionIdAndProductCode(@Param("materialPlanVersionId")String materialPlanVersionId, @Param("productCode")String productCode);

    List<MaterialPlanInventoryShiftVO> selectMonthTotalDemand(@Param("latestVersionId") String latestVersionId);

    void deleteAll();

    Date getLastCreateTime(@Param("list") List<String> productCodes);
}
