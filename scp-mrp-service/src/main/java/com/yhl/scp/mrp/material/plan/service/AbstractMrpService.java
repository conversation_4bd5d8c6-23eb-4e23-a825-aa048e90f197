package com.yhl.scp.mrp.material.plan.service;

import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedVO;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RedisKeyManageEnum;
import com.yhl.scp.mds.basic.routing.vo.RoutingStepBasicVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepInputVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.capacityBalance.vo.CapacitySupplyRelationshipVO;
import com.yhl.scp.mps.demand.vo.OutsourceTransferDemandDetailVO;
import com.yhl.scp.mps.feign.MpsFeign;
import com.yhl.scp.mps.operationPublished.vo.DemandPublishedVO;
import com.yhl.scp.mps.operationPublished.vo.OperationInputPublishedVO;
import com.yhl.scp.mps.operationPublished.vo.OperationPublishedVO;
import com.yhl.scp.mps.operationPublished.vo.WorkOrderPublishedVO;
import com.yhl.scp.mrp.MrpNewProductStockPoint.infrastructure.dao.MrpNewProductStockPointDao;
import com.yhl.scp.mrp.common.constants.MaterialRedisConstants;
import com.yhl.scp.mrp.halfsubinventory.service.WarehouseHalfSubinventoryService;
import com.yhl.scp.mrp.inventory.service.*;
import com.yhl.scp.mrp.material.MrpResultVO;
import com.yhl.scp.mrp.material.arrival.dto.MaterialArrivalTrackingDTO;
import com.yhl.scp.mrp.material.arrival.service.MaterialArrivalTrackingService;
import com.yhl.scp.mrp.material.plan.convertor.MaterialPlanVersionConvertor;
import com.yhl.scp.mrp.material.plan.dto.*;
import com.yhl.scp.mrp.material.plan.enums.MrpDemandSourceEnum;
import com.yhl.scp.mrp.material.plan.infrastructure.po.MrpResultPO;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanVersionVO;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandService;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandVersionService;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandVO;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandVersionVO;
import com.yhl.scp.mrp.supplier.service.MaterialSupplierPurchaseService;
import com.yhl.scp.mrp.supplier.service.SupplierPurchaseRatioService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName AbstractMrpService
 * @Description TODO
 * @Date 2024-10-25 09:47:01
 * <AUTHOR>
 * @Copyright 瑞之泽
 * @Version 1.0
 */
@Slf4j
public abstract class AbstractMrpService implements MrpService {

    protected static final List<String> noGlassMaterialTypeList = Lists.newArrayList("RA.V", "BB", "BG", "BJ");
    @Resource
    protected MpsFeign mpsFeign;
    @Resource
    protected NewMdsFeign mdsFeign;
    @Resource
    protected DfpFeign dfpFeign;
    @Resource
    protected MaterialArrivalTrackingService materialArrivalTrackingService;
    @Resource
    protected InventoryQuayDetailService inventoryQuayDetailService;
    @Resource
    protected InventoryOurFactoryDetailService inventoryOurFactoryDetailService;
    @Resource
    protected InventoryFloatGlassDetailService inventoryFloatGlassDetailService;
    @Resource
    protected InventoryFloatGlassShippedDetailService inventoryFloatGlassShippedDetailService;
    @Resource
    protected MaterialPlanInventoryShiftSupplyService materialPlanInventoryShiftSupplyService;
    @Resource
    protected MaterialPlanInventoryShiftDemandService materialPlanInventoryShiftDemandService;
    @Resource
    protected MaterialPlanReplaceService materialPlanReplaceService;
    @Resource
    protected MaterialPlanVersionService materialPlanVersionService;
    @Resource
    protected SafetyInventoryService safetyInventoryService;
    @Resource
    protected NoGlassInventoryShiftService noGlassInventoryShiftService;
    @Resource
    protected MaterialSupplierPurchaseService materialSupplierPurchaseService;
    @Resource
    protected SupplierPurchaseRatioService supplierPurchaseRatioService;
    @Resource
    protected MaterialPlanNeedService materialPlanNeedService;
    @Resource
    protected WarehouseHalfSubinventoryService warehouseHalfSubinventoryService;
    @Resource
    protected NoGlassInventoryShiftDataService noGlassInventoryShiftDataService;
    @Resource
    protected NoGlassInventoryShiftDetailService noGlassInventoryShiftDetailService;
    @Resource
    protected MaterialGrossDemandService materialGrossDemandService;
    @Resource
    protected MaterialGrossDemandVersionService materialGrossDemandVersionService;

    @Resource
    protected MrpNewProductStockPointDao mrpNewProductStockPointDao;

    @Resource
    protected RedisUtil redisUtil;

    protected static void setProductInfo(MrpContextDTO mrpContextDTO, List<NewProductStockPointVO> newProductStockPointVOS) {
        Map<String, NewProductStockPointVO> productStockPointVOMapOfId = newProductStockPointVOS.stream().collect(Collectors.toMap(NewProductStockPointVO::getId, Function.identity()));
        Map<String, NewProductStockPointVO> productStockPointVOMapOfCode = newProductStockPointVOS.stream().collect(Collectors.toMap(NewProductStockPointVO::getProductCode, Function.identity(), (k1, k2) -> k2));
        mrpContextDTO.setNewProductStockPointVOList(newProductStockPointVOS);
        mrpContextDTO.setNewProductStockPointVOMapOfId(productStockPointVOMapOfId);
        mrpContextDTO.setNewProductStockPointVOMapOfCode(productStockPointVOMapOfCode);
    }

    private static Date getLastDeliveryPlanTime2(List<CapacitySupplyRelationshipVO> list,
                                                 List<DeliveryPlanPublishedVO> deliveryPlanDetailVOSAfter) {
        BigDecimal lastSupplyQuantity = list.get(list.size() - 1).getSupplyQuantity();
        BigDecimal totalDeliveryPlanQty = BigDecimal.ZERO;
        Date lastDeliveryPlanTime = null;
        for (DeliveryPlanPublishedVO deliveryPlanDetailVO : deliveryPlanDetailVOSAfter) {
            totalDeliveryPlanQty = totalDeliveryPlanQty.add(BigDecimal.valueOf(deliveryPlanDetailVO.getDemandQuantity()));
            if (totalDeliveryPlanQty.compareTo(lastSupplyQuantity) >= 0) {
                lastDeliveryPlanTime = deliveryPlanDetailVO.getDemandTime();
            }
        }
        return lastDeliveryPlanTime;
    }

    private static Date getLastDeliveryPlanTime(List<WorkOrderPublishedVO> fgWorkOrderList,
                                                List<DeliveryPlanPublishedVO> deliveryPlanDetailVOSAfter) {
        BigDecimal lastWorkOrderQty = fgWorkOrderList.get(fgWorkOrderList.size() - 1).getQuantity();
        BigDecimal totalDeliveryPlanQty = BigDecimal.ZERO;
        Date lastDeliveryPlanTime = null;
        for (DeliveryPlanPublishedVO deliveryPlanDetailVO : deliveryPlanDetailVOSAfter) {
            totalDeliveryPlanQty = totalDeliveryPlanQty.add(BigDecimal.valueOf(deliveryPlanDetailVO.getDemandQuantity()));
            if (totalDeliveryPlanQty.compareTo(lastWorkOrderQty) >= 0) {
                lastDeliveryPlanTime = deliveryPlanDetailVO.getDemandTime();
            }
        }
        return lastDeliveryPlanTime;
    }

    @Transactional
    public BaseResponse<String> doRunMrp(MrpParamDTO mrpParam) {
        String redisKey = String.join("#", RedisKeyManageEnum.MRP_COMPUTE.getKey(), mrpParam.getCalcType());
        if (redisUtil.hasKey(redisKey)) {
            return BaseResponse.error("当前已有MRP在计算中，请等待计算完成");
        }
        String redisResultKey = String.join("#", RedisKeyManageEnum.MRP_COMPUTE_RESULT.getKey(), mrpParam.getCalcType());
        redisUtil.set(redisKey, mrpParam.getCalcType(), 60 * 60);
        redisUtil.set(redisResultKey, JSONObject.toJSONString(new MrpResultVO("WAIT", "运行中", null)), 60 * 60);
        StopWatch stopWatch = new StopWatch("MRP计算");
        try {
            stopWatch.start("初始化MRP上下文");
            MrpContextDTO mrpContextDTO = initMrpContext(mrpParam);
            stopWatch.stop();

            stopWatch.start("MRP计算开始");
            MrpResultPO mrpResultPO = doMrpCalc(mrpContextDTO);
            Set<String> errorMsgList = mrpResultPO.getErrorMsgList();
            stopWatch.stop();

            stopWatch.start("计划采购日期推荐");
            List<MaterialArrivalTrackingDTO> arrivalTrackingDTOList = doDateRecommend(mrpContextDTO);
            mrpResultPO.setUpdateMaterialArrivalTrackingDTOList(arrivalTrackingDTOList);
            stopWatch.stop();

            stopWatch.start("MRP结果保存");
            // 保存计算结果
            save(mrpResultPO);
            stopWatch.stop();
            log.info(stopWatch.prettyPrint(TimeUnit.SECONDS));
            if (CollectionUtils.isEmpty(errorMsgList)) {
                // 无错误信息
                redisUtil.set(redisResultKey, JSONObject.toJSONString(new MrpResultVO("SUCCESS", "运行完成", "运行成功")), 60 * 60);
                return BaseResponse.success();
            } else {
                redisUtil.set(redisResultKey, JSONObject.toJSONString(new MrpResultVO("SUCCESS", "运行完成", errorMsgList)), 60 * 60);
                return BaseResponse.success("", String.join(",", errorMsgList));
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("mrp计算失败", e);
            redisUtil.set(redisResultKey, JSONObject.toJSONString(new MrpResultVO("FAIL", "运行失败", e.getLocalizedMessage())), 60 * 60);
            throw new BusinessException("mrp计算失败,{0}", e.getLocalizedMessage());
        } finally {
            // 确保无论如何都会释放 Redis 锁
            if (redisUtil.hasKey(redisKey)) {
                redisUtil.delete(redisKey);
            }
        }
    }

    @Override
    public BaseResponse<List<MaterialArrivalTrackingDTO>> doArrivalTrackingDateRecommend(MrpParamDTO mrpParam) {
        StopWatch stopWatch = new StopWatch("计划采购日期推荐");
        stopWatch.start("初始化MRP上下文");
        MrpContextDTO mrpContextDTO = initMrpContext(mrpParam);
        stopWatch.stop();

        stopWatch.start("日期推荐开始");
        // mrp计算
        List<MaterialArrivalTrackingDTO> result = doDateRecommend(mrpContextDTO);
        stopWatch.stop();
        log.info(stopWatch.prettyPrint(TimeUnit.SECONDS));
        return new BaseResponse<>(null, result);
    }

    protected abstract List<MaterialArrivalTrackingDTO> doDateRecommend(MrpContextDTO mrpContextDTO);

    protected MrpContextDTO initMrpContext(MrpParamDTO mrpParam) {

        MrpContextDTO mrpContextDTO = new MrpContextDTO();

        if (StringUtils.isNotEmpty(mrpParam.getScenario())) {
            mrpContextDTO.setScenario(mrpParam.getScenario());
        } else {
            mrpContextDTO.setScenario(SystemHolder.getScenario());
        }
        mrpContextDTO.setMpsDemandRule(mrpParam.getDemandSource());
        mrpContextDTO.setUserId(mrpParam.getUserId());
        if (mrpParam.getWhetherAutomatic()) {
            mrpContextDTO.setWhetherAutomatic(true);
        }
        if (null != mrpParam.getCalcStartTime()) {
            mrpContextDTO.setMrpCalcDate(mrpParam.getCalcStartTime());
        } else {
            mrpContextDTO.setMrpCalcDate(DateUtils.getDayFirstTime(new Date()));
        }
        mrpContextDTO.setAdjustInventoryShiftDetail(mrpParam.getGlassInventoryShiftDetailDTO());

        StopWatch stopWatch = new StopWatch("获取上下文数据");
        // 查询最新的毛需求版本
        stopWatch.start("查询最新的毛需求版本");
        getNewMaterialGrossDemandVersion(mrpContextDTO);
        stopWatch.stop();

        // 组装用户权限物料
        stopWatch.start("组装用户权限物料");
        assemblePlanUserProduct(mrpContextDTO);
        stopWatch.stop();

        stopWatch.start("库存点查询");
        initStockPointInfo(mrpContextDTO);
        stopWatch.stop();

        // 查询库存点物品数据
        stopWatch.start("物料数据查询");
        initProductInfo(mrpContextDTO);
        stopWatch.stop();

        // 组装需求数据，产能平衡数据、主生产计划数据、中转库需求(材料MRP需要考虑)
        stopWatch.start("组装需求数据");
        initMrpDemand(mrpContextDTO);
        stopWatch.stop();

        stopWatch.start("组装其他数据");
        initOtherInfo(mrpContextDTO);
        stopWatch.stop();

        log.info(stopWatch.prettyPrint(TimeUnit.SECONDS));
        return mrpContextDTO;
    }

    private void getNewMaterialGrossDemandVersion(MrpContextDTO mrpContextDTO) {
        MaterialGrossDemandVersionVO materialGrossDemandVersionVO = materialGrossDemandVersionService.selectLastVersion();
        if (null == materialGrossDemandVersionVO) {
            throw new BusinessException("未找到最新毛需求版本");
        }
        mrpContextDTO.setMaterialGrossDemandVersionId(materialGrossDemandVersionVO.getId());
        mrpContextDTO.setMaterialGrossDemandVersionCode(materialGrossDemandVersionVO.getVersionCode());
        log.info("mrp计算,最新毛需求版本ID:{}", materialGrossDemandVersionVO.getId());
    }

    private void initDeliveryPlanPublishedInfo(MrpContextDTO mrpContextDTO) {
        List<Date> dateList = new ArrayList<>();
        Date moveDay = DateUtils.moveDay(mrpContextDTO.getMrpCalcDate(), 30);
        log.info("查询未来30天的数据:{}", JSON.toJSONString(dateList));

        FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder().dynamicColumnParam(Lists.newArrayList("id",
                        "product_code", "demand_time", "demand_quantity", "demand_version_id", "create_time"))
                .queryParam(ImmutableMap.of("productCodes", mrpContextDTO.getPlanUserFactoryCodeList(),
                        "startTimeStr", DateUtils.dateToString(mrpContextDTO.getMrpCalcDate()),
                        "endTimeStr", DateUtils.dateToString(moveDay)))
                .build();
        List<DeliveryPlanPublishedVO> deliveryPlanFuture30Days = dfpFeign.selectDeliveryPlanPublishedByParamOnDynamicColumns(mrpContextDTO.getScenario(), feignDynamicParam);
//        List<DeliveryPlanPublishedVO> deliveryPlanFuture30Days = dfpFeign.getDeliveryPlanFuture30Days(mrpParam.getScenario(), mrpContextDTO.getPlanUserFactoryCodeList());
        mrpContextDTO.setDeliveryPlanDetailVOList(deliveryPlanFuture30Days);
    }

    private void assemblePlanUserProduct(MrpContextDTO mrpContextDTO) {
        // 优先跑指定的物料需求
        if (CollectionUtils.isNotEmpty(mrpContextDTO.getComputeProductCodeList())) {
            mrpContextDTO.setPlanUserProductCodeList(mrpContextDTO.getComputeProductCodeList().stream().distinct().collect(Collectors.toList()));
            return;
        }

        List<String> dynamicColumnParam = Lists.newArrayList("id", "product_code");
        Map<String, Object> queryParams = new HashMap<>();
        if (mrpContextDTO.getWhetherAutomatic()) {
            // 定时执行、获取材料计划员不为空的材料
            queryParams.put("materialPlannerNotNull", YesOrNoEnum.YES.getCode());
        } else {
            // 查询计划员下的物料
//            queryParams.put("materialPlanner", SystemHolder.getUserId());
            queryParams.put("materialPlanner", "8d62ab0f-018d-72b118fe-8a87cdd8-0029");
        }
        FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder()
                .dynamicColumnParam(dynamicColumnParam)
                .queryParam(queryParams).build();

        List<NewProductStockPointVO> newProductStockPointVOList = mrpNewProductStockPointDao.selectColumnVOByParams(queryParams);
//        List<NewProductStockPointVO> newProductStockPointVOList = mdsFeign.selectProductListByParamOnDynamicColumns(mrpContextDTO.getScenario(), feignDynamicParam);
        if (CollectionUtils.isEmpty(newProductStockPointVOList)) {
            throw new BusinessException("用户没有物料权限");
        }
        List<String> productCodeList = newProductStockPointVOList.stream()
                .map(NewProductStockPointVO::getProductCode).distinct()
                .collect(Collectors.toList());
        // 查询权限下材料关联的半品和成品
//        List<String> planUserFactoryCodeList = new ArrayList<>();

        // 因为有重复的productCode
//        List<NewProductStockPointVO> newProductStockPointVOS = mdsFeign.selectByProductCode(mrpContextDTO.getScenario(), productCodeList);
//        List<String> productIdList = newProductStockPointVOS.stream().map(NewProductStockPointVO::getId).collect(Collectors.toList());
        // 查询productBom
//        List<ProductBomVO> productBomVOList = mdsFeign.selectProductBomVOByParams(mrpContextDTO.getScenario(), ImmutableMap.of("ioProductIdList", productIdList));
//        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(productBomVOList)){
//            List<String> bomVersionIdList = productBomVOList.stream().map(ProductBomVO::getBomVersionId).distinct().collect(Collectors.toList());
//            List<ProductBomVersionVO> productBomVersionVOList = mdsFeign.selectProductBomVersionVOByParams(mrpContextDTO.getScenario(), ImmutableMap.of("ids", bomVersionIdList));
//            if (CollectionUtils.isNotEmpty(productBomVersionVOList)){
//                planUserFactoryCodeList = productBomVersionVOList.stream().map(ProductBomVersionVO::getProductCode).distinct().collect(Collectors.toList());
//            }
//        }
        mrpContextDTO.setPlanUserProductCodeList(productCodeList);
//        mrpContextDTO.setPlanUserFactoryCodeList(planUserFactoryCodeList);
    }

    protected abstract void initOtherInfo(MrpContextDTO mrpContextDTO);

    protected void initProductInfo(MrpContextDTO mrpContextDTO) {

        List<NewProductStockPointVO> newProductStockPointVOS = new ArrayList<>(30000);

        FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder().dynamicColumnParam(Lists.newArrayList("id",
                        "product_code", "product_type", "product_classify"))
                .queryParam(new HashMap<>())
                .build();
        newProductStockPointVOS = mrpNewProductStockPointDao.selectColumnVOByParams(new HashMap<>());
//        newProductStockPointVOS = mrpNewProductStockPointDao.selectVOByParams(new HashMap<>());
        setProductInfo(mrpContextDTO, newProductStockPointVOS);
    }

    protected void initStockPointInfo(MrpContextDTO mrpContextDTO) {
        List<NewStockPointVO> stockPointVOList = mdsFeign.selectAllStockPoint(mrpContextDTO.getScenario());
        mrpContextDTO.setStockPointVOList(stockPointVOList);
        // 本厂库存点编码
        mrpContextDTO.setBcStockPointCodeList(mrpContextDTO.getStockPointVOList().stream()
                .filter(t -> StockPointTypeEnum.BC.getCode().equals(t.getStockPointType()))
                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList()));
        // 码头库存点编码
        mrpContextDTO.setPortStockPointCodeList(mrpContextDTO.getStockPointVOList().stream()
                .filter(t -> StockPointTypeEnum.MT.getCode().equals(t.getStockPointType()))
                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList()));
        // 浮法库存点编码
        mrpContextDTO.setFloatStockPointCodeList(mrpContextDTO.getStockPointVOList().stream()
                .filter(t -> StockPointTypeEnum.FF.getCode().equals(t.getStockPointType()))
                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList()));
    }

    protected void initMrpDemand(MrpContextDTO mrpContextDTO) {
        StopWatch stopWatch = new StopWatch("组装需求数据明细");
        stopWatch.start("查询毛需求明细");
        String scenario = mrpContextDTO.getScenario();
        String mpsDemandRule = mrpContextDTO.getMpsDemandRule();
        List<String> planUserFactoryCodeList = mrpContextDTO.getPlanUserFactoryCodeList();
        List<String> planUserProductCodeList = mrpContextDTO.getPlanUserProductCodeList();

        DynamicDataSourceContextHolder.setDataSource(scenario);
        List<MrpDemandDTO> mrpDemandDataList = new ArrayList<>();

        mrpContextDTO.setMrpDemandList(mrpDemandDataList);

        // 查询毛需求数据
        List<MaterialGrossDemandVO> materialGrossDemandVOList = new ArrayList<>();
        Lists.partition(planUserProductCodeList, 1000)
                .forEach(productCodeList -> materialGrossDemandVOList.addAll(
                        materialGrossDemandService.selectByParams(ImmutableMap.of("productCodeList", productCodeList,
                                "materialGrossDemandVersionId", mrpContextDTO.getMaterialGrossDemandVersionId()))));

        if (CollectionUtils.isNotEmpty(materialGrossDemandVOList)) {
            for (MaterialGrossDemandVO materialGrossDemandVO : materialGrossDemandVOList) {
                MrpDemandDTO mrpDemandDTO = MrpDemandDTO
                        .builder()
                        .productCode(materialGrossDemandVO.getProductCode())
                        .productClassify(materialGrossDemandVO.getProductClassify())
                        .productThickness(materialGrossDemandVO.getProductThickness())
                        .productColor(materialGrossDemandVO.getProductColor())
                        .demandQuantity(materialGrossDemandVO.getDemandQuantity())
                        .unFulfillmentQuantity(materialGrossDemandVO.getDemandQuantity())
                        .demandTime(materialGrossDemandVO.getDemandTime())
                        .demandSource(materialGrossDemandVO.getDemandSource())
                        .productFactoryCode(materialGrossDemandVO.getProductFactoryCode())
                        .vehicleModeCode(materialGrossDemandVO.getVehicleModeCode())
                        .build();
                mrpDemandDataList.add(mrpDemandDTO);
            }
        }
        // 记录用户使用的毛需求版本号
        if (org.apache.commons.lang3.StringUtils.isNotBlank(mrpContextDTO.getUserId())) {
            // 非自动触发
            redisUtil.set(RedisKeyManageEnum.MRP_COMPUTE_GROSS_DEMAND_VERSION_CODE.getKey().replace("{userId}", mrpContextDTO.getUserId()), mrpContextDTO.getMaterialGrossDemandVersionCode());
        } else {
            // 自动触发,替换所有的用户版本
            Set<String> keys = redisUtil.keys(RedisKeyManageEnum.MRP_COMPUTE_GROSS_DEMAND_VERSION_CODE.getKey().replace("{userId}", "*"));
            for (String key : keys) {
                redisUtil.set(key, mrpContextDTO.getMaterialGrossDemandVersionCode());
            }
        }


//        if (StringUtils.equals(mpsDemandRule, "DELIVERY_PLAN_DEMAND")) {
//            // 生产需求为从发货计划获取
//            getMpsDemandFromDeliveryPlan(scenario, mrpDemandDataList, mrpContextDTO);
//        } else {
//            // 生产需求从MPS生产计划中获取
//            getMpsDemandFromMps(scenario, mrpDemandDataList, mrpContextDTO);
//        }
//        stopWatch.stop();
//
//        stopWatch.start("组装中转库需求");
//        getGradeInventoryDemand(mrpContextDTO);
//        stopWatch.stop();
//
//        // 查询委外需求
////        getOutsourcedDemand(mrpContextDTO);
//
//        stopWatch.start("产能平衡数据组装");
//        // 获取最晚发货计划日期往后的365天的产能平衡数据
//        Map<String, Date> lastDeliveryPlanTimeMapOfProductCode = new HashMap<>();
//        Map<String, NewProductStockPointVO> newProductStockPointVOMapOfId = mrpContextDTO.getNewProductStockPointVOMapOfId();
//
//        if (MapUtils.isNotEmpty(mrpContextDTO.getLastDeliveryPlanTimeMapOfProductCode())) {
//            lastDeliveryPlanTimeMapOfProductCode = mrpContextDTO.getLastDeliveryPlanTimeMapOfProductCode();
//        }
//
//        CapacityBalanceVersionVO capacityBalanceVersionVO = mrpContextDTO.getCapacityBalanceVersionVO();
//        Map<String, Object> params = new HashMap<>();
//        params.put("versionId", capacityBalanceVersionVO.getVersionCode());
////        params.put("supplyModel", SupplyModelEnum.LOCAL.getCode());
//        params.put("productCodeList", planUserFactoryCodeList);
//        List<CapacitySupplyRelationshipVO> capacitySupplyRelationshipVOList = mpsFeign.selectCapacitySupplyRelationshipByParams(scenario, params);
//        // 查询工艺路径步骤输入物品
//        List<String> routingStepIdList = capacitySupplyRelationshipVOList.stream().map(CapacitySupplyRelationshipVO::getRoutingStepId).distinct().collect(Collectors.toList());
//        List<RoutingStepInputVO> stepInputVOList = new ArrayList<>();
//        Lists.partition(routingStepIdList, 1000)
//                .forEach(idList ->
//                        stepInputVOList.addAll(mdsFeign.selectInputByRoutingStepIds(scenario, idList)));
//        Map<String, List<RoutingStepInputVO>> routingStepInputVOGroupOfStepId = stepInputVOList.stream().collect(Collectors.groupingBy(RoutingStepInputVO::getRoutingStepId));
//
//        if (CollectionUtils.isNotEmpty(capacitySupplyRelationshipVOList)) {
//            Map<String, List<CapacitySupplyRelationshipVO>> capacitySupplyRelationshipVoGroup = capacitySupplyRelationshipVOList.stream().collect(Collectors.groupingBy(CapacitySupplyRelationshipVO::getProductCode));
//            for (Map.Entry<String, List<CapacitySupplyRelationshipVO>> entry : capacitySupplyRelationshipVoGroup.entrySet()) {
//                String productCode = entry.getKey();
//                List<CapacitySupplyRelationshipVO> value = entry.getValue();
//                if ("00907LFW00006".equals(productCode)) {
//                    log.info("产能平衡物品:{}, 数据量:{}", productCode, value.size());
//                }
//                // 获取发货计划满足的时间
//                Date lastDeliveryPlanTime = lastDeliveryPlanTimeMapOfProductCode.get(productCode);
//                if (lastDeliveryPlanTime == null) {
//                    lastDeliveryPlanTime = mrpContextDTO.getMrpCalcDate();
//                }
//                Date moveDay = DateUtils.moveDay(lastDeliveryPlanTime, 365);
//                Date finalLastDeliveryPlanTime = lastDeliveryPlanTime;
//                value = value.stream()
//                        .filter(item -> item.getForecastTime().compareTo(finalLastDeliveryPlanTime) >= 0 && item.getForecastTime().compareTo(moveDay) <= 0)
//                        .collect(Collectors.toList());
//                if ("00907LFW00006".equals(productCode)) {
//                    log.info("产能平衡物品:{}, 开始时间:{}, 结束时间:{}, 过滤后数量:{}", productCode, DateUtils.dateToString(finalLastDeliveryPlanTime), DateUtils.dateToString(moveDay), value.size());
//                }
//                if (CollectionUtils.isNotEmpty(value)) {
//                    for (CapacitySupplyRelationshipVO capacitySupplyRelationshipVO : value) {
//                        List<RoutingStepInputVO> stepInputVOS = routingStepInputVOGroupOfStepId.get(capacitySupplyRelationshipVO.getRoutingStepId());
//                        if (CollectionUtils.isEmpty(stepInputVOS)) {
//                            continue;
//                        }
//                        for (RoutingStepInputVO stepInputVO : stepInputVOS) {
//                            NewProductStockPointVO inputStockPointVO = newProductStockPointVOMapOfId.get(stepInputVO.getInputProductId());
//                            if (null == inputStockPointVO ||
//                                    !StringUtils.equals("P", inputStockPointVO.getProductType())) {
//                                continue;
//                            }
//                            MrpDemandDTO mrpDemandDTO = MrpDemandDTO.builder()
//                                    .productCode(inputStockPointVO.getProductCode())
//                                    .productClassify(inputStockPointVO.getProductClassify())
//                                    .demandTime(capacitySupplyRelationshipVO.getForecastTime())
//                                    .demandQuantity(capacitySupplyRelationshipVO.getDemandQuantity().multiply(stepInputVO.getInputFactor()))
//                                    .demandSource(MrpDemandSourceEnum.MCB.getCode())
//                                    .build();
//                            mrpDemandDataList.add(mrpDemandDTO);
//                        }
//                    }
//                }
//            }
//        }
        stopWatch.stop();
        log.info(stopWatch.prettyPrint(TimeUnit.SECONDS));
    }

    private void getOutsourcedDemand(MrpContextDTO mrpContextDTO) {
        // 查询委外需求
        List<OutsourceTransferDemandDetailVO> outsourceTransferDemandDetailList = mpsFeign.selectOutsourceTransferDemandDetailByParams(
                mrpContextDTO.getScenario(),
                ImmutableMap.of("requiredMaterials", YesOrNoEnum.YES.getCode()));

        Date mrpCalcDate = mrpContextDTO.getMrpCalcDate();
        if (CollectionUtils.isNotEmpty(outsourceTransferDemandDetailList)) {
            for (OutsourceTransferDemandDetailVO outsourceTransferDemandDetailVO : outsourceTransferDemandDetailList) {
                String demandMonth = outsourceTransferDemandDetailVO.getDemandMonth();
                // demandMonth为yyyyy-MM格式，如果是当前月设置为当前时间，不少当前月则设置为demandMonth月的第一天
                Calendar currentCalendar = Calendar.getInstance();
                currentCalendar.setTime(mrpCalcDate);
                int currentYear = currentCalendar.get(Calendar.YEAR);
                int currentMonth = currentCalendar.get(Calendar.MONTH) + 1; // 注意月份是从0开始的，所以需要加1

                String[] dateParts = demandMonth.split("-");
                int demandYear = Integer.parseInt(dateParts[0]);
                int demandMonthValue = Integer.parseInt(dateParts[1]);

                Date demandDate;
                if (currentYear == demandYear && currentMonth == demandMonthValue) {
                    demandDate = mrpCalcDate;
                } else {
                    Calendar demandCalendar = Calendar.getInstance();
                    // 月份是从0开始的，所以需要减1
                    demandCalendar.set(demandYear, demandMonthValue - 1, 1);
                    demandDate = demandCalendar.getTime();
                }
                MrpDemandDTO mrpDemandDTO = MrpDemandDTO.builder()
                        .productCode(outsourceTransferDemandDetailVO.getMaterialsCode())
                        .productClassify("productClassify")
                        .demandTime(demandDate)
                        .demandQuantity(outsourceTransferDemandDetailVO.getSupplyQuantity())
                        .unFulfillmentQuantity(outsourceTransferDemandDetailVO.getSupplyQuantity())
                        .demandSource(MrpDemandSourceEnum.OUTSOURCE_TRANSFER.getCode())
                        .build();
            }
        }
    }

    protected abstract void getGradeInventoryDemand(MrpContextDTO mrpContextDTO);

    // 根据BOM拆解成品所需原材料需求数量
    protected void getCapacityBalancedDemandData(MrpContextDTO mrpContextDTO,
                                                 NewProductStockPointVO productStockPointVO,
                                                 Date demandDate,
                                                 BigDecimal demandQty,
                                                 List<MrpDemandDTO> mrpDemandDataList, String wipSupplyType) {
        // 库存点物品数据
        Map<String, NewProductStockPointVO> newProductStockPointVOMap = mrpContextDTO.getNewProductStockPointVOList().stream()
                .collect(Collectors.toMap(NewProductStockPointVO::getId, Function.identity()));
        // 工艺路径数据
        Map<String, RoutingVO> routingVOMap = mrpContextDTO.getRoutingVOList().stream().collect(Collectors.toMap(RoutingVO::getProductId, Function.identity()));
        // 工艺路径步骤数据
        Map<String, List<RoutingStepVO>> routingStepGroup = mrpContextDTO.getRoutingStepVOList().stream().collect(Collectors.groupingBy(RoutingStepVO::getRoutingId));
        // 工艺路径输入
        Map<String, List<RoutingStepInputVO>> routingStepInputGroup = mrpContextDTO.getRoutingStepInputVOList().stream().collect(Collectors.groupingBy(RoutingStepInputVO::getRoutingStepId));

        RoutingVO routingVO = routingVOMap.get(productStockPointVO.getId());
        List<RoutingStepVO> routingStepVOList = routingStepGroup.get(routingVO.getId());
        for (RoutingStepVO routingStepVO : routingStepVOList) {
            List<RoutingStepInputVO> routingStepInputVOList = routingStepInputGroup.get(routingStepVO.getId());
            if (CollectionUtils.isEmpty(routingStepInputVOList)) {
                continue;
            }
            for (RoutingStepInputVO routingStepInputVO : routingStepInputVOList) {
                // 获取输入的原材料
                NewProductStockPointVO inputStockPointVO = newProductStockPointVOMap.get(routingStepInputVO.getInputProductId());
                if (null == inputStockPointVO) {
                    continue;
                }
                // 获取输入的原材料需求数量
                if ("FG".equals(productStockPointVO.getProductType())
                        && StringUtils.isEmpty(routingStepVO.getNextRoutingStepSequenceNo())
                        && wipSupplyType.equals(routingStepInputVO.getSupplyType())) {
                    continue;
                }
                if (StringUtils.equals("P", inputStockPointVO.getProductType())) {
                    MrpDemandDTO mrpDemandDTO = MrpDemandDTO.builder()
                            .productCode(inputStockPointVO.getProductCode())
                            .productClassify(inputStockPointVO.getProductClassify())
                            .demandTime(demandDate)
                            .demandQuantity(demandQty.multiply(routingStepInputVO.getInputFactor()).setScale(6, BigDecimal.ROUND_HALF_UP))
                            .demandSource(MrpDemandSourceEnum.MCB.getCode())
                            .build();
                    mrpDemandDataList.add(mrpDemandDTO);
                } else {
                    getCapacityBalancedDemandData(mrpContextDTO, inputStockPointVO, demandDate, demandQty.multiply(routingStepInputVO.getInputFactor()), mrpDemandDataList, wipSupplyType);
                }

            }
        }
    }

    protected abstract MrpResultPO doMrpCalc(MrpContextDTO mrpContextDTO);

    protected void save(MrpResultPO mrpResultPO) {
        if (null == mrpResultPO) {
            return;
        }
        if (null != mrpResultPO.getInsertMaterialPlanVersionDTO()) {
            materialPlanVersionService.doCreate(mrpResultPO.getInsertMaterialPlanVersionDTO());
        }
        if (null != mrpResultPO.getUpdateMaterialPlanVersionDTO()) {
            materialPlanVersionService.doUpdate(mrpResultPO.getUpdateMaterialPlanVersionDTO());
        }

//        if (CollectionUtils.isNotEmpty(mrpResultPO.getNoGlassInventoryShiftList())) {
//            Lists.partition(mrpResultPO.getNoGlassInventoryShiftList(), 2500).forEach(item -> {
//                noGlassInventoryShiftService.doCreateBatch(item);
//            });
//        }

        // 创建一个线程池，根据实际情况调整线程数量
        Executor executor = Executors.newFixedThreadPool(5);
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        processBatch(mrpResultPO.getNoGlassInventoryShiftDataList(), 2000,
                noGlassInventoryShiftDataService::doCreateBatch, futures, executor);

        processBatch(mrpResultPO.getNoGlassInventoryShiftDetailList(), 2000,
                noGlassInventoryShiftDetailService::doCreateBatch, futures, executor);

        processBatch(mrpResultPO.getReplaceList(), 1000,
                materialPlanReplaceService::doCreateBatch, futures, executor);

        processBatch(mrpResultPO.getDemandList(), 1000,
                materialPlanInventoryShiftDemandService::doCreateBatch, futures, executor);

        processBatch(mrpResultPO.getSupplyList(), 500,
                materialPlanInventoryShiftSupplyService::doCreateBatch, futures, executor);

        processBatch(mrpResultPO.getUpdateNoGlassInventoryShiftList(), 500,
                noGlassInventoryShiftService::doUpdateBatch, futures, executor);

        processBatch(mrpResultPO.getUpdateMaterialArrivalTrackingDTOList(), 500,
                materialArrivalTrackingService::doUpdateBatchSelective, futures, executor);
    }

    /**
     * 异步批量处理方法
     *
     * @param list           待处理列表
     * @param batchSize      每批大小
     * @param batchProcessor 批处理器
     * @param futures        异步任务列表
     * @param executor       线程池
     * @param <T>            泛型类型
     */
    private <T> void processBatch(List<T> list, int batchSize,
                                  Consumer<List<T>> batchProcessor,
                                  List<CompletableFuture<Void>> futures,
                                  Executor executor) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        int size = list.size();
        // 直接使用索引进行分批，避免创建多个子List对象
        for (int i = 0; i < size; i += batchSize) {
            int end = Math.min(i + batchSize, size);
            List<T> batch = list.subList(i, end);

            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    batchProcessor.accept(batch);
                } catch (Exception e) {
                    // 记录批处理异常，避免影响其他批次
                    log.error("批处理失败，批次大小: {}", batch.size(), e);
                    throw e;
                }
            }, executor));
        }
    }

    protected MrpSupplyDTO assembleMrpSupplyDTO(String supplyId, String productCode, BigDecimal supplyQuantity, Date supplyTime,
                                                String supplySource, String purchaseOrderCode, String productClassify,
                                                String sourceId, String dataSource) {
        MrpSupplyDTO mrpSupplyDTO = new MrpSupplyDTO();
        mrpSupplyDTO.setSupplyId(supplyId);
        mrpSupplyDTO.setProductCode(productCode);
        mrpSupplyDTO.setSupplyQuantity(supplyQuantity);
        mrpSupplyDTO.setOriginSupplyTime(supplyTime);
        mrpSupplyDTO.setSupplyTime(supplyTime);
        mrpSupplyDTO.setSupplySource(supplySource);
        mrpSupplyDTO.setPurchaseOrderCode(purchaseOrderCode);
        mrpSupplyDTO.setProductClassify(productClassify);
        mrpSupplyDTO.setSourceId(sourceId);
        mrpSupplyDTO.setDataSource(dataSource);
        return mrpSupplyDTO;
    }

    protected List<Date> getInventoryShiftDateList(Date date, int days) {
        // 获取当前日期到12后的日期
        Date currentDate = DateUtils.getDayFirstTime(date);
//        Date currentDate = DateUtils.stringToDate("2025-01-17");
        Date moveDate = DateUtils.moveDay(currentDate, days);
        return DateUtils.getIntervalDates(currentDate, moveDate);
    }

    protected MaterialPlanVersionDTO getNewMaterialPlanVersionDTO(MaterialPlanVersionDTO materialPlanVersionDTO) {
        String dateSerial = DateUtils.dateToString(new Date(), "yyyyMMdd");
        // 查询版本，目前就只有一个版本
        List<MaterialPlanVersionVO> materialPlanVersionVOS = materialPlanVersionService.selectByParams(new HashMap<>());
        if (CollectionUtils.isNotEmpty(materialPlanVersionVOS)) {
            materialPlanVersionVOS.sort(Comparator.comparing(MaterialPlanVersionVO::getCreateTime));
            MaterialPlanVersionVO materialPlanVersionVO = materialPlanVersionVOS.get(materialPlanVersionVOS.size() - 1);
            materialPlanVersionDTO = MaterialPlanVersionConvertor.INSTANCE.vo2Dto(materialPlanVersionVO);
        } else {
            materialPlanVersionDTO.setVersionCode(dateSerial + "-0001");
        }


//        String dateSerial = DateUtils.dateToString(new Date(), "yyyyMMdd");
//        // 查询最新未发布的mrp计划版本
//        Map<String, Object> params = new HashMap<>();
//        params.put("publishStatus", PublishStatusEnum.UNPUBLISH.getCode());
//        params.put("creator", SystemHolder.getUserId());
//        params.put("materialType", materialPlanVersionDTO.getMaterialType());
//        List<MaterialPlanVersionVO> unPublishMaterialPlanVersionList = materialPlanVersionService.selectByParams(params);
//        // 查询已发布的版本
//        params.put("publishStatus", PublishStatusEnum.PUBLISHED.getCode());
//        List<MaterialPlanVersionVO> publishMaterialPlanVersionList = materialPlanVersionService.selectByParams(params);
//
//        if (org.apache.commons.collections4.CollectionUtils.isEmpty(unPublishMaterialPlanVersionList) && org.apache.commons.collections4.CollectionUtils.isEmpty(publishMaterialPlanVersionList)) {
//            materialPlanVersionDTO.setVersionCode(dateSerial + "001");
//        }
//        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(unPublishMaterialPlanVersionList)) {
//            // 使用现在的版本号
//            MaterialPlanVersionVO materialPlanVersionVO = unPublishMaterialPlanVersionList.get(0);
//            materialPlanVersionDTO = MaterialPlanVersionConvertor.INSTANCE.vo2Dto(materialPlanVersionVO);
//        }
//        // 未发版版本为空，已发布版本不为空
//        if (org.apache.commons.collections4.CollectionUtils.isEmpty(unPublishMaterialPlanVersionList) && org.apache.commons.collections4.CollectionUtils.isNotEmpty(publishMaterialPlanVersionList)) {
//            publishMaterialPlanVersionList = publishMaterialPlanVersionList.stream().sorted(Comparator.comparing(MaterialPlanVersionVO::getCreateTime)).collect(Collectors.collectingAndThen(Collectors.toList(), list -> {
//                Collections.reverse(list);
//                return list;
//            }));
//            String versionCode = publishMaterialPlanVersionList.get(0).getVersionCode();
//            // 获取最后3位补充versionCode+1并且填补为3位数
//
//            // versionCode取最后3位
//            String lastVersionCode = versionCode.substring(versionCode.length() - 3);
//
//            int newVersionNumber = Integer.parseInt(lastVersionCode) + 1;
//            materialPlanVersionDTO.setVersionCode(dateSerial + String.format("%03d", newVersionNumber));
//        }
        return materialPlanVersionDTO;
    }

    private void getMpsDemandFromMps(String scenario, List<MrpDemandDTO> mrpDemandDataList, MrpContextDTO mrpContextDTO) {
        // 查询最新的版本
//        String logId = masterPlanPublishedLogService.selectNewestLogIdByOperatorId(null);
//        String logId = mpsFeign.selectMasterPlanPublishedLogIdByOperatorId();
        // 去除logId的双引号
//        logId = logId.replace("\"", "");

//        log.info("=========最新的主生产计划版本:{}", logId);
        Date nowDate = mrpContextDTO.getMrpCalcDate();


        // 查询制造订单发布表
        List<WorkOrderPublishedVO> workOrderVOList = mpsFeign.selectWorkOrderPublishedByParams(scenario, new HashMap<>());
        // 查询工序发布表
        List<OperationPublishedVO> operationVOList = mpsFeign.selectOperationPublishedByParams(scenario, new HashMap<>());
        // 查询需求发布表
        List<DemandPublishedVO> demandVOList = mpsFeign.selectDemandPublishedByParams(scenario, new HashMap<>());

        if (CollectionUtils.isEmpty(workOrderVOList) || CollectionUtils.isEmpty(demandVOList)) {
            throw new BusinessException("计算失败，无可计算的生产需求");
        }
        List<String> routingStepIdList = operationVOList.stream().map(OperationPublishedVO::getRoutingStepId).distinct().collect(Collectors.toList());
        List<RoutingStepVO> routingStepList = mdsFeign.getRoutingStepByRoutingStepIds(scenario, routingStepIdList);
        mrpContextDTO.setRoutingStepVOList(routingStepList);

        List<RoutingStepInputVO> routingStepInputList = mdsFeign.selectInputByRoutingStepIds(scenario, routingStepIdList);
        Map<String, List<RoutingStepInputVO>> routingStepInputVOGroup = routingStepInputList.stream().collect(Collectors.groupingBy(RoutingStepInputVO::getRoutingStepId));
        mrpContextDTO.setRoutingStepInputVOList(routingStepInputList);

        List<String> routingIdList = routingStepList.stream().map(RoutingStepVO::getRoutingId).collect(Collectors.toList());
        List<RoutingVO> routingVOList = mdsFeign.selectRoutingByScenarioRoutingIds(scenario, routingIdList);
        mrpContextDTO.setRoutingVOList(routingVOList);

        List<OperationInputPublishedVO> operationInputVOList = mpsFeign.selectOperationInputPublishedByParams(scenario, new HashMap<>());

        Map<String, NewProductStockPointVO> newProductStockPointVOMapOfId = mrpContextDTO.getNewProductStockPointVOMapOfId();
        Map<String, NewProductStockPointVO> newProductStockPointVOMapOfCode = mrpContextDTO.getNewProductStockPointVOMapOfCode();


        Map<String, WorkOrderPublishedVO> workOrderVOMap = workOrderVOList.stream().collect(Collectors.toMap(WorkOrderPublishedVO::getId, Function.identity(), (k1, k2) -> k2));
        Map<String, OperationInputPublishedVO> operationInputVOMap = operationInputVOList.stream().collect(Collectors.toMap(OperationInputPublishedVO::getId, Function.identity(), (k1, k2) -> k2));
        Map<String, OperationPublishedVO> operationVOMap = operationVOList.stream().collect(Collectors.toMap(OperationPublishedVO::getId, Function.identity(), (k1, k2) -> k2));

        Map<String, List<DeliveryPlanPublishedVO>> deliveryPlanDetailGroup = mrpContextDTO.getDeliveryPlanDetailVOList()
                .stream().collect(Collectors.groupingBy(DeliveryPlanPublishedVO::getProductCode));

        demandVOList.forEach(demandVO -> {
            NewProductStockPointVO productStockPointVO = newProductStockPointVOMapOfId.get(demandVO.getProductId());
            if (null == productStockPointVO ||
                    !"P".equalsIgnoreCase(productStockPointVO.getProductType()) ||
                    demandVO.getDemandTime().compareTo(nowDate) < 0) {
                return;
            }
            String finishedProductCode = demandVO.getFinishedProductCode();
            NewProductStockPointVO bcProductStockPointVO = newProductStockPointVOMapOfCode.get(finishedProductCode);

            String operationInputId = demandVO.getOperationInputId();
            OperationInputPublishedVO operationInputVO = operationInputVOMap.get(operationInputId);
            OperationPublishedVO operationVO = operationVOMap.get(operationInputVO.getOperationId());
            WorkOrderPublishedVO workOrderVO = workOrderVOMap.get(operationVO.getOrderId());
            Map<String, RoutingStepVO> routingStepVOMap = routingStepList.stream().collect(Collectors.toMap(RoutingStepVO::getId, Function.identity()));
            RoutingStepVO routingStepVO = routingStepVOMap.get(operationVO.getRoutingStepId());
            List<RoutingStepInputVO> routingStepInputVOS = routingStepInputVOGroup.get(operationVO.getRoutingStepId());
            if (CollectionUtils.isEmpty(routingStepInputVOS)) {
                return;
            }
            Map<String, RoutingStepInputVO> routingStepInputVOMap = routingStepInputVOS.stream()
                    .filter(t -> YesOrNoEnum.YES.getCode().equals(t.getEnabled()))
                    .collect(Collectors.toMap(RoutingStepInputVO::getInputProductId, Function.identity(), (k1, k2) -> k2));
            RoutingStepInputVO routingStepInputVO = routingStepInputVOMap.get(demandVO.getProductId());

            // 如果当前工序本厂最后一道工序，输入物料为推式，则剔除
            if (StringUtils.isEmpty(routingStepVO.getNextRoutingStepSequenceNo()) &&
                    "推式".equals(routingStepInputVO.getSupplyType())) {
                return;
            }
            MrpDemandDTO mrpDemandDTO = MrpDemandDTO.builder()
                    .id(demandVO.getId())
                    .workOrderCode(workOrderVO.getOrderNo())
                    .productCode(productStockPointVO.getProductCode())
                    .productClassify(productStockPointVO.getProductClassify())
                    .demandTime(demandVO.getDemandTime())
                    .demandQuantity(demandVO.getQuantity())
                    .unFulfillmentQuantity(demandVO.getQuantity())
                    .routingStepId(operationInputVO.getRoutingStepId())
                    .demandSource(MrpDemandSourceEnum.MPS.getCode())
                    .productColor(productStockPointVO.getProductColor())
                    .productThickness(productStockPointVO.getProductThickness())
                    .productFactoryCode(finishedProductCode)
                    .vehicleModeCode(bcProductStockPointVO.getVehicleModelCode())
                    .build();
            mrpDemandDataList.add(mrpDemandDTO);
        });

        Map<String, Date> lastDeliveryPlanTimeMapOfProductCode = new HashMap<>();
        workOrderVOList.stream()
                .filter(workOrderVO -> {
                    NewProductStockPointVO productStockPointVO = newProductStockPointVOMapOfId.get(workOrderVO.getProductId());
                    return deliveryPlanDetailGroup.get(productStockPointVO.getProductCode()) != null;
                })
                .collect(Collectors.groupingBy(WorkOrderPublishedVO::getProductId))
                .forEach((productId, fgWorkOrderList) -> {
                    NewProductStockPointVO productStockPointVO = newProductStockPointVOMapOfId.get(productId);
                    fgWorkOrderList.sort(Comparator.comparing(WorkOrderPublishedVO::getDueDate));
                    // 主生产计划制造订单最晚日期
                    Date lastDueDate = fgWorkOrderList.get(fgWorkOrderList.size() - 1).getDueDate();
//                    String lastDueDateStr = DateUtils.dateToString(lastDueDate, DateUtils.YEAR_MONTH);

                    List<DeliveryPlanPublishedVO> deliveryPlanPublishedVOList = deliveryPlanDetailGroup.get(productStockPointVO.getProductCode());
                    // 过滤需求时间晚于主生产计划最晚日期的发货计划数据
                    List<DeliveryPlanPublishedVO> deliveryPlanDetailVOSAfter = deliveryPlanPublishedVOList.stream()
                            .filter(dp -> !dp.getDemandTime().before(lastDueDate)).collect(Collectors.toList());
                    if ("00685LFW00004".equals(productStockPointVO.getProductCode()) || "00685LFW00006".equals(productStockPointVO.getProductCode())) {
                        log.info("最晚制造订单物品:{}, 最晚时间:{}, 制造数量:{},", productStockPointVO.getProductCode(), DateUtils.dateToString(lastDueDate), fgWorkOrderList.get(fgWorkOrderList.size() - 1).getQuantity());
                    }
                    deliveryPlanDetailVOSAfter.sort(Comparator.comparing(DeliveryPlanPublishedVO::getDemandTime));

                    // 计算出主生产计划能覆盖的最晚发货计划日期
                    Date lastDeliveryPlanTime = getLastDeliveryPlanTime(fgWorkOrderList, deliveryPlanDetailVOSAfter);
                    if ("00685LFW00004".equals(productStockPointVO.getProductCode()) || "00685LFW00006".equals(productStockPointVO.getProductCode())) {
                        for (DeliveryPlanPublishedVO deliveryPlanPublishedVO : deliveryPlanDetailVOSAfter) {
                            log.info("最晚制造订单物品:{}, 发货时间:{}, 发货数量:{}", productStockPointVO.getProductCode(), DateUtils.dateToString(deliveryPlanPublishedVO.getDemandTime()), deliveryPlanPublishedVO.getDemandQuantity());
                        }
                    }

                    if (null != lastDeliveryPlanTime) {
                        if ("00685LFW00004".equals(productStockPointVO.getProductCode()) || "00685LFW00006".equals(productStockPointVO.getProductCode())) {
                            log.info("最晚制造订单物品:{}, 获取能覆盖的时间为:{}", productStockPointVO.getProductCode(), DateUtils.dateToString(lastDeliveryPlanTime));
                        }
                        lastDeliveryPlanTimeMapOfProductCode.put(productStockPointVO.getProductCode(), lastDeliveryPlanTime);
                    }
                });
        mrpContextDTO.setLastDeliveryPlanTimeMapOfProductCode(lastDeliveryPlanTimeMapOfProductCode);
        mrpContextDTO.setRoutingStepInputVOGroup(routingStepInputVOGroup);
    }

    private void getMpsDemandFromDeliveryPlan(String scenario, List<MrpDemandDTO> mrpDemandDataList, MrpContextDTO mrpContextDTO) {
        Map<String, List<DeliveryPlanPublishedVO>> deliveryPlanDetailGroup = mrpContextDTO.getDeliveryPlanDetailVOList()
                .stream().collect(Collectors.groupingBy(DeliveryPlanPublishedVO::getProductCode));
        Map<String, NewProductStockPointVO> newProductStockPointVOMapOfCode = mrpContextDTO.getNewProductStockPointVOMapOfCode();

        // 查询mps_capacity_supply_relationship表,versionId为WEEK的, 只查询本厂的需求
//        List<CapacitySupplyRelationshipVO> list = mpsFeign.selectCapacitySupplyRelationshipByParams(ImmutableMap.of("versionId", "WEEK",
//                "supplyModel", SupplyModelEnum.LOCAL.getCode()));
        List<CapacitySupplyRelationshipVO> list = mpsFeign.selectCapacitySupplyRelationshipByParams(scenario, ImmutableMap.of("versionId", "WEEK"));
        Map<String, Date> lastDeliveryPlanTimeMapOfProductCode = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)) {
            // 获取路径步骤ID
            List<String> routingStepIdList = list.stream().map(CapacitySupplyRelationshipVO::getRoutingStepId).distinct().collect(Collectors.toList());
            // 查询步骤输入
            List<RoutingStepInputVO> routingStepInputList = mdsFeign.selectInputByRoutingStepIds(scenario, routingStepIdList);
            List<RoutingStepVO> routingStepVOList = mdsFeign.getRoutingStepByRoutingStepIds(scenario, routingStepIdList);
            List<String> routingIds = routingStepVOList.stream().map(RoutingStepBasicVO::getRoutingId).distinct().collect(Collectors.toList());
            List<RoutingVO> routingVOList = mdsFeign.selectRoutingByRoutingIds(routingIds);

            Map<String, RoutingStepVO> routingStepVOMapOfId = routingStepVOList.stream().collect(Collectors.toMap(RoutingStepVO::getId, Function.identity()));

            Map<String, List<RoutingStepInputVO>> routingStepInputGroupOfStepId = routingStepInputList.stream().collect(Collectors.groupingBy(RoutingStepInputVO::getRoutingStepId));

            // 根据产能供应关系进行展BOM(周产能平衡)
            for (CapacitySupplyRelationshipVO capacitySupplyRelationshipVO : list) {
                String productCode = capacitySupplyRelationshipVO.getProductCode();
                NewProductStockPointVO bcProductStockPointVO = newProductStockPointVOMapOfCode.get(productCode);
                RoutingStepVO routingStepVO = routingStepVOMapOfId.get(capacitySupplyRelationshipVO.getRoutingStepId());
                if (org.apache.commons.lang3.StringUtils.equals("00887LRF00008", capacitySupplyRelationshipVO.getProductCode())) {
                    log.info("周产能平衡,物品:{}, 时间:{}, 需求:{}, ID:{}", capacitySupplyRelationshipVO.getProductCode(),
                            DateUtils.dateToString(capacitySupplyRelationshipVO.getSupplyTime()),
                            capacitySupplyRelationshipVO.getSupplyQuantity(),
                            capacitySupplyRelationshipVO.getId());
                }

                // 获取输入物品
                List<RoutingStepInputVO> stepInputVOS = routingStepInputGroupOfStepId.get(capacitySupplyRelationshipVO.getRoutingStepId());
                if (CollectionUtils.isEmpty(stepInputVOS)) {
                    continue;
                }
                for (RoutingStepInputVO stepInputVO : stepInputVOS) {
                    // 获取物品
                    NewProductStockPointVO newProductStockPointVO = mrpContextDTO.getNewProductStockPointVOMapOfId()
                            .get(stepInputVO.getInputProductId());
                    // 判断是否为最后一道工序
                    if (null == newProductStockPointVO ||
                            !StringUtils.equals("P", newProductStockPointVO.getProductType()) ||
                            (StringUtils.isEmpty(routingStepVO.getNextRoutingStepSequenceNo())
                                    && "推式".equals(stepInputVO.getSupplyType()))) {
                        continue;
                    }
                    if (null == stepInputVO.getYield()) {
                        stepInputVO.setYield(BigDecimal.ONE);
                    }
                    if (null == stepInputVO.getInputFactor()) {
                        stepInputVO.setInputFactor(BigDecimal.ONE);
                    }

                    BigDecimal demandQuantity = capacitySupplyRelationshipVO.getSupplyQuantity().multiply(stepInputVO.getInputFactor()).divide(stepInputVO.getYield(), RoundingMode.CEILING);

                    if (org.apache.commons.lang3.StringUtils.equals("BGD0136G", newProductStockPointVO.getProductCode()) || org.apache.commons.lang3.StringUtils.equals("BGD0137G", newProductStockPointVO.getProductCode())) {
                        log.info("周产能平衡-输入物品,物品:{}, 时间:{}, 需求:{}", newProductStockPointVO.getProductCode(),
                                DateUtils.dateToString(capacitySupplyRelationshipVO.getSupplyTime()),
                                demandQuantity);
                    }

                    MrpDemandDTO mrpDemandDTO = MrpDemandDTO.builder()
                            .id(capacitySupplyRelationshipVO.getId())
                            .productCode(newProductStockPointVO.getProductCode())
                            .productClassify(newProductStockPointVO.getProductClassify())
                            .demandTime(capacitySupplyRelationshipVO.getSupplyTime())
                            .demandQuantity(demandQuantity)
                            .unFulfillmentQuantity(demandQuantity)
                            .routingStepId(stepInputVO.getRoutingStepId())
                            .demandSource(MrpDemandSourceEnum.MPS.getCode())
                            .productColor(newProductStockPointVO.getProductColor())
                            .productThickness(newProductStockPointVO.getProductThickness())
                            .productFactoryCode(productCode)
                            .vehicleModeCode(bcProductStockPointVO != null ? bcProductStockPointVO.getVehicleModelCode() : null)
                            .build();
                    mrpDemandDataList.add(mrpDemandDTO);
                }
            }

            list.stream()
                    .filter(item -> {
                        NewProductStockPointVO newProductStockPointVO = mrpContextDTO.getNewProductStockPointVOMapOfCode()
                                .get(item.getProductCode());
                        return deliveryPlanDetailGroup.containsKey(newProductStockPointVO.getProductCode());
                    })
                    .collect(Collectors.groupingBy(CapacitySupplyRelationshipVO::getProductCode))
                    .forEach((productCode, capacitySupplyRelationshipVOS) -> {
                        NewProductStockPointVO newProductStockPointVO = mrpContextDTO.getNewProductStockPointVOMapOfCode().get(productCode);
                        capacitySupplyRelationshipVOS.sort(Comparator.comparing(CapacitySupplyRelationshipVO::getSupplyTime));
                        // 产能平衡的最晚日期
                        Date lastSupplyTime = capacitySupplyRelationshipVOS.get(capacitySupplyRelationshipVOS.size() - 1).getSupplyTime();

                        List<DeliveryPlanPublishedVO> deliveryPlanPublishedVOList = deliveryPlanDetailGroup.get(newProductStockPointVO.getProductCode());
                        // 过滤需求时间晚于主生产计划最晚日期的发货计划数据
                        List<DeliveryPlanPublishedVO> deliveryPlanDetailVOSAfter = deliveryPlanPublishedVOList.stream()
                                .filter(dp -> !dp.getDemandTime().before(lastSupplyTime)).collect(Collectors.toList());
                        // 计算出主生产计划能覆盖的最晚发货计划日期
                        Date lastDeliveryPlanTime = getLastDeliveryPlanTime2(capacitySupplyRelationshipVOS, deliveryPlanDetailVOSAfter);
                        if (null != lastDeliveryPlanTime) {
                            lastDeliveryPlanTimeMapOfProductCode.put(productCode, lastDeliveryPlanTime);
                        }
                    });
            mrpContextDTO.setRoutingStepInputVOGroup(routingStepInputGroupOfStepId);
            mrpContextDTO.setRoutingVOList(routingVOList);
            mrpContextDTO.setMrpDemandList(mrpDemandDataList);
            mrpContextDTO.setRoutingStepVOList(routingStepVOList);
            mrpContextDTO.setRoutingStepInputVOList(routingStepInputList);
        }
        mrpContextDTO.setLastDeliveryPlanTimeMapOfProductCode(lastDeliveryPlanTimeMapOfProductCode);
    }

    /**
     * 计算安全库存水位
     *
     * @param inventoryShiftDate    规划日期
     * @param stockDays             安全库存天数
     * @param mrpDemandMapOfDateStr 规划日期对应的需求数据
     * @return 安全库存水位
     */
    protected BigDecimal caleInventoryWaterLevel(Date inventoryShiftDate, BigDecimal stockDays, Map<String, List<MrpDemandDTO>> mrpDemandMapOfDateStr) {
        BigDecimal stockQuantity = BigDecimal.ZERO;
        for (int i = 0; i < stockDays.intValue(); i++) {
            inventoryShiftDate = DateUtils.moveDay(inventoryShiftDate, 1);
            List<MrpDemandDTO> dayMrpDemandList = mrpDemandMapOfDateStr.get(DateUtils.dateToString(inventoryShiftDate, DateUtils.COMMON_DATE_STR3));
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(dayMrpDemandList)) {
                continue;
            }
            BigDecimal demandQuantity = dayMrpDemandList.stream().map(MrpDemandDTO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            stockQuantity = stockQuantity.add(demandQuantity);
        }
        return stockQuantity;
    }


    protected MrpDemandDTO createMrpDemandDTO(String productCode, String productClassify, BigDecimal demandQuantity, Date demandDate) {
        // 计算差异量
        return MrpDemandDTO.builder()
                .productCode(productCode)
                .productClassify(productClassify)
                .demandTime(demandDate)
                .demandQuantity(demandQuantity)
                .unFulfillmentQuantity(demandQuantity)
                .demandSource(MrpDemandSourceEnum.ZZK.getCode())
                .build();
    }

}