<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.material.plan.infrastructure.dao.MaterialPlanTransferDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanTransferPO">
        <!--@Table mrp_material_plan_transfer-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="plan_transfer_no" jdbcType="VARCHAR" property="planTransferNo"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="transfer_routing_id" jdbcType="VARCHAR" property="transferRoutingId"/>
        <result column="cabinet_no" jdbcType="VARCHAR" property="cabinetNo"/>
        <result column="inventory_type" jdbcType="VARCHAR" property="inventoryType"/>
        <result column="stock_point_code_from" jdbcType="VARCHAR" property="stockPointCodeFrom"/>
        <result column="stock_point_code_to" jdbcType="VARCHAR" property="stockPointCodeTo"/>
        <result column="transfer_status" jdbcType="VARCHAR" property="transferStatus"/>
        <result column="history_quantity" jdbcType="DECIMAL" property="historyQuantity"/>
        <result column="adjust_quantity" jdbcType="DECIMAL" property="adjustQuantity"/>
        <result column="transfer_quantity" jdbcType="DECIMAL" property="transferQuantity"/>
        <result column="transferred_quantity" jdbcType="DECIMAL" property="transferredQuantity"/>
        <result column="transfer_date_depart" jdbcType="TIMESTAMP" property="transferDateDepart"/>
        <result column="transfer_date_arrive" jdbcType="TIMESTAMP" property="transferDateArrive"/>
        <result column="storage_flag" jdbcType="VARCHAR" property="storageFlag"/>
        <result column="container_delivery_time" jdbcType="TIMESTAMP" property="containerDeliveryTime"/>
        <result column="purchase_order_code" jdbcType="VARCHAR" property="purchaseOrderCode"/>
        <result column="purchase_order_line_code" jdbcType="VARCHAR" property="purchaseOrderLineCode"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mrp.material.plan.vo.MaterialPlanTransferVO">
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="product_length" jdbcType="VARCHAR" property="productLength"/>
        <result column="product_width" jdbcType="VARCHAR" property="productWidth"/>
        <result column="product_thickness" jdbcType="VARCHAR" property="productThickness"/>
        <result column="product_color" jdbcType="VARCHAR" property="productColor"/>
        <result column="routing_code" jdbcType="VARCHAR" property="routingCode"/>
        <result column="stock_point_code_from" jdbcType="VARCHAR" property="stockPointCodeFrom"/>
        <result column="stock_point_name_from" jdbcType="VARCHAR" property="stockPointNameFrom"/>
        <result column="stock_point_type_from" jdbcType="VARCHAR" property="stockPointTypeFrom"/>
        <result column="stock_point_code_to" jdbcType="VARCHAR" property="stockPointCodeTo"/>
        <result column="stock_point_name_to" jdbcType="VARCHAR" property="stockPointNameTo"/>
        <result column="stock_point_type_to" jdbcType="VARCHAR" property="stockPointTypeTo"/>
        <result column="product_spec" jdbcType="VARCHAR" property="productSpec"/>
        <result column="level" jdbcType="VARCHAR" property="level"/>
        <result column="per_box" jdbcType="VARCHAR" property="perBox"/>
        <result column="box" jdbcType="VARCHAR" property="box"/>
        <result column="actual_arrival_time" jdbcType="TIMESTAMP" property="actualArrivalTime"/>
        <result column="port_name" jdbcType="VARCHAR" property="portName"/>
        <result column="carrier" jdbcType="VARCHAR" property="carrier"/>
        <result column="package_type" jdbcType="VARCHAR" property="packageType"/>
        <result column="delivered" jdbcType="VARCHAR" property="delivered"/>
        <result column="actual_sent_quantity" jdbcType="VARCHAR" property="actualSentQuantity"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="weight" jdbcType="VARCHAR" property="weight"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="classify_desc" jdbcType="VARCHAR" property="classifyDesc"/>
        <result column="container_number" jdbcType="VARCHAR" property="containerNumber"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,plan_transfer_no,product_code,transfer_routing_id,cabinet_no,inventory_type,stock_point_code_from,stock_point_code_to
        ,transfer_status,history_quantity, adjust_quantity,transfer_quantity,transferred_quantity,transfer_date_depart
        ,transfer_date_arrive,storage_flag,container_delivery_time,purchase_order_code,purchase_order_line_code
        ,remark,enabled,creator,create_time,modifier,modify_time
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
        ,product_id,product_name,product_length,product_width,product_thickness,product_color,routing_code
        ,stock_point_code_from,stock_point_name_from,stock_point_type_from,stock_point_code_to,stock_point_name_to,stock_point_type_to
    </sql>
    <sql id="VO_Column_List_MT">
        <!-- TODO -->
        <include refid="Base_Column_List"/>,product_name,product_thickness,product_color,
        routing_code,stock_point_name_from,stock_point_name_to,product_spec,level,per_box,box,actual_arrival_time,port_name,
        carrier,package_type,delivered,actual_sent_quantity,area,weight,stock_point_code,classify_desc,product_length,product_width,container_number
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.ids != null and params.ids.size() > 0">
                and id in
                <foreach collection="params.ids" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.planTransferNo != null and params.planTransferNo != ''">
                and plan_transfer_no = #{params.planTransferNo,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.transferRoutingId != null and params.transferRoutingId != ''">
                and transfer_routing_id = #{params.transferRoutingId,jdbcType=VARCHAR}
            </if>
            <if test="params.cabinetNo != null and params.cabinetNo != ''">
                and cabinet_no = #{params.cabinetNo,jdbcType=VARCHAR}
            </if>
            <if test="params.cabinetNos != null and params.cabinetNos.size() > 0">
                and cabinet_no in
                <foreach collection="params.cabinetNos" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.inventoryType != null and params.inventoryType != ''">
                and inventory_type = #{params.inventoryType,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCodeFrom != null and params.stockPointCodeFrom != ''">
                and stock_point_code_from = #{params.stockPointCodeFrom,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCodeTo != null and params.stockPointCodeTo != ''">
                and stock_point_code_to = #{params.stockPointCodeTo,jdbcType=VARCHAR}
            </if>
            <if test="params.transferStatus != null and params.transferStatus != ''">
                and transfer_status = #{params.transferStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.historyQuantity != null">
                and history_quantity = #{params.historyQuantity,jdbcType=DECIMAL}
            </if>
            <if test="params.adjustQuantity != null">
                and adjust_quantity = #{params.adjustQuantity,jdbcType=DECIMAL}
            </if>
            <if test="params.transferQuantity != null">
                and transfer_quantity = #{params.transferQuantity,jdbcType=DECIMAL}
            </if>
            <if test="params.transferredQuantity != null">
                and transferred_quantity = #{params.transferredQuantity,jdbcType=DECIMAL}
            </if>
            <if test="params.transferDateDepart != null">
                and transfer_date_depart = #{params.transferDateDepart,jdbcType=TIMESTAMP}
            </if>
            <if test="params.transferDateDepartStart != null">
                and transfer_date_depart &gt; #{params.transferDateDepartStart,jdbcType=TIMESTAMP}
            </if>
            <if test="params.transferDateArrive != null">
                and transfer_date_arrive = #{params.transferDateArrive,jdbcType=TIMESTAMP}
            </if>
            <if test="params.transferDateArriveList != null and params.transferDateArriveList.size() > 0">
                and transfer_date_arrive in
                <foreach collection="params.transferDateArriveList" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item,jdbcType=TIMESTAMP}
                </foreach>
            </if>
            <if test="params.storageFlag != null and params.storageFlag != ''">
                and storage_flag = #{params.storageFlag,jdbcType=VARCHAR}
            </if>
            <if test="params.containerDeliveryTime != null">
                and container_delivery_time = #{params.containerDeliveryTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.purchaseOrderCode != null and params.purchaseOrderCode != ''">
                and purchase_order_code = #{params.purchaseOrderCode,jdbcType=VARCHAR}
            </if>
            <if test="params.purchaseOrderLineCode != null and params.purchaseOrderLineCode != ''">
                and purchase_order_line_code = #{params.purchaseOrderLineCode,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_plan_transfer
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_plan_transfer
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mrp_material_plan_transfer
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <select id="selectMtByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List_MT"/>
        from v_mrp_material_plan_transfer_mt
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_plan_transfer
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mrp_material_plan_transfer_published
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanTransferPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_material_plan_transfer(
        id,
        plan_transfer_no,
        product_code,
        transfer_routing_id,
        cabinet_no,
        inventory_type,
        stock_point_code_from,
        stock_point_code_to,
        transfer_status,
        history_quantity,
        adjust_quantity,
        transfer_quantity,
        transferred_quantity,
        transfer_date_depart,
        transfer_date_arrive,
        storage_flag,
        container_delivery_time,
        purchase_order_code,
        purchase_order_line_code,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{planTransferNo,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{transferRoutingId,jdbcType=VARCHAR},
        #{cabinetNo,jdbcType=VARCHAR},
        #{inventoryType,jdbcType=VARCHAR},
        #{stockPointCodeFrom,jdbcType=VARCHAR},
        #{stockPointCodeTo,jdbcType=VARCHAR},
        #{transferStatus,jdbcType=VARCHAR},
        #{historyQuantity,jdbcType=DECIMAL},
        #{adjustQuantity,jdbcType=DECIMAL},
        #{transferQuantity,jdbcType=DECIMAL},
        #{transferredQuantity,jdbcType=DECIMAL},
        #{transferDateDepart,jdbcType=TIMESTAMP},
        #{transferDateArrive,jdbcType=TIMESTAMP},
        #{storageFlag,jdbcType=VARCHAR},
        #{containerDeliveryTime,jdbcType=TIMESTAMP},
        #{purchaseOrderCode,jdbcType=VARCHAR},
        #{purchaseOrderLineCode,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanTransferPO">
        insert into mrp_material_plan_transfer(id,
                                               plan_transfer_no,
                                               product_code,
                                               transfer_routing_id,
                                               cabinet_no,
                                               inventory_type,
                                               stock_point_code_from,
                                               stock_point_code_to,
                                               transfer_status,
                                               history_quantity,
                                               adjust_quantity,
                                               transfer_quantity,
                                               transferred_quantity,
                                               transfer_date_depart,
                                               transfer_date_arrive,
                                               storage_flag,
                                               container_delivery_time,
                                               purchase_order_code,
                                               purchase_order_line_code,
                                               remark,
                                               enabled,
                                               creator,
                                               create_time,
                                               modifier,
                                               modify_time)
        values (#{id,jdbcType=VARCHAR},
                #{planTransferNo,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{transferRoutingId,jdbcType=VARCHAR},
                #{cabinetNo,jdbcType=VARCHAR},
                #{inventoryType,jdbcType=VARCHAR},
                #{stockPointCodeFrom,jdbcType=VARCHAR},
                #{stockPointCodeTo,jdbcType=VARCHAR},
                #{transferStatus,jdbcType=VARCHAR},
                #{historyQuantity,jdbcType=DECIMAL},
                #{adjustQuantity,jdbcType=DECIMAL},
                #{transferQuantity,jdbcType=DECIMAL},
                #{transferredQuantity,jdbcType=DECIMAL},
                #{transferDateDepart,jdbcType=TIMESTAMP},
                #{transferDateArrive,jdbcType=TIMESTAMP},
                #{storageFlag,jdbcType=VARCHAR},
                #{containerDeliveryTime,jdbcType=TIMESTAMP},
                #{purchaseOrderCode,jdbcType=VARCHAR},
                #{purchaseOrderLineCode,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_material_plan_transfer(
        id,
        plan_transfer_no,
        product_code,
        transfer_routing_id,
        cabinet_no,
        inventory_type,
        stock_point_code_from,
        stock_point_code_to,
        transfer_status,
        history_quantity,
        adjust_quantity,
        transfer_quantity,
        transferred_quantity,
        transfer_date_depart,
        transfer_date_arrive,
        storage_flag,
        container_delivery_time,
        purchase_order_code,
        purchase_order_line_code,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.planTransferNo,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.transferRoutingId,jdbcType=VARCHAR},
            #{entity.cabinetNo,jdbcType=VARCHAR},
            #{entity.inventoryType,jdbcType=VARCHAR},
            #{entity.stockPointCodeFrom,jdbcType=VARCHAR},
            #{entity.stockPointCodeTo,jdbcType=VARCHAR},
            #{entity.transferStatus,jdbcType=VARCHAR},
            #{entity.historyQuantity,jdbcType=DECIMAL},
            #{entity.adjustQuantity,jdbcType=DECIMAL},
            #{entity.transferQuantity,jdbcType=DECIMAL},
            #{entity.transferredQuantity,jdbcType=DECIMAL},
            #{entity.transferDateDepart,jdbcType=TIMESTAMP},
            #{entity.transferDateArrive,jdbcType=TIMESTAMP},
            #{entity.storageFlag,jdbcType=VARCHAR},
            #{entity.containerDeliveryTime,jdbcType=TIMESTAMP},
            #{entity.purchaseOrderCode,jdbcType=VARCHAR},
            #{entity.purchaseOrderLineCode,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_material_plan_transfer(
        id,
        plan_transfer_no,
        product_code,
        transfer_routing_id,
        cabinet_no,
        inventory_type,
        stock_point_code_from,
        stock_point_code_to,
        transfer_status,
        history_quantity,
        adjust_quantity,
        transfer_quantity,
        transferred_quantity,
        transfer_date_depart,
        transfer_date_arrive,
        storage_flag,
        container_delivery_time,
        purchase_order_code,
        purchase_order_line_code,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.planTransferNo,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.transferRoutingId,jdbcType=VARCHAR},
            #{entity.cabinetNo,jdbcType=VARCHAR},
            #{entity.inventoryType,jdbcType=VARCHAR},
            #{entity.stockPointCodeFrom,jdbcType=VARCHAR},
            #{entity.stockPointCodeTo,jdbcType=VARCHAR},
            #{entity.transferStatus,jdbcType=VARCHAR},
            #{entity.historyQuantity,jdbcType=DECIMAL},
            #{entity.adjustQuantity,jdbcType=DECIMAL},
            #{entity.transferQuantity,jdbcType=DECIMAL},
            #{entity.transferredQuantity,jdbcType=DECIMAL},
            #{entity.transferDateDepart,jdbcType=TIMESTAMP},
            #{entity.transferDateArrive,jdbcType=TIMESTAMP},
            #{entity.storageFlag,jdbcType=VARCHAR},
            #{entity.containerDeliveryTime,jdbcType=TIMESTAMP},
            #{entity.purchaseOrderCode,jdbcType=VARCHAR},
            #{entity.purchaseOrderLineCode,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanTransferPO">
        update mrp_material_plan_transfer
        set plan_transfer_no         = #{planTransferNo,jdbcType=VARCHAR},
            product_code             = #{productCode,jdbcType=VARCHAR},
            transfer_routing_id      = #{transferRoutingId,jdbcType=VARCHAR},
            cabinet_no               = #{cabinetNo,jdbcType=VARCHAR},
            inventory_type           = #{inventoryType,jdbcType=VARCHAR},
            stock_point_code_from    = #{stockPointCodeFrom,jdbcType=VARCHAR},
            stock_point_code_to      = #{stockPointCodeTo,jdbcType=VARCHAR},
            transfer_status          = #{transferStatus,jdbcType=VARCHAR},
            history_quantity         = #{historyQuantity,jdbcType=DECIMAL},
            adjust_quantity          = #{adjustQuantity,jdbcType=DECIMAL},
            transfer_quantity        = #{transferQuantity,jdbcType=DECIMAL},
            transferred_quantity     = #{transferredQuantity,jdbcType=DECIMAL},
            transfer_date_depart     = #{transferDateDepart,jdbcType=TIMESTAMP},
            transfer_date_arrive     = #{transferDateArrive,jdbcType=TIMESTAMP},
            storage_flag             = #{storageFlag,jdbcType=VARCHAR},
            container_delivery_time  = #{containerDeliveryTime,jdbcType=TIMESTAMP},
            purchase_order_code      = #{purchaseOrderCode,jdbcType=VARCHAR},
            purchase_order_line_code = #{purchaseOrderLineCode,jdbcType=VARCHAR},
            remark                   = #{remark,jdbcType=VARCHAR},
            enabled                  = #{enabled,jdbcType=VARCHAR},
            modifier                 = #{modifier,jdbcType=VARCHAR},
            modify_time              = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanTransferPO">
        update mrp_material_plan_transfer
        <set>
            <if test="item.planTransferNo != null and item.planTransferNo != ''">
                plan_transfer_no = #{item.planTransferNo,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.transferRoutingId != null and item.transferRoutingId != ''">
                transfer_routing_id = #{item.transferRoutingId,jdbcType=VARCHAR},
            </if>
            <if test="item.cabinetNo != null and item.cabinetNo != ''">
                cabinet_no = #{item.cabinetNo,jdbcType=VARCHAR},
            </if>
            <if test="item.inventoryType != null and item.inventoryType != ''">
                inventory_type = #{item.inventoryType,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointCodeFrom != null and item.stockPointCodeFrom != ''">
                stock_point_code_from = #{item.stockPointCodeFrom,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointCodeTo != null and item.stockPointCodeTo != ''">
                stock_point_code_to = #{item.stockPointCodeTo,jdbcType=VARCHAR},
            </if>
            <if test="item.transferStatus != null and item.transferStatus != ''">
                transfer_status = #{item.transferStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.historyQuantity != null">
                history_quantity = #{item.historyQuantity,jdbcType=DECIMAL},
            </if>
            <if test="item.adjustQuantity != null">
                adjust_quantity = #{item.adjustQuantity,jdbcType=DECIMAL},
            </if>
            <if test="item.transferQuantity != null">
                transfer_quantity = #{item.transferQuantity,jdbcType=DECIMAL},
            </if>
            <if test="item.transferredQuantity != null">
                transferred_quantity = #{item.transferredQuantity,jdbcType=DECIMAL},
            </if>
            <if test="item.transferDateDepart != null">
                transfer_date_depart = #{item.transferDateDepart,jdbcType=TIMESTAMP},
            </if>
            <if test="item.transferDateArrive != null">
                transfer_date_arrive = #{item.transferDateArrive,jdbcType=TIMESTAMP},
            </if>
            <if test="item.storageFlag != null and item.storageFlag != ''">
                storage_flag = #{item.storageFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.containerDeliveryTime != null">
                container_delivery_time = #{item.containerDeliveryTime,jdbcType=TIMESTAMP}
            </if>
            <if test="item.purchaseOrderCode != null and item.purchaseOrderCode != ''">
                purchase_order_code = #{item.purchaseOrderCode,jdbcType=VARCHAR}
            </if>
            <if test="item.purchaseOrderLineCode != null and item.purchaseOrderLineCode != ''">
                purchase_order_line_code = #{item.purchaseOrderLineCode,jdbcType=VARCHAR}
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_material_plan_transfer
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="plan_transfer_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.planTransferNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transfer_routing_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transferRoutingId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cabinet_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.cabinetNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="inventory_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inventoryType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_code_from = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointCodeFrom,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_code_to = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointCodeTo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transfer_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transferStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="history_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.historyQuantity,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="adjust_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.adjustQuantity,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="transfer_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transferQuantity,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="transferred_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transferredQuantity,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="transfer_date_depart = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transferDateDepart,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="transfer_date_arrive = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transferDateArrive,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="storage_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.storageFlag,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="container_delivery_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.containerDeliveryTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="purchase_order_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.purchaseOrderCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="purchase_order_line_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.purchaseOrderLineCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_material_plan_transfer
            <set>
                <if test="item.planTransferNo != null and item.planTransferNo != ''">
                    plan_transfer_no = #{item.planTransferNo,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.transferRoutingId != null and item.transferRoutingId != ''">
                    transfer_routing_id = #{item.transferRoutingId,jdbcType=VARCHAR},
                </if>
                <if test="item.cabinetNo != null and item.cabinetNo != ''">
                    cabinet_no = #{item.cabinetNo,jdbcType=VARCHAR},
                </if>
                <if test="item.inventoryType != null and item.inventoryType != ''">
                    inventory_type = #{item.inventoryType,jdbcType=VARCHAR},
                </if>
                <if test="item.stockPointCodeFrom != null and item.stockPointCodeFrom != ''">
                    stock_point_code_from = #{item.stockPointCodeFrom,jdbcType=VARCHAR},
                </if>
                <if test="item.stockPointCodeTo != null and item.stockPointCodeTo != ''">
                    stock_point_code_to = #{item.stockPointCodeTo,jdbcType=VARCHAR},
                </if>
                <if test="item.transferStatus != null and item.transferStatus != ''">
                    transfer_status = #{item.transferStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.historyQuantity != null">
                    history_quantity = #{item.historyQuantity,jdbcType=DECIMAL},
                </if>
                <if test="item.adjustQuantity != null">
                    adjust_quantity = #{item.adjustQuantity,jdbcType=DECIMAL},
                </if>
                <if test="item.transferQuantity != null">
                    transfer_quantity = #{item.transferQuantity,jdbcType=DECIMAL},
                </if>
                <if test="item.transferredQuantity != null">
                    transferred_quantity = #{item.transferredQuantity,jdbcType=DECIMAL},
                </if>
                <if test="item.transferDateDepart != null">
                    transfer_date_depart = #{item.transferDateDepart,jdbcType=TIMESTAMP},
                </if>
                <if test="item.transferDateArrive != null">
                    transfer_date_arrive = #{item.transferDateArrive,jdbcType=TIMESTAMP},
                </if>
                <if test="item.storageFlag != null and item.storageFlag != ''">
                    storage_flag = #{item.storageFlag,jdbcType=VARCHAR},
                </if>
                <if test="item.containerDeliveryTime != null">
                    container_delivery_time = #{item.containerDeliveryTime,jdbcType=TIMESTAMP}
                </if>
                <if test="item.purchaseOrderCode != null and item.purchaseOrderCode != ''">
                    purchase_order_code = #{item.purchaseOrderCode,jdbcType=VARCHAR}
                </if>
                <if test="item.purchaseOrderLineCode != null and item.purchaseOrderLineCode != ''">
                    purchase_order_line_code = #{item.purchaseOrderLineCode,jdbcType=VARCHAR}
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_material_plan_transfer
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_material_plan_transfer where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <delete id="deleteUnpublishedBatch">
        delete
        from mrp_material_plan_transfer
        where transfer_status = 'UN_PUBLISHED'
    </delete>

    <delete id="deleteByCabinetNo">
        delete
        from mrp_material_plan_transfer
        where cabinet_no in
        <foreach collection="cabinetNos" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

</mapper>
