package com.yhl.scp.mrp.material.plan.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RedisKeyManageEnum;
import com.yhl.scp.mrp.inventory.dto.MaterialPlanInventoryShiftParamDTO;
import com.yhl.scp.mrp.material.plan.dto.NoGlassInventoryShiftDTO;
import com.yhl.scp.mrp.material.plan.enums.MaterialPlanTypeEnum;
import com.yhl.scp.mrp.material.plan.service.NoGlassInventoryShiftService;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanInventoryShiftPageVO;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>NoGlassInventoryShiftController</code>
 * <p>
 * 非原片库存推移表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-10 15:13:58
 */
@Slf4j
@Api(tags = "非原片库存推移表控制器")
@RestController
@RequestMapping("noGlassInventoryShift")
public class NoGlassInventoryShiftController extends BaseController {

    @Resource
    private NoGlassInventoryShiftService noGlassInventoryShiftService;

    @Resource
    private RedisUtil redisUtil;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<NoGlassInventoryShiftVO>> page() {
        List<NoGlassInventoryShiftVO> noGlassInventoryShiftList = noGlassInventoryShiftService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<NoGlassInventoryShiftVO> pageInfo = new PageInfo<>(noGlassInventoryShiftList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody NoGlassInventoryShiftDTO noGlassInventoryShiftDTO) {
        return noGlassInventoryShiftService.doCreate(noGlassInventoryShiftDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody NoGlassInventoryShiftDTO noGlassInventoryShiftDTO) {
        return noGlassInventoryShiftService.doUpdate(noGlassInventoryShiftDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        noGlassInventoryShiftService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<NoGlassInventoryShiftVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, noGlassInventoryShiftService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "分页查询(非组件)")
    @PostMapping(value = "page2")
    public BaseResponse<PageInfo<MaterialPlanInventoryShiftPageVO>> page2(@RequestBody MaterialPlanInventoryShiftParamDTO materialPlanInventoryShiftParamDTO) {
//        if (StringUtils.isBlank(materialPlanInventoryShiftParamDTO.getMaterialPlanVersionId())){
//            // 查询最新版本
//            MaterialPlanVersionVO materialPlanVersionVO = materialPlanVersionService.selectLatestVersion(MaterialTypeEnum.NO_GLASS.getCode(), SystemHolder.getUserId());
//            if (null == materialPlanVersionVO){
//                return BaseResponse.success(new PageInfo<>());
//            }
//            materialPlanInventoryShiftParamDTO.setMaterialPlanVersionId(materialPlanVersionVO.getId());
//            materialPlanInventoryShiftParamDTO.setMaterialPlanVersionCreateTime(materialPlanVersionVO.getCreateTime());
//        }else {
//            MaterialPlanVersionVO materialPlanVersionVO = materialPlanVersionService.selectByPrimaryKey(materialPlanInventoryShiftParamDTO.getMaterialPlanVersionId());
//            materialPlanInventoryShiftParamDTO.setMaterialPlanVersionCreateTime(materialPlanVersionVO.getCreateTime());
//        }
        PageInfo<MaterialPlanInventoryShiftPageVO> pageInfo = noGlassInventoryShiftService.selectPage2(materialPlanInventoryShiftParamDTO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "校验是否可以调整")
    @PostMapping(value = "checkAdjust")
    public BaseResponse<String> checkAdjust(@RequestBody NoGlassInventoryShiftDTO noGlassInventoryShiftDTO) {
        try {
            return noGlassInventoryShiftService.checkAdjust(noGlassInventoryShiftDTO);
        } catch (Exception e) {
            log.error("校验失败", e);
            throw new BusinessException("校验失败,{0}", e.getLocalizedMessage());
        }
    }

    @ApiOperation(value = "调整数据")
    @PostMapping(value = "adjustData")
    public BaseResponse<String> adjustData(@RequestBody NoGlassInventoryShiftDTO noGlassInventoryShiftDTO) {
        try {
            return noGlassInventoryShiftService.doAdjustData(noGlassInventoryShiftDTO);
        } catch (Exception e) {
            log.error("调整失败", e);
            throw new BusinessException("调整失败,{0}", e.getLocalizedMessage());
        }
    }

    @ApiOperation(value = "发布")
    @GetMapping(value = "publish")
    @BusinessMonitorLog(businessCode = "材料计划发布", moduleCode = "MRP", businessFrequency = "DAY")
    public BaseResponse<Void> publish(@RequestParam(name = "publishType") String publishType) {
        try {
            if (redisUtil.hasKey(RedisKeyManageEnum.NO_GLASS_MRP_PUBLISHED_KEY.getKey())) {
                return BaseResponse.error("当前已有推移结果正在发布，请等待发布完成");
            }
            redisUtil.set(RedisKeyManageEnum.NO_GLASS_MRP_PUBLISHED_KEY.getKey(), SystemHolder.getUserId(), 60 * 60);

            if (StringUtils.equals(publishType, MaterialPlanTypeEnum.NO_GLASS.getCode())) {
                noGlassInventoryShiftService.doPublish();
            }
            return BaseResponse.success();
        } catch (Exception e) {
            log.error("发布失败", e);
            throw new BusinessException("发布失败,{0}", e.getLocalizedMessage());
        } finally {
            // 释放key
            redisUtil.delete(RedisKeyManageEnum.NO_GLASS_MRP_PUBLISHED_KEY.getKey());
        }
    }

}
