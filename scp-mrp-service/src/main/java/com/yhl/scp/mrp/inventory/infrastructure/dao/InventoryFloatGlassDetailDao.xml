<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.inventory.infrastructure.dao.InventoryFloatGlassDetailDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.inventory.infrastructure.po.InventoryFloatGlassDetailPO">
        <!--@Table sds_material_inventory_float_glass_detail-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_spec" jdbcType="VARCHAR" property="productSpec"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="level" jdbcType="VARCHAR" property="level"/>
        <result column="lot_level_code" jdbcType="VARCHAR" property="lotLevelCode"/>
        <result column="lot_number" jdbcType="VARCHAR" property="lotNumber"/>
        <result column="per_box" jdbcType="VARCHAR" property="perBox"/>
        <result column="weight_box" jdbcType="VARCHAR" property="weightBox"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="weight" jdbcType="VARCHAR" property="weight"/>
        <result column="overdue_count" jdbcType="VARCHAR" property="overdueCount"/>
        <result column="cutting_rate" jdbcType="VARCHAR" property="cuttingRate"/>
        <result column="qty" jdbcType="VARCHAR" property="qty"/>
        <result column="plan_quantity" jdbcType="VARCHAR" property="planQuantity"/>
        <result column="org_id" jdbcType="VARCHAR" property="orgId"/>
        <result column="sub" jdbcType="VARCHAR" property="sub"/>
        <result column="location" jdbcType="VARCHAR" property="location"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="storage_time" jdbcType="TIMESTAMP" property="storageTime"/>
        <result column="source_type" jdbcType="VARCHAR" property="sourceType"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mrp.inventory.vo.InventoryFloatGlassDetailVO">
        <!-- TODO -->
        <result column="product_thickness" jdbcType="VARCHAR" property="productThickness"/>
        <result column="product_color" jdbcType="VARCHAR" property="productColor"/>
        <result column="classify_desc" jdbcType="VARCHAR" property="classifyDesc"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,product_id,product_code,product_spec,stock_point_code,level,lot_level_code,lot_number,per_box,weight_box
        ,area,weight,overdue_count,cutting_rate,plan_quantity,qty
        ,enabled,creator,create_time,modifier,modify_time,storage_time,source_type

    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>,product_thickness,product_color,classify_desc
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.productId != null and params.productId != ''">
                and product_id = #{params.productId,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productSpec != null and params.productSpec != ''">
                and product_spec = #{params.productSpec,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.level != null and params.level != ''">
                and level = #{params.level,jdbcType=VARCHAR}
            </if>
            <if test="params.lotLevelCode != null and params.lotLevelCode != ''">
                and lot_level_code = #{params.lotLevelCode,jdbcType=VARCHAR}
            </if>
            <if test="params.lotNumber != null and params.lotNumber != ''">
                and lot_number = #{params.lotNumber,jdbcType=VARCHAR}
            </if>
            <if test="params.perBox != null">
                and per_box = #{params.perBox,jdbcType=VARCHAR}
            </if>
            <if test="params.weightBox != null">
                and weight_box = #{params.weightBox,jdbcType=VARCHAR}
            </if>
            <if test="params.area != null">
                and area = #{params.area,jdbcType=VARCHAR}
            </if>
            <if test="params.weight != null">
                and weight = #{params.weight,jdbcType=VARCHAR}
            </if>
            <if test="params.overdueCount != null">
                and overdue_count = #{params.overdueCount,jdbcType=VARCHAR}
            </if>
            <if test="params.cuttingRate != null">
                and cutting_rate = #{params.cuttingRate,jdbcType=VARCHAR}
            </if>
            <if test="params.planQuantity != null">
                and plan_quantity = #{params.planQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.orgId != null and params.orgId != ''">
                and org_id = #{params.orgId,jdbcType=VARCHAR}
            </if>
            <if test="params.sub != null and params.sub != ''">
                and sub = #{params.sub,jdbcType=VARCHAR}
            </if>
            <if test="params.location != null and params.location != ''">
                and location = #{params.location,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.qty != null">
                and qty = #{params.qty,jdbcType=VARCHAR}
            </if>
            <if test="params.storageTime != null">
                and storage_time = #{params.storageTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.sourceType != null and params.sourceType != ''">
                and source_type = #{params.sourceType,jdbcType=VARCHAR}
            </if>
            <if test="params.combineKeys != null and params.combineKeys.size() > 0">
                and CONCAT_WS('_',product_code,stock_point_code,lot_number) in
                <foreach collection="params.combineKeys" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sds_material_inventory_float_glass_detail
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sds_material_inventory_float_glass_detail
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_sds_material_inventory_float_glass_detail
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <!-- 动态排序 -->
        <choose>
            <when test="sortParam != null and sortParam != ''">
                order by ${sortParam}
            </when>
            <when test="overdueSort != null and overdueSort != '' and overdueSort == 'YES'">
                order by overdue_count DESC
            </when>
        </choose>
    </select>
    <select id="selectProductInventory" resultMap="VOResultMap">
        SELECT t1.product_code,
               t1.stock_point_code,
               t1.qty,
               t1.storage_time,
               t2.per_box
        FROM (SELECT product_code, stock_point_code, SUM(qty * per_box) qty, MIN(storage_time) storage_time
              FROM sds_material_inventory_float_glass_detail
              GROUP BY product_code, stock_point_code) t1
                 LEFT JOIN (WITH RankedData AS (SELECT product_code,
                                                       stock_point_code,
                                                       per_box,
                                                       storage_time,
                                                       ROW_NUMBER() OVER ( PARTITION BY product_code, stock_point_code ORDER BY storage_time ) AS rn
                                                FROM sds_material_inventory_float_glass_detail)
                            SELECT product_code,
                                   stock_point_code,
                                   per_box,
                                   storage_time
                            FROM RankedData
                            WHERE rn = 1) t2 ON t1.product_code = t2.product_code
            AND t1.stock_point_code = t2.stock_point_code;
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sds_material_inventory_float_glass_detail
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 条件查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_sds_material_inventory_float_glass_detail
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.InventoryFloatGlassDetailPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into sds_material_inventory_float_glass_detail(
        id,
        product_id,
        product_code,
        product_spec,
        stock_point_code,
        level,
        lot_level_code,
        lot_number,
        per_box,
        weight_box,
        area,
        weight,
        overdue_count,
        cutting_rate,
        plan_quantity,
        qty,
        org_id,
        sub,
        location,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        storage_time,
        source_type)
        values (
        #{id,jdbcType=VARCHAR},
        #{productId,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{productSpec,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{level,jdbcType=VARCHAR},
        #{lotLevelCode,jdbcType=VARCHAR},
        #{lotNumber,jdbcType=VARCHAR},
        #{perBox,jdbcType=VARCHAR},
        #{weightBox,jdbcType=VARCHAR},
        #{area,jdbcType=VARCHAR},
        #{weight,jdbcType=VARCHAR},
        #{overdueCount,jdbcType=VARCHAR},
        #{cuttingRate,jdbcType=VARCHAR},
        #{planQuantity,jdbcType=VARCHAR},
        #{qty,jdbcType=VARCHAR},
        #{orgId,jdbcType=VARCHAR},
        #{sub,jdbcType=VARCHAR},
        #{location,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{storageTime,jdbcType=TIMESTAMP},
        #{sourceType,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.InventoryFloatGlassDetailPO">
        insert into sds_material_inventory_float_glass_detail(id,
                                                              product_id,
                                                              product_code,
                                                              product_spec,
                                                              stock_point_code,
                                                              level,
                                                              lot_level_code,
                                                              lot_number,
                                                              per_box,
                                                              weight_box,
                                                              area,
                                                              weight,
                                                              overdue_count,
                                                              cutting_rate,
                                                              plan_quantity,
                                                              qty,
                                                              org_id,
                                                              sub,
                                                              location,
                                                              enabled,
                                                              creator,
                                                              create_time,
                                                              modifier,
                                                              modify_time,
                                                              storage_time,
                                                              source_type)
        values (#{id,jdbcType=VARCHAR},
                #{productId,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{productSpec,jdbcType=VARCHAR},
                #{stockPointCode,jdbcType=VARCHAR},
                #{level,jdbcType=VARCHAR},
                #{lotLevelCode,jdbcType=VARCHAR},
                #{lotNumber,jdbcType=VARCHAR},
                #{perBox,jdbcType=VARCHAR},
                #{weightBox,jdbcType=VARCHAR},
                #{area,jdbcType=VARCHAR},
                #{weight,jdbcType=VARCHAR},
                #{overdueCount,jdbcType=VARCHAR},
                #{cuttingRate,jdbcType=VARCHAR},
                #{planQuantity,jdbcType=VARCHAR},
                #{qty,jdbcType=VARCHAR},
                #{orgId,jdbcType=VARCHAR},
                #{sub,jdbcType=VARCHAR},
                #{location,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{storageTime,jdbcType=TIMESTAMP},
                #{sourceType,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into sds_material_inventory_float_glass_detail(
        id,
        product_id,
        product_code,
        product_spec,
        stock_point_code,
        level,
        lot_level_code,
        lot_number,
        per_box,
        weight_box,
        area,
        weight,
        overdue_count,
        cutting_rate,
        plan_quantity,
        qty,
        org_id,
        sub,
        location,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        storage_time,
        source_type)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.productId,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.productSpec,jdbcType=VARCHAR},
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.level,jdbcType=VARCHAR},
            #{entity.lotLevelCode,jdbcType=VARCHAR},
            #{entity.lotNumber,jdbcType=VARCHAR},
            #{entity.perBox,jdbcType=VARCHAR},
            #{entity.weightBox,jdbcType=VARCHAR},
            #{entity.area,jdbcType=VARCHAR},
            #{entity.weight,jdbcType=VARCHAR},
            #{entity.overdueCount,jdbcType=VARCHAR},
            #{entity.cuttingRate,jdbcType=VARCHAR},
            #{entity.planQuantity,jdbcType=VARCHAR},
            #{entity.qty,jdbcType=VARCHAR},
            #{entity.orgId,jdbcType=VARCHAR},
            #{entity.sub,jdbcType=VARCHAR},
            #{entity.location,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.storageTime,jdbcType=TIMESTAMP},
            #{entity.sourceType,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into sds_material_inventory_float_glass_detail(
        id,
        product_id,
        product_code,
        product_spec,
        stock_point_code,
        level,
        lot_level_code,
        lot_number,
        per_box,
        weight_box,
        area,
        weight,
        overdue_count,
        cutting_rate,
        plan_quantity,
        qty,
        org_id,
        sub,
        location,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        storage_time,
        source_type)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.productId,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.productSpec,jdbcType=VARCHAR},
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.level,jdbcType=VARCHAR},
            #{entity.lotLevelCode,jdbcType=VARCHAR},
            #{entity.lotNumber,jdbcType=VARCHAR},
            #{entity.perBox,jdbcType=VARCHAR},
            #{entity.weightBox,jdbcType=VARCHAR},
            #{entity.area,jdbcType=VARCHAR},
            #{entity.weight,jdbcType=VARCHAR},
            #{entity.overdueCount,jdbcType=VARCHAR},
            #{entity.cuttingRate,jdbcType=VARCHAR},
            #{entity.planQuantity,jdbcType=VARCHAR},
            #{entity.qty,jdbcType=VARCHAR},
            #{entity.orgId,jdbcType=VARCHAR},
            #{entity.sub,jdbcType=VARCHAR},
            #{entity.location,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.storageTime,jdbcType=TIMESTAMP},
            #{entity.sourceType,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.InventoryFloatGlassDetailPO">
        update sds_material_inventory_float_glass_detail
        set product_id       = #{productId,jdbcType=VARCHAR},
            product_code     = #{productCode,jdbcType=VARCHAR},
            product_spec     = #{productSpec,jdbcType=VARCHAR},
            stock_point_code = #{stockPointCode,jdbcType=VARCHAR},
            level            = #{level,jdbcType=VARCHAR},
            lot_level_code   = #{lotLevelCode,jdbcType=VARCHAR},
            lot_number       = #{lotNumber,jdbcType=VARCHAR},
            per_box          = #{perBox,jdbcType=VARCHAR},
            weight_box       = #{weightBox,jdbcType=VARCHAR},
            area             = #{area,jdbcType=VARCHAR},
            weight           = #{weight,jdbcType=VARCHAR},
            overdue_count    = #{overdueCount,jdbcType=VARCHAR},
            cutting_rate     = #{cuttingRate,jdbcType=VARCHAR},
            plan_quantity    = #{planQuantity,jdbcType=VARCHAR},
            enabled          = #{enabled,jdbcType=VARCHAR},
            modifier         = #{modifier,jdbcType=VARCHAR},
            modify_time      = #{modifyTime,jdbcType=TIMESTAMP},
            storage_time     = #{storageTime,jdbcType=TIMESTAMP},
            source_type      = #{sourceType,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.InventoryFloatGlassDetailPO">
        update sds_material_inventory_float_glass_detail
        <set>
            <if test="item.productId != null and item.productId != ''">
                product_id = #{item.productId,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productSpec != null and item.productSpec != ''">
                product_spec = #{item.productSpec,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.level != null and item.level != ''">
                level = #{item.level,jdbcType=VARCHAR},
            </if>
            <if test="item.lotLevelCode != null and item.lotLevelCode != ''">
                lot_level_code = #{item.lotLevelCode,jdbcType=VARCHAR},
            </if>
            <if test="item.lotNumber != null and item.lotNumber != ''">
                lot_number = #{item.lotNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.perBox != null">
                per_box = #{item.perBox,jdbcType=VARCHAR},
            </if>
            <if test="item.weightBox != null">
                weight_box = #{item.weightBox,jdbcType=VARCHAR},
            </if>
            <if test="item.area != null">
                area = #{item.area,jdbcType=VARCHAR},
            </if>
            <if test="item.weight != null">
                weight = #{item.weight,jdbcType=VARCHAR},
            </if>
            <if test="item.overdueCount != null">
                overdue_count = #{item.overdueCount,jdbcType=VARCHAR},
            </if>
            <if test="item.cuttingRate != null">
                cutting_rate = #{item.cuttingRate,jdbcType=VARCHAR},
            </if>
            <if test="item.planQuantity != null">
                plan_quantity = #{item.planQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.qty != null">
                qty = #{item.qty,jdbcType=VARCHAR},
            </if>
            <if test="item.orgId != null and item.orgId != ''">
                org_id = #{item.orgId,jdbcType=VARCHAR},
            </if>
            <if test="item.sub != null and item.sub != ''">
                sub = #{item.sub,jdbcType=VARCHAR},
            </if>
            <if test="item.location != null and item.location != ''">
                location = #{item.location,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.storageTime != null">
                storage_time = #{item.storageTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.sourceType != null and item.sourceType != ''">
                source_type = #{item.sourceType,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update sds_material_inventory_float_glass_detail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_spec = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productSpec,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="level = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.level,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="lot_level_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lotLevelCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="lot_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lotNumber,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="per_box = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.perBox,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="weight_box = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.weightBox,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="area = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.area,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="weight = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.weight,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="overdue_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.overdueCount,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cutting_rate = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.cuttingRate,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="plan_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.planQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="qty = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.qty,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="org_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.orgId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sub = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sub,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="location = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.location,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="storage_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.storageTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="source_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sourceType,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update sds_material_inventory_float_glass_detail
            <set>
                <if test="item.productId != null and item.productId != ''">
                    product_id = #{item.productId,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productSpec != null and item.productSpec != ''">
                    product_spec = #{item.productSpec,jdbcType=VARCHAR},
                </if>
                <if test="item.stockPointCode != null and item.stockPointCode != ''">
                    stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
                </if>
                <if test="item.level != null and item.level != ''">
                    level = #{item.level,jdbcType=VARCHAR},
                </if>
                <if test="item.lotLevelCode != null and item.lotLevelCode != ''">
                    lot_level_code = #{item.lotLevelCode,jdbcType=VARCHAR},
                </if>
                <if test="item.lotNumber != null and item.lotNumber != ''">
                    lot_number = #{item.lotNumber,jdbcType=VARCHAR},
                </if>
                <if test="item.perBox != null">
                    per_box = #{item.perBox,jdbcType=VARCHAR},
                </if>
                <if test="item.weightBox != null">
                    weight_box = #{item.weightBox,jdbcType=VARCHAR},
                </if>
                <if test="item.area != null">
                    area = #{item.area,jdbcType=VARCHAR},
                </if>
                <if test="item.weight != null">
                    weight = #{item.weight,jdbcType=VARCHAR},
                </if>
                <if test="item.overdueCount != null">
                    overdue_count = #{item.overdueCount,jdbcType=VARCHAR},
                </if>
                <if test="item.cuttingRate != null">
                    cutting_rate = #{item.cuttingRate,jdbcType=VARCHAR},
                </if>
                <if test="item.planQuantity != null">
                    plan_quantity = #{item.planQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.qty != null">
                    qty = #{item.qty,jdbcType=VARCHAR},
                </if>
                <if test="item.orgId != null and item.orgId != ''">
                    org_id = #{item.orgId,jdbcType=VARCHAR},
                </if>
                <if test="item.sub != null and item.sub != ''">
                    sub = #{item.sub,jdbcType=VARCHAR},
                </if>
                <if test="item.location != null and item.location != ''">
                    location = #{item.location,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.storageTime != null">
                    storage_time = #{item.storageTime,jdbcType=TIMESTAMP}
                </if>
                <if test="item.sourceType != null and item.sourceType != ''">
                    source_type = #{item.sourceType,jdbcType=VARCHAR},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from sds_material_inventory_float_glass_detail
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from sds_material_inventory_float_glass_detail where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <delete id="doDeleteByStockPointCode" parameterType="java.lang.String">
        delete
        from sds_material_inventory_float_glass_detail
        where stock_point_code = #{stockPointCode,jdbcType=VARCHAR}
    </delete>
</mapper>
