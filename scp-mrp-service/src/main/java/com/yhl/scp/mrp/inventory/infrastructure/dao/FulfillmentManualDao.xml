<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.inventory.infrastructure.dao.FulfillmentManualDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.inventory.infrastructure.po.FulfillmentManualPO">
        <!--@Table sds_peg_fulfillment_manual-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="supply_id" jdbcType="VARCHAR" property="supplyId"/>
        <result column="demand_id" jdbcType="VARCHAR" property="demandId"/>
        <result column="master_plan_version_id" jdbcType="VARCHAR" property="masterPlanVersionId"/>
        <result column="fulfillment_quantity" jdbcType="VARCHAR" property="fulfillmentQuantity"/>
        <result column="supply_order_id" jdbcType="VARCHAR" property="supplyOrderId"/>
        <result column="demand_order_id" jdbcType="VARCHAR" property="demandOrderId"/>
        <result column="demand_type" jdbcType="VARCHAR" property="demandType"/>
        <result column="supply_type" jdbcType="VARCHAR" property="supplyType"/>
        <result column="demand_product_stock_point_id" jdbcType="VARCHAR" property="demandProductStockPointId"/>
        <result column="demand_product_id" jdbcType="VARCHAR" property="demandProductId"/>
        <result column="demand_stock_point_id" jdbcType="VARCHAR" property="demandStockPointId"/>
        <result column="supply_product_stock_point_id" jdbcType="VARCHAR" property="supplyProductStockPointId"/>
        <result column="supply_product_id" jdbcType="VARCHAR" property="supplyProductId"/>
        <result column="supply_stock_point_id" jdbcType="VARCHAR" property="supplyStockPointId"/>
        <result column="fixed" jdbcType="VARCHAR" property="fixed"/>
        <result column="alt_material_used" jdbcType="VARCHAR" property="altMaterialUsed"/>
        <result column="alt_rate" jdbcType="VARCHAR" property="altRate"/>
        <result column="unit_conversion_ratio" jdbcType="VARCHAR" property="unitConversionRatio"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mrp.inventory.vo.FulfillmentManualVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,supply_id,demand_id,master_plan_version_id,fulfillment_quantity,supply_order_id,demand_order_id,demand_type,supply_type,demand_product_stock_point_id,demand_product_id,demand_stock_point_id,supply_product_stock_point_id,supply_product_id,supply_stock_point_id,fixed,alt_material_used,alt_rate,unit_conversion_ratio,remark,enabled,creator,create_time,modifier,modify_time

    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.supplyId != null and params.supplyId != ''">
                and supply_id = #{params.supplyId,jdbcType=VARCHAR}
            </if>
            <if test="params.demandId != null and params.demandId != ''">
                and demand_id = #{params.demandId,jdbcType=VARCHAR}
            </if>
            <if test="params.masterPlanVersionId != null and params.masterPlanVersionId != ''">
                and master_plan_version_id = #{params.masterPlanVersionId,jdbcType=VARCHAR}
            </if>
            <if test="params.fulfillmentQuantity != null">
                and fulfillment_quantity = #{params.fulfillmentQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.supplyOrderId != null and params.supplyOrderId != ''">
                and supply_order_id = #{params.supplyOrderId,jdbcType=VARCHAR}
            </if>
            <if test="params.demandOrderId != null and params.demandOrderId != ''">
                and demand_order_id = #{params.demandOrderId,jdbcType=VARCHAR}
            </if>
            <if test="params.demandType != null and params.demandType != ''">
                and demand_type = #{params.demandType,jdbcType=VARCHAR}
            </if>
            <if test="params.supplyType != null and params.supplyType != ''">
                and supply_type = #{params.supplyType,jdbcType=VARCHAR}
            </if>
            <if test="params.demandProductStockPointId != null and params.demandProductStockPointId != ''">
                and demand_product_stock_point_id = #{params.demandProductStockPointId,jdbcType=VARCHAR}
            </if>
            <if test="params.demandProductId != null and params.demandProductId != ''">
                and demand_product_id = #{params.demandProductId,jdbcType=VARCHAR}
            </if>
            <if test="params.demandStockPointId != null and params.demandStockPointId != ''">
                and demand_stock_point_id = #{params.demandStockPointId,jdbcType=VARCHAR}
            </if>
            <if test="params.supplyProductStockPointId != null and params.supplyProductStockPointId != ''">
                and supply_product_stock_point_id = #{params.supplyProductStockPointId,jdbcType=VARCHAR}
            </if>
            <if test="params.supplyProductId != null and params.supplyProductId != ''">
                and supply_product_id = #{params.supplyProductId,jdbcType=VARCHAR}
            </if>
            <if test="params.supplyStockPointId != null and params.supplyStockPointId != ''">
                and supply_stock_point_id = #{params.supplyStockPointId,jdbcType=VARCHAR}
            </if>
            <if test="params.fixed != null and params.fixed != ''">
                and fixed = #{params.fixed,jdbcType=VARCHAR}
            </if>
            <if test="params.altMaterialUsed != null and params.altMaterialUsed != ''">
                and alt_material_used = #{params.altMaterialUsed,jdbcType=VARCHAR}
            </if>
            <if test="params.altRate != null">
                and alt_rate = #{params.altRate,jdbcType=VARCHAR}
            </if>
            <if test="params.unitConversionRatio != null and params.unitConversionRatio != ''">
                and unit_conversion_ratio = #{params.unitConversionRatio,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sds_peg_fulfillment_manual
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sds_peg_fulfillment_manual
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from sds_peg_fulfillment_manual
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sds_peg_fulfillment_manual
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.FulfillmentManualPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into sds_peg_fulfillment_manual(
        id,
        supply_id,
        demand_id,
        master_plan_version_id,
        fulfillment_quantity,
        supply_order_id,
        demand_order_id,
        demand_type,
        supply_type,
        demand_product_stock_point_id,
        demand_product_id,
        demand_stock_point_id,
        supply_product_stock_point_id,
        supply_product_id,
        supply_stock_point_id,
        fixed,
        alt_material_used,
        alt_rate,
        unit_conversion_ratio,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{supplyId,jdbcType=VARCHAR},
        #{demandId,jdbcType=VARCHAR},
        #{masterPlanVersionId,jdbcType=VARCHAR},
        #{fulfillmentQuantity,jdbcType=VARCHAR},
        #{supplyOrderId,jdbcType=VARCHAR},
        #{demandOrderId,jdbcType=VARCHAR},
        #{demandType,jdbcType=VARCHAR},
        #{supplyType,jdbcType=VARCHAR},
        #{demandProductStockPointId,jdbcType=VARCHAR},
        #{demandProductId,jdbcType=VARCHAR},
        #{demandStockPointId,jdbcType=VARCHAR},
        #{supplyProductStockPointId,jdbcType=VARCHAR},
        #{supplyProductId,jdbcType=VARCHAR},
        #{supplyStockPointId,jdbcType=VARCHAR},
        #{fixed,jdbcType=VARCHAR},
        #{altMaterialUsed,jdbcType=VARCHAR},
        #{altRate,jdbcType=VARCHAR},
        #{unitConversionRatio,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.FulfillmentManualPO">
        insert into sds_peg_fulfillment_manual(id,
                                               supply_id,
                                               demand_id,
                                               master_plan_version_id,
                                               fulfillment_quantity,
                                               supply_order_id,
                                               demand_order_id,
                                               demand_type,
                                               supply_type,
                                               demand_product_stock_point_id,
                                               demand_product_id,
                                               demand_stock_point_id,
                                               supply_product_stock_point_id,
                                               supply_product_id,
                                               supply_stock_point_id,
                                               fixed,
                                               alt_material_used,
                                               alt_rate,
                                               unit_conversion_ratio,
                                               remark,
                                               enabled,
                                               creator,
                                               create_time,
                                               modifier,
                                               modify_time)
        values (#{id,jdbcType=VARCHAR},
                #{supplyId,jdbcType=VARCHAR},
                #{demandId,jdbcType=VARCHAR},
                #{masterPlanVersionId,jdbcType=VARCHAR},
                #{fulfillmentQuantity,jdbcType=VARCHAR},
                #{supplyOrderId,jdbcType=VARCHAR},
                #{demandOrderId,jdbcType=VARCHAR},
                #{demandType,jdbcType=VARCHAR},
                #{supplyType,jdbcType=VARCHAR},
                #{demandProductStockPointId,jdbcType=VARCHAR},
                #{demandProductId,jdbcType=VARCHAR},
                #{demandStockPointId,jdbcType=VARCHAR},
                #{supplyProductStockPointId,jdbcType=VARCHAR},
                #{supplyProductId,jdbcType=VARCHAR},
                #{supplyStockPointId,jdbcType=VARCHAR},
                #{fixed,jdbcType=VARCHAR},
                #{altMaterialUsed,jdbcType=VARCHAR},
                #{altRate,jdbcType=VARCHAR},
                #{unitConversionRatio,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into sds_peg_fulfillment_manual(
        id,
        supply_id,
        demand_id,
        master_plan_version_id,
        fulfillment_quantity,
        supply_order_id,
        demand_order_id,
        demand_type,
        supply_type,
        demand_product_stock_point_id,
        demand_product_id,
        demand_stock_point_id,
        supply_product_stock_point_id,
        supply_product_id,
        supply_stock_point_id,
        fixed,
        alt_material_used,
        alt_rate,
        unit_conversion_ratio,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.supplyId,jdbcType=VARCHAR},
            #{entity.demandId,jdbcType=VARCHAR},
            #{entity.masterPlanVersionId,jdbcType=VARCHAR},
            #{entity.fulfillmentQuantity,jdbcType=VARCHAR},
            #{entity.supplyOrderId,jdbcType=VARCHAR},
            #{entity.demandOrderId,jdbcType=VARCHAR},
            #{entity.demandType,jdbcType=VARCHAR},
            #{entity.supplyType,jdbcType=VARCHAR},
            #{entity.demandProductStockPointId,jdbcType=VARCHAR},
            #{entity.demandProductId,jdbcType=VARCHAR},
            #{entity.demandStockPointId,jdbcType=VARCHAR},
            #{entity.supplyProductStockPointId,jdbcType=VARCHAR},
            #{entity.supplyProductId,jdbcType=VARCHAR},
            #{entity.supplyStockPointId,jdbcType=VARCHAR},
            #{entity.fixed,jdbcType=VARCHAR},
            #{entity.altMaterialUsed,jdbcType=VARCHAR},
            #{entity.altRate,jdbcType=VARCHAR},
            #{entity.unitConversionRatio,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into sds_peg_fulfillment_manual(
        id,
        supply_id,
        demand_id,
        master_plan_version_id,
        fulfillment_quantity,
        supply_order_id,
        demand_order_id,
        demand_type,
        supply_type,
        demand_product_stock_point_id,
        demand_product_id,
        demand_stock_point_id,
        supply_product_stock_point_id,
        supply_product_id,
        supply_stock_point_id,
        fixed,
        alt_material_used,
        alt_rate,
        unit_conversion_ratio,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.supplyId,jdbcType=VARCHAR},
            #{entity.demandId,jdbcType=VARCHAR},
            #{entity.masterPlanVersionId,jdbcType=VARCHAR},
            #{entity.fulfillmentQuantity,jdbcType=VARCHAR},
            #{entity.supplyOrderId,jdbcType=VARCHAR},
            #{entity.demandOrderId,jdbcType=VARCHAR},
            #{entity.demandType,jdbcType=VARCHAR},
            #{entity.supplyType,jdbcType=VARCHAR},
            #{entity.demandProductStockPointId,jdbcType=VARCHAR},
            #{entity.demandProductId,jdbcType=VARCHAR},
            #{entity.demandStockPointId,jdbcType=VARCHAR},
            #{entity.supplyProductStockPointId,jdbcType=VARCHAR},
            #{entity.supplyProductId,jdbcType=VARCHAR},
            #{entity.supplyStockPointId,jdbcType=VARCHAR},
            #{entity.fixed,jdbcType=VARCHAR},
            #{entity.altMaterialUsed,jdbcType=VARCHAR},
            #{entity.altRate,jdbcType=VARCHAR},
            #{entity.unitConversionRatio,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.FulfillmentManualPO">
        update sds_peg_fulfillment_manual
        set supply_id                     = #{supplyId,jdbcType=VARCHAR},
            demand_id                     = #{demandId,jdbcType=VARCHAR},
            master_plan_version_id        = #{masterPlanVersionId,jdbcType=VARCHAR},
            fulfillment_quantity          = #{fulfillmentQuantity,jdbcType=VARCHAR},
            supply_order_id               = #{supplyOrderId,jdbcType=VARCHAR},
            demand_order_id               = #{demandOrderId,jdbcType=VARCHAR},
            demand_type                   = #{demandType,jdbcType=VARCHAR},
            supply_type                   = #{supplyType,jdbcType=VARCHAR},
            demand_product_stock_point_id = #{demandProductStockPointId,jdbcType=VARCHAR},
            demand_product_id             = #{demandProductId,jdbcType=VARCHAR},
            demand_stock_point_id         = #{demandStockPointId,jdbcType=VARCHAR},
            supply_product_stock_point_id = #{supplyProductStockPointId,jdbcType=VARCHAR},
            supply_product_id             = #{supplyProductId,jdbcType=VARCHAR},
            supply_stock_point_id         = #{supplyStockPointId,jdbcType=VARCHAR},
            fixed                         = #{fixed,jdbcType=VARCHAR},
            alt_material_used             = #{altMaterialUsed,jdbcType=VARCHAR},
            alt_rate                      = #{altRate,jdbcType=VARCHAR},
            unit_conversion_ratio         = #{unitConversionRatio,jdbcType=VARCHAR},
            remark                        = #{remark,jdbcType=VARCHAR},
            enabled                       = #{enabled,jdbcType=VARCHAR},
            modifier                      = #{modifier,jdbcType=VARCHAR},
            modify_time                   = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.FulfillmentManualPO">
        update sds_peg_fulfillment_manual
        <set>
            <if test="item.supplyId != null and item.supplyId != ''">
                supply_id = #{item.supplyId,jdbcType=VARCHAR},
            </if>
            <if test="item.demandId != null and item.demandId != ''">
                demand_id = #{item.demandId,jdbcType=VARCHAR},
            </if>
            <if test="item.masterPlanVersionId != null and item.masterPlanVersionId != ''">
                master_plan_version_id = #{item.masterPlanVersionId,jdbcType=VARCHAR},
            </if>
            <if test="item.fulfillmentQuantity != null">
                fulfillment_quantity = #{item.fulfillmentQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.supplyOrderId != null and item.supplyOrderId != ''">
                supply_order_id = #{item.supplyOrderId,jdbcType=VARCHAR},
            </if>
            <if test="item.demandOrderId != null and item.demandOrderId != ''">
                demand_order_id = #{item.demandOrderId,jdbcType=VARCHAR},
            </if>
            <if test="item.demandType != null and item.demandType != ''">
                demand_type = #{item.demandType,jdbcType=VARCHAR},
            </if>
            <if test="item.supplyType != null and item.supplyType != ''">
                supply_type = #{item.supplyType,jdbcType=VARCHAR},
            </if>
            <if test="item.demandProductStockPointId != null and item.demandProductStockPointId != ''">
                demand_product_stock_point_id = #{item.demandProductStockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.demandProductId != null and item.demandProductId != ''">
                demand_product_id = #{item.demandProductId,jdbcType=VARCHAR},
            </if>
            <if test="item.demandStockPointId != null and item.demandStockPointId != ''">
                demand_stock_point_id = #{item.demandStockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.supplyProductStockPointId != null and item.supplyProductStockPointId != ''">
                supply_product_stock_point_id = #{item.supplyProductStockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.supplyProductId != null and item.supplyProductId != ''">
                supply_product_id = #{item.supplyProductId,jdbcType=VARCHAR},
            </if>
            <if test="item.supplyStockPointId != null and item.supplyStockPointId != ''">
                supply_stock_point_id = #{item.supplyStockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.fixed != null and item.fixed != ''">
                fixed = #{item.fixed,jdbcType=VARCHAR},
            </if>
            <if test="item.altMaterialUsed != null and item.altMaterialUsed != ''">
                alt_material_used = #{item.altMaterialUsed,jdbcType=VARCHAR},
            </if>
            <if test="item.altRate != null">
                alt_rate = #{item.altRate,jdbcType=VARCHAR},
            </if>
            <if test="item.unitConversionRatio != null and item.unitConversionRatio != ''">
                unit_conversion_ratio = #{item.unitConversionRatio,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update sds_peg_fulfillment_manual
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="supply_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplyId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="master_plan_version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.masterPlanVersionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="fulfillment_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.fulfillmentQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supply_order_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplyOrderId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_order_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandOrderId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supply_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplyType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_product_stock_point_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandProductStockPointId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandProductId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_stock_point_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandStockPointId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supply_product_stock_point_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplyProductStockPointId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supply_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplyProductId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supply_stock_point_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplyStockPointId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="fixed = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.fixed,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="alt_material_used = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.altMaterialUsed,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="alt_rate = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.altRate,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="unit_conversion_ratio = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.unitConversionRatio,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update sds_peg_fulfillment_manual
            <set>
                <if test="item.supplyId != null and item.supplyId != ''">
                    supply_id = #{item.supplyId,jdbcType=VARCHAR},
                </if>
                <if test="item.demandId != null and item.demandId != ''">
                    demand_id = #{item.demandId,jdbcType=VARCHAR},
                </if>
                <if test="item.masterPlanVersionId != null and item.masterPlanVersionId != ''">
                    master_plan_version_id = #{item.masterPlanVersionId,jdbcType=VARCHAR},
                </if>
                <if test="item.fulfillmentQuantity != null">
                    fulfillment_quantity = #{item.fulfillmentQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.supplyOrderId != null and item.supplyOrderId != ''">
                    supply_order_id = #{item.supplyOrderId,jdbcType=VARCHAR},
                </if>
                <if test="item.demandOrderId != null and item.demandOrderId != ''">
                    demand_order_id = #{item.demandOrderId,jdbcType=VARCHAR},
                </if>
                <if test="item.demandType != null and item.demandType != ''">
                    demand_type = #{item.demandType,jdbcType=VARCHAR},
                </if>
                <if test="item.supplyType != null and item.supplyType != ''">
                    supply_type = #{item.supplyType,jdbcType=VARCHAR},
                </if>
                <if test="item.demandProductStockPointId != null and item.demandProductStockPointId != ''">
                    demand_product_stock_point_id = #{item.demandProductStockPointId,jdbcType=VARCHAR},
                </if>
                <if test="item.demandProductId != null and item.demandProductId != ''">
                    demand_product_id = #{item.demandProductId,jdbcType=VARCHAR},
                </if>
                <if test="item.demandStockPointId != null and item.demandStockPointId != ''">
                    demand_stock_point_id = #{item.demandStockPointId,jdbcType=VARCHAR},
                </if>
                <if test="item.supplyProductStockPointId != null and item.supplyProductStockPointId != ''">
                    supply_product_stock_point_id = #{item.supplyProductStockPointId,jdbcType=VARCHAR},
                </if>
                <if test="item.supplyProductId != null and item.supplyProductId != ''">
                    supply_product_id = #{item.supplyProductId,jdbcType=VARCHAR},
                </if>
                <if test="item.supplyStockPointId != null and item.supplyStockPointId != ''">
                    supply_stock_point_id = #{item.supplyStockPointId,jdbcType=VARCHAR},
                </if>
                <if test="item.fixed != null and item.fixed != ''">
                    fixed = #{item.fixed,jdbcType=VARCHAR},
                </if>
                <if test="item.altMaterialUsed != null and item.altMaterialUsed != ''">
                    alt_material_used = #{item.altMaterialUsed,jdbcType=VARCHAR},
                </if>
                <if test="item.altRate != null">
                    alt_rate = #{item.altRate,jdbcType=VARCHAR},
                </if>
                <if test="item.unitConversionRatio != null and item.unitConversionRatio != ''">
                    unit_conversion_ratio = #{item.unitConversionRatio,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from sds_peg_fulfillment_manual
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from sds_peg_fulfillment_manual where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
