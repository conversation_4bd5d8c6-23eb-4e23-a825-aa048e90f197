package com.yhl.scp.mrp.material.plan.controller;

import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RedisKeyManageEnum;
import com.yhl.scp.mrp.material.MrpResultVO;
import com.yhl.scp.mrp.material.arrival.dto.MaterialArrivalTrackingDTO;
import com.yhl.scp.mrp.material.plan.dto.GlassInventoryShiftDetailDTO;
import com.yhl.scp.mrp.material.plan.dto.MrpParamDTO;
import com.yhl.scp.mrp.material.plan.service.MrpService;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @ClassName MrpController
 * @Description TODO
 * @Date 2024-10-25 09:27:41
 * <AUTHOR>
 * @Copyright 瑞之泽
 * @Version 1.0
 */
@Slf4j
@Api(tags = "材料计划MRP计算")
@RestController
@RequestMapping("materialPlan")
public class MaterialPlanController extends BaseController {

    @Resource
    private Map<String, MrpService> mrpService;

    @Resource
    private MaterialGrossDemandService materialGrossDemandService;

    @Resource
    private RedisUtil redisUtil;

    @ApiOperation(value = "mrp计算")
    @GetMapping(value = "mrpCalc/{calcType}")
    @BusinessMonitorLog(businessCode = "材料计划计算", moduleCode = "MRP", businessFrequency = "DAY")
    public BaseResponse<String> mrpCalc(@PathVariable("calcType") String calcType,
                                        @RequestParam(value = "mpsDemandRule", required = false,
                                                defaultValue = "MPS_DEMAND") String mpsDemandRule,
                                        @RequestParam(value = "mrpCalDate", required = false) String mrpCalDate) {

        MrpService mrpServiceImpl = mrpService.get(calcType + "MrpServiceImpl");
        // mrp计算时间
        Date nowDate;
        if (StringUtils.isNotBlank(mrpCalDate)) {
            nowDate = DateUtils.getDayFirstTime(DateUtils.stringToDate(mrpCalDate));
        } else {
            nowDate = DateUtils.getDayFirstTime(new Date());
        }

        // mrp计算
        return mrpServiceImpl.doRunMrp(new MrpParamDTO(calcType, SystemHolder.getScenario(), mpsDemandRule,
                nowDate, null, SystemHolder.getUserId(), Boolean.FALSE, null));
    }

    @ApiOperation(value = "本厂调整")
    @PostMapping(value = "bcAdjust")
    public BaseResponse<String> bcAdjust(@RequestBody GlassInventoryShiftDetailDTO glassInventoryShiftDetailDTO) {
        MrpParamDTO mrpParam = new MrpParamDTO();
        mrpParam.setGlassInventoryShiftDetailDTO(glassInventoryShiftDetailDTO);
        MrpService mrpServiceImpl = mrpService.get("handworkBcMrpServiceImpl");
        return mrpServiceImpl.doRunMrp(mrpParam);
    }


    @ApiOperation(value = "码头调整")
    @PostMapping(value = "mtAdjust")
    public BaseResponse<String> mtAdjust(@RequestBody GlassInventoryShiftDetailDTO glassInventoryShiftDetailDTO) {
        MrpParamDTO mrpParam = new MrpParamDTO();
        mrpParam.setGlassInventoryShiftDetailDTO(glassInventoryShiftDetailDTO);
        MrpService mrpServiceImpl = mrpService.get("handworkMtMrpServiceImpl");
        return mrpServiceImpl.doRunMrp(mrpParam);
    }

    @ApiOperation(value = "发布")
    @GetMapping(value = "publish/{mrpType}")
    public BaseResponse<Void> publish(@PathVariable(name = "mrpType") String mrpType) {
        return BaseResponse.success();
    }

    @ApiOperation(value = "获取mrp运行状态")
    @GetMapping(value = "getMrpStatus")
    public BaseResponse<Object> getMrpStatus(@RequestParam(value = "calcType") String calcType) {
        String redisResultKey = String.join("#", RedisKeyManageEnum.MRP_COMPUTE_RESULT.getKey(), calcType);
        MrpResultVO mrpResultVO = new MrpResultVO();
        if (redisUtil.hasKey(redisResultKey)) {
            String syncStatusMsg = redisUtil.get(redisResultKey).toString();
            mrpResultVO = JSONObject.parseObject(syncStatusMsg, MrpResultVO.class);
            if ("FAIL".equals(mrpResultVO.getStatus())) {
                redisUtil.delete(redisResultKey);
                return BaseResponse.error(mrpResultVO.getMsg());
            } else if ("WAIT".equals(mrpResultVO.getStatus())) {
                return BaseResponse.success(null, null, "WAIT");
            }
            redisUtil.delete(redisResultKey);
        }
        // 校验锁是否被占用
        return BaseResponse.success(mrpResultVO.getData(), null, "SUCCESS");
    }

    @ApiOperation(value = "产能平衡发布时间校验")
    @GetMapping(value = "checkCapacityBalancePublishDate")
    public BaseResponse<Void> checkCapacityBalancePublishDate() {
        return materialGrossDemandService.checkCapacityBalancePublishDate(SystemHolder.getScenario());
    }

    @ApiOperation(value = "到货跟踪日期推荐")
    @GetMapping(value = "arrivalTrackingDateRecommend")
    public BaseResponse<List<MaterialArrivalTrackingDTO>> arrivalTrackingDateRecommend(@RequestParam(value = "mrpCalDate", required = false) String mrpCalDate) {
        MrpService mrpServiceImpl = mrpService.get("noGlassMrpServiceImpl");
        // mrp计算时间
        Date nowDate;
        if (StringUtils.isNotBlank(mrpCalDate)) {
            nowDate = DateUtils.getDayFirstTime(DateUtils.stringToDate(mrpCalDate));
        } else {
            nowDate = DateUtils.getDayFirstTime(new Date());
        }
        // mrp计算
        return mrpServiceImpl.doArrivalTrackingDateRecommend(new MrpParamDTO("noGlass", SystemHolder.getScenario(),
                "DELIVERY_PLAN_DEMAND", nowDate, null, SystemHolder.getUserId(), Boolean.FALSE, null));
    }

}
