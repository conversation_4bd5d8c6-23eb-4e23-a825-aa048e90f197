package com.yhl.scp.mrp.materialDemand.service.impl;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.map.MapUtil;
import com.alibaba.excel.EasyExcel;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastVersionVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedVO;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepInputVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.substitution.vo.ProductSubstitutionRelationshipVO;
import com.yhl.scp.mps.capacityBalance.vo.CapacityBalanceVersionVO;
import com.yhl.scp.mps.capacityBalance.vo.CapacitySupplyRelationshipVO;
import com.yhl.scp.mps.demand.vo.OutsourceTransferDemandDetailVO;
import com.yhl.scp.mps.enums.SupplyModelEnum;
import com.yhl.scp.mps.feign.MpsFeign;
import com.yhl.scp.mrp.excel.handler.CustomColumnWidthHandler;
import com.yhl.scp.mrp.excel.listener.ExcelDynamicDataListener;
import com.yhl.scp.mrp.halfsubinventory.service.WarehouseHalfSubinventoryService;
import com.yhl.scp.mrp.halfsubinventory.vo.WarehouseHalfSubinventoryVO;
import com.yhl.scp.mrp.material.plan.dto.TransferWarehouseDemandDTO;
import com.yhl.scp.mrp.material.plan.enums.MrpDemandSourceEnum;
import com.yhl.scp.mrp.materialDemand.convertor.BigDecimalConverter;
import com.yhl.scp.mrp.materialDemand.convertor.MaterialGrossDemandConvertor;
import com.yhl.scp.mrp.materialDemand.domain.entity.MaterialGrossDemandDO;
import com.yhl.scp.mrp.materialDemand.domain.service.MaterialGrossDemandDomainService;
import com.yhl.scp.mrp.materialDemand.dto.*;
import com.yhl.scp.mrp.materialDemand.handler.MaterialGrossDemandComponentHandler;
import com.yhl.scp.mrp.materialDemand.infrastructure.dao.MaterialGrossDemandDao;
import com.yhl.scp.mrp.materialDemand.infrastructure.dao.MaterialGrossDemandVersionDao;
import com.yhl.scp.mrp.materialDemand.infrastructure.po.MaterialGrossDemandBasicPO;
import com.yhl.scp.mrp.materialDemand.infrastructure.po.MaterialGrossDemandPO;
import com.yhl.scp.mrp.materialDemand.infrastructure.po.MaterialGrossDemandVersionPO;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandHistoryService;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandService;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandVersionDetailService;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandVersionService;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandHistoryVO;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandSummaryVO;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandVO;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandVersionVO;
import com.yhl.scp.mrp.substitutionRelationship.service.GlassSubstitutionRelationshipService;
import com.yhl.scp.mrp.substitutionRelationship.vo.GlassSubstitutionRelationshipVO;
import com.yhl.scp.mrp.supplier.infrastructure.dao.MaterialSupplierPurchaseDao;
import com.yhl.scp.mrp.supplier.infrastructure.po.MaterialSupplierPurchasePO;
import com.yhl.scp.mrp.utils.EasyExcelUtil;
import com.yhl.scp.sds.extension.order.vo.OperationInputVO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import com.yhl.scp.sds.extension.pegging.vo.DemandVO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>MaterialGrossDemandServiceImpl</code>
 * <p>
 * 材料计划毛需求应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-02 09:51:48
 */
@Slf4j
@Service
public class MaterialGrossDemandServiceImpl extends AbstractService implements MaterialGrossDemandService {

    @Resource
    private MaterialGrossDemandDao materialGrossDemandDao;

    @Resource
    private MaterialGrossDemandDomainService materialGrossDemandDomainService;

    @Resource
    private WarehouseHalfSubinventoryService warehouseHalfSubinventoryService;

    @Resource
    @Lazy
    private MaterialGrossDemandComponentHandler materialGrossDemandComponentHandler;

    @Resource
    private MaterialGrossDemandVersionService materialGrossDemandVersionService;

    @Resource
    private MaterialGrossDemandVersionDao materialGrossDemandVersionDao;

    @Resource
    private MaterialSupplierPurchaseDao materialSupplierPurchaseDao;

    @Resource
    private GlassSubstitutionRelationshipService glassSubstitutionRelationshipService;

    @Resource
    private MaterialGrossDemandHistoryService materialGrossDemandDetailService;

    @Resource
    private MaterialGrossDemandVersionDetailService materialGrossDemandVersionDetailService;

    @Resource
    private NewMdsFeign mdsFeign;

    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private MpsFeign mpsFeign;

    @Resource
    private SpringBeanUtils springBeanUtils;

    protected static final List<String> noGlassMaterialTypeList = Lists.newArrayList("RA.V", "BB", "BG", "BJ");

    protected static final List<String> demandProductCategory = Lists.newArrayList("RA.V", "RA.A", "B");

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(MaterialGrossDemandDTO materialGrossDemandDTO) {
        // 0.数据转换
        MaterialGrossDemandDO materialGrossDemandDO = MaterialGrossDemandConvertor.INSTANCE.dto2Do(materialGrossDemandDTO);
        MaterialGrossDemandPO materialGrossDemandPO = MaterialGrossDemandConvertor.INSTANCE.dto2Po(materialGrossDemandDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialGrossDemandDomainService.validation(materialGrossDemandDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(materialGrossDemandPO);
        materialGrossDemandDao.insertWithPrimaryKey(materialGrossDemandPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(MaterialGrossDemandDTO materialGrossDemandDTO) {
        // 0.数据转换
        MaterialGrossDemandDO materialGrossDemandDO = MaterialGrossDemandConvertor.INSTANCE.dto2Do(materialGrossDemandDTO);
        MaterialGrossDemandPO materialGrossDemandPO = MaterialGrossDemandConvertor.INSTANCE.dto2Po(materialGrossDemandDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialGrossDemandDomainService.validation(materialGrossDemandDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(materialGrossDemandPO);
        materialGrossDemandDao.update(materialGrossDemandPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MaterialGrossDemandDTO> list) {
        List<MaterialGrossDemandPO> newList = MaterialGrossDemandConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        materialGrossDemandDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<MaterialGrossDemandDTO> list) {
        List<MaterialGrossDemandPO> newList = MaterialGrossDemandConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialGrossDemandDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return materialGrossDemandDao.deleteBatch(idList);
        }
        return materialGrossDemandDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MaterialGrossDemandVO selectByPrimaryKey(String id) {
        MaterialGrossDemandPO po = materialGrossDemandDao.selectByPrimaryKey(id);
        return MaterialGrossDemandConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "MATERIAL_GROSS_DEMAND")
    public List<MaterialGrossDemandVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    public PageInfo<MaterialGrossDemandVO> selectByPage2(MaterialGrossDemandParam materialGrossDemandParam) {
        // 查询分页数据,根据productFactoryCode、productCode分组
        if (StringUtils.isBlank(materialGrossDemandParam.getMaterialGrossDemandVersionId())) {
            // 查询最新版本
            MaterialGrossDemandVersionVO materialGrossDemandVersionVO = materialGrossDemandVersionService.selectLastVersion();
            if (null == materialGrossDemandVersionVO) {
                return null;
            }
            materialGrossDemandParam.setMaterialGrossDemandVersionId(materialGrossDemandVersionVO.getId());
        }

        Map<String, Object> params = new HashMap<>();
        params.put("productCode", materialGrossDemandParam.getProductCode());
        params.put("productFactoryCode", materialGrossDemandParam.getProductFactoryCode());
        params.put("vehicleModeCode", materialGrossDemandParam.getVehicleModeCode());
        params.put("productClassify", materialGrossDemandParam.getProductClassify());
        params.put("materialGrossDemandVersionId", materialGrossDemandParam.getMaterialGrossDemandVersionId());
        List<String> productCategoryList = new ArrayList<>();
        if (StringUtils.equals(YesOrNoEnum.YES.getCode(), materialGrossDemandParam.getWhetherGlass())) {
            productCategoryList = Lists.newArrayList("RA.A");
        }
        if (StringUtils.equals(YesOrNoEnum.NO.getCode(), materialGrossDemandParam.getWhetherGlass())) {
            productCategoryList = Lists.newArrayList("PVB", "B");
        }
        params.put("productCategoryList", productCategoryList);

        // 分组查询材料的信息
        PageHelper.startPage(materialGrossDemandParam.getPageNum(), materialGrossDemandParam.getPageSize());
        List<MaterialGrossDemandVO> materialGrossDemandVOList = materialGrossDemandDao.selectGroupByParams(params);
        PageInfo<MaterialGrossDemandVO> pageInfo = new PageInfo<>(materialGrossDemandVOList);

        // 查询详细数据
        List<String> productCodeList = materialGrossDemandVOList.stream().map(MaterialGrossDemandVO::getProductCode).distinct().collect(Collectors.toList());
        List<MaterialGrossDemandVO> list = materialGrossDemandDao.selectVOByParams(ImmutableMap.of("productCodeList", productCodeList, "materialGrossDemandVersionId", materialGrossDemandParam.getMaterialGrossDemandVersionId()));
        // 按照本厂编码+物料编码分组
        Map<String, List<MaterialGrossDemandVO>> grossDemandDetailGroup = list.stream().collect(Collectors.groupingBy(item -> String.join("#", item.getProductFactoryCode(), item.getProductCode())));

        List<String> dateStrList = list.stream().map(item -> DateUtils.dateToString(item.getDemandTime(), DateUtils.COMMON_DATE_STR3)).distinct().sorted() // 添加排序步骤
                .collect(Collectors.toList());

        for (MaterialGrossDemandVO materialGrossDemandVO : materialGrossDemandVOList) {
            List<MaterialGrossDemandVO> detailList = grossDemandDetailGroup.get(String.join("#", materialGrossDemandVO.getProductFactoryCode(), materialGrossDemandVO.getProductCode()));
            if (CollectionUtils.isEmpty(detailList)) {
                continue;
            }
            // 获取详细数据根据日期来汇总
            Map<String, List<MaterialGrossDemandVO>> detailsGroup = detailList.stream().collect(Collectors.groupingBy(item -> DateUtils.dateToString(item.getDemandTime(), DateUtils.COMMON_DATE_STR3)));

            List<MaterialGrossDemandSummaryVO> detailVOList = new ArrayList<>();
            for (Map.Entry<String, List<MaterialGrossDemandVO>> entry : detailsGroup.entrySet()) {
                List<MaterialGrossDemandVO> materialGrossDemandVOS = entry.getValue();
                // 组装每天汇总数据
                MaterialGrossDemandSummaryVO materialGrossDemandSummaryVO = new MaterialGrossDemandSummaryVO();
                BigDecimal dayDemandQuantitySum = materialGrossDemandVOS.stream().map(MaterialGrossDemandVO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                materialGrossDemandSummaryVO.setDemandQuantity(dayDemandQuantitySum);
                materialGrossDemandSummaryVO.setDemandTime(entry.getKey());

                List<MaterialGrossDemandHistoryVO> childrenList = new ArrayList<>();
                for (MaterialGrossDemandVO grossDemandVO : materialGrossDemandVOS) {
                    MaterialGrossDemandHistoryVO materialGrossDemandDetailVO = new MaterialGrossDemandHistoryVO();
                    materialGrossDemandDetailVO.setId(grossDemandVO.getId());
                    materialGrossDemandDetailVO.setDemandTime(grossDemandVO.getDemandTime());
                    materialGrossDemandDetailVO.setDemandQuantity(grossDemandVO.getDemandQuantity());
                    materialGrossDemandDetailVO.setDemandSource(grossDemandVO.getDemandSource());
                    childrenList.add(materialGrossDemandDetailVO);
                }
                materialGrossDemandSummaryVO.setChildrenList(childrenList);
                detailVOList.add(materialGrossDemandSummaryVO);
            }
            materialGrossDemandVO.setProductId(detailList.get(0).getProductId());
            materialGrossDemandVO.setProductName(detailList.get(0).getProductName());
            materialGrossDemandVO.setProductCategory(detailList.get(0).getProductCategory());
            materialGrossDemandVO.setProductClassify(detailList.get(0).getProductClassify());
            materialGrossDemandVO.setProductFactoryCode(detailList.get(0).getProductFactoryCode());
            materialGrossDemandVO.setVehicleModeCode(detailList.get(0).getVehicleModeCode());
            materialGrossDemandVO.setDataList(dateStrList);
            materialGrossDemandVO.setDetailList(detailVOList);
        }

        PageInfo<MaterialGrossDemandVO> pageInfoResult = new PageInfo<>(materialGrossDemandVOList);
        pageInfoResult.setTotal(pageInfo.getTotal());
        pageInfoResult.setPages(pageInfo.getPages());
        return pageInfoResult;
    }

    @Override
    @Expression(value = "MATERIAL_GROSS_DEMAND")
    public List<MaterialGrossDemandVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MaterialGrossDemandVO> dataList = materialGrossDemandDao.selectByCondition(sortParam, queryCriteriaParam);
        MaterialGrossDemandServiceImpl target = SpringBeanUtils.getBean(MaterialGrossDemandServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MaterialGrossDemandVO> selectByParams(Map<String, Object> params) {
        List<MaterialGrossDemandPO> list = materialGrossDemandDao.selectByParams(params);
        return MaterialGrossDemandConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MaterialGrossDemandVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<MaterialGrossDemandVO> invocation(List<MaterialGrossDemandVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public void doComputeDemand(String scenario, String mpsDemandRule) {
        // 需求计算
        StopWatch stopWatch = new StopWatch("组装需求数据明细");
        GrossDemandContextDTO grossDemandContextDTO = new GrossDemandContextDTO();
        Date mrpCalcDate = DateUtils.getDayFirstTime(new Date());
        grossDemandContextDTO.setScenario(scenario);
        grossDemandContextDTO.setMrpCalcDate(mrpCalcDate);

        stopWatch.start("初始化物料数据");
        FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder().dynamicColumnParam(Lists.newArrayList("id", "product_code", "product_name", "product_type", "product_classify", "vehicle_model_code")).queryParam(new HashMap<>()).build();
        // 查询物料主数据
        List<NewProductStockPointVO> newProductStockPointVOS = mdsFeign.selectProductListByParamOnDynamicColumns(scenario, feignDynamicParam);
        Map<String, NewProductStockPointVO> productStockPointVOMapOfId = newProductStockPointVOS.stream().collect(Collectors.toMap(NewProductStockPointVO::getId, Function.identity()));
        Map<String, NewProductStockPointVO> productStockPointVOMapOfProductCode = newProductStockPointVOS.stream().collect(Collectors.toMap(NewProductStockPointVO::getProductCode, Function.identity(), (k1, k2) -> k2));
        grossDemandContextDTO.setProductStockPointVOMapOfId(productStockPointVOMapOfId);
        grossDemandContextDTO.setProductStockPointVOMapOfProductCode(productStockPointVOMapOfProductCode);
        stopWatch.stop();

        stopWatch.start("初始化工艺路径数据");
        // 查询工艺路径数据
        FeignDynamicParam routingParam = FeignDynamicParam.builder()
                .dynamicColumnParam(Lists.newArrayList("id", "product_id", "stock_point_id", "product_code", "stock_point_code"))
                .queryParam(ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode())).build();

        List<RoutingVO> routingVOList = mdsFeign.selectRoutingByParamOnDynamicColumns(scenario, routingParam);
        Map<String, RoutingVO> routingVOMapOfProductCode = routingVOList.stream()
                .collect(Collectors.toMap(RoutingVO::getProductCode, Function.identity(), (k1, k2) -> k2));
        grossDemandContextDTO.setRoutingVOMapOfProductCode(routingVOMapOfProductCode);
        stopWatch.stop();

        stopWatch.start("初始化路径步骤数据");
        // 查询工艺路径步骤数据
        FeignDynamicParam routingStepParam = FeignDynamicParam.builder()
                .dynamicColumnParam(Lists.newArrayList("id", "routing_id", "sequence_no", "next_routing_step_sequence_no", "yield"))
                .queryParam(ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode())).build();
        List<RoutingStepVO> routingStepVOList = mdsFeign.selectRoutingStepByParamOnDynamicColumns(scenario, routingStepParam);
        Map<String, RoutingStepVO> routingStepMapOfId = routingStepVOList.stream().collect(Collectors.toMap(RoutingStepVO::getId, Function.identity()));
        Map<String, List<RoutingStepVO>> routingStepGroupOfRoutingId = routingStepVOList.stream().collect(Collectors.groupingBy(RoutingStepVO::getRoutingId));
        grossDemandContextDTO.setRoutingStepMapOfId(routingStepMapOfId);
        grossDemandContextDTO.setRoutingStepGroupOfRoutingId(routingStepGroupOfRoutingId);
        stopWatch.stop();

        stopWatch.start("初始化步骤输入数据");
        // 查询工艺路径步骤输入数据
        FeignDynamicParam routingStepInputParam = FeignDynamicParam.builder()
                .dynamicColumnParam(Lists.newArrayList("id", "routing_step_id", "input_product_id", "yield", "scrap", "input_factor", "supply_type"))
                .queryParam(ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode()))
                .build();
        List<RoutingStepInputVO> routingStepInputVOList = mdsFeign.selectRoutingStepInputByParamOnDynamicColumns(scenario, routingStepInputParam);
        Map<String, List<RoutingStepInputVO>> routingStepInputGroupOfStepId = routingStepInputVOList.stream()
                .collect(Collectors.groupingBy(RoutingStepInputVO::getRoutingStepId));
        grossDemandContextDTO.setRoutingStepInputGroupOfStepId(routingStepInputGroupOfStepId);
        stopWatch.stop();

        stopWatch.start("初始化30天内已发布发货计划");
        // 查询30内的发货计划
        List<DeliveryPlanPublishedVO> deliveryPlanFuture30Days = initDeliveryPlanPublishedInfo(grossDemandContextDTO);
        grossDemandContextDTO.setDeliveryPlanFuture30Days(deliveryPlanFuture30Days);
        stopWatch.stop();

        // 查询最新的产能平衡数据
        CapacityBalanceVersionVO capacityBalanceVersionVO = mpsFeign.selectLatestCapacityBalanceVersionCode(grossDemandContextDTO.getScenario());
        grossDemandContextDTO.setCapacityBalanceVersionVO(capacityBalanceVersionVO);

        List<MaterialGrossDemandPO> insertMaterialGrossDemandList = new ArrayList<>();

        stopWatch.start("组装生产需求");
        //1、计算生产需求
        if (StringUtils.equals(mpsDemandRule, "DELIVERY_PLAN_DEMAND")) {
            // 生产需求为从发货计划获取
            getMpsDemandFromDeliveryPlan(insertMaterialGrossDemandList, grossDemandContextDTO);
        } else {
            // 生产需求从MPS生产计划中获取
            getMpsDemandFromMps(insertMaterialGrossDemandList, grossDemandContextDTO);
        }
        stopWatch.stop();

        //2、计算中转库需求
        stopWatch.start("组装中转库需求");
        getGradeInventoryDemand(insertMaterialGrossDemandList, grossDemandContextDTO);
        stopWatch.stop();

        //3、计算委外需求
        getOutsourcedDemand(insertMaterialGrossDemandList, grossDemandContextDTO);

        stopWatch.start("组装产能平衡需求");
        //4、计算产能平衡需求
        getMcbGrossDemand(insertMaterialGrossDemandList, grossDemandContextDTO);
        stopWatch.stop();

        stopWatch.start("持久化材料毛需求数据");

        Date noGlassEndDate = DateUtils.moveDay(new Date(), 240);
        Date glassEndDate = DateUtils.moveDay(new Date(), 60);

        List<MaterialGrossDemandPO> noGlassMaterialGrossDemandPOS;
        // 5、持久化数据
        if (CollectionUtils.isEmpty(insertMaterialGrossDemandList)) {
            return;
        }
        // 过滤出B、RA.A、RA.V的需求
        List<MaterialGrossDemandPO> materialGrossDemandPOS = insertMaterialGrossDemandList.stream()
                .filter(item -> demandProductCategory.contains(item.getProductCategory()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(materialGrossDemandPOS)) {
            return;
        }

        List<MaterialGrossDemandPO> insertMergeList = new ArrayList<>();
        // 汇总同物料、本厂编码、来源、时间的数据
        Map<String, List<MaterialGrossDemandPO>> collect = materialGrossDemandPOS.stream()
                .collect(Collectors.groupingBy(item -> String.join("&",
                        item.getProductCode(), item.getProductFactoryCode(),
                        item.getDemandSource(),
                        DateUtils.dateToString(item.getDemandTime(), "yyyy-MM-dd"))));

        for (Map.Entry<String, List<MaterialGrossDemandPO>> entry : collect.entrySet()) {
            List<MaterialGrossDemandPO> value = entry.getValue();
            MaterialGrossDemandPO materialGrossDemandPO = value.get(0);
            // 求和demandQuantity
            BigDecimal sumDemandQuantity = value.stream().map(MaterialGrossDemandPO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            materialGrossDemandPO.setDemandQuantity(sumDemandQuantity);
            insertMergeList.add(materialGrossDemandPO);
        }

        noGlassMaterialGrossDemandPOS = insertMergeList.stream()
                .filter(item -> !StringUtils.equals(item.getProductCategory(), "RA.A") &&
                        item.getDemandTime().compareTo(mrpCalcDate) >= 0 && item.getDemandTime().compareTo(noGlassEndDate) <= 0)
                .collect(Collectors.toList());

        List<MaterialGrossDemandPO> glassMaterialGrossDemandPOS = insertMergeList.stream()
                .filter(item -> StringUtils.equals(item.getProductCategory(), "RA.A") &&
                        item.getDemandTime().compareTo(mrpCalcDate) >= 0 && item.getDemandTime().compareTo(glassEndDate) <= 0)
                .collect(Collectors.toList());
        // PVB、B类替代
//        noGlassMaterialGrossDemandPOS = noGlassDemandInvertSubstitute(grossDemandContextDTO.getScenario(), noGlassMaterialGrossDemandPOS);
        // 原片的需求考虑原片替代映射
        glassMaterialGrossDemandPOS = glassDemandInvertSubstitute(glassMaterialGrossDemandPOS);

        // 获取最新版本
        DynamicDataSourceContextHolder.setDataSource(scenario);
        String versionCode = materialGrossDemandVersionService.getVersionCode();
        MaterialGrossDemandVersionDTO materialGrossDemandVersionDTO = new MaterialGrossDemandVersionDTO();
        materialGrossDemandVersionDTO.setId(UUID.randomUUID().toString());
        materialGrossDemandVersionDTO.setVersionCode(versionCode);

        // 删除之前生成的需求 和 需求版本
        materialGrossDemandDao.deleteAll();
        materialGrossDemandVersionService.deleteAll();

        // 创建需求版本
        materialGrossDemandVersionService.doCreate(materialGrossDemandVersionDTO);

        // 创建需求版本历史明细
        MaterialGrossDemandVersionHistoryDTO materialGrossDemandVersionDetailDTO = new MaterialGrossDemandVersionHistoryDTO();
        BeanUtils.copyProperties(materialGrossDemandVersionDTO,materialGrossDemandVersionDetailDTO);
        materialGrossDemandVersionDetailService.doCreate02(materialGrossDemandVersionDetailDTO);

         // 创建需求
        if (CollectionUtils.isNotEmpty(noGlassMaterialGrossDemandPOS)) {
            noGlassMaterialGrossDemandPOS.forEach(item -> item.setMaterialGrossDemandVersionId(materialGrossDemandVersionDTO.getId()));
            Lists.partition(noGlassMaterialGrossDemandPOS, 1000).forEach(item -> materialGrossDemandDao.insertBatchWithPrimaryKey(item));
        }
        if (CollectionUtils.isNotEmpty(glassMaterialGrossDemandPOS)) {
            glassMaterialGrossDemandPOS.forEach(item -> item.setMaterialGrossDemandVersionId(materialGrossDemandVersionDTO.getId()));
            Lists.partition(glassMaterialGrossDemandPOS, 1000).forEach(item -> materialGrossDemandDao.insertBatchWithPrimaryKey(item));
        }

        // 创建需求历史明细
        if (CollectionUtils.isNotEmpty(noGlassMaterialGrossDemandPOS)) {
            noGlassMaterialGrossDemandPOS.forEach(item -> item.setMaterialGrossDemandVersionId(materialGrossDemandVersionDTO.getId()));

            // 转换历史明细
            List<MaterialGrossDemandHistoryDTO> materialGrossDemandDetailDTOList = noGlassMaterialGrossDemandPOS.stream()
                    .map(data -> MaterialGrossDemandHistoryDTO.builder()
                            .materialGrossDemandVersionId(materialGrossDemandVersionDTO.getId())
                            .productCode(data.getProductCode())
                            .productId(data.getProductId())
                            .productName(data.getProductName())
                            .productClassify(data.getProductClassify())
                            .productCategory(data.getProductCategory())
                            .productFactoryCode(data.getProductFactoryCode())
                            .vehicleModeCode(data.getVehicleModeCode())
                            .inputFactor(data.getInputFactor())
                            .demandTime(data.getDemandTime())
                            .demandQuantity(data.getDemandQuantity())
                            .unFulfillmentQuantity(data.getUnFulfillmentQuantity())
                            .operationCode(data.getOperationCode())
                            .supplyModel(data.getSupplyModel())
                            .productColor(data.getProductColor())
                            .productThickness(data.getProductThickness())
                            .demandSource(data.getDemandSource())
                            .remark(data.getRemark())
                            .enabled(data.getEnabled())
                            .id(null)
                            .build())
                    .collect(Collectors.toList());

            Lists.partition(materialGrossDemandDetailDTOList, 1000).forEach(item -> materialGrossDemandDetailService.doCreateBatch(item));
        }
        if (CollectionUtils.isNotEmpty(glassMaterialGrossDemandPOS)) {
            glassMaterialGrossDemandPOS.forEach(item -> item.setMaterialGrossDemandVersionId(materialGrossDemandVersionDTO.getId()));

            // 转换历史明细
            List<MaterialGrossDemandHistoryDTO> materialGrossDemandDetailDTOList = glassMaterialGrossDemandPOS.stream()
                    .map(data -> MaterialGrossDemandHistoryDTO.builder()
                            .materialGrossDemandVersionId(materialGrossDemandVersionDTO.getId())
                            .productCode(data.getProductCode())
                            .productId(data.getProductId())
                            .productName(data.getProductName())
                            .productClassify(data.getProductClassify())
                            .productCategory(data.getProductCategory())
                            .productFactoryCode(data.getProductFactoryCode())
                            .vehicleModeCode(data.getVehicleModeCode())
                            .inputFactor(data.getInputFactor())
                            .demandTime(data.getDemandTime())
                            .demandQuantity(data.getDemandQuantity())
                            .unFulfillmentQuantity(data.getUnFulfillmentQuantity())
                            .operationCode(data.getOperationCode())
                            .supplyModel(data.getSupplyModel())
                            .productColor(data.getProductColor())
                            .productThickness(data.getProductThickness())
                            .demandSource(data.getDemandSource())
                            .remark(data.getRemark())
                            .enabled(data.getEnabled())
                            .id(null)
                            .build())
                    .collect(Collectors.toList());

            Lists.partition(materialGrossDemandDetailDTOList, 1000).forEach(item -> materialGrossDemandDetailService.doCreateBatch(item));
        }
        stopWatch.stop();

        stopWatch.start("更新采购与供应商关系是否专用字段");
        if (CollectionUtils.isNotEmpty(noGlassMaterialGrossDemandPOS)) {
            calculatedSpecific(noGlassMaterialGrossDemandPOS);
        }
        stopWatch.stop();
        log.info(stopWatch.prettyPrint(TimeUnit.SECONDS));
    }

    private List<MaterialGrossDemandPO> noGlassDemandInvertSubstitute(String scenario, List<MaterialGrossDemandPO> noGlassMaterialGrossDemandPOS){
        // 查询物料替代关系
        List<ProductSubstitutionRelationshipVO> productSubstitutionRelationshipVOList = mdsFeign.getProductSubstitutionRelationshipVOByParams(scenario, new HashMap<>());
        if (CollectionUtils.isEmpty(productSubstitutionRelationshipVOList)){
            return noGlassMaterialGrossDemandPOS;
        }
        List<MaterialGrossDemandPO> result = new ArrayList<>();
        // 根据本厂编码和ERP-BOM进行分组
        Map<String, List<ProductSubstitutionRelationshipVO>> substitutionRelationshipGroup = productSubstitutionRelationshipVOList.stream()
                .collect(Collectors.groupingBy(item -> String.join("#", item.getProductCode(), item.getRawProductCode())));

        Map<String, List<MaterialGrossDemandPO>> noGlassGrossDemandGroup = noGlassMaterialGrossDemandPOS.stream()
                .collect(Collectors.groupingBy(item -> String.join("#", item.getProductFactoryCode(), item.getProductCode())));
        for (Map.Entry<String, List<MaterialGrossDemandPO>> entry : noGlassGrossDemandGroup.entrySet()) {
            String key = entry.getKey();
            List<MaterialGrossDemandPO> valueList = entry.getValue();
            // 获取替代数据
            List<ProductSubstitutionRelationshipVO> productSubstitutionRelationshipVOS = substitutionRelationshipGroup.get(key);
            if (CollectionUtils.isEmpty(productSubstitutionRelationshipVOS)){
                result.addAll(valueList);
                continue;
            }
            // 按照优先级排序
            productSubstitutionRelationshipVOS = productSubstitutionRelationshipVOS.stream()
                    .sorted(Comparator.comparing(ProductSubstitutionRelationshipVO::getPriority))
                    .collect(Collectors.toList());
            ProductSubstitutionRelationshipVO substitutionRelationshipVO = productSubstitutionRelationshipVOS.get(0);
            for (MaterialGrossDemandPO materialGrossDemandPO : valueList) {
                materialGrossDemandPO.setProductCode(substitutionRelationshipVO.getSubstituteProductCode());
                materialGrossDemandPO.setProductId(null);
                materialGrossDemandPO.setDemandSource(MrpDemandSourceEnum.ALTERNATIVE_DEMAND.getCode());
                materialGrossDemandPO.setMainProductCode(substitutionRelationshipVO.getRawProductCode());
                result.add(materialGrossDemandPO);
            }
        }
        return result;
    }

    private List<MaterialGrossDemandPO> glassDemandInvertSubstitute(List<MaterialGrossDemandPO> glassMaterialGrossDemandPOS) {
        // 1.查询原片替代映射
        List<GlassSubstitutionRelationshipVO> glassSubstitutionRelationshipVOS = glassSubstitutionRelationshipService.selectByParams(
                ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode())
        );
        if(CollectionUtils.isEmpty(glassSubstitutionRelationshipVOS)){
            return glassMaterialGrossDemandPOS;
        }

        List<MaterialGrossDemandPO> result = new ArrayList<>();

        // 根据本厂编码和ERP-BOM进行分组
        Map<String, List<GlassSubstitutionRelationshipVO>> substitutionRelationshipGroup = glassSubstitutionRelationshipVOS.stream()
                .collect(Collectors.groupingBy(item -> String.join("#", item.getProductCode(), item.getRawProductCode())));

        Map<String, List<MaterialGrossDemandPO>> glassGrossDemandGroup = glassMaterialGrossDemandPOS.stream()
                .collect(Collectors.groupingBy(item -> String.join("#", item.getProductFactoryCode(), item.getProductCode())));

        for (Map.Entry<String, List<MaterialGrossDemandPO>> entry : glassGrossDemandGroup.entrySet()) {
            String key = entry.getKey();
            List<MaterialGrossDemandPO> valueList = entry.getValue();
            List<GlassSubstitutionRelationshipVO> glassSubstitutionRelationshipVOList = substitutionRelationshipGroup.get(key);
            if (CollectionUtils.isEmpty(glassSubstitutionRelationshipVOList)){
                result.addAll(valueList);
                continue;
            }
            List<GlassSubstitutionRelationshipVO> filterList = glassSubstitutionRelationshipVOList.stream()
                    .filter(item -> StringUtils.isNotBlank(item.getProductionSubstituteProductCode()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterList)){
                result.addAll(valueList);
                continue;
            }
            GlassSubstitutionRelationshipVO glassSubstitutionRelationshipVO = filterList.get(0);
            for (MaterialGrossDemandPO materialGrossDemandPO : valueList) {
                materialGrossDemandPO.setProductCode(glassSubstitutionRelationshipVO.getProductionSubstituteProductCode());
                materialGrossDemandPO.setProductId(null);
                materialGrossDemandPO.setDemandSource(MrpDemandSourceEnum.ALTERNATIVE_DEMAND.getCode());
                materialGrossDemandPO.setMainProductCode(glassSubstitutionRelationshipVO.getRawProductCode());
                // 需求数量需要除以生产单耗
                glassSubstitutionRelationshipVO.setProductionInputFactor(null == glassSubstitutionRelationshipVO.getProductionInputFactor() ? BigDecimal.ONE : glassSubstitutionRelationshipVO.getProductionInputFactor());
                materialGrossDemandPO.setDemandQuantity(materialGrossDemandPO.getDemandQuantity().divide(glassSubstitutionRelationshipVO.getProductionInputFactor()));
                materialGrossDemandPO.setUnFulfillmentQuantity(materialGrossDemandPO.getDemandQuantity());
                result.add(materialGrossDemandPO);
            }
        }
        return result;
    }

    private List<MaterialGrossDemandPO> getHybridSubstitutionGrossDemand(MaterialGrossDemandPO glassMaterialGrossDemandPO,
                                                                         List<GlassSubstitutionRelationshipVO> relationshipVOS,
                                                                         Map<String, List<InventoryBatchDetailVO>> inventoryBatchDetailMap){
        List<MaterialGrossDemandPO> result = new ArrayList<>();
        HashMap<String, BigDecimal> substituteInventoryBatchQuantityMap = new HashMap<>();
        List<String> substituteProductCodeList = relationshipVOS.stream()
                .map(GlassSubstitutionRelationshipVO::getSubstituteProductCode)
                .distinct().collect(Collectors.toList());
        substituteProductCodeList.add(glassMaterialGrossDemandPO.getProductCode());
        for (String substituteProductCode : substituteProductCodeList) {
            List<InventoryBatchDetailVO> inventoryBatchDetailList = inventoryBatchDetailMap.get(substituteProductCode);
            if (CollectionUtils.isEmpty(inventoryBatchDetailList)) {
                substituteInventoryBatchQuantityMap.put(substituteProductCode, BigDecimal.ZERO);
                continue;
            }
            // 过滤在需求日期之后的实时库存
            List<InventoryBatchDetailVO> filterList = inventoryBatchDetailList.stream()
                    .filter(item -> DateUtils.stringToDate(item.getAssignedTime(),DateUtils.COMMON_DATE_STR1).compareTo(glassMaterialGrossDemandPO.getDemandTime()) >= 0)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterList)) {
                substituteInventoryBatchQuantityMap.put(substituteProductCode, BigDecimal.ZERO);
                continue;
            }
            BigDecimal currentQuantitySum = filterList.stream().map(item -> new BigDecimal(item.getCurrentQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add);
            substituteInventoryBatchQuantityMap.put(substituteProductCode, currentQuantitySum);
        }

        // 降序
        Map<String, BigDecimal> sortedMap = substituteInventoryBatchQuantityMap.entrySet().stream()
                .sorted(Map.Entry.comparingByValue(Comparator.reverseOrder()))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (e1, e2) -> e1,
                        LinkedHashMap::new
                ));
        // 根据排序后的替代产生毛需求
        BigDecimal demandQuantity = glassMaterialGrossDemandPO.getDemandQuantity();
        for (Map.Entry<String, BigDecimal> entry : sortedMap.entrySet()) {
            // 组装替代毛需求数据
            MaterialGrossDemandPO substitutionGrossDemand = assembleSubstituteMaterialGrossDemandPO(entry.getKey(), glassMaterialGrossDemandPO);
            BigDecimal supplyQuantity = entry.getValue();
            if (supplyQuantity.compareTo(demandQuantity) >= 0){
                substitutionGrossDemand.setDemandQuantity(demandQuantity);
                substitutionGrossDemand.setUnFulfillmentQuantity(demandQuantity);
                result.add(substitutionGrossDemand);
                demandQuantity = BigDecimal.ZERO;
                break;
            }
            if (supplyQuantity.compareTo(demandQuantity) < 0){
                substitutionGrossDemand.setDemandQuantity(supplyQuantity);
                substitutionGrossDemand.setUnFulfillmentQuantity(supplyQuantity);
                demandQuantity = demandQuantity.subtract(supplyQuantity);
            }
        }
        if (demandQuantity.compareTo(BigDecimal.ZERO) > 0){
            MaterialGrossDemandPO substitutionGrossDemand = assembleSubstituteMaterialGrossDemandPO(glassMaterialGrossDemandPO.getProductCode(), glassMaterialGrossDemandPO);
            substitutionGrossDemand.setDemandQuantity(demandQuantity);
            substitutionGrossDemand.setUnFulfillmentQuantity(demandQuantity);
            result.add(substitutionGrossDemand);
        }
        return result;
    }

    private MaterialGrossDemandPO assembleSubstituteMaterialGrossDemandPO(String productCode, MaterialGrossDemandPO originMaterialGrossDemandPO){
        MaterialGrossDemandPO substitutionGrossDemand = new MaterialGrossDemandPO();
//            substitutionGrossDemand.setProductId();
//            substitutionGrossDemand.setProductName();
        substitutionGrossDemand.setProductCode(productCode);
        substitutionGrossDemand.setDemandTime(originMaterialGrossDemandPO.getDemandTime());
        substitutionGrossDemand.setMaterialGrossDemandVersionId(originMaterialGrossDemandPO.getMaterialGrossDemandVersionId());
        substitutionGrossDemand.setProductClassify(originMaterialGrossDemandPO.getProductClassify());
        substitutionGrossDemand.setProductCategory(originMaterialGrossDemandPO.getProductCategory());
        substitutionGrossDemand.setProductFactoryCode(originMaterialGrossDemandPO.getProductFactoryCode());
        substitutionGrossDemand.setVehicleModeCode(originMaterialGrossDemandPO.getVehicleModeCode());
        substitutionGrossDemand.setInputFactor(originMaterialGrossDemandPO.getInputFactor());
        substitutionGrossDemand.setOperationCode(originMaterialGrossDemandPO.getOperationCode());
        substitutionGrossDemand.setSupplyModel(originMaterialGrossDemandPO.getSupplyModel());
        substitutionGrossDemand.setProductColor(originMaterialGrossDemandPO.getProductColor());
        substitutionGrossDemand.setProductThickness(originMaterialGrossDemandPO.getProductThickness());
        if (StringUtils.equals(originMaterialGrossDemandPO.getProductCode(), productCode)){
            substitutionGrossDemand.setDemandSource(originMaterialGrossDemandPO.getDemandSource());
        }else {
            substitutionGrossDemand.setDemandSource(MrpDemandSourceEnum.ALTERNATIVE_DEMAND.getCode());
        }
        return substitutionGrossDemand;
    }

    private void calculatedSpecific(List<MaterialGrossDemandPO> noGlassMaterialGrossDemandPOS) {
        Map<String, List<MaterialGrossDemandPO>> grossDemandGroupOfProductCode = noGlassMaterialGrossDemandPOS.stream()
                .collect(Collectors.groupingBy(MaterialGrossDemandBasicPO::getProductCode));

        List<String> materialCodeList = noGlassMaterialGrossDemandPOS.stream()
                .map(MaterialGrossDemandBasicPO::getProductCode)
                .distinct().collect(Collectors.toList());
        List<MaterialSupplierPurchasePO> materialSupplierPurchasePOS = new ArrayList<>();
        // 分批量查询
        Lists.partition(materialCodeList, 1000).forEach(item -> materialSupplierPurchasePOS.addAll(materialSupplierPurchaseDao.selectByParams(ImmutableMap.of("materialCodeList", materialCodeList))));

        if (CollectionUtils.isNotEmpty(materialSupplierPurchasePOS)) {
            for (MaterialSupplierPurchasePO materialSupplierPurchasePO : materialSupplierPurchasePOS) {
                if (!grossDemandGroupOfProductCode.containsKey(materialSupplierPurchasePO.getMaterialCode())) {
                    continue;
                }
                List<MaterialGrossDemandPO> grossDemandVOList = grossDemandGroupOfProductCode.get(materialSupplierPurchasePO.getMaterialCode());
                List<String> productFactoryCodeList = grossDemandVOList.stream()
                        .map(MaterialGrossDemandPO::getProductFactoryCode).distinct()
                        .collect(Collectors.toList());
                if (productFactoryCodeList.size() == 1) {
                    materialSupplierPurchasePO.setSpecific(YesOrNoEnum.YES.getCode());
                } else {
                    materialSupplierPurchasePO.setSpecific(YesOrNoEnum.NO.getCode());
                }
            }
        }
        materialSupplierPurchaseDao.updateBatch(materialSupplierPurchasePOS);
    }


    private List<DeliveryPlanPublishedVO> initDeliveryPlanPublishedInfo(GrossDemandContextDTO grossDemandContextDTO) {
        Date moveDay = DateUtils.moveDay(grossDemandContextDTO.getMrpCalcDate(), 30);
        FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder().dynamicColumnParam(Lists.newArrayList("id", "product_code", "demand_time", "demand_quantity", "demand_version_id", "create_time")).queryParam(ImmutableMap.of("startTimeStr", DateUtils.dateToString(grossDemandContextDTO.getMrpCalcDate()), "endTimeStr", DateUtils.dateToString(moveDay))).build();
        return dfpFeign.selectDeliveryPlanPublishedByParamOnDynamicColumns(grossDemandContextDTO.getScenario(), feignDynamicParam);
    }

    private void getMpsDemandFromDeliveryPlan(List<MaterialGrossDemandPO> insertGrossDemandList, GrossDemandContextDTO grossDemandContextDTO) {
        String scenario = grossDemandContextDTO.getScenario();
        List<DeliveryPlanPublishedVO> deliveryPlanFuture30Days = grossDemandContextDTO.getDeliveryPlanFuture30Days();
        Map<String, NewProductStockPointVO> productStockPointVOMapOfId = grossDemandContextDTO.getProductStockPointVOMapOfId();
        Map<String, NewProductStockPointVO> productStockPointVOMapOfProductCode = grossDemandContextDTO.getProductStockPointVOMapOfProductCode();
        Map<String, Date> lastDeliveryPlanTimeMapOfProductCode = grossDemandContextDTO.getLastDeliveryPlanTimeMapOfProductCode();
        Map<String, RoutingStepVO> routingStepMapOfId = grossDemandContextDTO.getRoutingStepMapOfId();
        Map<String, List<RoutingStepInputVO>> routingStepInputGroupOfStepId = grossDemandContextDTO.getRoutingStepInputGroupOfStepId();

        Map<String, List<DeliveryPlanPublishedVO>> deliveryPlanDetailGroup = deliveryPlanFuture30Days.stream().collect(Collectors.groupingBy(DeliveryPlanPublishedVO::getProductCode));
        // 查询mps_capacity_supply_relationship表,versionId为WEEK的, 只查询本厂的需求
        List<CapacitySupplyRelationshipVO> list = mpsFeign.selectCapacitySupplyRelationshipByParams(scenario, ImmutableMap.of("versionId", "WEEK", "supplyModel", SupplyModelEnum.LOCAL.getCode()));
        if (com.yhl.platform.common.utils.CollectionUtils.isNotEmpty(list)) {
            // 根据产能供应关系进行展BOM(周产能平衡)
            for (CapacitySupplyRelationshipVO capacitySupplyRelationshipVO : list) {
                String productCode = capacitySupplyRelationshipVO.getProductCode();
                if ("64510TRWCSGTYB".equals(productCode)) {
                    log.info("生产需求从发货计划获取，本厂物料:{},需求时间:{}, 需求量:{}", productCode, DateUtils.dateToString(capacitySupplyRelationshipVO.getSupplyTime(), DateUtils.COMMON_DATE_STR3), capacitySupplyRelationshipVO.getDemandQuantity());
                }
                NewProductStockPointVO bcProductStockPointVO = productStockPointVOMapOfProductCode.get(productCode);
                RoutingStepVO routingStepVO = routingStepMapOfId.get(capacitySupplyRelationshipVO.getRoutingStepId());

                // 获取输入物品
                List<RoutingStepInputVO> stepInputVOS = routingStepInputGroupOfStepId.get(capacitySupplyRelationshipVO.getRoutingStepId());
                if (com.yhl.platform.common.utils.CollectionUtils.isEmpty(stepInputVOS)) {
                    continue;
                }
                for (RoutingStepInputVO stepInputVO : stepInputVOS) {
                    // 获取物品
                    NewProductStockPointVO newProductStockPointVO = productStockPointVOMapOfId.get(stepInputVO.getInputProductId());
                    // 判断是否为最后一道工序
                    if (null == newProductStockPointVO || !StringUtils.equals("P", newProductStockPointVO.getProductType()) ||
                            (StringUtils.isEmpty(routingStepVO.getNextRoutingStepSequenceNo()) && "推式".equals(stepInputVO.getSupplyType()))) {
                        continue;
                    }
                    if (null == stepInputVO.getYield()) {
                        stepInputVO.setYield(BigDecimal.ONE);
                    }
                    if (null == stepInputVO.getInputFactor()) {
                        stepInputVO.setInputFactor(BigDecimal.ONE);
                    }
                    String productCategory = getProductCategory(newProductStockPointVO.getProductClassify());
                    MaterialGrossDemandPO materialGrossDemandPO = new MaterialGrossDemandPO();
                    materialGrossDemandPO.setId(UUID.randomUUID().toString());
                    materialGrossDemandPO.setProductCode(newProductStockPointVO.getProductCode());
                    materialGrossDemandPO.setProductName(newProductStockPointVO.getProductName());
                    materialGrossDemandPO.setProductId(newProductStockPointVO.getId());
                    materialGrossDemandPO.setProductClassify(newProductStockPointVO.getProductClassify());
                    materialGrossDemandPO.setProductCategory(productCategory);
                    materialGrossDemandPO.setProductFactoryCode(productCode);
                    materialGrossDemandPO.setVehicleModeCode(bcProductStockPointVO != null ? bcProductStockPointVO.getVehicleModelCode() : null);
                    materialGrossDemandPO.setInputFactor(stepInputVO.getInputFactor());
                    materialGrossDemandPO.setDemandTime(capacitySupplyRelationshipVO.getSupplyTime());
                    // 需求数量
                    materialGrossDemandPO.setDemandQuantity(capacitySupplyRelationshipVO.getSupplyQuantity().multiply(stepInputVO.getInputFactor()).divide(stepInputVO.getYield(), RoundingMode.CEILING));
                    materialGrossDemandPO.setUnFulfillmentQuantity(capacitySupplyRelationshipVO.getSupplyQuantity().multiply(stepInputVO.getInputFactor()).divide(stepInputVO.getYield(), RoundingMode.CEILING));
                    materialGrossDemandPO.setSupplyModel(null);
                    materialGrossDemandPO.setDemandSource(MrpDemandSourceEnum.MPS.getCode());
                    insertGrossDemandList.add(materialGrossDemandPO);
                    if ("64510TRWCSGTYB".equals(productCode)) {
                        log.info("生产需求从发货计划获取，输入材料:{}, 需求时间:{}, 需求量:{}", newProductStockPointVO.getProductCode(), DateUtils.dateToString(materialGrossDemandPO.getDemandTime(), DateUtils.COMMON_DATE_STR3), materialGrossDemandPO.getDemandQuantity());
                    }
                }
            }

            list.stream().filter(item -> {
                NewProductStockPointVO newProductStockPointVO = productStockPointVOMapOfProductCode.get(item.getProductCode());
                return deliveryPlanDetailGroup.containsKey(newProductStockPointVO.getProductCode());
            }).collect(Collectors.groupingBy(CapacitySupplyRelationshipVO::getProductCode)).forEach((productCode, capacitySupplyRelationshipVOS) -> {
                NewProductStockPointVO newProductStockPointVO = productStockPointVOMapOfProductCode.get(productCode);
                capacitySupplyRelationshipVOS.sort(Comparator.comparing(CapacitySupplyRelationshipVO::getSupplyTime));
                // 产能平衡的最晚日期
                Date lastSupplyTime = capacitySupplyRelationshipVOS.get(capacitySupplyRelationshipVOS.size() - 1).getSupplyTime();

                List<DeliveryPlanPublishedVO> deliveryPlanPublishedVOList = deliveryPlanDetailGroup.get(newProductStockPointVO.getProductCode());
                // 过滤需求时间晚于主生产计划最晚日期的发货计划数据
                List<DeliveryPlanPublishedVO> deliveryPlanDetailVOSAfter = deliveryPlanPublishedVOList.stream().filter(dp -> !dp.getDemandTime().before(lastSupplyTime)).collect(Collectors.toList());
                // 计算出主生产计划能覆盖的最晚发货计划日期
                Date lastDeliveryPlanTime = getLastDeliveryPlanTime2(capacitySupplyRelationshipVOS, deliveryPlanDetailVOSAfter);
                if (null != lastDeliveryPlanTime) {
                    lastDeliveryPlanTimeMapOfProductCode.put(productCode, lastDeliveryPlanTime);
                }
            });
        }
    }


    private void getMpsDemandFromMps(List<MaterialGrossDemandPO> insertGrossDemandList, GrossDemandContextDTO grossDemandContextDTO) {
        String scenario = grossDemandContextDTO.getScenario();
        Date mrpCalcDate = grossDemandContextDTO.getMrpCalcDate();
        Map<String, NewProductStockPointVO> productStockPointVOMapOfId = grossDemandContextDTO.getProductStockPointVOMapOfId();
        Map<String, NewProductStockPointVO> productStockPointVOMapOfProductCode = grossDemandContextDTO.getProductStockPointVOMapOfProductCode();
        List<DeliveryPlanPublishedVO> deliveryPlanFuture30Days = grossDemandContextDTO.getDeliveryPlanFuture30Days();
        Map<String, Date> lastDeliveryPlanTimeMapOfProductCode = grossDemandContextDTO.getLastDeliveryPlanTimeMapOfProductCode();


        // 查询制造订单发布表
//        List<WorkOrderPublishedVO> workOrderVOList = mpsFeign.selectWorkOrderPublishedByParams(scenario, new HashMap<>());
        List<WorkOrderVO> workOrderVOList = mpsFeign.selectWorkOrderByParams(scenario, new HashMap<>());
        // 查询工序发布表
//        List<OperationPublishedVO> operationVOList = mpsFeign.selectOperationPublishedByParams(scenario, new HashMap<>());
        List<OperationVO> operationVOList = mpsFeign.selectOperationByParams(scenario, new HashMap<>());
        // 查询需求发布表
//        List<DemandPublishedVO> demandVOList = mpsFeign.selectDemandPublishedByParams(scenario, new HashMap<>());
        List<DemandVO> demandVOList = mpsFeign.selectDemandByParams(scenario, new HashMap<>());

        if (com.yhl.platform.common.utils.CollectionUtils.isEmpty(workOrderVOList) || com.yhl.platform.common.utils.CollectionUtils.isEmpty(demandVOList)) {
            throw new BusinessException("计算失败，无可计算的生产需求");
        }

        Map<String, RoutingStepVO> routingStepMapOfId = grossDemandContextDTO.getRoutingStepMapOfId();
        Map<String, List<RoutingStepInputVO>> routingStepInputGroupOfStepId = grossDemandContextDTO.getRoutingStepInputGroupOfStepId();

//        List<OperationInputPublishedVO> operationInputVOList = mpsFeign.selectOperationInputPublishedByParams(scenario, new HashMap<>());
        List<OperationInputVO> operationInputVOList = mpsFeign.selectOperationInputByParams(scenario, new HashMap<>());

        Map<String, WorkOrderVO> workOrderVOMap = workOrderVOList.stream().collect(Collectors.toMap(WorkOrderVO::getId, Function.identity(), (k1, k2) -> k2));

        Map<String, OperationInputVO> operationInputVOMap = operationInputVOList.stream().collect(Collectors.toMap(OperationInputVO::getId, Function.identity(), (k1, k2) -> k2));

        Map<String, OperationVO> operationVOMap = operationVOList.stream().collect(Collectors.toMap(OperationVO::getId, Function.identity(), (k1, k2) -> k2));

        Map<String, List<DeliveryPlanPublishedVO>> deliveryPlanDetailGroup = deliveryPlanFuture30Days.stream().collect(Collectors.groupingBy(DeliveryPlanPublishedVO::getProductCode));

        demandVOList.forEach(demandVO -> {
            NewProductStockPointVO productStockPointVO = productStockPointVOMapOfId.get(demandVO.getProductId());
            if (null == productStockPointVO || !"P".equalsIgnoreCase(productStockPointVO.getProductType()) || demandVO.getDemandTime().compareTo(mrpCalcDate) < 0) {
                return;
            }
            String finishedProductCode = demandVO.getFinishedProductCode();
            NewProductStockPointVO bcProductStockPointVO = productStockPointVOMapOfProductCode.get(finishedProductCode);

            String operationInputId = demandVO.getOperationInputId();
            OperationInputVO operationInputVO = operationInputVOMap.get(operationInputId);
            OperationVO operationVO = operationVOMap.get(operationInputVO.getOperationId());
            WorkOrderVO workOrderVO = workOrderVOMap.get(operationVO.getOrderId());

            RoutingStepVO routingStepVO = routingStepMapOfId.get(operationVO.getRoutingStepId());
            List<RoutingStepInputVO> routingStepInputVOS = routingStepInputGroupOfStepId.get(operationVO.getRoutingStepId());
            if (com.yhl.platform.common.utils.CollectionUtils.isEmpty(routingStepInputVOS)) {
                return;
            }
            Map<String, RoutingStepInputVO> routingStepInputVOMap = routingStepInputVOS.stream().filter(t -> YesOrNoEnum.YES.getCode().equals(t.getEnabled())).collect(Collectors.toMap(RoutingStepInputVO::getInputProductId, Function.identity(), (k1, k2) -> k2));
            RoutingStepInputVO routingStepInputVO = routingStepInputVOMap.get(demandVO.getProductId());

            // 如果当前工序本厂最后一道工序，输入物料为推式，则剔除
            if (StringUtils.isEmpty(routingStepVO.getNextRoutingStepSequenceNo()) && "推式".equals(routingStepInputVO.getSupplyType())) {
                return;
            }

            String productCategory = getProductCategory(productStockPointVO.getProductClassify());
            MaterialGrossDemandPO materialGrossDemandPO = new MaterialGrossDemandPO();
            materialGrossDemandPO.setId(UUID.randomUUID().toString());
            materialGrossDemandPO.setProductCode(productStockPointVO.getProductCode());
            materialGrossDemandPO.setProductName(productStockPointVO.getProductName());
            materialGrossDemandPO.setProductId(productStockPointVO.getId());
            materialGrossDemandPO.setProductClassify(productStockPointVO.getProductClassify());
            materialGrossDemandPO.setProductCategory(productCategory);
            materialGrossDemandPO.setProductFactoryCode(finishedProductCode);
            materialGrossDemandPO.setVehicleModeCode(null != bcProductStockPointVO ? bcProductStockPointVO.getVehicleModelCode() : null);
            materialGrossDemandPO.setInputFactor(null);
            materialGrossDemandPO.setDemandTime(demandVO.getDemandTime());
            materialGrossDemandPO.setDemandQuantity(demandVO.getQuantity());
            materialGrossDemandPO.setUnFulfillmentQuantity(demandVO.getQuantity());
            materialGrossDemandPO.setSupplyModel(null);
            materialGrossDemandPO.setDemandSource(MrpDemandSourceEnum.MPS.getCode());
            insertGrossDemandList.add(materialGrossDemandPO);
        });

        workOrderVOList.stream().filter(workOrderVO -> {
            NewProductStockPointVO productStockPointVO = productStockPointVOMapOfId.get(workOrderVO.getProductId());
            return deliveryPlanDetailGroup.get(productStockPointVO.getProductCode()) != null;
        }).collect(Collectors.groupingBy(WorkOrderVO::getProductId)).forEach((productId, fgWorkOrderList) -> {
            NewProductStockPointVO productStockPointVO = productStockPointVOMapOfId.get(productId);
            fgWorkOrderList.sort(Comparator.comparing(WorkOrderVO::getDueDate));
            // 主生产计划制造订单最晚日期
            Date lastDueDate = fgWorkOrderList.get(fgWorkOrderList.size() - 1).getDueDate();
//                    String lastDueDateStr = DateUtils.dateToString(lastDueDate, DateUtils.YEAR_MONTH);

            List<DeliveryPlanPublishedVO> deliveryPlanPublishedVOList = deliveryPlanDetailGroup.get(productStockPointVO.getProductCode());
            // 过滤需求时间晚于主生产计划最晚日期的发货计划数据
            List<DeliveryPlanPublishedVO> deliveryPlanDetailVOSAfter = deliveryPlanPublishedVOList.stream().filter(dp -> !dp.getDemandTime().before(lastDueDate)).collect(Collectors.toList());
            if ("00685LFW00004".equals(productStockPointVO.getProductCode()) || "00685LFW00006".equals(productStockPointVO.getProductCode())) {
                log.info("最晚制造订单物品:{}, 最晚时间:{}, 制造数量:{},", productStockPointVO.getProductCode(), DateUtils.dateToString(lastDueDate), fgWorkOrderList.get(fgWorkOrderList.size() - 1).getQuantity());
            }
            deliveryPlanDetailVOSAfter.sort(Comparator.comparing(DeliveryPlanPublishedVO::getDemandTime));

            // 计算出主生产计划能覆盖的最晚发货计划日期
            Date lastDeliveryPlanTime = getLastDeliveryPlanTime(fgWorkOrderList, deliveryPlanDetailVOSAfter);
            if ("00685LFW00004".equals(productStockPointVO.getProductCode()) || "00685LFW00006".equals(productStockPointVO.getProductCode())) {
                for (DeliveryPlanPublishedVO deliveryPlanPublishedVO : deliveryPlanDetailVOSAfter) {
                    log.info("最晚制造订单物品:{}, 发货时间:{}, 发货数量:{}", productStockPointVO.getProductCode(), DateUtils.dateToString(deliveryPlanPublishedVO.getDemandTime()), deliveryPlanPublishedVO.getDemandQuantity());
                }
            }

            if (null != lastDeliveryPlanTime) {
                if ("00685LFW00004".equals(productStockPointVO.getProductCode()) || "00685LFW00006".equals(productStockPointVO.getProductCode())) {
                    log.info("最晚制造订单物品:{}, 获取能覆盖的时间为:{}", productStockPointVO.getProductCode(), DateUtils.dateToString(lastDeliveryPlanTime));
                }
                lastDeliveryPlanTimeMapOfProductCode.put(productStockPointVO.getProductCode(), lastDeliveryPlanTime);
            }
        });
    }

    private String getProductCategory(String productClassify) {
        String productCategory;
        if (productClassify.startsWith("RA.A")) {
            productCategory = "RA.A";
        } else {
            productCategory = productClassify.startsWith("RA.V") ? "PVB" : "B";
        }
        return productCategory;
    }

    private void getOutsourcedDemand(List<MaterialGrossDemandPO> insertGrossDemandList, GrossDemandContextDTO grossDemandContextDTO) {
        // 查询委外需求
        List<OutsourceTransferDemandDetailVO> outsourceTransferDemandDetailList = mpsFeign.selectOutsourceTransferDemandDetailByParams(grossDemandContextDTO.getScenario(),
                ImmutableMap.of("requiredMaterials", YesOrNoEnum.YES.getCode()));

        Date mrpCalcDate = grossDemandContextDTO.getMrpCalcDate();
        if (CollectionUtils.isEmpty(outsourceTransferDemandDetailList)) {
            return;
        }
        for (OutsourceTransferDemandDetailVO outsourceTransferDemandDetailVO : outsourceTransferDemandDetailList) {
            Map<String, NewProductStockPointVO> productStockPointVOMapOfProductCode = grossDemandContextDTO.getProductStockPointVOMapOfProductCode();
            // 获取材料物品
            NewProductStockPointVO productStockPointOfMaterial = productStockPointVOMapOfProductCode.get(outsourceTransferDemandDetailVO.getMaterialsCode());
            // 获取本厂编码物品
            NewProductStockPointVO productStockPointOfBc = productStockPointVOMapOfProductCode.get(outsourceTransferDemandDetailVO.getProductCode());

            String demandMonth = outsourceTransferDemandDetailVO.getDemandMonth();
            // demandMonth为yyyyy-MM格式，如果是当前月设置为当前时间，不少当前月则设置为demandMonth月的第一天
            Calendar currentCalendar = Calendar.getInstance();
            currentCalendar.setTime(mrpCalcDate);
            int currentYear = currentCalendar.get(Calendar.YEAR);
            // 注意月份是从0开始的，所以需要加1
            int currentMonth = currentCalendar.get(Calendar.MONTH) + 1;

            String[] dateParts = demandMonth.split("-");
            int demandYear = Integer.parseInt(dateParts[0]);
            int demandMonthValue = Integer.parseInt(dateParts[1]);

            Date demandDate;
            if (currentYear == demandYear && currentMonth == demandMonthValue) {
                demandDate = mrpCalcDate;
            } else {
                Calendar demandCalendar = Calendar.getInstance();
                // 月份是从0开始的，所以需要减1
                demandCalendar.set(demandYear, demandMonthValue - 1, 1);
                demandDate = demandCalendar.getTime();
            }
            String productCategory = getProductCategory(productStockPointOfMaterial.getProductClassify());
            MaterialGrossDemandPO materialGrossDemandPO = new MaterialGrossDemandPO();
            materialGrossDemandPO.setId(UUID.randomUUID().toString());
            materialGrossDemandPO.setProductId(productStockPointOfMaterial.getId());
            materialGrossDemandPO.setProductCode(outsourceTransferDemandDetailVO.getMaterialsCode());
            materialGrossDemandPO.setProductName(productStockPointOfMaterial.getProductName());
            materialGrossDemandPO.setProductFactoryCode(null != productStockPointOfBc ? productStockPointOfBc.getProductCode() : null);
            materialGrossDemandPO.setProductClassify(productStockPointOfMaterial.getProductClassify());
            materialGrossDemandPO.setProductCategory(productCategory);
            materialGrossDemandPO.setVehicleModeCode(null != productStockPointOfBc ? productStockPointOfBc.getVehicleModelCode() : null);
            materialGrossDemandPO.setDemandTime(demandDate);
            materialGrossDemandPO.setDemandQuantity(outsourceTransferDemandDetailVO.getSupplyQuantity());
            materialGrossDemandPO.setUnFulfillmentQuantity(outsourceTransferDemandDetailVO.getSupplyQuantity());
            materialGrossDemandPO.setDemandSource(MrpDemandSourceEnum.OUTSOURCE_TRANSFER.getCode());
            insertGrossDemandList.add(materialGrossDemandPO);
        }
    }

    /**
     * 获取中转库需求
     * @return
     */
    protected void getGradeInventoryDemand(List<MaterialGrossDemandPO> insertGrossDemandList, GrossDemandContextDTO grossDemandContextDTO) {
        String scenario = grossDemandContextDTO.getScenario();
        Map<String, NewProductStockPointVO> productStockPointVOMapOfId = grossDemandContextDTO.getProductStockPointVOMapOfId();
        Map<String, NewProductStockPointVO> productStockPointVOMapOfProductCode = grossDemandContextDTO.getProductStockPointVOMapOfProductCode();
        List<DeliveryPlanPublishedVO> deliveryPlanFuture30Days = grossDemandContextDTO.getDeliveryPlanFuture30Days();

        Map<String, RoutingVO> routingVOMapOfProductCode = grossDemandContextDTO.getRoutingVOMapOfProductCode();
        Map<String, List<RoutingStepVO>> routingStepGroupOfRoutingId = grossDemandContextDTO.getRoutingStepGroupOfRoutingId();
        Map<String, List<RoutingStepInputVO>> routingStepInputGroupOfStepId = grossDemandContextDTO.getRoutingStepInputGroupOfStepId();

        Map<String, Date> lastDeliveryPlanTimeMapOfProductCode = grossDemandContextDTO.getLastDeliveryPlanTimeMapOfProductCode();

        // 查询中转库半品辅料表（不超过百条数据，查询全表）

        log.info("查询中转库半品辅料表、数据源:{}", scenario);
        DynamicDataSourceContextHolder.setDataSource(scenario);
        List<WarehouseHalfSubinventoryVO> warehouseHalfSubinventoryVOS = warehouseHalfSubinventoryService.selectAll();
        // 获取辅料子库存
        List<String> auxiliarySubInventoryList = warehouseHalfSubinventoryVOS.stream().map(WarehouseHalfSubinventoryVO::getAuxiliarySubinventory).distinct().collect(Collectors.toList());
        // 获取辅料货位
        List<String> auxiliaryStorageList = warehouseHalfSubinventoryVOS.stream().map(WarehouseHalfSubinventoryVO::getAuxiliaryStorage).distinct().collect(Collectors.toList());

        // 中转库需求定义：【本厂编码】的物品类型为成品、【工序】为末道工序、【供应方式】为推式的材料

        Map<String, Object> params = new HashMap<>();
        params.put("subinventory", "BPK");
        // 查询实时库存《半品库》
        List<InventoryBatchDetailVO> bpkInventoryBatchDetailVOList = dfpFeign.selectInventoryBatchDetailVOByParams(scenario, params);

        // 查询实时库存《原材料》
        params.put("subinventory", null);
        params.put("subinventoryList", auxiliarySubInventoryList);
        params.put("freightSpaceList", auxiliaryStorageList);
        List<InventoryBatchDetailVO> materialInventoryBatchDetailVOList = dfpFeign.selectInventoryBatchDetailVOByParams(scenario, params);

        // 获取中转库原材料库存
        Map<String, List<InventoryBatchDetailVO>> inventoryBatchDetailVOMapOfProductCode = materialInventoryBatchDetailVOList.stream().collect(Collectors.groupingBy(InventoryBatchDetailVO::getProductCode));


        // 获取最新版本的发货计划
        List<String> deliveryPlanProductCodeList = deliveryPlanFuture30Days.stream().map(DeliveryPlanPublishedVO::getProductCode).distinct().collect(Collectors.toList());

        // 最新已发布的发货计划数据按照productCode组装Map
        Map<String, String> deliveryPlanProductCodeToDemandVersionId = deliveryPlanFuture30Days.stream().filter(item -> StringUtils.isNotBlank(item.getDemandVersionId())).sorted(Comparator.comparing(DeliveryPlanPublishedVO::getCreateTime).reversed()).collect(Collectors.toMap(DeliveryPlanPublishedVO::getProductCode, DeliveryPlanPublishedVO::getDemandVersionId, (k1, k2) -> k1 // 保留第一个出现的值，即创建时间最晚的
        ));

        // 获取日需求版本ID
        List<String> demandVersionIdList = deliveryPlanFuture30Days.stream().map(DeliveryPlanPublishedVO::getDemandVersionId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());

        // 根据日需求版本ID查询日需求
//        List<CleanDemandDataVO> demandDataVOList = dfpFeign.selectCleanDemandDataByParams(
//                ImmutableMap.of("versionIds", demandVersionIdList, "productCodeList", productCodeList));
//
//        Map<String, List<CleanDemandDataVO>> cleanDemandDataVoOfVersionId = demandDataVOList.stream().collect(Collectors.groupingBy(CleanDemandDataVO::getVersionId));
//
//        Map<String, List<CleanDemandDataDetailVO>> cleanDemandDataDetailMapOfDataId = new HashMap<>();
//        log.info("材料MRP-------开始查询日需求数据");
//        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(demandDataVOList)) {
//            // 查询日需求明细数据
//            List<String> cleanDemandDataIdList = demandDataVOList.stream().map(CleanDemandDataVO::getId).distinct().collect(Collectors.toList());
//            log.info("材料MRP-------开始查询日需求数据明细, 明细的dataId数据量:{}", cleanDemandDataIdList.size());
//            List<CleanDemandDataDetailVO> cleanDemandDataDetailVOList = new ArrayList<>();
//            Lists.partition(cleanDemandDataIdList, 1000).forEach(dataIdList ->
//                    cleanDemandDataDetailVOList.addAll(dfpFeign.selectCleanDemandDataDetailVOByDataIds(dataIdList)));
//
//            cleanDemandDataDetailMapOfDataId = cleanDemandDataDetailVOList.stream().collect(Collectors.groupingBy(CleanDemandDataDetailVO::getCleanDemandDataId));
//        }
        log.info("材料MRP-------日需求查询结束");
        // 存放缺口数据
        Map<String, BigDecimal> gapDemandQuantityMap = new HashMap<>();
        Map<String, List<InventoryBatchDetailVO>> bpkInventoryBatchDetailVOGroup = bpkInventoryBatchDetailVOList.stream().collect(Collectors.groupingBy(InventoryBatchDetailVO::getProductCode));

        for (String deliveryPlanProductCode : deliveryPlanProductCodeList) {
            BigDecimal currentQuantitySum = BigDecimal.ZERO;
            List<InventoryBatchDetailVO> inventoryBatchDetailVOS = bpkInventoryBatchDetailVOGroup.get(deliveryPlanProductCode);
            if (CollectionUtils.isNotEmpty(inventoryBatchDetailVOS)) {
                currentQuantitySum = inventoryBatchDetailVOS.stream().map(item -> new BigDecimal(item.getCurrentQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            if (deliveryPlanProductCode.equals("00393LRF00006")) {
                log.info("------------ 半品:{}, 中转库库存:{}", deliveryPlanProductCode, currentQuantitySum);
            }

            //【本厂编码】的物品类型为成品、【工序】为末道工序、【供应方式】为推式的材料
            // 获取工艺路径
            RoutingVO routingVO = routingVOMapOfProductCode.get(deliveryPlanProductCode);
            if (null == routingVO) {
                continue;
            }
            List<RoutingStepVO> stepVOList = routingStepGroupOfRoutingId.get(routingVO.getId());
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(stepVOList)) {
                continue;
            }
            // 获取末道工序, 顺序号最大的
            stepVOList = stepVOList.stream().filter(item -> null != item.getSequenceNo()).sorted(Comparator.comparing(RoutingStepVO::getSequenceNo).reversed()).collect(Collectors.toList());

            // 判断是否末道工序
            RoutingStepVO routingStepVO = stepVOList.get(0);
            if (null == routingStepVO) {
                continue;
            }
            // 获取末道工序输入物品
            List<RoutingStepInputVO> stepInputVOSList = routingStepInputGroupOfStepId.get(routingStepVO.getId());
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(stepInputVOSList)) {
                continue;
            }
            // 获取BOM类型为推式的，并且为采购件和B类
            List<RoutingStepInputVO> filterInputList = stepInputVOSList.stream().filter(item -> StringUtils.equals("推式", item.getSupplyType())).collect(Collectors.toList());
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(filterInputList)) {
                continue;
            }

            // 获取该半成品的日需求数据
            BigDecimal dayDemandQuantitySum = BigDecimal.ZERO;
//            String demandVersionId = deliveryPlanProductCodeToDemandVersionId.get(deliveryPlanProductCode);
//            if (StringUtils.isNotBlank(demandVersionId) && cleanDemandDataVoOfVersionId.containsKey(demandVersionId)) {
//                List<CleanDemandDataVO> productCleanDemandDataVOList = cleanDemandDataVoOfVersionId.get(demandVersionId);
//                // 获取日需求明细
//                List<String> dataIdList = productCleanDemandDataVOList.stream().map(CleanDemandDataVO::getId).collect(Collectors.toList());
//                for (String dataId : dataIdList) {
//                    List<CleanDemandDataDetailVO> cleanDemandDataDetailVOList = cleanDemandDataDetailMapOfDataId.get(dataId);
//                    if (com.yhl.platform.common.utils.CollectionUtils.isEmpty(cleanDemandDataDetailVOList)){
//                        continue;
//                    }
//                    // 获取demandQuantity
//                    // 引入临时变量以提高可读性
//                    List<BigDecimal> demandQuantities = cleanDemandDataDetailVOList
//                            .stream().map(CleanDemandDataDetailVO::getDemandQuantity)
//                            .filter(Objects::nonNull) // 过滤掉可能的 null 值
//                            .collect(Collectors.toList());
//
//                    // 使用单一流操作计算总和
//                    dayDemandQuantitySum = dayDemandQuantitySum.add(demandQuantities.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
//                }
//            }

            // 中转库材料的需求
            for (RoutingStepInputVO routingStepInputVO : filterInputList) {
                NewProductStockPointVO inputNewProductStockPointVO = productStockPointVOMapOfId.get(routingStepInputVO.getInputProductId());
                // 需要为采购件、并且是B类
                if (null == inputNewProductStockPointVO || !StringUtils.equals(inputNewProductStockPointVO.getProductType(), "P") || noGlassMaterialTypeList.stream().noneMatch(materialType -> inputNewProductStockPointVO.getProductClassify().matches(materialType + ".*"))) {
                    continue;
                }

                BigDecimal currentQuantity = currentQuantitySum.multiply(routingStepInputVO.getInputFactor());

                BigDecimal rawMaterialsCurrentQuantity;
                // 获取原材料的现有量
                rawMaterialsCurrentQuantity = Optional.ofNullable(inventoryBatchDetailVOMapOfProductCode.get(inputNewProductStockPointVO.getProductCode())).map(list -> list.stream().map(item -> new BigDecimal(item.getCurrentQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add)).orElse(BigDecimal.ZERO);
                // 原材料库存数量
                if (gapDemandQuantityMap.containsKey(inputNewProductStockPointVO.getProductCode())) {
                    BigDecimal bigDecimal = gapDemandQuantityMap.get(inputNewProductStockPointVO.getProductCode());
                    if (bigDecimal.compareTo(BigDecimal.ZERO) < 0) {
                        bigDecimal = bigDecimal.abs();
                        rawMaterialsCurrentQuantity = bigDecimal;
                    }
                }

                // 获取currentQuantity和demandQuantity的最小值
                BigDecimal minDemandQuantity = currentQuantity.min(rawMaterialsCurrentQuantity);
                if (StringUtils.equals("BLA2150G", inputNewProductStockPointVO.getProductCode())) {
                    log.info("&&&&&&&&&&&&&&&&&&& MRP计算中转库需求,半成品编码:{}, 半成品库存:{}, 需要原材料的库存:{}, 原材料库存:{}, 日需求数量:{}，最小库存数量:{}", deliveryPlanProductCode, currentQuantitySum, currentQuantity, rawMaterialsCurrentQuantity, dayDemandQuantitySum, minDemandQuantity);
                }
                if (dayDemandQuantitySum.compareTo(minDemandQuantity) <= 0) {
                    // 如果原材料库存大于半品库存，作为负缺口
                    if (rawMaterialsCurrentQuantity.compareTo(currentQuantitySum) > 0) {
                        gapDemandQuantityMap.put(inputNewProductStockPointVO.getProductCode(), currentQuantity.subtract(rawMaterialsCurrentQuantity));
                    } else {
                        // 产生库存
                        // rawMaterialsCurrentQuantity 取绝对值
                        gapDemandQuantityMap.put(inputNewProductStockPointVO.getProductCode(), rawMaterialsCurrentQuantity.abs());
                    }
                    // 则不产生缺口
                    continue;
                }

                if (rawMaterialsCurrentQuantity.compareTo(BigDecimal.ZERO) == 0) {
                    // 产生缺口
                    gapDemandQuantityMap.put(inputNewProductStockPointVO.getProductCode(), currentQuantity);
                } else {
                    // 缺口 = 半成品库存 - 原材料库存
                    BigDecimal demandQuantity = currentQuantity.subtract(rawMaterialsCurrentQuantity);
                    gapDemandQuantityMap.put(inputNewProductStockPointVO.getProductCode(), demandQuantity);
                }
            }
        }

        // 获取发货计划
        List<TransferWarehouseDemandDTO> transferWarehouseDemandDTOList = new ArrayList<>();
        Map<String, List<DeliveryPlanPublishedVO>> collect = deliveryPlanFuture30Days.stream().collect(Collectors.groupingBy(DeliveryPlanPublishedVO::getProductCode));
        for (Map.Entry<String, List<DeliveryPlanPublishedVO>> entry : collect.entrySet()) {
            String productCode = entry.getKey();
            List<DeliveryPlanPublishedVO> value = entry.getValue();
            NewProductStockPointVO newProductStockPointVO = productStockPointVOMapOfProductCode.get(productCode);
            if (null == newProductStockPointVO) {
                continue;
            }
            // 需要汇总所有原材料的需求
            for (DeliveryPlanPublishedVO deliveryPlanDetailVO : value) {
                if (productCode.equals("00393LRF00006")) {
                    log.info("########  中转库需求, 发货计划物品:{}, 发货计划时间:{}, 发货计划数量:{}", productCode, DateUtils.dateToString(deliveryPlanDetailVO.getDemandTime()), deliveryPlanDetailVO.getDemandQuantity());
                }
                if (deliveryPlanDetailVO.getDemandQuantity() > 0) {
                    // 发货计划与中转库需求扣减
                    Date date = lastDeliveryPlanTimeMapOfProductCode.get(productCode);
                    if (null != date && date.compareTo(deliveryPlanDetailVO.getDemandTime()) > 0) {
                        lastDeliveryPlanTimeMapOfProductCode.put(productCode, date);
                    } else {
                        lastDeliveryPlanTimeMapOfProductCode.put(productCode, deliveryPlanDetailVO.getDemandTime());
                    }
                }
                // 根据发货计划拆解BOM,拆出推式的原材料需求，推式材料具体的需求时间 = 发货计划时间 - 中转提前期
                getTransitDepotsDemandData(newProductStockPointVO, deliveryPlanDetailVO.getDemandQuantity(), deliveryPlanDetailVO.getDemandTime(), routingVOMapOfProductCode, routingStepGroupOfRoutingId, routingStepInputGroupOfStepId, productStockPointVOMapOfId, transferWarehouseDemandDTOList);
            }
        }


        if (CollectionUtils.isEmpty(transferWarehouseDemandDTOList)) {
            log.info("毛需求计算，没有需要生成的中转库需求");
            return;
        }

        // 原材料需求数量汇总，生成MRP中转库需求
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        // 按照原材料分组中转库需求
        Map<String, List<TransferWarehouseDemandDTO>> collect1 = transferWarehouseDemandDTOList.stream().collect(Collectors.groupingBy(TransferWarehouseDemandDTO::getProductCode));

        for (Map.Entry<String, List<TransferWarehouseDemandDTO>> entry : collect1.entrySet()) {
            String productCode = entry.getKey();
            List<TransferWarehouseDemandDTO> value = entry.getValue();
            String productClassify = value.get(0).getProductClassify();
            String productFactoryCode = value.get(0).getProductFactoryCode();
            String vehicleModeCode = value.get(0).getVehicleModeCode();
            BigDecimal inputFactor = value.get(0).getInputFactor();
            String productId = value.get(0).getProductId();
            String productName = value.get(0).getProductName();
            // 获取原材料缺口
            BigDecimal gapDemandQuantity = gapDemandQuantityMap.get(productCode);

            // 使用Map根据demandTime分组，并累加quantity
            Map<String, BigDecimal> mergedQuantities = value.stream().collect(Collectors.groupingBy(item -> DateUtils.dateToString(item.getDemandTime()), Collectors.reducing(BigDecimal.ZERO, TransferWarehouseDemandDTO::getDemandQuantity, BigDecimal::add)));
            if ("BLA2150G".equals(productCode)) {
                for (Map.Entry<String, BigDecimal> decimalEntry : mergedQuantities.entrySet()) {
                    log.info("######## 生成中转库需求,原材料;{}, 需求时间:{}, 需求数量:{}", productCode, decimalEntry.getKey(), decimalEntry.getValue());
                }
            }

            // 将Map转换为List并排序
            List<Map.Entry<String, BigDecimal>> sortedEntries = new ArrayList<>(mergedQuantities.entrySet());
            sortedEntries.sort(Comparator.comparing(e -> {
                try {
                    return dateFormat.parse(e.getKey());
                } catch (ParseException ex) {
                    throw new RuntimeException(ex);
                }
            }));

            for (Map.Entry<String, BigDecimal> sortedEntry : sortedEntries) {
                String demandDate = sortedEntry.getKey();
                BigDecimal demandQuantity = sortedEntry.getValue();
                if ("BLA2150G".equals(productCode)) {
                    log.info("######## MRP计算中转库需求,原材料:{}, 需求时间:{}, 需求数量:{},缺口数量:{}", productCode, demandDate, demandQuantity, gapDemandQuantity);
                }
                if (null != gapDemandQuantity && gapDemandQuantity.compareTo(BigDecimal.ZERO) != 0) {
                    // 计算差异量
                    BigDecimal needDemandQuantity = demandQuantity.add(gapDemandQuantity);
                    if (needDemandQuantity.compareTo(BigDecimal.ZERO) > 0) {
                        // 生成中转库需求
                        MaterialGrossDemandPO materialGrossDemandPO = createMaterialGrossDemandDTO(productCode, productClassify, needDemandQuantity, DateUtils.stringToDate(demandDate), productFactoryCode, vehicleModeCode, inputFactor, productId, productName);
                        insertGrossDemandList.add(materialGrossDemandPO);
                    }
                    // 计算新的缺口
                    if (gapDemandQuantity.compareTo(BigDecimal.ZERO) < 0) {
                        gapDemandQuantity = gapDemandQuantity.add(demandQuantity);
                        gapDemandQuantity = gapDemandQuantity.compareTo(BigDecimal.ZERO) > 0 ? BigDecimal.ZERO : gapDemandQuantity;
                    } else {
                        gapDemandQuantity = BigDecimal.ZERO;
                    }
                } else {
                    if (demandQuantity.compareTo(BigDecimal.ZERO) > 0) {
                        // 生成中转库需求
                        MaterialGrossDemandPO materialGrossDemandPO = createMaterialGrossDemandDTO(productCode, productClassify, demandQuantity, DateUtils.stringToDate(demandDate), productFactoryCode, vehicleModeCode, inputFactor, productId, productName);
                        insertGrossDemandList.add(materialGrossDemandPO);
                    }
                }
            }
        }
    }


    // 根据BOM拆解成品所需原材料需求数量(中转库)
    private void getTransitDepotsDemandData(NewProductStockPointVO productStockPointVO, Integer demandQuantityInt, Date demandTime, Map<String, RoutingVO> routingVOMap, Map<String, List<RoutingStepVO>> routingStepGroup, Map<String, List<RoutingStepInputVO>> routingStepInputGroup, Map<String, NewProductStockPointVO> newProductStockPointVOMapOfId, List<TransferWarehouseDemandDTO> transferWarehouseDemandDTOList) {
        // 生成中转库材料需求
        getTransitDepotsDemand(productStockPointVO, routingVOMap, routingStepGroup, routingStepInputGroup, demandQuantityInt, demandTime, newProductStockPointVOMapOfId, transferWarehouseDemandDTOList);
    }

    private List<RoutingStepVO> getTransitDepotsDemand(NewProductStockPointVO productStockPointVO, Map<String, RoutingVO> routingVOMap, Map<String, List<RoutingStepVO>> routingStepGroup, Map<String, List<RoutingStepInputVO>> routingStepInputGroup, Integer demandQuantityInt, Date demandDate, Map<String, NewProductStockPointVO> newProductStockPointVOMapOfId, List<TransferWarehouseDemandDTO> transferWarehouseDemandDTOList) {
        // 根据发货计划拆解BOM,拆出推式的原材料需求，推式材料具体的需求时间 = 发货计划时间 - 中转提前期
        RoutingVO routingVO = routingVOMap.get(productStockPointVO.getProductCode());
        if (null == routingVO) {
            return null;
        }
        // 获取路径步骤
        List<RoutingStepVO> routingStepVOS = routingStepGroup.get(routingVO.getId());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(routingStepVOS)) {
            return null;
        }
        // 获取末道步骤
        RoutingStepVO routingStepVO = routingStepVOS.stream().sorted(Comparator.comparing(RoutingStepVO::getSequenceNo)).collect(Collectors.toList()).get(routingStepVOS.size() - 1);
        // 获取输入物品
        List<RoutingStepInputVO> stepInputVOS = routingStepInputGroup.get(routingStepVO.getId());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(stepInputVOS)) {
            return routingStepVOS;
        }
        // 过滤出BOM类型为推式的
        List<RoutingStepInputVO> pushInputList = stepInputVOS.stream().filter(item -> StringUtils.equals("推式", item.getSupplyType())).collect(Collectors.toList());

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(pushInputList)) {
            for (RoutingStepInputVO routingStepInputVO : pushInputList) {
                NewProductStockPointVO inputProductStockPointVO = newProductStockPointVOMapOfId.get(routingStepInputVO.getInputProductId());
                // 需要为采购件、并且是B类
                if (null == inputProductStockPointVO || !StringUtils.equals(inputProductStockPointVO.getProductType(), "P") || noGlassMaterialTypeList.stream().noneMatch(materialType -> inputProductStockPointVO.getProductClassify().matches(materialType + ".*"))) {
                    continue;
                }

                if (null == routingStepInputVO.getYield()) {
                    routingStepInputVO.setYield(BigDecimal.ONE);
                }
                if (null == routingStepInputVO.getInputFactor()) {
                    routingStepInputVO.setInputFactor(BigDecimal.ONE);
                }

                TransferWarehouseDemandDTO transferWarehouseDemandDTO = TransferWarehouseDemandDTO.builder().productId(inputProductStockPointVO.getId()).productCode(inputProductStockPointVO.getProductCode()).productName(inputProductStockPointVO.getProductName()).productClassify(inputProductStockPointVO.getProductClassify()).productFactoryCode(productStockPointVO.getProductCode()).vehicleModeCode(productStockPointVO.getVehicleModelCode()).inputFactor(routingStepInputVO.getInputFactor()).demandTime(demandDate)
                        // 需求数量
                        .demandQuantity(new BigDecimal(demandQuantityInt).multiply(routingStepInputVO.getInputFactor()).divide(routingStepInputVO.getYield(), RoundingMode.CEILING)).build();
                transferWarehouseDemandDTOList.add(transferWarehouseDemandDTO);
            }
        }
        return routingStepVOS;
    }

    protected MaterialGrossDemandPO createMaterialGrossDemandDTO(String productCode, String productClassify, BigDecimal demandQuantity, Date demandDate, String productFactoryCode, String vehicleModeCode, BigDecimal inputFactor, String productId, String productName) {
        String productCategory = getProductCategory(productClassify);
        MaterialGrossDemandPO materialGrossDemandPO = new MaterialGrossDemandPO();
        materialGrossDemandPO.setId(UUID.randomUUID().toString());
        materialGrossDemandPO.setProductId(productId);
        materialGrossDemandPO.setProductCode(productCode);
        materialGrossDemandPO.setProductName(productName);
        materialGrossDemandPO.setProductClassify(productClassify);
        materialGrossDemandPO.setProductCategory(productCategory);
        materialGrossDemandPO.setDemandTime(demandDate);
        materialGrossDemandPO.setDemandQuantity(demandQuantity);
        materialGrossDemandPO.setProductFactoryCode(productFactoryCode);
        materialGrossDemandPO.setVehicleModeCode(vehicleModeCode);
        materialGrossDemandPO.setInputFactor(inputFactor);
        materialGrossDemandPO.setUnFulfillmentQuantity(demandQuantity);
        materialGrossDemandPO.setDemandSource(MrpDemandSourceEnum.ZZK.getCode());
        return materialGrossDemandPO;
    }

    private void getMcbGrossDemand(List<MaterialGrossDemandPO> insertGrossDemandList, GrossDemandContextDTO grossDemandContextDTO) {
        // 获取最晚发货计划日期往后的365天的产能平衡数据
        Map<String, Date> lastDeliveryPlanTimeMapOfProductCode = new HashMap<>();
        if (MapUtils.isNotEmpty(grossDemandContextDTO.getLastDeliveryPlanTimeMapOfProductCode())) {
            lastDeliveryPlanTimeMapOfProductCode = grossDemandContextDTO.getLastDeliveryPlanTimeMapOfProductCode();
        }
        String scenario = grossDemandContextDTO.getScenario();
        Date mrpCalcDate = grossDemandContextDTO.getMrpCalcDate();
        Map<String, NewProductStockPointVO> productStockPointVOMapOfId = grossDemandContextDTO.getProductStockPointVOMapOfId();
        Map<String, NewProductStockPointVO> productStockPointVOMapOfProductCode = grossDemandContextDTO.getProductStockPointVOMapOfProductCode();


        CapacityBalanceVersionVO capacityBalanceVersionVO = grossDemandContextDTO.getCapacityBalanceVersionVO();
        Map<String, Object> params = new HashMap<>();
        params.put("versionId", capacityBalanceVersionVO.getVersionCode());
        params.put("supplyModel", SupplyModelEnum.LOCAL.getCode());
        List<CapacitySupplyRelationshipVO> capacitySupplyRelationshipVOList = mpsFeign.selectCapacitySupplyRelationshipByParams(scenario, params);
        // 查询工艺路径步骤输入物品
//        List<String> routingStepIdList = capacitySupplyRelationshipVOList.stream().map(CapacitySupplyRelationshipVO::getRoutingStepId).distinct().collect(Collectors.toList());
//        List<RoutingStepInputVO> stepInputVOList = new ArrayList<>();
//        Lists.partition(routingStepIdList, 1000).forEach(idList -> stepInputVOList.addAll(mdsFeign.selectInputByRoutingStepIds(scenario, idList)));
//        Map<String, List<RoutingStepInputVO>> routingStepInputVOGroupOfStepId = stepInputVOList.stream().collect(Collectors.groupingBy(RoutingStepInputVO::getRoutingStepId));
        Map<String, List<RoutingStepInputVO>> routingStepInputVOGroupOfStepId = grossDemandContextDTO.getRoutingStepInputGroupOfStepId();

        if (CollectionUtils.isEmpty(capacitySupplyRelationshipVOList)) {
            return;
        }

        Map<String, List<CapacitySupplyRelationshipVO>> capacitySupplyRelationshipVoGroup = capacitySupplyRelationshipVOList.stream().collect(Collectors.groupingBy(CapacitySupplyRelationshipVO::getProductCode));

        for (Map.Entry<String, List<CapacitySupplyRelationshipVO>> entry : capacitySupplyRelationshipVoGroup.entrySet()) {
            String productCode = entry.getKey();
            List<CapacitySupplyRelationshipVO> value = entry.getValue();
            if ("00907LFW00006".equals(productCode)) {
                log.info("产能平衡物品:{}, 数据量:{}", productCode, value.size());
            }
            // 获取发货计划满足的时间
            Date lastDeliveryPlanTime = lastDeliveryPlanTimeMapOfProductCode.get(productCode);
            if (lastDeliveryPlanTime == null) {
                lastDeliveryPlanTime = mrpCalcDate;
            }
            Date moveDay = DateUtils.moveDay(lastDeliveryPlanTime, 365);
            Date finalLastDeliveryPlanTime = lastDeliveryPlanTime;
            value = value.stream().filter(item -> item.getForecastTime().compareTo(finalLastDeliveryPlanTime) >= 0 && item.getForecastTime().compareTo(moveDay) <= 0).collect(Collectors.toList());
            if ("00907LFW00006".equals(productCode)) {
                log.info("产能平衡物品:{}, 开始时间:{}, 结束时间:{}, 过滤后数量:{}", productCode, DateUtils.dateToString(finalLastDeliveryPlanTime), DateUtils.dateToString(moveDay), value.size());
            }
            if (CollectionUtils.isEmpty(value)) {
                continue;
            }
            NewProductStockPointVO productStockPointOfBc = productStockPointVOMapOfProductCode.get(productCode);

            for (CapacitySupplyRelationshipVO capacitySupplyRelationshipVO : value) {
                List<RoutingStepInputVO> stepInputVOS = routingStepInputVOGroupOfStepId.get(capacitySupplyRelationshipVO.getRoutingStepId());
                if (com.yhl.platform.common.utils.CollectionUtils.isEmpty(stepInputVOS)) {
                    continue;
                }
                for (RoutingStepInputVO stepInputVO : stepInputVOS) {
                    NewProductStockPointVO inputStockPointVO = productStockPointVOMapOfId.get(stepInputVO.getInputProductId());
                    if (null == inputStockPointVO || !StringUtils.equals("P", inputStockPointVO.getProductType())) {
                        continue;
                    }
                    String productCategory = getProductCategory(inputStockPointVO.getProductClassify());
                    MaterialGrossDemandPO materialGrossDemandPO = new MaterialGrossDemandPO();
                    materialGrossDemandPO.setId(UUID.randomUUID().toString());
                    materialGrossDemandPO.setProductId(inputStockPointVO.getId());
                    materialGrossDemandPO.setProductCode(inputStockPointVO.getProductCode());
                    materialGrossDemandPO.setProductName(inputStockPointVO.getProductName());
                    materialGrossDemandPO.setProductClassify(inputStockPointVO.getProductClassify());
                    materialGrossDemandPO.setProductCategory(productCategory);
                    materialGrossDemandPO.setProductFactoryCode(productCode);
                    materialGrossDemandPO.setVehicleModeCode(null != productStockPointOfBc ? productStockPointOfBc.getVehicleModelCode() : null);
                    materialGrossDemandPO.setInputFactor(stepInputVO.getInputFactor());
                    materialGrossDemandPO.setDemandTime(capacitySupplyRelationshipVO.getForecastTime());
                    materialGrossDemandPO.setDemandQuantity(capacitySupplyRelationshipVO.getDemandQuantity().multiply(stepInputVO.getInputFactor()));
                    materialGrossDemandPO.setDemandSource(MrpDemandSourceEnum.MCB.getCode());
                    insertGrossDemandList.add(materialGrossDemandPO);
                }
            }
        }
    }

    private static Date getLastDeliveryPlanTime(List<WorkOrderVO> fgWorkOrderList, List<DeliveryPlanPublishedVO> deliveryPlanDetailVOSAfter) {
        BigDecimal lastWorkOrderQty = fgWorkOrderList.get(fgWorkOrderList.size() - 1).getQuantity();
        BigDecimal totalDeliveryPlanQty = BigDecimal.ZERO;
        Date lastDeliveryPlanTime = null;
        for (DeliveryPlanPublishedVO deliveryPlanDetailVO : deliveryPlanDetailVOSAfter) {
            totalDeliveryPlanQty = totalDeliveryPlanQty.add(BigDecimal.valueOf(deliveryPlanDetailVO.getDemandQuantity()));
            if (totalDeliveryPlanQty.compareTo(lastWorkOrderQty) >= 0) {
                lastDeliveryPlanTime = deliveryPlanDetailVO.getDemandTime();
            }
        }
        return lastDeliveryPlanTime;
    }

    private static Date getLastDeliveryPlanTime2(List<CapacitySupplyRelationshipVO> list, List<DeliveryPlanPublishedVO> deliveryPlanDetailVOSAfter) {
        BigDecimal lastSupplyQuantity = list.get(list.size() - 1).getSupplyQuantity();
        BigDecimal totalDeliveryPlanQty = BigDecimal.ZERO;
        Date lastDeliveryPlanTime = null;
        for (DeliveryPlanPublishedVO deliveryPlanDetailVO : deliveryPlanDetailVOSAfter) {
            totalDeliveryPlanQty = totalDeliveryPlanQty.add(BigDecimal.valueOf(deliveryPlanDetailVO.getDemandQuantity()));
            if (totalDeliveryPlanQty.compareTo(lastSupplyQuantity) >= 0) {
                lastDeliveryPlanTime = deliveryPlanDetailVO.getDemandTime();
            }
        }
        return lastDeliveryPlanTime;
    }

    @SneakyThrows
    @Override
    public void exportTemplate(HttpServletResponse response) {
        ServletOutputStream out = response.getOutputStream();
        EasyExcelUtil.initResponse(response, "材料毛需求导入模板");
        List<List<String>> headers = Lists.newArrayList();
        headers.add(Collections.singletonList("材料编码*"));
        headers.add(Collections.singletonList("材料名称*"));
        headers.add(Collections.singletonList("物料类别(PVB、B、RA.A)*"));
        headers.add(Collections.singletonList("通用类别*"));
        headers.add(Collections.singletonList("本厂编码"));
        headers.add(Collections.singletonList("车型编码"));
        Date moveDate = DateUtils.moveDay(new Date(), 30);
        List<Date> intervalDates = DateUtils.getIntervalDates(new Date(), moveDate);
        intervalDates.forEach(day -> headers.add(Collections.singletonList(DateUtils.dateToString(day, DateUtils.COMMON_DATE_STR3))));
        EasyExcel.write(out).sheet("材料毛需求").head(headers).registerWriteHandler(new CustomColumnWidthHandler()).doWrite(Collections.emptyList());
    }

    @Override
    public BaseResponse<Void> doImportGrossDemand(MultipartFile file) {
        Map<String, String> extMap = MapUtil.newHashMap();
        try {
            if (file.isEmpty()) {
                throw new BusinessException("文件为空");
            }
            ExcelDynamicDataListener excelDynamicDataListener = new ExcelDynamicDataListener(this, 6, 0, extMap);
            EasyExcel.read(file.getInputStream(), excelDynamicDataListener).sheet().doReadSync();
        } catch (Exception e) {
            log.error("材料毛需求导入失败:", e);
            throw new BusinessException("导入失败, {0}", e.getLocalizedMessage());
        }
        String successInfo = extMap.get("successInfo");
        String errorInfo = extMap.get("errorInfo");
        if (StringUtils.isBlank(errorInfo)) {
            return BaseResponse.success(successInfo);
        } else {
            return BaseResponse.error(successInfo + errorInfo);
        }
    }

    @Override
    public void doImport(Map<Integer, String> headers, List<Map<Integer, String>> data, int fixedColumns, Map<String, String> extMap) {
        List<String> collect = new ArrayList<>(headers.values());
        if (!collect.contains("材料编码*") || !collect.contains("材料名称*") || !collect.contains("物料类别(PVB、B、RA.A)*") || !collect.contains("通用类别*")) {
            throw new BusinessException("导入数据模板错误，请检查");
        }
        if (CollectionUtils.isEmpty(data)) {
            throw new BusinessException("模板数据为空,没有数据");
        }
        String contentType = extMap.get("contentType");
        materialGrossDemandComponentHandler.handle(contentType, headers, data, extMap);
    }

    @Override
    public void doDeleteByProductCodeList(List<String> productCodeList) {
        materialGrossDemandDao.deleteByProductCodeList(productCodeList);
    }

    @Override
    public void exportMaterialGrossDemand(HttpServletResponse response, MaterialGrossDemandParam materialGrossDemandParam) {
        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("物料毛需求导出", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            // 查询最新的版本
            MaterialGrossDemandVersionPO materialGrossDemandVersionPO = materialGrossDemandVersionDao.selectLastVersion();

            List<MaterialGrossDemandVO> list = this.selectByParams(ImmutableMap.of("materialGrossDemandVersionId", materialGrossDemandVersionPO.getId()));
            // 按唯一分组键（本厂编码+物料编码+数据来源）分组
            Map<String, List<MaterialGrossDemandVO>> collect = list.stream()
                    .collect(Collectors.groupingBy(item ->
                            String.join("#",
                                    item.getProductFactoryCode(),
                                    item.getProductCode(),
                                    item.getDemandSource()
                            )
                    ));

            // 动态日期列：提取所有日期并排序
            List<String> dateColumns = list.stream()
                    .map(item -> DateUtils.dateToString(item.getDemandTime(), DateUtils.COMMON_DATE_STR3))
                    .distinct()
                    .sorted()
                    .collect(Collectors.toList());

            // 构建表头（固定列+动态日期列）
            List<List<String>> head = new ArrayList<>();
            head.add(Collections.singletonList("物料"));
            head.add(Collections.singletonList("物料名称"));
            head.add(Collections.singletonList("物料类型"));
            head.add(Collections.singletonList("本厂编码"));
            head.add(Collections.singletonList("数据来源"));
            dateColumns.forEach(date -> head.add(Collections.singletonList(date)));

            // 构建数据行
            List<List<Object>> data = new ArrayList<>();
            for (Map.Entry<String, List<MaterialGrossDemandVO>> entry : collect.entrySet()) {
                List<MaterialGrossDemandVO> groupItems = entry.getValue();
                // 创建一行数据
                List<Object> row = new ArrayList<>();

                // 固定列数据（取分组中的第一条）
                MaterialGrossDemandVO firstItem = groupItems.get(0);
                row.add(firstItem.getProductCode());
                row.add(firstItem.getProductName());
                row.add(firstItem.getProductCategory());
                row.add(firstItem.getProductFactoryCode());
                row.add(MrpDemandSourceEnum.getDescByCode(firstItem.getDemandSource()));

                // 将当前分组的日期-需求量转换为Map，方便快速查找
                Map<String, BigDecimal> dateToDemand = groupItems.stream()
                        .collect(Collectors.toMap(
                                item -> DateUtils.dateToString(item.getDemandTime(), DateUtils.COMMON_DATE_STR3),
                                MaterialGrossDemandVO::getDemandQuantity,
                                (v1, v2) -> v1  // 如果日期重复，取第一个值
                        ));

                // 填充动态日期列
                for (String date : dateColumns) {
                    BigDecimal demand = dateToDemand.getOrDefault(date, BigDecimal.ZERO);
                    row.add(demand);
                }

                data.add(row); // 整行添加一次
            }

            // 使用EasyExcel导出
            EasyExcel.write(response.getOutputStream())
                    .head(head)
                    .registerConverter(new BigDecimalConverter())
                    .sheet("物料毛需求")
                    .doWrite(data);
        } catch (IOException e) {
            throw new BusinessException(e.getMessage());
        }
    }

    @Override
    public BaseResponse<Void> checkCapacityBalancePublishDate(String scenario) {
        // 最新一致性预测需求版本的发布时间
        ConsistenceDemandForecastVersionVO consistenceDemandForecastVersionVO = dfpFeign.selectConsistenceDemandForecastVersionLatestPublished(scenario);
        // 最新产能平衡的发布时间
        String capacityBalancePublishDateStr = mpsFeign.selectWeekMaxVersionTime(scenario);

        // 获取一致性需求预测的发布时间、产能平衡的发布时间, 时间相差24小时则提醒
        if (null == consistenceDemandForecastVersionVO && "暂无".contains(capacityBalancePublishDateStr)) {
            return BaseResponse.error("最新的一致性需求预测版本，没有进行产能平衡计算发布，请与生产计划及时确");
        }

        if (consistenceDemandForecastVersionVO != null && !"暂无".contains(capacityBalancePublishDateStr)) {
            Date consistenceDemandForecastPublishDate = consistenceDemandForecastVersionVO.getModifyTime();
            // capacityBalancePublishDateStr去除第一个和最后一个双引号
            capacityBalancePublishDateStr = capacityBalancePublishDateStr.substring(1, capacityBalancePublishDateStr.length() - 1);
            Date capacityBalancePublishDate = DateUtils.stringToDate(capacityBalancePublishDateStr, DateUtils.COMMON_DATE_STR1);
            if (capacityBalancePublishDate.compareTo(consistenceDemandForecastPublishDate) >= 0) {
                return BaseResponse.success();
            }
            if (DateUtils.getDateInterval(consistenceDemandForecastPublishDate, capacityBalancePublishDate) > 1) {
                return BaseResponse.error(String.format("最新的一致性需求预测版本是%s，没有进行产能平衡计算发布，请与生产计划及时确认！", DateUtils.dateToString(consistenceDemandForecastVersionVO.getModifyTime(), DateUtils.COMMON_DATE_STR3)));
            }
        }
        return BaseResponse.success();
    }
}