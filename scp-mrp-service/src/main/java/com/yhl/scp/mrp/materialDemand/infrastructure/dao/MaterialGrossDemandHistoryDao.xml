<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.materialDemand.infrastructure.dao.MaterialGrossDemandHistoryDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.materialDemand.infrastructure.po.MaterialGrossDemandHistoryPO">
        <!--@Table mrp_material_gross_demand_history-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="material_gross_demand_version_id" jdbcType="VARCHAR" property="materialGrossDemandVersionId"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="product_classify" jdbcType="VARCHAR" property="productClassify"/>
        <result column="product_category" jdbcType="VARCHAR" property="productCategory"/>
        <result column="product_factory_code" jdbcType="VARCHAR" property="productFactoryCode"/>
        <result column="vehicle_mode_code" jdbcType="VARCHAR" property="vehicleModeCode"/>
        <result column="input_factor" jdbcType="VARCHAR" property="inputFactor"/>
        <result column="demand_time" jdbcType="TIMESTAMP" property="demandTime"/>
        <result column="demand_quantity" jdbcType="VARCHAR" property="demandQuantity"/>
        <result column="un_fulfillment_quantity" jdbcType="VARCHAR" property="unFulfillmentQuantity"/>
        <result column="operation_code" jdbcType="VARCHAR" property="operationCode"/>
        <result column="supply_model" jdbcType="VARCHAR" property="supplyModel"/>
        <result column="product_color" jdbcType="VARCHAR" property="productColor"/>
        <result column="product_thickness" jdbcType="VARCHAR" property="productThickness"/>
        <result column="demand_source" jdbcType="VARCHAR" property="demandSource"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandHistoryVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,material_gross_demand_version_id,product_code,product_id,product_name,product_classify,product_category,product_factory_code,vehicle_mode_code,input_factor,demand_time,demand_quantity,un_fulfillment_quantity,operation_code,supply_model,product_color,product_thickness,demand_source,remark,enabled,creator,create_time,modifier,modify_time

    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.materialGrossDemandVersionId != null and params.materialGrossDemandVersionId != ''">
                and material_gross_demand_version_id = #{params.materialGrossDemandVersionId,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productId != null and params.productId != ''">
                and product_id = #{params.productId,jdbcType=VARCHAR}
            </if>
            <if test="params.productName != null and params.productName != ''">
                and product_name = #{params.productName,jdbcType=VARCHAR}
            </if>
            <if test="params.productClassify != null and params.productClassify != ''">
                and product_classify = #{params.productClassify,jdbcType=VARCHAR}
            </if>
            <if test="params.productCategory != null and params.productCategory != ''">
                and product_category = #{params.productCategory,jdbcType=VARCHAR}
            </if>
            <if test="params.productFactoryCode != null and params.productFactoryCode != ''">
                and product_factory_code = #{params.productFactoryCode,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleModeCode != null and params.vehicleModeCode != ''">
                and vehicle_mode_code = #{params.vehicleModeCode,jdbcType=VARCHAR}
            </if>
            <if test="params.inputFactor != null">
                and input_factor = #{params.inputFactor,jdbcType=VARCHAR}
            </if>
            <if test="params.demandTime != null">
                and demand_time = #{params.demandTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.demandQuantity != null">
                and demand_quantity = #{params.demandQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.unFulfillmentQuantity != null">
                and un_fulfillment_quantity = #{params.unFulfillmentQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.operationCode != null and params.operationCode != ''">
                and operation_code = #{params.operationCode,jdbcType=VARCHAR}
            </if>
            <if test="params.supplyModel != null and params.supplyModel != ''">
                and supply_model = #{params.supplyModel,jdbcType=VARCHAR}
            </if>
            <if test="params.productColor != null and params.productColor != ''">
                and product_color = #{params.productColor,jdbcType=VARCHAR}
            </if>
            <if test="params.productThickness != null">
                and product_thickness = #{params.productThickness,jdbcType=VARCHAR}
            </if>
            <if test="params.demandSource != null and params.demandSource != ''">
                and demand_source = #{params.demandSource,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.productCodeList != null and params.productCodeList.size() > 0">
                and product_code in
                <foreach collection="params.productCodeList" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productCategoryList != null and params.productCategoryList.size() > 0">
                and product_category in
                <foreach collection="params.productCategoryList" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.startDate != null">
                and demand_time &gt;= #{params.startDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.endDate != null">
                and demand_time &lt;= #{params.endDate,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_gross_demand_history
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_gross_demand_history
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from mrp_material_gross_demand_history
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_gross_demand_history
        <include refid="Base_Where_Condition"/>
    </select>

    <select id="selectVOByParams" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from mrp_material_gross_demand_history
        <include refid="Base_Where_Condition"/>
    </select>

    <select id="selectGroupByParams" resultMap="VOResultMap">
        select
        product_factory_code,product_code
        from mrp_material_gross_demand_history
        <include refid="Base_Where_Condition"/>
        group by product_factory_code,product_code
    </select>

    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.materialDemand.infrastructure.po.MaterialGrossDemandHistoryPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_material_gross_demand_history(
        id,
        material_gross_demand_version_id,
        product_code,
        product_id,
        product_name,
        product_classify,
        product_category,
        product_factory_code,
        vehicle_mode_code,
        input_factor,
        demand_time,
        demand_quantity,
        un_fulfillment_quantity,
        operation_code,
        supply_model,
        product_color,
        product_thickness,
        demand_source,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{materialGrossDemandVersionId,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{productId,jdbcType=VARCHAR},
        #{productName,jdbcType=VARCHAR},
        #{productClassify,jdbcType=VARCHAR},
        #{productCategory,jdbcType=VARCHAR},
        #{productFactoryCode,jdbcType=VARCHAR},
        #{vehicleModeCode,jdbcType=VARCHAR},
        #{inputFactor,jdbcType=VARCHAR},
        #{demandTime,jdbcType=TIMESTAMP},
        #{demandQuantity,jdbcType=VARCHAR},
        #{unFulfillmentQuantity,jdbcType=VARCHAR},
        #{operationCode,jdbcType=VARCHAR},
        #{supplyModel,jdbcType=VARCHAR},
        #{productColor,jdbcType=VARCHAR},
        #{productThickness,jdbcType=VARCHAR},
        #{demandSource,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.materialDemand.infrastructure.po.MaterialGrossDemandHistoryPO">
        insert into mrp_material_gross_demand_history(id,
                                                     material_gross_demand_version_id,
                                                     product_code,
                                                     product_id,
                                                     product_name,
                                                     product_classify,
                                                     product_category,
                                                     product_factory_code,
                                                     vehicle_mode_code,
                                                     input_factor,
                                                     demand_time,
                                                     demand_quantity,
                                                     un_fulfillment_quantity,
                                                     operation_code,
                                                     supply_model,
                                                     product_color,
                                                     product_thickness,
                                                     demand_source,
                                                     remark,
                                                     enabled,
                                                     creator,
                                                     create_time,
                                                     modifier,
                                                     modify_time)
        values (#{id,jdbcType=VARCHAR},
                #{materialGrossDemandVersionId,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{productId,jdbcType=VARCHAR},
                #{productName,jdbcType=VARCHAR},
                #{productClassify,jdbcType=VARCHAR},
                #{productCategory,jdbcType=VARCHAR},
                #{productFactoryCode,jdbcType=VARCHAR},
                #{vehicleModeCode,jdbcType=VARCHAR},
                #{inputFactor,jdbcType=VARCHAR},
                #{demandTime,jdbcType=TIMESTAMP},
                #{demandQuantity,jdbcType=VARCHAR},
                #{unFulfillmentQuantity,jdbcType=VARCHAR},
                #{operationCode,jdbcType=VARCHAR},
                #{supplyModel,jdbcType=VARCHAR},
                #{productColor,jdbcType=VARCHAR},
                #{productThickness,jdbcType=VARCHAR},
                #{demandSource,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_material_gross_demand_history(
        id,
        material_gross_demand_version_id,
        product_code,
        product_id,
        product_name,
        product_classify,
        product_category,
        product_factory_code,
        vehicle_mode_code,
        input_factor,
        demand_time,
        demand_quantity,
        un_fulfillment_quantity,
        operation_code,
        supply_model,
        product_color,
        product_thickness,
        demand_source,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.materialGrossDemandVersionId,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.productId,jdbcType=VARCHAR},
            #{entity.productName,jdbcType=VARCHAR},
            #{entity.productClassify,jdbcType=VARCHAR},
            #{entity.productCategory,jdbcType=VARCHAR},
            #{entity.productFactoryCode,jdbcType=VARCHAR},
            #{entity.vehicleModeCode,jdbcType=VARCHAR},
            #{entity.inputFactor,jdbcType=VARCHAR},
            #{entity.demandTime,jdbcType=TIMESTAMP},
            #{entity.demandQuantity,jdbcType=VARCHAR},
            #{entity.unFulfillmentQuantity,jdbcType=VARCHAR},
            #{entity.operationCode,jdbcType=VARCHAR},
            #{entity.supplyModel,jdbcType=VARCHAR},
            #{entity.productColor,jdbcType=VARCHAR},
            #{entity.productThickness,jdbcType=VARCHAR},
            #{entity.demandSource,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_material_gross_demand_history(
        id,
        material_gross_demand_version_id,
        product_code,
        product_id,
        product_name,
        product_classify,
        product_category,
        product_factory_code,
        vehicle_mode_code,
        input_factor,
        demand_time,
        demand_quantity,
        un_fulfillment_quantity,
        operation_code,
        supply_model,
        product_color,
        product_thickness,
        demand_source,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.materialGrossDemandVersionId,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.productId,jdbcType=VARCHAR},
            #{entity.productName,jdbcType=VARCHAR},
            #{entity.productClassify,jdbcType=VARCHAR},
            #{entity.productCategory,jdbcType=VARCHAR},
            #{entity.productFactoryCode,jdbcType=VARCHAR},
            #{entity.vehicleModeCode,jdbcType=VARCHAR},
            #{entity.inputFactor,jdbcType=VARCHAR},
            #{entity.demandTime,jdbcType=TIMESTAMP},
            #{entity.demandQuantity,jdbcType=VARCHAR},
            #{entity.unFulfillmentQuantity,jdbcType=VARCHAR},
            #{entity.operationCode,jdbcType=VARCHAR},
            #{entity.supplyModel,jdbcType=VARCHAR},
            #{entity.productColor,jdbcType=VARCHAR},
            #{entity.productThickness,jdbcType=VARCHAR},
            #{entity.demandSource,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.materialDemand.infrastructure.po.MaterialGrossDemandHistoryPO">
        update mrp_material_gross_demand_history
        set material_gross_demand_version_id = #{materialGrossDemandVersionId,jdbcType=VARCHAR},
            product_code                     = #{productCode,jdbcType=VARCHAR},
            product_id                       = #{productId,jdbcType=VARCHAR},
            product_name                     = #{productName,jdbcType=VARCHAR},
            product_classify                 = #{productClassify,jdbcType=VARCHAR},
            product_category                 = #{productCategory,jdbcType=VARCHAR},
            product_factory_code             = #{productFactoryCode,jdbcType=VARCHAR},
            vehicle_mode_code                = #{vehicleModeCode,jdbcType=VARCHAR},
            input_factor                     = #{inputFactor,jdbcType=VARCHAR},
            demand_time                      = #{demandTime,jdbcType=TIMESTAMP},
            demand_quantity                  = #{demandQuantity,jdbcType=VARCHAR},
            un_fulfillment_quantity          = #{unFulfillmentQuantity,jdbcType=VARCHAR},
            operation_code                   = #{operationCode,jdbcType=VARCHAR},
            supply_model                     = #{supplyModel,jdbcType=VARCHAR},
            product_color                    = #{productColor,jdbcType=VARCHAR},
            product_thickness                = #{productThickness,jdbcType=VARCHAR},
            demand_source                    = #{demandSource,jdbcType=VARCHAR},
            remark                           = #{remark,jdbcType=VARCHAR},
            enabled                          = #{enabled,jdbcType=VARCHAR},
            modifier                         = #{modifier,jdbcType=VARCHAR},
            modify_time                      = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mrp.materialDemand.infrastructure.po.MaterialGrossDemandHistoryPO">
        update mrp_material_gross_demand_history
        <set>
            <if test="item.materialGrossDemandVersionId != null and item.materialGrossDemandVersionId != ''">
                material_gross_demand_version_id = #{item.materialGrossDemandVersionId,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productId != null and item.productId != ''">
                product_id = #{item.productId,jdbcType=VARCHAR},
            </if>
            <if test="item.productName != null and item.productName != ''">
                product_name = #{item.productName,jdbcType=VARCHAR},
            </if>
            <if test="item.productClassify != null and item.productClassify != ''">
                product_classify = #{item.productClassify,jdbcType=VARCHAR},
            </if>
            <if test="item.productCategory != null and item.productCategory != ''">
                product_category = #{item.productCategory,jdbcType=VARCHAR},
            </if>
            <if test="item.productFactoryCode != null and item.productFactoryCode != ''">
                product_factory_code = #{item.productFactoryCode,jdbcType=VARCHAR},
            </if>
            <if test="item.vehicleModeCode != null and item.vehicleModeCode != ''">
                vehicle_mode_code = #{item.vehicleModeCode,jdbcType=VARCHAR},
            </if>
            <if test="item.inputFactor != null">
                input_factor = #{item.inputFactor,jdbcType=VARCHAR},
            </if>
            <if test="item.demandTime != null">
                demand_time = #{item.demandTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.demandQuantity != null">
                demand_quantity = #{item.demandQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.unFulfillmentQuantity != null">
                un_fulfillment_quantity = #{item.unFulfillmentQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.operationCode != null and item.operationCode != ''">
                operation_code = #{item.operationCode,jdbcType=VARCHAR},
            </if>
            <if test="item.supplyModel != null and item.supplyModel != ''">
                supply_model = #{item.supplyModel,jdbcType=VARCHAR},
            </if>
            <if test="item.productColor != null and item.productColor != ''">
                product_color = #{item.productColor,jdbcType=VARCHAR},
            </if>
            <if test="item.productThickness != null">
                product_thickness = #{item.productThickness,jdbcType=VARCHAR},
            </if>
            <if test="item.demandSource != null and item.demandSource != ''">
                demand_source = #{item.demandSource,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_material_gross_demand_history
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="material_gross_demand_version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialGrossDemandVersionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_classify = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productClassify,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_category = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCategory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_factory_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productFactoryCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="vehicle_mode_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.vehicleModeCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="input_factor = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inputFactor,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="demand_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="un_fulfillment_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.unFulfillmentQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="operation_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.operationCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supply_model = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplyModel,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_color = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productColor,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_thickness = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productThickness,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_source = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandSource,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_material_gross_demand_history
            <set>
                <if test="item.materialGrossDemandVersionId != null and item.materialGrossDemandVersionId != ''">
                    material_gross_demand_version_id = #{item.materialGrossDemandVersionId,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productId != null and item.productId != ''">
                    product_id = #{item.productId,jdbcType=VARCHAR},
                </if>
                <if test="item.productName != null and item.productName != ''">
                    product_name = #{item.productName,jdbcType=VARCHAR},
                </if>
                <if test="item.productClassify != null and item.productClassify != ''">
                    product_classify = #{item.productClassify,jdbcType=VARCHAR},
                </if>
                <if test="item.productCategory != null and item.productCategory != ''">
                    product_category = #{item.productCategory,jdbcType=VARCHAR},
                </if>
                <if test="item.productFactoryCode != null and item.productFactoryCode != ''">
                    product_factory_code = #{item.productFactoryCode,jdbcType=VARCHAR},
                </if>
                <if test="item.vehicleModeCode != null and item.vehicleModeCode != ''">
                    vehicle_mode_code = #{item.vehicleModeCode,jdbcType=VARCHAR},
                </if>
                <if test="item.inputFactor != null">
                    input_factor = #{item.inputFactor,jdbcType=VARCHAR},
                </if>
                <if test="item.demandTime != null">
                    demand_time = #{item.demandTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.demandQuantity != null">
                    demand_quantity = #{item.demandQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.unFulfillmentQuantity != null">
                    un_fulfillment_quantity = #{item.unFulfillmentQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.operationCode != null and item.operationCode != ''">
                    operation_code = #{item.operationCode,jdbcType=VARCHAR},
                </if>
                <if test="item.supplyModel != null and item.supplyModel != ''">
                    supply_model = #{item.supplyModel,jdbcType=VARCHAR},
                </if>
                <if test="item.productColor != null and item.productColor != ''">
                    product_color = #{item.productColor,jdbcType=VARCHAR},
                </if>
                <if test="item.productThickness != null">
                    product_thickness = #{item.productThickness,jdbcType=VARCHAR},
                </if>
                <if test="item.demandSource != null and item.demandSource != ''">
                    demand_source = #{item.demandSource,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_material_gross_demand_history
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_material_gross_demand_history where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
