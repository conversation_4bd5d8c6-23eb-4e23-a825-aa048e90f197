package com.yhl.scp.mrp.published.domain.service;

import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDetailVO;
import com.yhl.scp.mrp.published.domain.entity.NoGlassInventoryShiftDetailPublishedDO;
import com.yhl.scp.mrp.published.infrastructure.dao.NoGlassInventoryShiftDetailPublishedDao;
import com.yhl.scp.mrp.published.infrastructure.po.NoGlassInventoryShiftDetailPublishedPO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <code>NoGlassInventoryShiftDetailPublishedDomainService</code>
 * <p>
 * 非原片库存推移发布详情表领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-27 14:03:38
 */
@Service
public class NoGlassInventoryShiftDetailPublishedDomainService {

    @Resource
    private NoGlassInventoryShiftDetailPublishedDao noGlassInventoryShiftDetailPublishedDao;

    /**
     * 数据校验
     *
     * @param noGlassInventoryShiftDetailPublishedDO 领域对象
     */
    public void validation(NoGlassInventoryShiftDetailPublishedDO noGlassInventoryShiftDetailPublishedDO) {
        checkNotNull(noGlassInventoryShiftDetailPublishedDO);
        checkUniqueCode(noGlassInventoryShiftDetailPublishedDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param noGlassInventoryShiftDetailPublishedDO 领域对象
     */
    private void checkNotNull(NoGlassInventoryShiftDetailPublishedDO noGlassInventoryShiftDetailPublishedDO) {

    }

    /**
     * 唯一性校验
     *
     * @param noGlassInventoryShiftDetailPublishedDO 领域对象
     */
    private void checkUniqueCode(NoGlassInventoryShiftDetailPublishedDO noGlassInventoryShiftDetailPublishedDO) {

    }

    public NoGlassInventoryShiftDetailPublishedPO getNoGlassInventoryShiftPublishedDTO(NoGlassInventoryShiftDetailVO noGlassInventoryShiftDetailVO) {
        NoGlassInventoryShiftDetailPublishedPO noGlassInventoryShiftDetailPublishedPO = new NoGlassInventoryShiftDetailPublishedPO();
        noGlassInventoryShiftDetailPublishedPO.setSafetyStockLevelMin(noGlassInventoryShiftDetailVO.getSafetyStockLevelMin());
        noGlassInventoryShiftDetailPublishedPO.setSafetyStockLevelStandard(noGlassInventoryShiftDetailVO.getSafetyStockLevelStandard());
        noGlassInventoryShiftDetailPublishedPO.setSafetyStockLevelMax(noGlassInventoryShiftDetailVO.getSafetyStockLevelMax());
        noGlassInventoryShiftDetailPublishedPO.setOpeningInventory(noGlassInventoryShiftDetailVO.getOpeningInventory());
        noGlassInventoryShiftDetailPublishedPO.setDemandQuantity(noGlassInventoryShiftDetailVO.getDemandQuantity());
        noGlassInventoryShiftDetailPublishedPO.setSupplyQuantity(noGlassInventoryShiftDetailVO.getSupplyQuantity());
        noGlassInventoryShiftDetailPublishedPO.setAdjustQuantity(noGlassInventoryShiftDetailVO.getAdjustQuantity());
        noGlassInventoryShiftDetailPublishedPO.setUsedAsReplaceQuantity(noGlassInventoryShiftDetailVO.getUsedAsReplaceQuantity());
        noGlassInventoryShiftDetailPublishedPO.setBeforeEndingInventory(noGlassInventoryShiftDetailVO.getBeforeEndingInventory());
        noGlassInventoryShiftDetailPublishedPO.setEndingInventory(noGlassInventoryShiftDetailVO.getEndingInventory());
        noGlassInventoryShiftDetailPublishedPO.setInventoryGap(noGlassInventoryShiftDetailVO.getInventoryGap());
        noGlassInventoryShiftDetailPublishedPO.setInventoryDate(noGlassInventoryShiftDetailVO.getInventoryDate());
        noGlassInventoryShiftDetailPublishedPO.setPlanPurchase(noGlassInventoryShiftDetailVO.getPlanPurchase());
        noGlassInventoryShiftDetailPublishedPO.setRealInventoryGap(noGlassInventoryShiftDetailVO.getRealInventoryGap());
        return noGlassInventoryShiftDetailPublishedPO;
    }
}
