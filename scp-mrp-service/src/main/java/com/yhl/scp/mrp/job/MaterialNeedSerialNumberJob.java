package com.yhl.scp.mrp.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.ips.enums.RedisKeyManageEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mds.extension.supplier.vo.SupplierVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Set;

/**
 * @ClassName MaterialNeedSerialNumberJob
 * @Description TODO
 * @Date 2025-01-08 15:14:13
 * <AUTHOR>
 * @Copyright 悠桦林信息科技（上海）有限公司
 * @Version 1.0
 */
@Component
@Slf4j
public class MaterialNeedSerialNumberJob {

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private NewMdsFeign newMdsFeign;

    @XxlJob("resetPlanNeedSerialNumberJobHandler")
    public ReturnT<String> resetPlanNeedSerialNumberJobHandler() {
        XxlJobHelper.log("重置要货计划序列号");
        List<Scenario> scenarios = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.MDS.getCode()).getData();
        if (CollectionUtils.isEmpty(scenarios)) {
            XxlJobHelper.log("租户下不存在MPS模块信息");
            return ReturnT.SUCCESS;
        }
        String jobParam = XxlJobHelper.getJobParam();
        XxlJobHelper.log("接收調度中心参数...[{}]", jobParam);
        for (Scenario scenario : scenarios) {
            // 获取承运商数据
            List<SupplierVO> supplierVOS = newMdsFeign.selectSupplierByParams(scenario.getDataBaseName(), new HashMap<>());
            Set<String> syncKeys = redisUtil.keys(RedisKeyManageEnum.PLAN_NEED_SERIAL_NUMBER_KEY.getKey() + "*");
            for (String key : syncKeys) {
                redisUtil.delete(key);
            }
            for (SupplierVO supplierVO : supplierVOS) {
                redisUtil.set(String.join("#", RedisKeyManageEnum.PLAN_NEED_SERIAL_NUMBER_KEY.getKey(), supplierVO.getSupplierCode()), 0, 0);
            }
        }
        XxlJobHelper.log("重置要货计划序列号结束");
        return ReturnT.SUCCESS;
    }
}
