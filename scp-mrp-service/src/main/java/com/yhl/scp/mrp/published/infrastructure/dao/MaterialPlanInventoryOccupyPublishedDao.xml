<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.published.infrastructure.dao.MaterialPlanInventoryOccupyPublishedDao">
    <resultMap id="BaseResultMap"
               type="com.yhl.scp.mrp.published.infrastructure.po.MaterialPlanInventoryOccupyPublishedPO">
        <!--@Table mrp_material_plan_inventory_occupy_published-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="material_plan_published_version_id" jdbcType="VARCHAR"
                property="materialPlanPublishedVersionId"/>
        <result column="inventory_id" jdbcType="VARCHAR" property="inventoryId"/>
        <result column="material_plan_transfer_id" jdbcType="VARCHAR" property="materialPlanTransferId"/>
        <result column="container_number" jdbcType="VARCHAR" property="containerNumber"/>
        <result column="inventory_shift_detail_id" jdbcType="VARCHAR" property="inventoryShiftDetailId"/>
        <result column="plan_replace_rule_id" jdbcType="VARCHAR" property="planReplaceRuleId"/>
        <result column="stock_point_code_from" jdbcType="VARCHAR" property="stockPointCodeFrom"/>
        <result column="stock_point_code_to" jdbcType="VARCHAR" property="stockPointCodeTo"/>
        <result column="transport_date_start" jdbcType="TIMESTAMP" property="transportDateStart"/>
        <result column="transport_date_end" jdbcType="TIMESTAMP" property="transportDateEnd"/>
        <result column="demand_product_code" jdbcType="VARCHAR" property="demandProductCode"/>
        <result column="supply_product_code" jdbcType="VARCHAR" property="supplyProductCode"/>
        <result column="transport_line" jdbcType="VARCHAR" property="transportLine"/>
        <result column="transport_line_segment" jdbcType="INTEGER" property="transportLineSegment"/>
        <result column="transport_id" jdbcType="VARCHAR" property="transportId"/>
        <result column="inventory_type" jdbcType="VARCHAR" property="inventoryType"/>
        <result column="occupy_type" jdbcType="VARCHAR" property="occupyType"/>
        <result column="occupy_quantity" jdbcType="VARCHAR" property="occupyQuantity"/>
        <result column="occupy_date" jdbcType="TIMESTAMP" property="occupyDate"/>
        <result column="transfer_routing_id" jdbcType="VARCHAR" property="transferRoutingId"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.mrp.published.vo.MaterialPlanInventoryOccupyPublishedVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,material_plan_published_version_id,inventory_id,material_plan_transfer_id,container_number,inventory_shift_detail_id,plan_replace_rule_id,stock_point_code_from,stock_point_code_to,transport_date_start,transport_date_end,demand_product_code,supply_product_code,transport_line,transport_line_segment,transport_id,inventory_type,occupy_type,occupy_quantity,occupy_date,transfer_routing_id,remark,enabled,creator,create_time,modifier,modify_time
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.materialPlanPublishedVersionId != null and params.materialPlanPublishedVersionId != ''">
                and material_plan_published_version_id = #{params.materialPlanPublishedVersionId,jdbcType=VARCHAR}
            </if>
            <if test="params.inventoryId != null and params.inventoryId != ''">
                and inventory_id = #{params.inventoryId,jdbcType=VARCHAR}
            </if>
            <if test="params.materialPlanTransferId != null and params.materialPlanTransferId != ''">
                and material_plan_transfer_id = #{params.materialPlanTransferId,jdbcType=VARCHAR}
            </if>
            <if test="params.containerNumber != null and params.containerNumber != ''">
                and container_number = #{params.containerNumber,jdbcType=VARCHAR}
            </if>
            <if test="params.inventoryShiftDetailId != null and params.inventoryShiftDetailId != ''">
                and inventory_shift_detail_id = #{params.inventoryShiftDetailId,jdbcType=VARCHAR}
            </if>
            <if test="params.materialPlanTransferIds != null and params.materialPlanTransferIds.size() > 0">
                and material_plan_transfer_id in
                <foreach collection="params.materialPlanTransferIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.demandId != null and params.demandId != ''">
                and demand_id = #{params.demandId,jdbcType=VARCHAR}
            </if>
            <if test="params.planReplaceRuleId != null and params.planReplaceRuleId != ''">
                and plan_replace_rule_id = #{params.planReplaceRuleId,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCodeFrom != null and params.stockPointCodeFrom != ''">
                and stock_point_code_from = #{params.stockPointCodeFrom,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCodeTo != null and params.stockPointCodeTo != ''">
                and stock_point_code_to = #{params.stockPointCodeTo,jdbcType=VARCHAR}
            </if>
            <if test="params.transportDateStart != null">
                and transport_date_start = #{params.transportDateStart,jdbcType=TIMESTAMP}
            </if>
            <if test="params.transportDateEnd != null">
                and transport_date_end = #{params.transportDateEnd,jdbcType=TIMESTAMP}
            </if>
            <if test="params.demandProductCode != null and params.demandProductCode != ''">
                and demand_product_code = #{params.demandProductCode,jdbcType=VARCHAR}
            </if>
            <if test="params.supplyProductCode != null and params.supplyProductCode != ''">
                and supply_product_code = #{params.supplyProductCode,jdbcType=VARCHAR}
            </if>
            <if test="params.transportLine != null and params.transportLine != ''">
                and transport_line = #{params.transportLine,jdbcType=VARCHAR}
            </if>
            <if test="params.transportLineSegment != null">
                and transport_line_segment = #{params.transportLineSegment,jdbcType=INTEGER}
            </if>
            <if test="params.transportId != null and params.transportId != ''">
                and transport_id = #{params.transportId,jdbcType=VARCHAR}
            </if>
            <if test="params.inventoryType != null and params.inventoryType != ''">
                and inventory_type = #{params.inventoryType,jdbcType=VARCHAR}
            </if>
            <if test="params.occupyType != null and params.occupyType != ''">
                and occupy_type = #{params.occupyType,jdbcType=VARCHAR}
            </if>
            <if test="params.occupyQuantity != null">
                and occupy_quantity = #{params.occupyQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.occupyDate != null">
                and occupy_date = #{params.occupyDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.transferRoutingId != null and params.transferRoutingId != ''">
                and transfer_routing_id = #{params.transferRoutingId,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_plan_inventory_occupy_published
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_plan_inventory_occupy_published
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from mrp_material_plan_inventory_occupy_published
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_plan_inventory_occupy_published
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert"
            parameterType="com.yhl.scp.mrp.published.infrastructure.po.MaterialPlanInventoryOccupyPublishedPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_material_plan_inventory_occupy_published(
        id,
        material_plan_published_version_id,
        inventory_id,
        material_plan_transfer_id,
        container_number,
        inventory_shift_detail_id,
        plan_replace_rule_id,
        stock_point_code_from,
        stock_point_code_to,
        transport_date_start,
        transport_date_end,
        demand_product_code,
        supply_product_code,
        transport_line,
        transport_line_segment,
        transport_id,
        inventory_type,
        occupy_type,
        occupy_quantity,
        occupy_date,
        transfer_routing_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{materialPlanPublishedVersionId,jdbcType=VARCHAR},
        #{inventoryId,jdbcType=VARCHAR},
        #{materialPlanTransferId,jdbcType=VARCHAR},
        #{containerNumber,jdbcType=VARCHAR},
        #{inventoryShiftDetailId,jdbcType=VARCHAR},
        #{planReplaceRuleId,jdbcType=VARCHAR},
        #{stockPointCodeFrom,jdbcType=VARCHAR},
        #{stockPointCodeTo,jdbcType=VARCHAR},
        #{transportDateStart,jdbcType=TIMESTAMP},
        #{transportDateEnd,jdbcType=TIMESTAMP},
        #{demandProductCode,jdbcType=VARCHAR},
        #{supplyProductCode,jdbcType=VARCHAR},
        #{transportLine,jdbcType=VARCHAR},
        #{transportLineSegment,jdbcType=INTEGER},
        #{transportId,jdbcType=VARCHAR},
        #{inventoryType,jdbcType=VARCHAR},
        #{occupyType,jdbcType=VARCHAR},
        #{occupyQuantity,jdbcType=VARCHAR},
        #{occupyDate,jdbcType=TIMESTAMP},
        #{transferRoutingId,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.published.infrastructure.po.MaterialPlanInventoryOccupyPublishedPO">
        insert into mrp_material_plan_inventory_occupy_published(id,
                                                                 material_plan_published_version_id,
                                                                 inventory_id,
                                                                 material_plan_transfer_id,
                                                                 container_number,
                                                                 inventory_shift_detail_id,
                                                                 plan_replace_rule_id,
                                                                 stock_point_code_from,
                                                                 stock_point_code_to,
                                                                 transport_date_start,
                                                                 transport_date_end,
                                                                 demand_product_code,
                                                                 supply_product_code,
                                                                 transport_line,
                                                                 transport_line_segment,
                                                                 transport_id,
                                                                 inventory_type,
                                                                 occupy_type,
                                                                 occupy_quantity,
                                                                 occupy_date,
                                                                 transfer_routing_id,
                                                                 remark,
                                                                 enabled,
                                                                 creator,
                                                                 create_time,
                                                                 modifier,
                                                                 modify_time)
        values (#{id,jdbcType=VARCHAR},
                #{materialPlanPublishedVersionId,jdbcType=VARCHAR},
                #{inventoryId,jdbcType=VARCHAR},
                #{materialPlanTransferId,jdbcType=VARCHAR},
                #{containerNumber,jdbcType=VARCHAR},
                #{inventoryShiftDetailId,jdbcType=VARCHAR},
                #{planReplaceRuleId,jdbcType=VARCHAR},
                #{stockPointCodeFrom,jdbcType=VARCHAR},
                #{stockPointCodeTo,jdbcType=VARCHAR},
                #{transportDateStart,jdbcType=TIMESTAMP},
                #{transportDateEnd,jdbcType=TIMESTAMP},
                #{demandProductCode,jdbcType=VARCHAR},
                #{supplyProductCode,jdbcType=VARCHAR},
                #{transportLine,jdbcType=VARCHAR},
                #{transportLineSegment,jdbcType=INTEGER},
                #{transportId,jdbcType=VARCHAR},
                #{inventoryType,jdbcType=VARCHAR},
                #{occupyType,jdbcType=VARCHAR},
                #{occupyQuantity,jdbcType=VARCHAR},
                #{occupyDate,jdbcType=TIMESTAMP},
                #{transferRoutingId,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_material_plan_inventory_occupy_published(
        id,
        material_plan_published_version_id,
        inventory_id,
        material_plan_transfer_id,
        container_number,
        inventory_shift_detail_id,
        plan_replace_rule_id,
        stock_point_code_from,
        stock_point_code_to,
        transport_date_start,
        transport_date_end,
        demand_product_code,
        supply_product_code,
        transport_line,
        transport_line_segment,
        transport_id,
        inventory_type,
        occupy_type,
        occupy_quantity,
        occupy_date,
        transfer_routing_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.materialPlanPublishedVersionId,jdbcType=VARCHAR},
            #{entity.inventoryId,jdbcType=VARCHAR},
            #{entity.materialPlanTransferId,jdbcType=VARCHAR},
            #{entity.containerNumber,jdbcType=VARCHAR},
            #{entity.inventoryShiftDetailId,jdbcType=VARCHAR},
            #{entity.planReplaceRuleId,jdbcType=VARCHAR},
            #{entity.stockPointCodeFrom,jdbcType=VARCHAR},
            #{entity.stockPointCodeTo,jdbcType=VARCHAR},
            #{entity.transportDateStart,jdbcType=TIMESTAMP},
            #{entity.transportDateEnd,jdbcType=TIMESTAMP},
            #{entity.demandProductCode,jdbcType=VARCHAR},
            #{entity.supplyProductCode,jdbcType=VARCHAR},
            #{entity.transportLine,jdbcType=VARCHAR},
            #{entity.transportLineSegment,jdbcType=INTEGER},
            #{entity.transportId,jdbcType=VARCHAR},
            #{entity.inventoryType,jdbcType=VARCHAR},
            #{entity.occupyType,jdbcType=VARCHAR},
            #{entity.occupyQuantity,jdbcType=VARCHAR},
            #{entity.occupyDate,jdbcType=TIMESTAMP},
            #{entity.transferRoutingId,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_material_plan_inventory_occupy_published(
        id,
        material_plan_published_version_id,
        inventory_id,
        material_plan_transfer_id,
        container_number,
        inventory_shift_detail_id,
        plan_replace_rule_id,
        stock_point_code_from,
        stock_point_code_to,
        transport_date_start,
        transport_date_end,
        demand_product_code,
        supply_product_code,
        transport_line,
        transport_line_segment,
        transport_id,
        inventory_type,
        occupy_type,
        occupy_quantity,
        occupy_date,
        transfer_routing_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.materialPlanPublishedVersionId,jdbcType=VARCHAR},
            #{entity.inventoryId,jdbcType=VARCHAR},
            #{entity.materialPlanTransferId,jdbcType=VARCHAR},
            #{entity.containerNumber,jdbcType=VARCHAR},
            #{entity.inventoryShiftDetailId,jdbcType=VARCHAR},
            #{entity.planReplaceRuleId,jdbcType=VARCHAR},
            #{entity.stockPointCodeFrom,jdbcType=VARCHAR},
            #{entity.stockPointCodeTo,jdbcType=VARCHAR},
            #{entity.transportDateStart,jdbcType=TIMESTAMP},
            #{entity.transportDateEnd,jdbcType=TIMESTAMP},
            #{entity.demandProductCode,jdbcType=VARCHAR},
            #{entity.supplyProductCode,jdbcType=VARCHAR},
            #{entity.transportLine,jdbcType=VARCHAR},
            #{entity.transportLineSegment,jdbcType=INTEGER},
            #{entity.transportId,jdbcType=VARCHAR},
            #{entity.inventoryType,jdbcType=VARCHAR},
            #{entity.occupyType,jdbcType=VARCHAR},
            #{entity.occupyQuantity,jdbcType=VARCHAR},
            #{entity.occupyDate,jdbcType=TIMESTAMP},
            #{entity.transferRoutingId,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update"
            parameterType="com.yhl.scp.mrp.published.infrastructure.po.MaterialPlanInventoryOccupyPublishedPO">
        update mrp_material_plan_inventory_occupy_published
        set material_plan_published_version_id = #{materialPlanPublishedVersionId,jdbcType=VARCHAR},
            inventory_id                       = #{inventoryId,jdbcType=VARCHAR},
            material_plan_transfer_id          = #{materialPlanTransferId,jdbcType=VARCHAR},
            container_number                      = #{containerNumber,jdbcType=VARCHAR},
            inventory_shift_detail_id                          = #{inventoryShiftDetailId,jdbcType=VARCHAR},
            plan_replace_rule_id               = #{planReplaceRuleId,jdbcType=VARCHAR},
            stock_point_code_from              = #{stockPointCodeFrom,jdbcType=VARCHAR},
            stock_point_code_to                = #{stockPointCodeTo,jdbcType=VARCHAR},
            transport_date_start               = #{transportDateStart,jdbcType=TIMESTAMP},
            transport_date_end                 = #{transportDateEnd,jdbcType=TIMESTAMP},
            demand_product_code                = #{demandProductCode,jdbcType=VARCHAR},
            supply_product_code                = #{supplyProductCode,jdbcType=VARCHAR},
            transport_line                     = #{transportLine,jdbcType=VARCHAR},
            transport_line_segment             = #{transportLineSegment,jdbcType=INTEGER},
            transport_id                       = #{transportId,jdbcType=VARCHAR},
            inventory_type                     = #{inventoryType,jdbcType=VARCHAR},
            occupy_type                        = #{occupyType,jdbcType=VARCHAR},
            occupy_quantity                    = #{occupyQuantity,jdbcType=VARCHAR},
            occupy_date                        = #{occupyDate,jdbcType=TIMESTAMP},
            transfer_routing_id                = #{transferRoutingId,jdbcType=VARCHAR},
            remark                             = #{remark,jdbcType=VARCHAR},
            enabled                            = #{enabled,jdbcType=VARCHAR},
            modifier                           = #{modifier,jdbcType=VARCHAR},
            modify_time                        = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mrp.published.infrastructure.po.MaterialPlanInventoryOccupyPublishedPO">
        update mrp_material_plan_inventory_occupy_published
        <set>
            <if test="item.materialPlanPublishedVersionId != null and item.materialPlanPublishedVersionId != ''">
                material_plan_published_version_id = #{item.materialPlanPublishedVersionId,jdbcType=VARCHAR},
            </if>
            <if test="item.inventoryId != null and item.inventoryId != ''">
                inventory_id = #{item.inventoryId,jdbcType=VARCHAR},
            </if>
            <if test="item.materialPlanTransferId != null and item.materialPlanTransferId != ''">
                material_plan_transfer_id = #{item.materialPlanTransferId,jdbcType=VARCHAR},
            </if>
            <if test="item.containerNumber != null and item.containerNumber != ''">
                container_number = #{item.containerNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.inventoryShiftDetailId != null and item.inventoryShiftDetailId != ''">
                inventory_shift_detail_id = #{item.inventoryShiftDetailId,jdbcType=VARCHAR},
            </if>
            <if test="item.planReplaceRuleId != null and item.planReplaceRuleId != ''">
                plan_replace_rule_id = #{item.planReplaceRuleId,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointCodeFrom != null and item.stockPointCodeFrom != ''">
                stock_point_code_from = #{item.stockPointCodeFrom,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointCodeTo != null and item.stockPointCodeTo != ''">
                stock_point_code_to = #{item.stockPointCodeTo,jdbcType=VARCHAR},
            </if>
            <if test="item.transportDateStart != null">
                transport_date_start = #{item.transportDateStart,jdbcType=TIMESTAMP},
            </if>
            <if test="item.transportDateEnd != null">
                transport_date_end = #{item.transportDateEnd,jdbcType=TIMESTAMP},
            </if>
            <if test="item.demandProductCode != null and item.demandProductCode != ''">
                demand_product_code = #{item.demandProductCode,jdbcType=VARCHAR},
            </if>
            <if test="item.supplyProductCode != null and item.supplyProductCode != ''">
                supply_product_code = #{item.supplyProductCode,jdbcType=VARCHAR},
            </if>
            <if test="item.transportLine != null and item.transportLine != ''">
                transport_line = #{item.transportLine,jdbcType=VARCHAR},
            </if>
            <if test="item.transportLineSegment != null">
                transport_line_segment = #{item.transportLineSegment,jdbcType=INTEGER},
            </if>
            <if test="item.transportId != null and item.transportId != ''">
                transport_id = #{item.transportId,jdbcType=VARCHAR},
            </if>
            <if test="item.inventoryType != null and item.inventoryType != ''">
                inventory_type = #{item.inventoryType,jdbcType=VARCHAR},
            </if>
            <if test="item.occupyType != null and item.occupyType != ''">
                occupy_type = #{item.occupyType,jdbcType=VARCHAR},
            </if>
            <if test="item.occupyQuantity != null">
                occupy_quantity = #{item.occupyQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.occupyDate != null">
                occupy_date = #{item.occupyDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.transferRoutingId != null and item.transferRoutingId != ''">
                transfer_routing_id = #{item.transferRoutingId,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_material_plan_inventory_occupy_published
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="material_plan_published_version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialPlanPublishedVersionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="inventory_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inventoryId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_plan_transfer_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialPlanTransferId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="container_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.containerNumber,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="inventory_shift_detail_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inventoryShiftDetailId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="plan_replace_rule_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.planReplaceRuleId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_code_from = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointCodeFrom,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_code_to = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointCodeTo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transport_date_start = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transportDateStart,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="transport_date_end = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transportDateEnd,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="demand_product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandProductCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supply_product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplyProductCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transport_line = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transportLine,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transport_line_segment = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transportLineSegment,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="transport_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transportId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="inventory_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inventoryType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="occupy_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.occupyType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="occupy_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.occupyQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="occupy_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.occupyDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="transfer_routing_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transferRoutingId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_material_plan_inventory_occupy_published
            <set>
                <if test="item.materialPlanPublishedVersionId != null and item.materialPlanPublishedVersionId != ''">
                    material_plan_published_version_id = #{item.materialPlanPublishedVersionId,jdbcType=VARCHAR},
                </if>
                <if test="item.inventoryId != null and item.inventoryId != ''">
                    inventory_id = #{item.inventoryId,jdbcType=VARCHAR},
                </if>
                <if test="item.materialPlanTransferId != null and item.materialPlanTransferId != ''">
                    material_plan_transfer_id = #{item.materialPlanTransferId,jdbcType=VARCHAR},
                </if>
                <if test="item.containerNumber != null and item.containerNumber != ''">
                    container_number = #{item.containerNumber,jdbcType=VARCHAR},
                </if>
                <if test="item.inventoryShiftDetailId != null and item.inventoryShiftDetailId != ''">
                    inventory_shift_detail_id = #{item.inventoryShiftDetailId,jdbcType=VARCHAR},
                </if>
                <if test="item.planReplaceRuleId != null and item.planReplaceRuleId != ''">
                    plan_replace_rule_id = #{item.planReplaceRuleId,jdbcType=VARCHAR},
                </if>
                <if test="item.stockPointCodeFrom != null and item.stockPointCodeFrom != ''">
                    stock_point_code_from = #{item.stockPointCodeFrom,jdbcType=VARCHAR},
                </if>
                <if test="item.stockPointCodeTo != null and item.stockPointCodeTo != ''">
                    stock_point_code_to = #{item.stockPointCodeTo,jdbcType=VARCHAR},
                </if>
                <if test="item.transportDateStart != null">
                    transport_date_start = #{item.transportDateStart,jdbcType=TIMESTAMP},
                </if>
                <if test="item.transportDateEnd != null">
                    transport_date_end = #{item.transportDateEnd,jdbcType=TIMESTAMP},
                </if>
                <if test="item.demandProductCode != null and item.demandProductCode != ''">
                    demand_product_code = #{item.demandProductCode,jdbcType=VARCHAR},
                </if>
                <if test="item.supplyProductCode != null and item.supplyProductCode != ''">
                    supply_product_code = #{item.supplyProductCode,jdbcType=VARCHAR},
                </if>
                <if test="item.transportLine != null and item.transportLine != ''">
                    transport_line = #{item.transportLine,jdbcType=VARCHAR},
                </if>
                <if test="item.transportLineSegment != null">
                    transport_line_segment = #{item.transportLineSegment,jdbcType=INTEGER},
                </if>
                <if test="item.transportId != null and item.transportId != ''">
                    transport_id = #{item.transportId,jdbcType=VARCHAR},
                </if>
                <if test="item.inventoryType != null and item.inventoryType != ''">
                    inventory_type = #{item.inventoryType,jdbcType=VARCHAR},
                </if>
                <if test="item.occupyType != null and item.occupyType != ''">
                    occupy_type = #{item.occupyType,jdbcType=VARCHAR},
                </if>
                <if test="item.occupyQuantity != null">
                    occupy_quantity = #{item.occupyQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.occupyDate != null">
                    occupy_date = #{item.occupyDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.transferRoutingId != null and item.transferRoutingId != ''">
                    transfer_routing_id = #{item.transferRoutingId,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_material_plan_inventory_occupy_published
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_material_plan_inventory_occupy_published where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
