package com.yhl.scp.mrp.assignment.domain.service;

import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.mrp.assignment.domain.entity.MaterialDemandAssignmentDO;
import com.yhl.scp.mrp.assignment.infrastructure.dao.MaterialDemandAssignmentDao;
import com.yhl.scp.mrp.assignment.infrastructure.po.MaterialDemandAssignmentPO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>MaterialDemandAssignmentDomainService</code>
 * <p>
 * 材料需求分配表领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-27 15:55:41
 */
@Service
public class MaterialDemandAssignmentDomainService {

    @Resource
    private MaterialDemandAssignmentDao materialDemandAssignmentDao;

    /**
     * 数据校验
     *
     * @param materialDemandAssignmentDO 领域对象
     */
    public void validation(MaterialDemandAssignmentDO materialDemandAssignmentDO) {
        checkNotNull(materialDemandAssignmentDO);
        checkUniqueCode(materialDemandAssignmentDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param materialDemandAssignmentDO 领域对象
     */
    private void checkNotNull(MaterialDemandAssignmentDO materialDemandAssignmentDO) {
//        if (StringUtils.isBlank(materialDemandAssignmentDO.getMaterialDemandAssignmentCode())) {
//            throw new BusinessException("材料需求分配表代码，不能为空");
//        }
//        if (StringUtils.isBlank(materialDemandAssignmentDO.getMaterialDemandAssignmentName())) {
//            throw new BusinessException("材料需求分配表名称，不能为空");
//        }
    }

    /**
     * 唯一性校验
     *
     * @param materialDemandAssignmentDO 领域对象
     */
    private void checkUniqueCode(MaterialDemandAssignmentDO materialDemandAssignmentDO) {
//        Map<String, Object> params = new HashMap<>(4);
//        params.put("materialDemandAssignmentCode", materialDemandAssignmentDO.getMaterialDemandAssignmentCode());
//        if (StringUtils.isBlank(materialDemandAssignmentDO.getId())) {
//            List<MaterialDemandAssignmentPO> list = materialDemandAssignmentDao.selectByParams(params);
//            if (CollectionUtils.isNotEmpty(list)) {
//                throw new BusinessException("新增失败，材料需求分配表代码已存在：" + materialDemandAssignmentDO.getMaterialDemandAssignmentCode());
//            }
//        } else {
//            MaterialDemandAssignmentPO old = materialDemandAssignmentDao.selectByPrimaryKey(materialDemandAssignmentDO.getId());
//            if (!materialDemandAssignmentDO.getMaterialDemandAssignmentCode().equals(old.getMaterialDemandAssignmentCode())) {
//                List<MaterialDemandAssignmentPO> list = materialDemandAssignmentDao.selectByParams(params);
//                if (CollectionUtils.isNotEmpty(list)) {
//                    throw new BusinessException("修改失败，材料需求分配表代码已存在：" + materialDemandAssignmentDO.getMaterialDemandAssignmentCode());
//                }
//            }
//        }
    }

}
