package com.yhl.scp.mrp.excel.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;

import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.common.excel.DataImportInfo;
import com.yhl.scp.common.excel.ImportDataTypeEnum;
import com.yhl.scp.common.excel.model.ImportAnalysisResultHolder;
import com.yhl.scp.common.excel.model.ImportContext;
import com.yhl.scp.common.excel.model.ImportRelatedDataHolder;
import com.yhl.scp.common.excel.service.AbstractExcelService;
import com.yhl.scp.common.vo.SimpleVO;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.mds.extension.supplier.vo.SupplierVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mrp.freestorage.convertor.ProFreeStorageConvertor;
import com.yhl.scp.mrp.freestorage.dto.ProFreeStorageDTO;
import com.yhl.scp.mrp.freestorage.infrastructure.dao.ProFreeStorageDao;
import com.yhl.scp.mrp.freestorage.infrastructure.po.ProFreeStoragePO;
import com.yhl.scp.mrp.freestorage.service.ProFreeStorageService;
import com.yhl.scp.mrp.freestorage.vo.ProFreeStorageVO;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>ProFreeStorageExcelService</code>
 * <p>
 * 免堆期导入服务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-26 14:48:34
 */
@Service
public class ProFreeStorageExcelService extends AbstractExcelService<ProFreeStorageDTO, ProFreeStoragePO, ProFreeStorageVO> {

    @Resource
    private ProFreeStorageService proFreeStorageService;

    @Resource
    private ProFreeStorageDao proFreeStorageDao;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;


    @Override
    public BaseDao<ProFreeStoragePO, ProFreeStorageVO> getBaseDao() {
        return proFreeStorageDao;
    }

    @Override
    public Function<ProFreeStorageDTO, ProFreeStoragePO> getDTO2POConvertor() {
        return ProFreeStorageConvertor.INSTANCE::dto2Po;
    }

    @Override
    public Class<ProFreeStorageDTO> getDTOClass() {
        return ProFreeStorageDTO.class;
    }

    @Override
    public BaseService<ProFreeStorageDTO, ProFreeStorageVO> getBaseService() {
        return proFreeStorageService;
    }

    @Override
    protected void fillIdForUpdateData(List<ProFreeStorageDTO> list, Map<String, ProFreeStoragePO> existingDataMap) {
        for (ProFreeStorageDTO proFreeStorageDTO : list) {
            ProFreeStoragePO po = existingDataMap.get(proFreeStorageDTO.getCompanyCode() + "&" + proFreeStorageDTO.getPortCode() + "&" + proFreeStorageDTO.getCarrierCode());
            if (po != null) {
                proFreeStorageDTO.setId(po.getId());
            }
        }
    }

    @Override
    protected void specialVerification(ImportAnalysisResultHolder<ProFreeStorageDTO, ProFreeStoragePO> resultHolder, ImportContext importContext) {
        List<ProFreeStorageDTO> insertList = resultHolder.getInsertList();
        List<ProFreeStorageDTO> updateList = resultHolder.getUpdateList();
        verifyPaternity(insertList, resultHolder.getImportLogList());
        verifyPaternity(updateList, resultHolder.getImportLogList());
        resultHolder.setInsertList(insertList);
        resultHolder.setUpdateList(updateList);
    }

    private void verifyPaternity(List<ProFreeStorageDTO> checkList, List<DataImportInfo> importLogList) {
        Map<String, Object> queryMap = MapUtil.newHashMap();
        queryMap.put("enabled", YesOrNoEnum.YES.getCode());
        BaseResponse<String> scenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
        List<NewStockPointVO> newStockPointVOS = newMdsFeign.selectStockPointByParams(scenario.getData(), queryMap);
        //查询销售组织和采购组织的库存点信息
        List<NewStockPointVO> saleOrganizeVOS = newMdsFeign.selectNewStockPointVOByParams(scenario.getData(),
        		ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(), 
            			"organizeTypeList" , 
            			Arrays.asList(StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode(),
            					StockPointOrganizeTypeEnum.PURCHASE_ORGANIZATION.getCode())));
        List<SupplierVO> supplierVOS = newMdsFeign.selectSupplierByParams(scenario.getData(),queryMap);
        //承运商只需要地点包含“运费”的数据
        supplierVOS = supplierVOS.stream().filter( e -> StringUtils.isNotEmpty(e.getAddress()) && e.getAddress().contains("运费"))
        		.collect(Collectors.toList());
        // 查询销售组织数据
        Map<String, NewStockPointVO> saleOrganizeMap = CollectionUtils.isEmpty(saleOrganizeVOS) ? MapUtil.newHashMap() :
                saleOrganizeVOS.stream().collect(Collectors.toMap(NewStockPointVO::getStockPointCode, Function.identity(), (v1, v2) -> v1));
        // 查询库存点数据
        Map<String, NewStockPointVO> stockPointVOMap = CollectionUtils.isEmpty(newStockPointVOS) ? MapUtil.newHashMap() :
                newStockPointVOS.stream().collect(Collectors.toMap(NewStockPointVO::getStockPointCode, Function.identity(), (v1, v2) -> v1));
        // 查询供应商数据
        Map<String, SupplierVO> supplierMap = CollectionUtils.isEmpty(supplierVOS) ? MapUtil.newHashMap() :
                supplierVOS.stream().collect(Collectors.toMap(SupplierVO::getSupplierCode, Function.identity(), (v1, v2) -> v1));
        Iterator<ProFreeStorageDTO> iterator = checkList.iterator();
        while (iterator.hasNext()) {
            ProFreeStorageDTO proFreeStorageDTO = iterator.next();
            if (isFieldEmpty(proFreeStorageDTO.getCompanyCode())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + proFreeStorageDTO.getRowIndex() + ";[公司代码]未填写");
                dataImportInfo.setDisplayIndex(proFreeStorageDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (isFieldEmpty(proFreeStorageDTO.getPortCode())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + proFreeStorageDTO.getRowIndex() + ";[港口代码]未填写");
                dataImportInfo.setDisplayIndex(proFreeStorageDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (isFieldEmpty(proFreeStorageDTO.getCarrierCode())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + proFreeStorageDTO.getRowIndex() + ";[承运商代码]未填写");
                dataImportInfo.setDisplayIndex(proFreeStorageDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (isFieldEmpty(proFreeStorageDTO.getFreeStorage()) || !isValidNonNegativeInteger(proFreeStorageDTO.getFreeStorage())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + proFreeStorageDTO.getRowIndex() + "[免堆期]未填写或非正整数与0");
                dataImportInfo.setDisplayIndex(proFreeStorageDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (!stockPointVOMap.containsKey(proFreeStorageDTO.getPortCode())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + proFreeStorageDTO.getRowIndex() + ";[港口代码]不存在");
                dataImportInfo.setDisplayIndex(proFreeStorageDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (!supplierMap.containsKey(proFreeStorageDTO.getCarrierCode())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + proFreeStorageDTO.getRowIndex() + ";[承运商代码]不存在");
                dataImportInfo.setDisplayIndex(proFreeStorageDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (!saleOrganizeMap.containsKey(proFreeStorageDTO.getCompanyCode())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + proFreeStorageDTO.getRowIndex() + ";[公司代码]不存在");
                dataImportInfo.setDisplayIndex(proFreeStorageDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
            }
        }
    }

    private boolean isFieldEmpty(String value) {
        return value == null || value.trim().isEmpty();
    }

    private boolean isValidNonNegativeInteger(String value) {
        try {
            int intValue = Integer.parseInt(value);
            return intValue >= 0;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    @Override
    protected ImportRelatedDataHolder<ProFreeStoragePO> prepareData(List<ProFreeStorageDTO> proFreeStorageDTOS) {
        // 找到数据库现在所有的数据
        List<ProFreeStoragePO> existingData = proFreeStorageDao.selectByParams(new HashMap<>(2));
        Map<String, ProFreeStoragePO> codeToPOMap = existingData.stream().collect(Collectors
                .toMap(x -> x.getCompanyCode() + "&" + x.getPortCode() + "&" + x.getCarrierCode(), Function.identity(), (v1, v2) -> v1));
        // 组成唯一键的字段
        List<String> uniqueKeys = ListUtil.of("companyCode", "portCode", "carrierCode");
        // 外键字段
        List<String> foreignKeys = ListUtil.of();
        Map<String, List<SimpleVO>> foreignDataMap = new HashMap<>();
        return ImportRelatedDataHolder.<ProFreeStoragePO>builder()
                .existingData(existingData)
                .mainKeys(uniqueKeys)
                .foreignKeys(foreignKeys)
                .foreignDataMap(foreignDataMap)
                .existingDataMap(codeToPOMap)
                .build();
    }

}
