<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.material.plan.infrastructure.dao.MaterialPlanInventoryShiftDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanInventoryShiftPO">
        <!--@Table mrp_material_plan_inventory_shift-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="material_plan_version_id" jdbcType="VARCHAR" property="materialPlanVersionId"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="stock_point_type" jdbcType="VARCHAR" property="stockPointType"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_classify" jdbcType="VARCHAR" property="productClassify"/>
        <result column="safety_stock_days_min" jdbcType="DECIMAL" property="safetyStockDaysMin"/>
        <result column="safety_stock_days_standard" jdbcType="DECIMAL" property="safetyStockDaysStandard"/>
        <result column="safety_stock_days_max" jdbcType="DECIMAL" property="safetyStockDaysMax"/>
        <result column="safety_stock_level_min" jdbcType="VARCHAR" property="safetyStockLevelMin"/>
        <result column="safety_stock_level_standard" jdbcType="VARCHAR" property="safetyStockLevelStandard"/>
        <result column="safety_stock_level_max" jdbcType="VARCHAR" property="safetyStockLevelMax"/>
        <result column="opening_inventory" jdbcType="VARCHAR" property="openingInventory"/>
        <result column="all_ff_inventory" jdbcType="VARCHAR" property="allFfInventory"/>
        <result column="bc_opening_inventory" jdbcType="VARCHAR" property="bcOpeningInventory"/>
        <result column="demand_quantity" jdbcType="VARCHAR" property="demandQuantity"/>
        <result column="supply_quantity" jdbcType="VARCHAR" property="supplyQuantity"/>
        <result column="adjust_quantity_from_port" jdbcType="VARCHAR" property="adjustQuantityFromPort"/>
        <result column="adjust_quantity_from_float" jdbcType="VARCHAR" property="adjustQuantityFromFloat"/>
        <result column="input_quantity" jdbcType="VARCHAR" property="inputQuantity"/>
        <result column="transit_quantity_from_port" jdbcType="VARCHAR" property="transitQuantityFromPort"/>
        <result column="transit_quantity_from_float" jdbcType="VARCHAR" property="transitQuantityFromFloat"/>
        <result column="output_quantity_to_bc" jdbcType="VARCHAR" property="outputQuantityToBc"/>
        <result column="output_quantity_to_port" jdbcType="VARCHAR" property="outputQuantityToPort"/>
        <result column="decision_output_quantity_to_bc" jdbcType="VARCHAR" property="decisionOutputQuantityToBc"/>
        <result column="decision_output_quantity_to_port" jdbcType="VARCHAR" property="decisionOutputQuantityToPort"/>
        <result column="use_standard_quantity" jdbcType="VARCHAR" property="useStandardQuantity"/>
        <result column="use_replace_quantity" jdbcType="VARCHAR" property="useReplaceQuantity"/>
        <result column="used_as_replace_quantity" jdbcType="VARCHAR" property="usedAsReplaceQuantity"/>
        <result column="before_ending_inventory" jdbcType="VARCHAR" property="beforeEndingInventory"/>
        <result column="ending_inventory" jdbcType="VARCHAR" property="endingInventory"/>
        <result column="safety_stock_gap" jdbcType="VARCHAR" property="safetyStockGap"/>
        <result column="inventory_date" jdbcType="TIMESTAMP" property="inventoryDate"/>
        <result column="warning_remind_start" jdbcType="VARCHAR" property="warningRemindStart"/>
        <result column="warning_remind_end" jdbcType="VARCHAR" property="warningRemindEnd"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mrp.material.plan.vo.MaterialPlanInventoryShiftVO">
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_color" jdbcType="VARCHAR" property="productColor"/>
        <result column="product_thickness" jdbcType="VARCHAR" property="productThickness"/>
        <result column="product_length" jdbcType="VARCHAR" property="productLength"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
    </resultMap>

    <resultMap id="VOResultMap2" type="com.yhl.scp.mrp.material.plan.vo.MaterialPlanInventoryShiftVO">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="demand_quantity" jdbcType="VARCHAR" property="demandQuantity"/>
        <result column="used_as_replace_quantity" jdbcType="VARCHAR" property="usedAsReplaceQuantity"/>
        <result column="use_replace_quantity" jdbcType="VARCHAR" property="useReplaceQuantity"/>
        <result column="inventory_date" jdbcType="TIMESTAMP" property="inventoryDate"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="stock_point_type" jdbcType="VARCHAR" property="stockPointType"/>
        <result column="opening_inventory" jdbcType="VARCHAR" property="openingInventory"/>
        <result column="transit_quantity_from_port" jdbcType="VARCHAR" property="transitQuantityFromPort"/>
        <result column="product_color" jdbcType="VARCHAR" property="productColor"/>
        <result column="product_thickness" jdbcType="VARCHAR" property="productThickness"/>
        <result column="product_length" jdbcType="VARCHAR" property="productLength"/>
        <result column="product_width" jdbcType="VARCHAR" property="productWidth"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,material_plan_version_id,stock_point_code,stock_point_type,product_code,product_classify,safety_stock_days_min,
        safety_stock_days_standard,safety_stock_days_max,safety_stock_level_min,safety_stock_level_standard,
        safety_stock_level_max,opening_inventory,all_ff_inventory,bc_opening_inventory,demand_quantity,supply_quantity,
        adjust_quantity_from_port,adjust_quantity_from_float,input_quantity,transit_quantity_from_port,transit_quantity_from_float,
        output_quantity_to_bc,output_quantity_to_port,decision_output_quantity_to_bc,decision_output_quantity_to_port,
        use_standard_quantity,use_replace_quantity,used_as_replace_quantity,before_ending_inventory,ending_inventory,
        safety_stock_gap,inventory_date,warning_remind_start,warning_remind_end,
        remark,enabled,creator,create_time,modifier,modify_time
    </sql>
    <sql id="VO_Column_List">
        id
        ,inventory_shift_data_id,stock_point_code,stock_point_type,inventory_date,all_ff_inventory,safety_stock_level_min,
        safety_stock_level_standard,safety_stock_level_max,opening_inventory,bc_opening_inventory,demand_quantity,
        adjust_quantity_from_float,input_quantity,adjust_quantity_from_port,transit_quantity_from_port,
        transit_quantity_from_float,output_quantity_to_bc,output_quantity_to_port,decision_output_quantity_to_bc,
        decision_output_quantity_to_port,use_replace_quantity,used_as_replace_quantity,safety_stock_gap,ending_inventory,
        remark,enabled,creator,create_time,modifier,modify_time,product_code,product_color,product_thickness,
        product_factory_code,vehicle_model_code
    </sql>

    <sql id="VO_Demand_Calculation_Column_List">
        id,
        demand_quantity,
        used_as_replace_quantity,
        use_replace_quantity,
        inventory_date,
        product_code,
        stock_point_type,
        opening_inventory,
        transit_quantity_from_port,
        product_length,
        product_width,
        product_thickness,
        product_color
    </sql>

    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.materialPlanVersionId != null and params.materialPlanVersionId != ''">
                and material_plan_version_id = #{params.materialPlanVersionId,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointType != null and params.stockPointType != ''">
                and stock_point_type = #{params.stockPointType,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCodeList != null and params.stockPointCodeList.size() > 0">
                and stock_point_code in
                <foreach collection="params.stockPointCodeList" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCodeList != null and params.productCodeList.size() > 0">
                and product_code in
                <foreach collection="params.productCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productCodes != null and params.productCodes.size() > 0">
                and product_code in
                <foreach collection="params.productCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productStockPointCodeList != null and params.productStockPointCodeList.size() > 0">
                and CONCAT(stock_point_code, '&amp;', product_code) in
                <foreach collection="params.productStockPointCodeList" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productClassify != null and params.productClassify != ''">
                and product_classify = #{params.productClassify,jdbcType=VARCHAR}
            </if>
            <if test="params.safetyStockDaysMin != null">
                and safety_stock_days_min = #{params.safetyStockDaysMin,jdbcType=DECIMAL}
            </if>
            <if test="params.safetyStockDaysStandard != null">
                and safety_stock_days_standard = #{params.safetyStockDaysStandard,jdbcType=DECIMAL}
            </if>
            <if test="params.safetyStockDaysMax != null">
                and safety_stock_days_max = #{params.safetyStockDaysMax,jdbcType=DECIMAL}
            </if>
            <if test="params.safetyStockLevelMin != null">
                and safety_stock_level_min = #{params.safetyStockLevelMin,jdbcType=VARCHAR}
            </if>
            <if test="params.safetyStockLevelStandard != null">
                and safety_stock_level_standard = #{params.safetyStockLevelStandard,jdbcType=VARCHAR}
            </if>
            <if test="params.safetyStockLevelMax != null">
                and safety_stock_level_max = #{params.safetyStockLevelMax,jdbcType=VARCHAR}
            </if>
            <if test="params.openingInventory != null">
                and opening_inventory = #{params.openingInventory,jdbcType=VARCHAR}
            </if>
            <if test="params.allFfInventory != null">
                and all_ff_inventory = #{params.allFfInventory,jdbcType=VARCHAR}
            </if>
            <if test="params.bcOpeningInventory != null">
                and bc_opening_inventory = #{params.bcOpeningInventory,jdbcType=VARCHAR}
            </if>
            <if test="params.demandQuantity != null">
                and demand_quantity = #{params.demandQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.supplyQuantity != null">
                and supply_quantity = #{params.supplyQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.adjustQuantityFromPort != null">
                and adjust_quantity_from_port = #{params.adjustQuantityFromPort,jdbcType=VARCHAR}
            </if>
            <if test="params.adjustQuantityFromFloat != null">
                and adjust_quantity_from_float = #{params.adjustQuantityFromFloat,jdbcType=VARCHAR}
            </if>
            <if test="params.inputQuantity != null">
                and input_quantity = #{params.inputQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.transitQuantityFromPort != null">
                and transit_quantity_from_port = #{params.transitQuantityFromPort,jdbcType=VARCHAR}
            </if>
            <if test="params.transitQuantityFromFloat != null">
                and transit_quantity_from_float = #{params.transitQuantityFromFloat,jdbcType=VARCHAR}
            </if>
            <if test="params.outputQuantityToBc != null">
                and output_quantity_to_bc = #{params.outputQuantityToBc,jdbcType=VARCHAR}
            </if>
            <if test="params.outputQuantityToPort != null">
                and output_quantity_to_port = #{params.outputQuantityToPort,jdbcType=VARCHAR}
            </if>
            <if test="params.decisionOutputQuantityToBc != null">
                and decision_output_quantity_to_bc = #{params.decisionOutputQuantityToBc,jdbcType=VARCHAR}
            </if>
            <if test="params.decisionOutputQuantityToPort != null">
                and decision_output_quantity_to_port = #{params.decisionOutputQuantityToPort,jdbcType=VARCHAR}
            </if>
            <if test="params.useStandardQuantity != null">
                and use_standard_quantity = #{params.useStandardQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.useReplaceQuantity != null">
                and use_replace_quantity = #{params.useReplaceQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.usedAsReplaceQuantity != null">
                and used_as_replace_quantity = #{params.usedAsReplaceQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.beforeEndingInventory != null">
                and before_ending_inventory = #{params.beforeEndingInventory,jdbcType=VARCHAR}
            </if>
            <if test="params.endingInventory != null">
                and ending_inventory = #{params.endingInventory,jdbcType=VARCHAR}
            </if>
            <if test="params.safetyStockGap != null">
                and safety_stock_gap = #{params.safetyStockGap,jdbcType=VARCHAR}
            </if>
            <if test="params.inventoryDate != null">
                and inventory_date = #{params.inventoryDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.startDate != null">
                and inventory_date &gt;= #{params.startDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.endDate != null">
                and inventory_date &lt;= #{params.endDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.warningRemindStart != null and params.warningRemindStart != ''">
                and warning_remind_start = #{params.warningRemindStart,jdbcType=VARCHAR}
            </if>
            <if test="params.warningRemindEnd != null and params.warningRemindEnd != ''">
                and warning_remind_end = #{params.warningRemindEnd,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.adjustQuantityNotZero != null">
                and adjust_quantity_from_port != 0
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_plan_inventory_shift
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_plan_inventory_shift
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mrp_material_plan_inventory_shift
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_plan_inventory_shift
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 条件查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_glass_inventory_shift
        <include refid="Base_Where_Condition"/>
        --         and stock_point_type is not null
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanInventoryShiftPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_material_plan_inventory_shift(
        id,
        material_plan_version_id,
        stock_point_code,
        stock_point_type,
        product_code,
        product_classify,
        safety_stock_days_min,
        safety_stock_days_standard,
        safety_stock_days_max,
        safety_stock_level_min,
        safety_stock_level_standard,
        safety_stock_level_max,
        opening_inventory,
        all_ff_inventory,
        bc_opening_inventory,
        demand_quantity,
        supply_quantity,
        adjust_quantity_from_port,
        adjust_quantity_from_float,
        input_quantity,
        transit_quantity_from_port,
        transit_quantity_from_float,
        output_quantity_to_bc,
        output_quantity_to_port,
        decision_output_quantity_to_bc,
        decision_output_quantity_to_port,
        use_standard_quantity,
        use_replace_quantity,
        used_as_replace_quantity,
        before_ending_inventory,
        ending_inventory,
        safety_stock_gap,
        inventory_date,
        warning_remind_start,
        warning_remind_end,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{materialPlanVersionId,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{stockPointType,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{productClassify,jdbcType=VARCHAR},
        #{safetyStockDaysMin,jdbcType=DECIMAL},
        #{safetyStockDaysStandard,jdbcType=DECIMAL},
        #{safetyStockDaysMax,jdbcType=DECIMAL},
        #{safetyStockLevelMin,jdbcType=VARCHAR},
        #{safetyStockLevelStandard,jdbcType=VARCHAR},
        #{safetyStockLevelMax,jdbcType=VARCHAR},
        #{openingInventory,jdbcType=VARCHAR},
        #{allFfInventory,jdbcType=VARCHAR},
        #{bcOpeningInventory,jdbcType=VARCHAR},
        #{demandQuantity,jdbcType=VARCHAR},
        #{supplyQuantity,jdbcType=VARCHAR},
        #{adjustQuantityFromPort,jdbcType=VARCHAR},
        #{adjustQuantityFromFloat,jdbcType=VARCHAR},
        #{inputQuantity,jdbcType=VARCHAR},
        #{transitQuantityFromPort,jdbcType=VARCHAR},
        #{transitQuantityFromFloat,jdbcType=VARCHAR},
        #{outputQuantityToBc,jdbcType=VARCHAR},
        #{outputQuantityToPort,jdbcType=VARCHAR},
        #{decisionOutputQuantityToBc,jdbcType=VARCHAR},
        #{decisionOutputQuantityToPort,jdbcType=VARCHAR},
        #{useStandardQuantity,jdbcType=VARCHAR},
        #{useReplaceQuantity,jdbcType=VARCHAR},
        #{usedAsReplaceQuantity,jdbcType=VARCHAR},
        #{beforeEndingInventory,jdbcType=VARCHAR},
        #{endingInventory,jdbcType=VARCHAR},
        #{safetyStockGap,jdbcType=VARCHAR},
        #{inventoryDate,jdbcType=TIMESTAMP},
        #{warningRemindStart,jdbcType=VARCHAR},
        #{warningRemindEnd,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanInventoryShiftPO">
        insert into mrp_material_plan_inventory_shift(id,
                                                      material_plan_version_id,
                                                      stock_point_code,
                                                      stock_point_type,
                                                      product_code,
                                                      product_classify,
                                                      safety_stock_days_min,
                                                      safety_stock_days_standard,
                                                      safety_stock_days_max,
                                                      safety_stock_level_min,
                                                      safety_stock_level_standard,
                                                      safety_stock_level_max,
                                                      opening_inventory,
                                                      all_ff_inventory,
                                                      bc_opening_inventory,
                                                      demand_quantity,
                                                      supply_quantity,
                                                      adjust_quantity_from_port,
                                                      adjust_quantity_from_float,
                                                      input_quantity,
                                                      transit_quantity_from_port,
                                                      transit_quantity_from_float,
                                                      output_quantity_to_bc,
                                                      output_quantity_to_port,
                                                      decision_output_quantity_to_bc,
                                                      decision_output_quantity_to_port,
                                                      use_standard_quantity,
                                                      use_replace_quantity,
                                                      used_as_replace_quantity,
                                                      before_ending_inventory,
                                                      ending_inventory,
                                                      safety_stock_gap,
                                                      inventory_date,
                                                      warning_remind_start,
                                                      warning_remind_end,
                                                      remark,
                                                      enabled,
                                                      creator,
                                                      create_time,
                                                      modifier,
                                                      modify_time)
        values (#{id,jdbcType=VARCHAR},
                #{materialPlanVersionId,jdbcType=VARCHAR},
                #{stockPointCode,jdbcType=VARCHAR},
                #{stockPointType,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{productClassify,jdbcType=VARCHAR},
                #{safetyStockDaysMin,jdbcType=DECIMAL},
                #{safetyStockDaysStandard,jdbcType=DECIMAL},
                #{safetyStockDaysMax,jdbcType=DECIMAL},
                #{safetyStockLevelMin,jdbcType=VARCHAR},
                #{safetyStockLevelStandard,jdbcType=VARCHAR},
                #{safetyStockLevelMax,jdbcType=VARCHAR},
                #{openingInventory,jdbcType=VARCHAR},
                #{allFfInventory,jdbcType=VARCHAR},
                #{bcOpeningInventory,jdbcType=VARCHAR},
                #{demandQuantity,jdbcType=VARCHAR},
                #{supplyQuantity,jdbcType=VARCHAR},
                #{adjustQuantityFromPort,jdbcType=VARCHAR},
                #{adjustQuantityFromFloat,jdbcType=VARCHAR},
                #{inputQuantity,jdbcType=VARCHAR},
                #{transitQuantityFromPort,jdbcType=VARCHAR},
                #{transitQuantityFromFloat,jdbcType=VARCHAR},
                #{outputQuantityToBc,jdbcType=VARCHAR},
                #{outputQuantityToPort,jdbcType=VARCHAR},
                #{decisionOutputQuantityToBc,jdbcType=VARCHAR},
                #{decisionOutputQuantityToPort,jdbcType=VARCHAR},
                #{useStandardQuantity,jdbcType=VARCHAR},
                #{useReplaceQuantity,jdbcType=VARCHAR},
                #{usedAsReplaceQuantity,jdbcType=VARCHAR},
                #{beforeEndingInventory,jdbcType=VARCHAR},
                #{endingInventory,jdbcType=VARCHAR},
                #{safetyStockGap,jdbcType=VARCHAR},
                #{inventoryDate,jdbcType=TIMESTAMP},
                #{warningRemindStart,jdbcType=VARCHAR},
                #{warningRemindEnd,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_material_plan_inventory_shift(
        id,
        material_plan_version_id,
        stock_point_code,
        stock_point_type,
        product_code,
        product_classify,
        safety_stock_days_min,
        safety_stock_days_standard,
        safety_stock_days_max,
        safety_stock_level_min,
        safety_stock_level_standard,
        safety_stock_level_max,
        opening_inventory,
        all_ff_inventory,
        bc_opening_inventory,
        demand_quantity,
        supply_quantity,
        adjust_quantity_from_port,
        adjust_quantity_from_float,
        input_quantity,
        transit_quantity_from_port,
        transit_quantity_from_float,
        output_quantity_to_bc,
        output_quantity_to_port,
        decision_output_quantity_to_bc,
        decision_output_quantity_to_port,
        use_standard_quantity,
        use_replace_quantity,
        used_as_replace_quantity,
        before_ending_inventory,
        ending_inventory,
        safety_stock_gap,
        inventory_date,
        warning_remind_start,
        warning_remind_end,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.materialPlanVersionId,jdbcType=VARCHAR},
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.stockPointType,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.productClassify,jdbcType=VARCHAR},
            #{entity.safetyStockDaysMin,jdbcType=DECIMAL},
            #{entity.safetyStockDaysStandard,jdbcType=DECIMAL},
            #{entity.safetyStockDaysMax,jdbcType=DECIMAL},
            #{entity.safetyStockLevelMin,jdbcType=VARCHAR},
            #{entity.safetyStockLevelStandard,jdbcType=VARCHAR},
            #{entity.safetyStockLevelMax,jdbcType=VARCHAR},
            #{entity.openingInventory,jdbcType=VARCHAR},
            #{entity.allFfInventory,jdbcType=VARCHAR},
            #{entity.bcOpeningInventory,jdbcType=VARCHAR},
            #{entity.demandQuantity,jdbcType=VARCHAR},
            #{entity.supplyQuantity,jdbcType=VARCHAR},
            #{entity.adjustQuantityFromPort,jdbcType=VARCHAR},
            #{entity.adjustQuantityFromFloat,jdbcType=VARCHAR},
            #{entity.inputQuantity,jdbcType=VARCHAR},
            #{entity.transitQuantityFromPort,jdbcType=VARCHAR},
            #{entity.transitQuantityFromFloat,jdbcType=VARCHAR},
            #{entity.outputQuantityToBc,jdbcType=VARCHAR},
            #{entity.outputQuantityToPort,jdbcType=VARCHAR},
            #{entity.decisionOutputQuantityToBc,jdbcType=VARCHAR},
            #{entity.decisionOutputQuantityToPort,jdbcType=VARCHAR},
            #{entity.useStandardQuantity,jdbcType=VARCHAR},
            #{entity.useReplaceQuantity,jdbcType=VARCHAR},
            #{entity.usedAsReplaceQuantity,jdbcType=VARCHAR},
            #{entity.beforeEndingInventory,jdbcType=VARCHAR},
            #{entity.endingInventory,jdbcType=VARCHAR},
            #{entity.safetyStockGap,jdbcType=VARCHAR},
            #{entity.inventoryDate,jdbcType=TIMESTAMP},
            #{entity.warningRemindStart,jdbcType=VARCHAR},
            #{entity.warningRemindEnd,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_material_plan_inventory_shift(
        id,
        material_plan_version_id,
        stock_point_code,
        stock_point_type,
        product_code,
        product_classify,
        safety_stock_days_min,
        safety_stock_days_standard,
        safety_stock_days_max,
        safety_stock_level_min,
        safety_stock_level_standard,
        safety_stock_level_max,
        opening_inventory,
        all_ff_inventory,
        bc_opening_inventory,
        demand_quantity,
        supply_quantity,
        adjust_quantity_from_port,
        adjust_quantity_from_float,
        input_quantity,
        transit_quantity_from_port,
        transit_quantity_from_float,
        output_quantity_to_bc,
        output_quantity_to_port,
        decision_output_quantity_to_bc,
        decision_output_quantity_to_port,
        use_standard_quantity,
        use_replace_quantity,
        used_as_replace_quantity,
        before_ending_inventory,
        ending_inventory,
        safety_stock_gap,
        inventory_date,
        warning_remind_start,
        warning_remind_end,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.materialPlanVersionId,jdbcType=VARCHAR},
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.stockPointType,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.productClassify,jdbcType=VARCHAR},
            #{entity.safetyStockDaysMin,jdbcType=DECIMAL},
            #{entity.safetyStockDaysStandard,jdbcType=DECIMAL},
            #{entity.safetyStockDaysMax,jdbcType=DECIMAL},
            #{entity.safetyStockLevelMin,jdbcType=VARCHAR},
            #{entity.safetyStockLevelStandard,jdbcType=VARCHAR},
            #{entity.safetyStockLevelMax,jdbcType=VARCHAR},
            #{entity.openingInventory,jdbcType=VARCHAR},
            #{entity.allFfInventory,jdbcType=VARCHAR},
            #{entity.bcOpeningInventory,jdbcType=VARCHAR},
            #{entity.demandQuantity,jdbcType=VARCHAR},
            #{entity.supplyQuantity,jdbcType=VARCHAR},
            #{entity.adjustQuantityFromPort,jdbcType=VARCHAR},
            #{entity.adjustQuantityFromFloat,jdbcType=VARCHAR},
            #{entity.inputQuantity,jdbcType=VARCHAR},
            #{entity.transitQuantityFromPort,jdbcType=VARCHAR},
            #{entity.transitQuantityFromFloat,jdbcType=VARCHAR},
            #{entity.outputQuantityToBc,jdbcType=VARCHAR},
            #{entity.outputQuantityToPort,jdbcType=VARCHAR},
            #{entity.decisionOutputQuantityToBc,jdbcType=VARCHAR},
            #{entity.decisionOutputQuantityToPort,jdbcType=VARCHAR},
            #{entity.useStandardQuantity,jdbcType=VARCHAR},
            #{entity.useReplaceQuantity,jdbcType=VARCHAR},
            #{entity.usedAsReplaceQuantity,jdbcType=VARCHAR},
            #{entity.beforeEndingInventory,jdbcType=VARCHAR},
            #{entity.endingInventory,jdbcType=VARCHAR},
            #{entity.safetyStockGap,jdbcType=VARCHAR},
            #{entity.inventoryDate,jdbcType=TIMESTAMP},
            #{entity.warningRemindStart,jdbcType=VARCHAR},
            #{entity.warningRemindEnd,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanInventoryShiftPO">
        update mrp_material_plan_inventory_shift
        set material_plan_version_id         = #{materialPlanVersionId,jdbcType=VARCHAR},
            stock_point_code                 = #{stockPointCode,jdbcType=VARCHAR},
            stock_point_type                = #{stockPointType,jdbcType=VARCHAR},
            product_code                     = #{productCode,jdbcType=VARCHAR},
            product_classify                 = #{productClassify,jdbcType=VARCHAR},
            safety_stock_days_min            = #{safetyStockDaysMin,jdbcType=DECIMAL},
            safety_stock_days_standard       = #{safetyStockDaysStandard,jdbcType=DECIMAL},
            safety_stock_days_max            = #{safetyStockDaysMax,jdbcType=DECIMAL},
            safety_stock_level_min           = #{safetyStockLevelMin,jdbcType=VARCHAR},
            safety_stock_level_standard      = #{safetyStockLevelStandard,jdbcType=VARCHAR},
            safety_stock_level_max           = #{safetyStockLevelMax,jdbcType=VARCHAR},
            opening_inventory                = #{openingInventory,jdbcType=VARCHAR},
            all_ff_inventory                 = #{allFfInventory,jdbcType=VARCHAR},
            bc_opening_inventory             = #{bcOpeningInventory,jdbcType=VARCHAR},
            demand_quantity                  = #{demandQuantity,jdbcType=VARCHAR},
            supply_quantity                  = #{supplyQuantity,jdbcType=VARCHAR},
            adjust_quantity_from_port        = #{adjustQuantityFromPort,jdbcType=VARCHAR},
            adjust_quantity_from_float       = #{adjustQuantityFromFloat,jdbcType=VARCHAR},
            input_quantity                   = #{inputQuantity,jdbcType=VARCHAR},
            transit_quantity_from_port       = #{transitQuantityFromPort,jdbcType=VARCHAR},
            transit_quantity_from_float      = #{transitQuantityFromFloat,jdbcType=VARCHAR},
            output_quantity_to_bc            = #{outputQuantityToBc,jdbcType=VARCHAR},
            output_quantity_to_port          = #{outputQuantityToPort,jdbcType=VARCHAR},
            decision_output_quantity_to_bc   = #{decisionOutputQuantityToBc,jdbcType=VARCHAR},
            decision_output_quantity_to_port = #{decisionOutputQuantityToPort,jdbcType=VARCHAR},
            use_standard_quantity            = #{useStandardQuantity,jdbcType=VARCHAR},
            use_replace_quantity             = #{useReplaceQuantity,jdbcType=VARCHAR},
            used_as_replace_quantity         = #{usedAsReplaceQuantity,jdbcType=VARCHAR},
            before_ending_inventory          = #{beforeEndingInventory,jdbcType=VARCHAR},
            ending_inventory                 = #{endingInventory,jdbcType=VARCHAR},
            safety_stock_gap                 = #{safetyStockGap,jdbcType=VARCHAR},
            inventory_date                   = #{inventoryDate,jdbcType=TIMESTAMP},
            warning_remind_start             = #{warningRemindStart,jdbcType=VARCHAR},
            warning_remind_end               = #{warningRemindEnd,jdbcType=VARCHAR},
            remark                           = #{remark,jdbcType=VARCHAR},
            enabled                          = #{enabled,jdbcType=VARCHAR},
            modifier                         = #{modifier,jdbcType=VARCHAR},
            modify_time                      = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanInventoryShiftPO">
        update mrp_material_plan_inventory_shift
        <set>
            <if test="item.materialPlanVersionId != null and item.materialPlanVersionId != ''">
                material_plan_version_id = #{item.materialPlanVersionId,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointType != null and item.stockPointType != ''">
                stock_point_type = #{item.stockPointType,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productClassify != null and item.productClassify != ''">
                product_classify = #{item.productClassify,jdbcType=VARCHAR},
            </if>
            <if test="item.safetyStockDaysMin != null">
                safety_stock_days_min = #{item.safetyStockDaysMin,jdbcType=DECIMAL},
            </if>
            <if test="item.safetyStockDaysStandard != null">
                safety_stock_days_standard = #{item.safetyStockDaysStandard,jdbcType=DECIMAL},
            </if>
            <if test="item.safetyStockDaysMax != null">
                safety_stock_days_max = #{item.safetyStockDaysMax,jdbcType=DECIMAL},
            </if>
            <if test="item.safetyStockLevelMin != null">
                safety_stock_level_min = #{item.safetyStockLevelMin,jdbcType=VARCHAR},
            </if>
            <if test="item.safetyStockLevelStandard != null">
                safety_stock_level_standard = #{item.safetyStockLevelStandard,jdbcType=VARCHAR},
            </if>
            <if test="item.safetyStockLevelMax != null">
                safety_stock_level_max = #{item.safetyStockLevelMax,jdbcType=VARCHAR},
            </if>
            <if test="item.openingInventory != null">
                opening_inventory = #{item.openingInventory,jdbcType=VARCHAR},
            </if>
            <if test="item.allFfInventory != null">
                all_ff_inventory = #{item.allFfInventory,jdbcType=VARCHAR},
            </if>
            <if test="item.bcOpeningInventory != null">
                bc_opening_inventory = #{item.bcOpeningInventory,jdbcType=VARCHAR},
            </if>
            <if test="item.demandQuantity != null">
                demand_quantity = #{item.demandQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.supplyQuantity != null">
                supply_quantity = #{item.supplyQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.adjustQuantityFromPort != null">
                adjust_quantity_from_port = #{item.adjustQuantityFromPort,jdbcType=VARCHAR},
            </if>
            <if test="item.adjustQuantityFromFloat != null">
                adjust_quantity_from_float = #{item.adjustQuantityFromFloat,jdbcType=VARCHAR},
            </if>
            <if test="item.inputQuantity != null">
                input_quantity = #{item.inputQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.transitQuantityFromPort != null">
                transit_quantity_from_port = #{item.transitQuantityFromPort,jdbcType=VARCHAR},
            </if>
            <if test="item.transitQuantityFromFloat != null">
                transit_quantity_from_float = #{item.transitQuantityFromFloat,jdbcType=VARCHAR},
            </if>
            <if test="item.outputQuantityToBc != null">
                output_quantity_to_bc = #{item.outputQuantityToBc,jdbcType=VARCHAR},
            </if>
            <if test="item.outputQuantityToPort != null">
                output_quantity_to_port = #{item.outputQuantityToPort,jdbcType=VARCHAR},
            </if>
            <if test="item.decisionOutputQuantityToBc != null">
                decision_output_quantity_to_bc = #{item.decisionOutputQuantityToBc,jdbcType=VARCHAR},
            </if>
            <if test="item.decisionOutputQuantityToPort != null">
                decision_output_quantity_to_port = #{item.decisionOutputQuantityToPort,jdbcType=VARCHAR},
            </if>
            <if test="item.useStandardQuantity != null">
                use_standard_quantity = #{item.useStandardQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.useReplaceQuantity != null">
                use_replace_quantity = #{item.useReplaceQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.usedAsReplaceQuantity != null">
                used_as_replace_quantity = #{item.usedAsReplaceQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.beforeEndingInventory != null">
                before_ending_inventory = #{item.beforeEndingInventory,jdbcType=VARCHAR},
            </if>
            <if test="item.endingInventory != null">
                ending_inventory = #{item.endingInventory,jdbcType=VARCHAR},
            </if>
            <if test="item.safetyStockGap != null">
                safety_stock_gap = #{item.safetyStockGap,jdbcType=VARCHAR},
            </if>
            <if test="item.inventoryDate != null">
                inventory_date = #{item.inventoryDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.warningRemindStart != null and item.warningRemindStart != ''">
                warning_remind_start = #{item.warningRemindStart,jdbcType=VARCHAR},
            </if>
            <if test="item.warningRemindEnd != null and item.warningRemindEnd != ''">
                warning_remind_end = #{item.warningRemindEnd,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_material_plan_inventory_shift
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="material_plan_version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialPlanVersionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_classify = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productClassify,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="safety_stock_days_min = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.safetyStockDaysMin,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="safety_stock_days_standard = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.safetyStockDaysStandard,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="safety_stock_days_max = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.safetyStockDaysMax,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="safety_stock_level_min = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.safetyStockLevelMin,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="safety_stock_level_standard = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.safetyStockLevelStandard,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="safety_stock_level_max = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.safetyStockLevelMax,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="opening_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.openingInventory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="all_ff_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.allFfInventory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="bc_opening_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.bcOpeningInventory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supply_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplyQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="adjust_quantity_from_port = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.adjustQuantityFromPort,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="adjust_quantity_from_float = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.adjustQuantityFromFloat,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="input_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inputQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transit_quantity_from_port = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transitQuantityFromPort,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transit_quantity_from_float = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transitQuantityFromFloat,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="output_quantity_to_bc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.outputQuantityToBc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="output_quantity_to_port = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.outputQuantityToPort,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="decision_output_quantity_to_bc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.decisionOutputQuantityToBc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="decision_output_quantity_to_port = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.decisionOutputQuantityToPort,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="use_standard_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.useStandardQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="use_replace_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.useReplaceQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="used_as_replace_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.usedAsReplaceQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="before_ending_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.beforeEndingInventory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="ending_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.endingInventory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="safety_stock_gap = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.safetyStockGap,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="inventory_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inventoryDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="warning_remind_start = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.warningRemindStart,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="warning_remind_end = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.warningRemindEnd,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_material_plan_inventory_shift
            <set>
                <if test="item.materialPlanVersionId != null and item.materialPlanVersionId != ''">
                    material_plan_version_id = #{item.materialPlanVersionId,jdbcType=VARCHAR},
                </if>
                <if test="item.stockPointCode != null and item.stockPointCode != ''">
                    stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
                </if>
                <if test="item.stockPointType != null and item.stockPointType != ''">
                    stock_point_type = #{item.stockPointType,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productClassify != null and item.productClassify != ''">
                    product_classify = #{item.productClassify,jdbcType=VARCHAR},
                </if>
                <if test="item.safetyStockDaysMin != null">
                    safety_stock_days_min = #{item.safetyStockDaysMin,jdbcType=DECIMAL},
                </if>
                <if test="item.safetyStockDaysStandard != null">
                    safety_stock_days_standard = #{item.safetyStockDaysStandard,jdbcType=DECIMAL},
                </if>
                <if test="item.safetyStockDaysMax != null">
                    safety_stock_days_max = #{item.safetyStockDaysMax,jdbcType=DECIMAL},
                </if>
                <if test="item.safetyStockLevelMin != null">
                    safety_stock_level_min = #{item.safetyStockLevelMin,jdbcType=VARCHAR},
                </if>
                <if test="item.safetyStockLevelStandard != null">
                    safety_stock_level_standard = #{item.safetyStockLevelStandard,jdbcType=VARCHAR},
                </if>
                <if test="item.safetyStockLevelMax != null">
                    safety_stock_level_max = #{item.safetyStockLevelMax,jdbcType=VARCHAR},
                </if>
                <if test="item.openingInventory != null">
                    opening_inventory = #{item.openingInventory,jdbcType=VARCHAR},
                </if>
                <if test="item.allFfInventory != null">
                    all_ff_inventory = #{item.allFfInventory,jdbcType=VARCHAR},
                </if>
                <if test="item.bcOpeningInventory != null">
                    bc_opening_inventory = #{item.bcOpeningInventory,jdbcType=VARCHAR},
                </if>
                <if test="item.demandQuantity != null">
                    demand_quantity = #{item.demandQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.supplyQuantity != null">
                    supply_quantity = #{item.supplyQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.adjustQuantityFromPort != null">
                    adjust_quantity_from_port = #{item.adjustQuantityFromPort,jdbcType=VARCHAR},
                </if>
                <if test="item.adjustQuantityFromFloat != null">
                    adjust_quantity_from_float = #{item.adjustQuantityFromFloat,jdbcType=VARCHAR},
                </if>
                <if test="item.inputQuantity != null">
                    input_quantity = #{item.inputQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.transitQuantityFromPort != null">
                    transit_quantity_from_port = #{item.transitQuantityFromPort,jdbcType=VARCHAR},
                </if>
                <if test="item.transitQuantityFromFloat != null">
                    transit_quantity_from_float = #{item.transitQuantityFromFloat,jdbcType=VARCHAR},
                </if>
                <if test="item.outputQuantityToBc != null">
                    output_quantity_to_bc = #{item.outputQuantityToBc,jdbcType=VARCHAR},
                </if>
                <if test="item.outputQuantityToPort != null">
                    output_quantity_to_port = #{item.outputQuantityToPort,jdbcType=VARCHAR},
                </if>
                <if test="item.decisionOutputQuantityToBc != null">
                    decision_output_quantity_to_bc = #{item.decisionOutputQuantityToBc,jdbcType=VARCHAR},
                </if>
                <if test="item.decisionOutputQuantityToPort != null">
                    decision_output_quantity_to_port = #{item.decisionOutputQuantityToPort,jdbcType=VARCHAR},
                </if>
                <if test="item.useStandardQuantity != null">
                    use_standard_quantity = #{item.useStandardQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.useReplaceQuantity != null">
                    use_replace_quantity = #{item.useReplaceQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.usedAsReplaceQuantity != null">
                    used_as_replace_quantity = #{item.usedAsReplaceQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.beforeEndingInventory != null">
                    before_ending_inventory = #{item.beforeEndingInventory,jdbcType=VARCHAR},
                </if>
                <if test="item.endingInventory != null">
                    ending_inventory = #{item.endingInventory,jdbcType=VARCHAR},
                </if>
                <if test="item.safetyStockGap != null">
                    safety_stock_gap = #{item.safetyStockGap,jdbcType=VARCHAR},
                </if>
                <if test="item.inventoryDate != null">
                    inventory_date = #{item.inventoryDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.warningRemindStart != null and item.warningRemindStart != ''">
                    warning_remind_start = #{item.warningRemindStart,jdbcType=VARCHAR},
                </if>
                <if test="item.warningRemindEnd != null and item.warningRemindEnd != ''">
                    warning_remind_end = #{item.warningRemindEnd,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_material_plan_inventory_shift
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_material_plan_inventory_shift where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <select id="selectForDemandCalculation" resultMap="VOResultMap2">
        SELECT
        <include refid="VO_Demand_Calculation_Column_List"/>
        FROM
        v_mrp_material_glass_consult_summary
        WHERE 1=1
        <if test="params.stockPointCode != null and params.stockPointCode != ''">
            and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
        </if>
        <if test="params.stockPointType != null and params.stockPointType != ''">
            and stock_point_type = #{params.stockPointType,jdbcType=VARCHAR}
        </if>
        <if test="params.outputQuantityToBc != null">
            and product_color = #{params.outputQuantityToBc,jdbcType=VARCHAR}
        </if>
        <if test="params.materialPlanVersionId != null">
            and material_plan_version_id = #{params.materialPlanVersionId,jdbcType=VARCHAR}
        </if>
        <if test="params.startProductThickness != null">
            and product_thickness <![CDATA[ >= ]]> #{params.startProductThickness,jdbcType=VARCHAR}
        </if>
        <if test="params.endProductThickness != null">
            and product_thickness <![CDATA[ <= ]]> #{params.endProductThickness,jdbcType=VARCHAR}
        </if>
        <if test="params.startDate!=null">
            and inventory_date <![CDATA[ >= ]]> #{params.startDate,jdbcType=VARCHAR}
        </if>
        <if test="params.endDate!=null">
            and inventory_date <![CDATA[ <= ]]> #{params.endDate,jdbcType=VARCHAR}
        </if>
        <if test="params.productColor != null and params.productColor != ''">
            and product_color = #{params.productColor,jdbcType=VARCHAR}
        </if>
        <if test="params.planUserProductCodeList != null and params.planUserProductCodeList.size() > 0">
            and product_code in
            <foreach collection="params.planUserProductCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

    <select id="selectGroupMaterialInfo" resultMap="VOResultMap">
        select
        stock_point_type, product_code
        from mrp_material_plan_inventory_shift
        where 1=1
        <if test="params.materialPlanVersionId != null and params.materialPlanVersionId != ''">
            and material_plan_version_id = #{params.materialPlanVersionId,jdbcType=VARCHAR}
        </if>
        <if test="params.productCode != null and params.productCode != ''">
            and product_code = #{params.productCode,jdbcType=VARCHAR}
        </if>
        <if test="params.productClassify != null and params.productClassify != ''">
            and product_classify = #{params.productClassify,jdbcType=VARCHAR}
        </if>
        <if test="params.productCodes != null and params.productCodes.size() > 0">
            and product_code in
            <foreach collection="params.productCodes" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        group by stock_point_type, product_code
    </select>
    <select id="selectGroupGlassInfo" resultMap="VOResultMap">
        select
        product_code
        from v_mrp_material_plan_inventory_shift
        --         where stock_point_type is not null
        where 1=1
        <if test="params.materialPlanVersionId != null and params.materialPlanVersionId != ''">
            and material_plan_version_id = #{params.materialPlanVersionId,jdbcType=VARCHAR}
        </if>
        <if test="params.productCode != null and params.productCode != ''">
            and product_code = #{params.productCode,jdbcType=VARCHAR}
        </if>
        <if test="params.stockPointCode != null and params.stockPointCode != ''">
            and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
        </if>

        <if test="params.productColor != null and params.productColor != ''">
            and product_color = #{params.productColor,jdbcType=VARCHAR}
        </if>
        <if test="params.productThickness != null and params.productThickness != ''">
            and product_thickness = #{params.productThickness,jdbcType=VARCHAR}
        </if>
        <if test="params.productClassify != null and params.productClassify != ''">
            and product_classify = #{params.productClassify,jdbcType=VARCHAR}
        </if>
        <if test="params.productCodes != null and params.productCodes.size() > 0">
            and product_code in
            <foreach collection="params.productCodes" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="params.stockPointCodes != null and params.stockPointCodes.size() > 0">
            and stock_point_code in
            <foreach collection="params.stockPointCodes" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="params.warningRemindStart != null and params.warningRemindStart != ''">
            and warning_remind_start = #{params.warningRemindStart,jdbcType=VARCHAR}
        </if>
        <if test="params.warningRemindEnd != null and params.warningRemindEnd != ''">
            and warning_remind_end = #{params.warningRemindEnd,jdbcType=VARCHAR}
        </if>
        <if test="params.planUserProductCodeList != null and params.planUserProductCodeList.size() > 0">
            and product_code in
            <foreach collection="params.planUserProductCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        group by product_code
    </select>
    <select id="selectGroupGlassInfoTotal" resultMap="VOResultMap">
        SELECT DISTINCT
        product_code,
        product_thickness,
        product_color,
        product_factory_code,
        vehicle_model_code
        FROM
         v_glass_inventory_shift
        where 1=1
        <if test="params.productCodes != null and params.productCodes.size() > 0">
            and product_code in
            <foreach collection="params.productCodes" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="params.productColor != null and params.productColor != ''">
            and product_color = #{params.productColor,jdbcType=VARCHAR}
        </if>
        <if test="params.productThickness != null and params.productThickness != ''">
            and product_thickness = #{params.productThickness,jdbcType=VARCHAR}
        </if>
        <if test="params.productClassify != null and params.productClassify != ''">
            and product_classify = #{params.productClassify,jdbcType=VARCHAR}
        </if>
        <if test="params.materialPlanner != null and params.materialPlanner != ''">
            and find_in_set(#{params.materialPlanner,jdbcType=VARCHAR}, material_planner) > 0
        </if>
        <if test="params.productCode != null and params.productCode != ''">
            and product_code = #{params.productCode,jdbcType=VARCHAR}
        </if>
        <if test="params.stockPointType != null and params.stockPointType != ''">
            and stock_point_type = #{params.stockPointType,jdbcType=VARCHAR}
        </if>
        <if test="params.warningRemindStart != null and params.warningRemindStart != ''">
            and warning_remind_start = #{params.warningRemindStart,jdbcType=VARCHAR}
        </if>
        <if test="params.warningRemindEnd != null and params.warningRemindEnd != ''">
            and warning_remind_end = #{params.warningRemindEnd,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectMaterialInventoryShift" resultMap="VOResultMap">
        SELECT
        <include refid="VO_Column_List"/>
        FROM
        v_mrp_material_plan_inventory_shift
        <where>
            <if test="productCodeList != null and productCodeList.size() > 0">
                AND product_code IN
                <foreach collection="productCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item, jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="scopeStart != null and scopeEnd != null">
                and inventory_date between #{scopeStart} and #{scopeEnd}
            </if>
            <if test="latestVersionId != null">
                and material_plan_version_id = #{latestVersionId}
            </if>
        </where>
    </select>

    <select id="selectMonthTotalDemand" resultMap="VOResultMap">
        select
        product_code,
        inventory_date,
        demand_quantity
        from mrp_material_plan_inventory_shift
        where 1=1
        <if test="latestVersionId != null">
            and material_plan_version_id = #{latestVersionId}
        </if>
    </select>

    <select id="getLastCreateTime" resultType="java.util.Date">
        select
        create_time
        from mrp_material_plan_inventory_shift
        where product_code in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        order by create_time desc
        limit 1
    </select>

    <delete id="deleteByVersionId">
        delete
        from mrp_material_plan_inventory_shift
        where material_plan_version_id = #{materialPlanVersionId,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteByProductCodes">
        delete
        from mrp_material_plan_inventory_shift
        where product_code in
        <foreach collection="productCodeList" item="item" index="index" open="(" separator="," close=")">
            #{item, jdbcType=VARCHAR}
        </foreach>
    </delete>

    <delete id="deleteByVersionIdAndProductCode">
        delete
        from mrp_material_plan_inventory_shift
        where material_plan_version_id = #{materialPlanVersionId,jdbcType=VARCHAR}
          and product_code = #{productCode,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteAll">
        delete
        from mrp_material_plan_inventory_shift
    </delete>
</mapper>
