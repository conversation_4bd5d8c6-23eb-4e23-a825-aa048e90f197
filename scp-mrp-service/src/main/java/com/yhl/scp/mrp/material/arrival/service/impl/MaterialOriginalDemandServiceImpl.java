package com.yhl.scp.mrp.material.arrival.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Random;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.alibaba.nacos.shaded.com.google.common.collect.ImmutableMap;
import com.ibm.icu.text.SimpleDateFormat;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.passenger.vo.PassengerCarSaleVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mrp.material.arrival.dto.MaterialOriginalDemandDTO;
import com.yhl.scp.mrp.material.arrival.service.MaterialOriginalDemandService;
import com.yhl.scp.mrp.material.arrival.vo.MaterialForecastDetailVO;
import com.yhl.scp.mrp.material.arrival.vo.MaterialOriginalDemandPushingVO;
import com.yhl.scp.mrp.material.arrival.vo.MaterialOriginalDemandVO;
import com.yhl.scp.mrp.material.arrival.vo.MaterialPushingDetailVO;
import com.yhl.scp.mrp.material.arrival.vo.MaterialVehicleModelForecastProductVO;
import com.yhl.scp.mrp.material.arrival.vo.MaterialVehicleModelForecastVO;
import com.yhl.scp.mrp.supplier.service.MaterialSupplierPurchaseService;
import com.yhl.scp.mrp.supplier.vo.MaterialSupplierPurchaseVO;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>MaterialOriginalDemandServiceImpl</code>
 * <p>
 * 材料原始需求追溯应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-04 14:17:36
 */
@Slf4j
@Service
public class MaterialOriginalDemandServiceImpl implements MaterialOriginalDemandService {
	
	@Resource
    private MaterialSupplierPurchaseService materialSupplierPurchaseService;
	
	@Resource
    private NewMdsFeign newMdsFeign;
	
	@Resource
    private DfpFeign dfpFeign;
	
	@SuppressWarnings("unlikely-arg-type")
	@Override
//	public MaterialOriginalDemandVO selectSourceDetail(MaterialOriginalDemandDTO materialOriginalDemandDTO) {
//		MaterialOriginalDemandVO returnResult = new MaterialOriginalDemandVO();
//		//1.获取材料推移数据信息
//		String stockPointCode = materialOriginalDemandDTO.getStockPointCode();
//		String productCode = materialOriginalDemandDTO.getProductCode();
//		//1.1：首先根据库存点编码，物料编码，获取物料id
//		List<NewProductStockPointVO> productInfoList = newMdsFeign.selectProductStockPointByParams(SystemHolder.getScenario(), 
//				ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(), 
//    			"stockPointCode" , materialOriginalDemandDTO.getStockPointCode(),
//    			"productCode" , materialOriginalDemandDTO.getProductCode()));
//		if(CollUtil.isEmpty(productInfoList)) {
//			String errorMsg = String.format("库存点【%s】物料编码【%s】未找到对应的物料数据信息", 
//					materialOriginalDemandDTO.getStockPointCode(), 
//					materialOriginalDemandDTO.getProductCode());
//			throw new BusinessException(errorMsg);
//		}
//		NewProductStockPointVO currentProduct = productInfoList.get(0);
//		//1.2：获取最小启订量和下单提前期
//		List<MaterialSupplierPurchaseVO> purchaseList = materialSupplierPurchaseService
//				.selectByParams(ImmutableMap.of("stockPointCode", stockPointCode,
//                "materialCode", productCode));
//		MaterialSupplierPurchaseVO materialSupplierPurchaseVO = new MaterialSupplierPurchaseVO();
//		if(CollUtil.isNotEmpty(purchaseList)) {
//			materialSupplierPurchaseVO = purchaseList.get(0);
//		}
//		MaterialOriginalDemandPushingVO modp = new MaterialOriginalDemandPushingVO();
//		modp.setSortNo(1);
//		modp.setProductCode(productCode);
//		modp.setProductName(currentProduct.getProductName());
//		modp.setMinOrderQty(materialSupplierPurchaseVO.getMinOrderQty());
//		modp.setOrderPlacementLeadTimeDay(materialSupplierPurchaseVO.getOrderPlacementLeadTimeDay());
//		//1.3：维护明细数据
//		List<MaterialPushingDetailVO> materialPushingDetailList = Lists.newArrayList();
//		//预测起始月份，结束月份所有的月份
//		List<String> allMonth = getMonthBetweenDate(
//				materialOriginalDemandDTO.getStartForecastMonth(),
//				materialOriginalDemandDTO.getEndForecastMonth(), null);
//		BigDecimal orderPlacementLeadTimeDay = materialSupplierPurchaseVO.getOrderPlacementLeadTimeDay();
//		List<String> allHeaderMonth = allMonth;
//		Integer moveMonthNum = 0;
//		if(orderPlacementLeadTimeDay != null) {
//			moveMonthNum = orderPlacementLeadTimeDay.divide(BigDecimal.valueOf(30), 0, BigDecimal.ROUND_UP).intValue();
//			allHeaderMonth = getMonthBetweenDate(
//					materialOriginalDemandDTO.getStartForecastMonth(),
//					materialOriginalDemandDTO.getEndForecastMonth(), moveMonthNum);
//		}
//		Random random = new Random();
//        int min = 200;
//        int max = 700;
//        int subMin = 200;
//        int subMax = 700;
//        Map<String, BigDecimal> forecastQuantityMap = new HashMap<>();
//        Map<String, BigDecimal> consumeRateMap = new HashMap<>();
//		for (int i = 0; i < allMonth.size() + 2; i++) {
//			MaterialPushingDetailVO materialPushingDetailVO = new MaterialPushingDetailVO();
//			if(i < allMonth.size()) {
//				materialPushingDetailVO.setSituationDesc(allMonth.get(i) + "预测量");
//			}else if (i == allMonth.size()) {
//				materialPushingDetailVO.setSituationDesc("实际消耗量");
//			}else if (i > allMonth.size()) {
//				materialPushingDetailVO.setSituationDesc("实际消耗量跟下单预测比");
//			}
//			//处理明细数据
//			List<MaterialForecastDetailVO> forecastList = Lists.newArrayList();
//			for (int z = 0; z < allHeaderMonth.size(); z++) {
//				MaterialForecastDetailVO forecast = new MaterialForecastDetailVO();
//				forecast.setForecastMonth(allHeaderMonth.get(z));
//				if(i <= allMonth.size()) {
//					Integer forecastQuantity = random.nextInt(max - min + 1) + min;
//					forecast.setForecastQuantity(forecastQuantity.toString());
//					if(!forecastQuantityMap.containsKey(allHeaderMonth.get(z))) {
//						forecastQuantityMap.put(allHeaderMonth.get(z), BigDecimal.valueOf(forecastQuantity));
//					}else {
//						forecast.setForecastQuantity(forecastQuantityMap
//								.get(allHeaderMonth.get(z)).stripTrailingZeros().toPlainString());
//					}
//					//处理消耗量
//					if(i == allMonth.size()) {
//						//实际消耗量
//						BigDecimal forecastQty = forecastQuantityMap.get(allHeaderMonth.get(z));
//						Integer consumeQty = random.nextInt(subMax - subMin + 1) + subMin;
//						forecast.setForecastQuantity(consumeQty.toString());
//						consumeRateMap.put(allHeaderMonth.get(z), BigDecimal.valueOf(consumeQty)
//								.multiply(BigDecimal.valueOf(100))
//								.divide(forecastQty, 0, BigDecimal.ROUND_UP));
//					}
//					//展望月份
//					if((z < i || z > i + moveMonthNum) && i != allMonth.size()) {
//						forecast.setForecastQuantity(null);
//					}
//				}else {
//					forecast.setForecastQuantity(consumeRateMap.get(allHeaderMonth.get(z)) + "%");
//				}
//				forecastList.add(forecast);
//			}
//			materialPushingDetailVO.setForecastList(forecastList);
//			materialPushingDetailList.add(materialPushingDetailVO);
//		}
//		modp.setMaterialPushingDetailList(materialPushingDetailList);
//		returnResult.setMaterialPushing(modp);
//		//2.获取车型预测信息
//		List<MaterialVehicleModelForecastVO> vehicleModelForecastList = Lists.newArrayList();
//		//2.1：根据物料ID,去BOM中找到对应的成品数据信息(递归)
//		List<NewProductStockPointVO> finishedProductList = newMdsFeign.selectFinishedProductByInputProductId(
//				SystemHolder.getScenario(), currentProduct.getId());
//		if(CollUtil.isEmpty(finishedProductList)) {
//			returnResult.setVehicleModelForecastList(vehicleModelForecastList);
//			return returnResult;
//		}
//		//2.2：按照车型分组
//		Map<String, List<NewProductStockPointVO>> finishedProductMap = finishedProductList.stream()
//				.filter( e -> StringUtils.isNotEmpty(e.getVehicleModelCode()))
//				.collect(Collectors.groupingBy(NewProductStockPointVO::getVehicleModelCode));
//		
//		//2.2.1 通过车型，开始时间，结束时间查询汽车销量数据
//		List<String> vehicleModelCodeList = new ArrayList<>(finishedProductMap.keySet());
//		Date startSaleTime = DateUtils.getMonthFirstDay(DateUtils.stringToDate(allHeaderMonth.get(0), DateUtils.YEAR_MONTH));
//		Date endSaleTime = DateUtils.getMonthLastDay(DateUtils.stringToDate(allHeaderMonth.get(allHeaderMonth.size() -1), DateUtils.YEAR_MONTH));
//		List<PassengerCarSaleVO> passengerCarSaleList = dfpFeign.selectPassengerCarSaleByParams(SystemHolder.getScenario(),
//				ImmutableMap.of(
//						"startSaleTime", DateUtils.dateToString(startSaleTime, DateUtils.COMMON_DATE_STR1),
//                "endSaleTime", DateUtils.dateToString(endSaleTime, DateUtils.COMMON_DATE_STR1),
//				"vehicleModelCodeList", vehicleModelCodeList));
//		Map<String, Integer> yearMonthQuantityMap = CollectionUtils.isEmpty(passengerCarSaleList) ?
//                MapUtil.newHashMap() :
//                	passengerCarSaleList.stream().collect(Collectors.groupingBy(x -> 
//                   x.getVehicleModelCode() + "_" + DateUtils.dateToString(x.getSaleTime(), DateUtils.YEAR_MONTH),
//                                Collectors.summingInt(PassengerCarSaleVO::getSaleQuantity)));
//		int forecastSortNo = 1;
//		for (Entry<String, List<NewProductStockPointVO>> finishedProductEntry : finishedProductMap.entrySet()) {
//			String vehicleModelCode = finishedProductEntry.getKey();
//			List<NewProductStockPointVO> finishedProducts = finishedProductEntry.getValue();
//			int finishedProductSize = finishedProducts.size();
//			MaterialVehicleModelForecastVO vehicleModelForecast = new MaterialVehicleModelForecastVO();
//			vehicleModelForecast.setSortNo(forecastSortNo);
//			vehicleModelForecast.setVehicleModelCode(vehicleModelCode);
//			//处理产品
//			List<MaterialVehicleModelForecastProductVO> forecastProductList = Lists.newArrayList();
//			finishedProducts.forEach( e -> {
//				MaterialVehicleModelForecastProductVO product = new MaterialVehicleModelForecastProductVO();
//				BeanUtils.copyProperties(e, product);
//				forecastProductList.add(product);
//			});
//			vehicleModelForecast.setForecastProductList(forecastProductList);
//			//处理车型预测明细数据
//			List<MaterialPushingDetailVO> forecastProductDetailList = Lists.newArrayList();
//			for (int i = 0; i < finishedProductSize + 3; i++) {
//				MaterialPushingDetailVO materialPushingDetailVO = new MaterialPushingDetailVO();
//				if(i < finishedProducts.size()) {
//					materialPushingDetailVO.setSituationDesc("零件预测量");
//				}else if (i < finishedProductSize + 1) {
//					materialPushingDetailVO.setSituationDesc("发货数量汇总");
//				}else if (i < finishedProductSize + 2) {
//					materialPushingDetailVO.setSituationDesc("预测与发货比");
//				}else if (i < finishedProductSize + 3) {
//					materialPushingDetailVO.setSituationDesc("车型销量");
//				}
//				//处理明细数据
//				List<MaterialForecastDetailVO> forecastList = Lists.newArrayList();
//				for (int z = 0; z < allHeaderMonth.size(); z++) {
//					MaterialForecastDetailVO forecast = new MaterialForecastDetailVO();
//					forecast.setForecastMonth(allHeaderMonth.get(z));
//					if(i == finishedProductSize + 1) {
//						forecast.setForecastQuantity("85%");
//					}else if(i == finishedProductSize + 2) {
//						//车型销量数据
//						Integer saleQuantity = yearMonthQuantityMap.get(vehicleModelCode + "_" + allHeaderMonth.get(z));
//						forecast.setForecastQuantity(saleQuantity == null ? "" : saleQuantity.toString());
//					}else {
//						forecast.setForecastQuantity("888");
//					}
//					forecastList.add(forecast);
//				}
//				materialPushingDetailVO.setForecastList(forecastList);
//				forecastProductDetailList.add(materialPushingDetailVO);
//			}
//			vehicleModelForecast.setForecastProductDetailList(forecastProductDetailList);
//			vehicleModelForecastList.add(vehicleModelForecast);
//			forecastSortNo ++;
//			if(forecastSortNo == 3) {
//				break;
//			}
//		}
//		returnResult.setVehicleModelForecastList(vehicleModelForecastList);
//		//维护明细
//		return returnResult;
//	}
	
	
	public MaterialOriginalDemandVO selectSourceDetail(MaterialOriginalDemandDTO materialOriginalDemandDTO) {
		MaterialOriginalDemandVO returnResult = new MaterialOriginalDemandVO();
		//1.获取材料推移数据信息
		String stockPointCode = materialOriginalDemandDTO.getStockPointCode();
		String productCode = materialOriginalDemandDTO.getProductCode();
		//1.1：首先根据库存点编码，物料编码，获取物料id
		List<NewProductStockPointVO> productInfoList = newMdsFeign.selectProductStockPointByParams(SystemHolder.getScenario(), 
				ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(), 
    			"stockPointCode" , materialOriginalDemandDTO.getStockPointCode(),
    			"productCode" , materialOriginalDemandDTO.getProductCode()));
		if(CollUtil.isEmpty(productInfoList)) {
			String errorMsg = String.format("库存点【%s】物料编码【%s】未找到对应的物料数据信息", 
					materialOriginalDemandDTO.getStockPointCode(), 
					materialOriginalDemandDTO.getProductCode());
			throw new BusinessException(errorMsg);
		}
		NewProductStockPointVO currentProduct = productInfoList.get(0);
		//1.2：获取最小启订量和下单提前期
		List<MaterialSupplierPurchaseVO> purchaseList = materialSupplierPurchaseService
				.selectByParams(ImmutableMap.of("stockPointCode", stockPointCode,
                "materialCode", productCode));
		MaterialSupplierPurchaseVO materialSupplierPurchaseVO = new MaterialSupplierPurchaseVO();
		if(CollUtil.isNotEmpty(purchaseList)) {
			materialSupplierPurchaseVO = purchaseList.get(0);
		}
		MaterialOriginalDemandPushingVO modp = new MaterialOriginalDemandPushingVO();
		modp.setSortNo(1);
		modp.setProductCode(productCode);
		modp.setProductName(currentProduct.getProductName());
//		modp.setMinOrderQty(materialSupplierPurchaseVO.getMinOrderQty());
//		modp.setOrderPlacementLeadTimeDay(materialSupplierPurchaseVO.getOrderPlacementLeadTimeDay());
		modp.setMinOrderQty(BigDecimal.valueOf(6480));
		modp.setOrderPlacementLeadTimeDay(BigDecimal.valueOf(150));
		//1.3：维护明细数据
		List<MaterialPushingDetailVO> materialPushingDetailList = Lists.newArrayList();
		//预测起始月份，结束月份所有的月份
		List<String> allMonth = getMonthBetweenDate(
				materialOriginalDemandDTO.getStartForecastMonth(),
				materialOriginalDemandDTO.getEndForecastMonth(), null);
		BigDecimal orderPlacementLeadTimeDay = modp.getOrderPlacementLeadTimeDay();
		List<String> allHeaderMonth = allMonth;
		Integer moveMonthNum = 0;
		if(orderPlacementLeadTimeDay != null) {
			moveMonthNum = orderPlacementLeadTimeDay.divide(BigDecimal.valueOf(30), 0, BigDecimal.ROUND_UP).intValue();
			allHeaderMonth = getMonthBetweenDate(
					materialOriginalDemandDTO.getStartForecastMonth(),
					materialOriginalDemandDTO.getEndForecastMonth(), moveMonthNum);
		}
		Random random = new Random();
        int min = 200;
        int max = 700;
        int subMin = 200;
        int subMax = 700;
        Map<String, BigDecimal> forecastQuantityMap = new HashMap<>();
        Map<String, BigDecimal> consumeRateMap = new HashMap<>();
        //预测数据
        Map<String, List<String>> forecastMap = new HashMap<>();
        List<String> forecast_1 = Arrays.asList("9464","7284","7950","8594","8582","10254","10","11","12","1","2","3","4","5","6","7","8","9","10");
        List<String> forecast_2 = Arrays.asList("4","5486","7930","8840","8580","10088","7826","11","12","1","2","3","4","5","6","7","8","9","10");
        List<String> forecast_3 = Arrays.asList("4","5","13052","8528","8216","9854","7384","11856","12","1","2","3","4","5","6","7","8","9","10");
        List<String> forecast_4 = Arrays.asList("4","5","6","10746","10182","8788","7650","12510","9234","1","2","3","4","5","6","7","8","9","10");
        List<String> forecast_5 = Arrays.asList("4","5","6","7","12926","11182","7708","10956","10214","9392","2","3","4","5","6","7","8","9","10");
        List<String> forecast_6 = Arrays.asList("4","5","6","7","8","9334","8066","11208","9782","9556","8786","3","4","5","6","7","8","9","10");
        List<String> forecast_7 = Arrays.asList("4","5","6","7","8","9","8320","9906","11758","9668","8568","10922","4","5","6","7","8","9","10");
        List<String> forecast_8 = Arrays.asList("4","5","6","7","8","9","10","10292","10756","9822","9632","11956","12902","5","6","7","8","9","10");
        List<String> forecast_9 = Arrays.asList("4","5","6","7","8","9","10","11","10062","10942","9118","11494","12242","7190","6","7","8","9","10");
        List<String> forecast_10 = Arrays.asList("4","5","6","7","8","9","10","11","12","19136","9178","9118","11494","12242","7190","7","8","9","10");
        List<String> forecast_11 = Arrays.asList("4","5","6","7","8","9","10","11","12","1","11882","10494","12474","10068","11390","15642","8","9","10");
        List<String> forecast_12 = Arrays.asList("4","5","6","7","8","9","10","11","12","1","2","17368","13094","10068","14282","15998","17912","9","10");
        List<String> forecast_13 = Arrays.asList("4","5","6","7","8","9","10","11","12","1","2","3","10192","8240","14370","16016","17970","15466","10");
        List<String> forecast_14 = Arrays.asList("4","5","6","7","8","9","10","11","12","1","2","3","4","18200","15912","17450","18154","15536","11796");
        forecastMap.put("2024-04", forecast_1);
        forecastMap.put("2024-05", forecast_2);
        forecastMap.put("2024-06", forecast_3);
        forecastMap.put("2024-07", forecast_4);
        forecastMap.put("2024-08", forecast_5);
        forecastMap.put("2024-09", forecast_6);
        forecastMap.put("2024-10", forecast_7);
        forecastMap.put("2024-11", forecast_8);
        forecastMap.put("2024-12", forecast_9);
        forecastMap.put("2025-01", forecast_10);
        forecastMap.put("2025-02", forecast_11);
        forecastMap.put("2025-03", forecast_12);
        forecastMap.put("2025-04", forecast_13);
        forecastMap.put("2025-05", forecast_14);
        //实际消耗
        List<String> asList = Arrays.asList("7500","6281","10646","7961","11288","3052","6257","11001","8168","17856","9179","14158","13558");
		for (int i = 0; i < allMonth.size() + 2; i++) {
			MaterialPushingDetailVO materialPushingDetailVO = new MaterialPushingDetailVO();
			List<String> currentForecast = new ArrayList<>();
			if(i < allMonth.size()) {
				materialPushingDetailVO.setSituationDesc(allMonth.get(i) + "预测量");
				currentForecast = forecastMap.get(allMonth.get(i));
			}else if (i == allMonth.size()) {
				materialPushingDetailVO.setSituationDesc("实际消耗量");
			}else if (i > allMonth.size()) {
				materialPushingDetailVO.setSituationDesc("实际消耗量跟下单预测比");
			}
			//处理明细数据
			List<MaterialForecastDetailVO> forecastList = Lists.newArrayList();
			for (int z = 0; z < allHeaderMonth.size(); z++) {
				MaterialForecastDetailVO forecast = new MaterialForecastDetailVO();
				forecast.setForecastMonth(allHeaderMonth.get(z));
				if(i <= allMonth.size()) {
					if(i < allMonth.size()) {
						Integer forecastQuantity = Integer.valueOf(currentForecast.get(z));
						forecast.setForecastQuantity(forecastQuantity.toString());
						if(i == z) {
							forecastQuantityMap.put(allHeaderMonth.get(z), BigDecimal.valueOf(forecastQuantity));
						}
					}else if(i == allMonth.size()) {
						//处理消耗量
						//实际消耗量
						if(z < 13) {
							BigDecimal forecastQty = forecastQuantityMap.get(allHeaderMonth.get(z));
							Integer consumeQty = Integer.valueOf(asList.get(z));
							forecast.setForecastQuantity(consumeQty.toString());
							consumeRateMap.put(allHeaderMonth.get(z), BigDecimal.valueOf(consumeQty)
									.multiply(BigDecimal.valueOf(100))
									.divide(forecastQty, 0, BigDecimal.ROUND_UP));
						}
					}
					//展望月份
					if((z < i || z > i + moveMonthNum) && i != allMonth.size()) {
						forecast.setForecastQuantity(null);
					}
				}else {
					if(consumeRateMap.containsKey(allHeaderMonth.get(z))){
						forecast.setForecastQuantity(consumeRateMap.get(allHeaderMonth.get(z)) + "%");
					}
				}
				forecastList.add(forecast);
			}
			materialPushingDetailVO.setForecastList(forecastList);
			materialPushingDetailList.add(materialPushingDetailVO);
		}
		modp.setMaterialPushingDetailList(materialPushingDetailList);
		returnResult.setMaterialPushing(modp);
		//2.获取车型预测信息
		List<MaterialVehicleModelForecastVO> vehicleModelForecastList = Lists.newArrayList();
		//2.1：根据物料ID,去BOM中找到对应的成品数据信息(递归)
//		List<NewProductStockPointVO> finishedProductList = newMdsFeign.selectFinishedProductByInputProductId(
//				SystemHolder.getScenario(), currentProduct.getId());
		List<NewProductStockPointVO> finishedProductList = new ArrayList<>();
		if(CollUtil.isEmpty(finishedProductList)) {
			returnResult.setVehicleModelForecastList(vehicleModelForecastList);
			return returnResult;
		}
		//2.2：按照车型分组
		Map<String, List<NewProductStockPointVO>> finishedProductMap = finishedProductList.stream()
				.filter( e -> StringUtils.isNotEmpty(e.getVehicleModelCode()))
				.collect(Collectors.groupingBy(NewProductStockPointVO::getVehicleModelCode));
		
		//2.2.1 通过车型，开始时间，结束时间查询汽车销量数据
		List<String> vehicleModelCodeList = new ArrayList<>(finishedProductMap.keySet());
		Date startSaleTime = DateUtils.getMonthFirstDay(DateUtils.stringToDate(allHeaderMonth.get(0), DateUtils.YEAR_MONTH));
		Date endSaleTime = DateUtils.getMonthLastDay(DateUtils.stringToDate(allHeaderMonth.get(allHeaderMonth.size() -1), DateUtils.YEAR_MONTH));
		List<PassengerCarSaleVO> passengerCarSaleList = dfpFeign.selectPassengerCarSaleByParams(SystemHolder.getScenario(),
				ImmutableMap.of(
						"startSaleTime", DateUtils.dateToString(startSaleTime, DateUtils.COMMON_DATE_STR1),
                "endSaleTime", DateUtils.dateToString(endSaleTime, DateUtils.COMMON_DATE_STR1),
				"vehicleModelCodeList", vehicleModelCodeList));
		Map<String, Integer> yearMonthQuantityMap = CollectionUtils.isEmpty(passengerCarSaleList) ?
                MapUtil.newHashMap() :
                	passengerCarSaleList.stream().collect(Collectors.groupingBy(x -> 
                   x.getVehicleModelCode() + "_" + DateUtils.dateToString(x.getSaleTime(), DateUtils.YEAR_MONTH),
                                Collectors.summingInt(PassengerCarSaleVO::getSaleQuantity)));
		int forecastSortNo = 1;
		for (Entry<String, List<NewProductStockPointVO>> finishedProductEntry : finishedProductMap.entrySet()) {
			String vehicleModelCode = finishedProductEntry.getKey();
			List<NewProductStockPointVO> finishedProducts = finishedProductEntry.getValue();
			int finishedProductSize = finishedProducts.size();
			MaterialVehicleModelForecastVO vehicleModelForecast = new MaterialVehicleModelForecastVO();
			vehicleModelForecast.setSortNo(forecastSortNo);
			vehicleModelForecast.setVehicleModelCode(vehicleModelCode);
			//处理产品
			List<MaterialVehicleModelForecastProductVO> forecastProductList = Lists.newArrayList();
			finishedProducts.forEach( e -> {
				MaterialVehicleModelForecastProductVO product = new MaterialVehicleModelForecastProductVO();
				BeanUtils.copyProperties(e, product);
				forecastProductList.add(product);
			});
			vehicleModelForecast.setForecastProductList(forecastProductList);
			//处理车型预测明细数据
			List<MaterialPushingDetailVO> forecastProductDetailList = Lists.newArrayList();
			for (int i = 0; i < finishedProductSize + 3; i++) {
				MaterialPushingDetailVO materialPushingDetailVO = new MaterialPushingDetailVO();
				if(i < finishedProducts.size()) {
					materialPushingDetailVO.setSituationDesc("零件预测量");
				}else if (i < finishedProductSize + 1) {
					materialPushingDetailVO.setSituationDesc("发货数量汇总");
				}else if (i < finishedProductSize + 2) {
					materialPushingDetailVO.setSituationDesc("预测与发货比");
				}else if (i < finishedProductSize + 3) {
					materialPushingDetailVO.setSituationDesc("车型销量");
				}
				//处理明细数据
				List<MaterialForecastDetailVO> forecastList = Lists.newArrayList();
				for (int z = 0; z < allHeaderMonth.size(); z++) {
					MaterialForecastDetailVO forecast = new MaterialForecastDetailVO();
					forecast.setForecastMonth(allHeaderMonth.get(z));
					if(i == finishedProductSize + 1) {
						forecast.setForecastQuantity("85%");
					}else if(i == finishedProductSize + 2) {
						//车型销量数据
						Integer saleQuantity = yearMonthQuantityMap.get(vehicleModelCode + "_" + allHeaderMonth.get(z));
						forecast.setForecastQuantity(saleQuantity == null ? "" : saleQuantity.toString());
					}else {
						forecast.setForecastQuantity("888");
					}
					forecastList.add(forecast);
				}
				materialPushingDetailVO.setForecastList(forecastList);
				forecastProductDetailList.add(materialPushingDetailVO);
			}
			vehicleModelForecast.setForecastProductDetailList(forecastProductDetailList);
			vehicleModelForecastList.add(vehicleModelForecast);
			forecastSortNo ++;
			if(forecastSortNo == 3) {
				break;
			}
		}
		returnResult.setVehicleModelForecastList(vehicleModelForecastList);
		//维护明细
		return returnResult;
	}
	
	/**
     * 获取两个日期之间的所有月份 (年月)
     *
     * @param startTime
     * @param endTime
     * @return：list
     */
    public static List<String> getMonthBetweenDate(String startTime, String endTime, Integer moveMonthNum) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        // 声明保存日期集合
        List<String> list = new ArrayList<>();
        try {
            // 转化成日期类型
            Date startDate = sdf.parse(startTime);
            Date endDate = sdf.parse(endTime);
            if(moveMonthNum != null) {
            	endDate = DateUtils.moveMonth(endDate, moveMonthNum);
            }
            //用Calendar 进行日期比较判断
            Calendar calendar = Calendar.getInstance();
            while (startDate.getTime() <= endDate.getTime()) {
                // 把日期添加到集合
                list.add(sdf.format(startDate));
                // 设置日期
                calendar.setTime(startDate);
                //把月数增加 1
                calendar.add(Calendar.MONTH, 1);
                // 获取增加后的日期
                startDate = calendar.getTime();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }

}
