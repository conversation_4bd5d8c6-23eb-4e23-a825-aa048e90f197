//package com.yhl.scp.mrp.material.plan.service.impl;
//
//import com.google.common.collect.ImmutableMap;
//import com.google.common.collect.Lists;
//import com.yhl.platform.common.entity.BaseResponse;
//import com.yhl.platform.common.enums.YesOrNoEnum;
//import com.yhl.platform.common.utils.CollectionUtils;
//import com.yhl.platform.common.utils.DateUtils;
//import com.yhl.scp.dfp.feign.DfpFeign;
//import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
//import com.yhl.scp.ips.common.SystemHolder;
//import com.yhl.scp.ips.utils.BasePOUtils;
//import com.yhl.scp.mds.bom.vo.ProductBomVO;
//import com.yhl.scp.mds.feign.common.NewMdsFeign;
//import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
//import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
//import com.yhl.scp.mds.stock.vo.NewStockPointVO;
//import com.yhl.scp.mds.substitution.vo.ProductSubstitutionRelationshipVO;
//import com.yhl.scp.mrp.enums.TransferStatusEnum;
//import com.yhl.scp.mrp.freestorage.infrastructure.dao.ProFreeStorageDao;
//import com.yhl.scp.mrp.freestorage.infrastructure.po.ProFreeStoragePO;
//import com.yhl.scp.mrp.inventory.service.*;
//import com.yhl.scp.mrp.inventory.vo.*;
//import com.yhl.scp.mrp.material.plan.allocate.InventoryAllocator;
//import com.yhl.scp.mrp.material.plan.convertor.MaterialPlanVersionConvertor;
//import com.yhl.scp.mrp.material.plan.domain.entity.MrpAllocateResultDO;
//import com.yhl.scp.mrp.material.plan.dto.*;
//import com.yhl.scp.mrp.material.plan.enums.MrpDemandSourceEnum;
//import com.yhl.scp.mrp.material.plan.enums.MrpReplaceTypeEnum;
//import com.yhl.scp.mrp.material.plan.enums.MrpSupplySourceEnum;
//import com.yhl.scp.mrp.material.plan.infrastructure.dao.MaterialPlanInventoryOccupyDao;
//import com.yhl.scp.mrp.material.plan.infrastructure.dao.MaterialPlanInventoryShiftDao;
//import com.yhl.scp.mrp.material.plan.infrastructure.dao.MaterialPlanReplaceDao;
//import com.yhl.scp.mrp.material.plan.infrastructure.dao.MaterialPlanVersionDao;
//import com.yhl.scp.mrp.material.plan.infrastructure.po.*;
//import com.yhl.scp.mrp.material.plan.service.HandworkAdjustService;
//import com.yhl.scp.mrp.material.plan.service.MaterialPlanTransferInventoryDetailService;
//import com.yhl.scp.mrp.material.plan.service.MaterialPlanTransferService;
//import com.yhl.scp.mrp.material.plan.vo.MaterialPlanTransferInventoryDetailVO;
//import com.yhl.scp.mrp.material.plan.vo.MaterialPlanTransferVO;
//import com.yhl.scp.mrp.material.plan.vo.MaterialPlanVersionVO;
//import com.yhl.scp.mrp.transport.service.TransportRoutingService;
//import com.yhl.scp.mrp.transport.service.TransportSectionService;
//import com.yhl.scp.mrp.transport.vo.TransportRoutingVO;
//import com.yhl.scp.mrp.transport.vo.TransportSectionVO;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.math.BigDecimal;
//import java.math.RoundingMode;
//import java.util.*;
//import java.util.function.Function;
//import java.util.stream.Collectors;
//
///**
// * <code>GlassInventoryShiftServiceImpl</code>
// * <p>
// * TODO
// * </p>
// *
// * <AUTHOR>
// * @version 1.0
// * @since 2024-12-31 10:43:22
// */
//@Service
//public class GlassInventoryShiftServiceImpl  implements HandworkAdjustService {
//
//    @Resource
//    ProFreeStorageDao proFreeStorageDao;
//    @Resource
//    MaterialPlanInventoryShiftDao materialPlanInventoryShiftDao;
//    @Resource
//    MaterialPlanInventoryOccupyDao materialPlanInventoryOccupyDao;
//    @Resource
//    MaterialPlanReplaceDao materialPlanReplaceDao;
//    @Resource
//    MaterialPlanVersionDao materialPlanVersionDao;
//    @Resource
//    GlassSafetyInventoryService glassSafetyInventoryService;
//    @Resource
//    protected InventoryFloatGlassDetailService inventoryFloatGlassDetailService;
//    @Resource
//    MaterialPlanTransferService materialPlanTransferService;
//    @Resource
//    protected InventoryFloatGlassShippedDetailService inventoryFloatGlassShippedDetailService;
//    @Resource
//    MaterialPlanTransferInventoryDetailService materialPlanTransferInventoryDetailService;
//    @Resource
//    protected InventoryQuayDetailService inventoryQuayDetailService;
//    @Resource
//    InventoryAlternativeRelationshipService inventoryAlternativeRelationshipService;
//    @Resource
//    TransportRoutingService transportRoutingService;
//    @Resource
//    TransportSectionService transportSectionService;
//    @Resource
//    NewMdsFeign mdsFeign;
//    @Resource
//    protected DfpFeign dfpFeign;
//    @Resource
//    NewMdsFeign newMdsFeign;
//
//    private void setMaterialPlanTransferList(MrpContextDTO mrpContextDTO) {
//        // 查询调拨开始时间发生在未来的调拨计划
//        Map<String, Object> params = new HashMap<>();
////        params.put("transferDateArriveStart",DateUtils.getDayFirstTime(new Date()));
//        params.put("s", YesOrNoEnum.YES.getCode());
//        params.put("storageFlag", Lists.newArrayList(TransferStatusEnum.PUBLISHED.getCode()));
//        List<MaterialPlanTransferVO> materialPlanTransferList = materialPlanTransferService.selectByParams(params);
//        mrpContextDTO.setMaterialPlanTransferList(materialPlanTransferList);
//    }
//
//    private void setFloatSupplyList(MrpContextDTO mrpContextDTO) {
//        // 查询浮法库存
////      List<InventoryFloatGlassDetailVO> inventoryFloatGlassDetailVOS = inventoryFloatGlassDetailService.selectByParams(new HashMap<>());
//        // 按产品编码分组统计
//        List<InventoryFloatGlassDetailVO> inventoryFloatGlassDetailVOS = inventoryFloatGlassDetailService.selectProductInventory();
//        // 浮法调拨计划根据物料、浮法库存点分组
//        Map<String, BigDecimal> materialPlanTransferGroup = mrpContextDTO.getMaterialPlanTransferList()
//                .stream()
//                .filter(t->mrpContextDTO.getFloatStockPointCodeList().contains(t.getStockPointCodeFrom()))
//                .collect(Collectors.groupingBy(MaterialPlanTransferVO::getGroupKey,
//                        Collectors.reducing(BigDecimal.ZERO,MaterialPlanTransferVO::getTransferQuantity,BigDecimal::add)));
//        // 原片浮法厂库存
//        List<MrpSupplyDTO> floatSupplyList = new ArrayList<>();
//        if (CollectionUtils.isNotEmpty(inventoryFloatGlassDetailVOS)){
//            for (InventoryFloatGlassDetailVO t : inventoryFloatGlassDetailVOS) {
//                if(null == t.getStorageTime()){
//                    continue;
//                }
//
//                String key = String.join("&&",t.getProductCode(),t.getStockPointCode());
//                BigDecimal transferQuantity = materialPlanTransferGroup.getOrDefault(key,BigDecimal.ZERO);
//
//                MrpSupplyDTO mrpSupplyDTO = createMrpSupply(String.join("&&", t.getProductCode(), t.getStockPointCode()),
//                        t.getProductCode(),t.getStockPointCode(),
//                        MrpSupplySourceEnum.FLOAT_INVENTORY.getCode(), t.getStorageTime(),
//                        t.getQty().subtract(transferQuantity));
//                floatSupplyList.add(mrpSupplyDTO);
//            }
//        }
//
//        if (CollectionUtils.isNotEmpty(floatSupplyList)){
//            floatSupplyList.sort(Comparator.comparing(MrpSupplyDTO::getSupplyTime));
//        }
//        mrpContextDTO.setFloatSupplyList(floatSupplyList);
//    }
//
//    private void setPortSupplyList(MrpContextDTO mrpContextDTO) {
//
//        // 码头调拨计划
//        List<String> portTransferIds = mrpContextDTO.getMaterialPlanTransferList().stream()
//                .filter(t -> mrpContextDTO.getPortStockPointCodeList().contains(t.getStockPointCodeFrom()))
//                .map(MaterialPlanTransferVO::getId).collect(Collectors.toList());
//        // 码头调拨计划库存批次占用信息
//        Map<String, BigDecimal> portInventoryOccupyGroup = new HashMap<>();
//        if (CollectionUtils.isNotEmpty(portTransferIds)) {
//            // 获取调拨计划库存批次明细
//            List<MaterialPlanTransferInventoryDetailVO> materialPlanTransferInventoryDetailVOList = materialPlanTransferInventoryDetailService.selectAll();
//            // 根据库存批次id信息分组统计
//            portInventoryOccupyGroup = materialPlanTransferInventoryDetailVOList.stream()
//                    .collect(Collectors.groupingBy(MaterialPlanTransferInventoryDetailVO::getInventoryId,
//                            Collectors.reducing(BigDecimal.ZERO, MaterialPlanTransferInventoryDetailVO::getTransferQuantity, BigDecimal::add)));
//
//        }
//
//        // 查询原片港口库存
//        List<InventoryQuayDetailVO> inventoryQuayDetailVOS = inventoryQuayDetailService.selectByParams(new HashMap<>());
//        Map<String, NewStockPointVO> stockPointVOMap = mrpContextDTO.getStockPointVOList().stream()
//                .collect(Collectors.toMap(NewStockPointVO::getStockPointName, Function.identity()));
//        // 原片港口库存(港口现有库存+港口在途库存)
//        List<MrpSupplyDTO> portSupplyList = new ArrayList<>();
//        if (CollectionUtils.isNotEmpty(inventoryQuayDetailVOS)){
//            for (InventoryQuayDetailVO inventoryQuayDetailVO : inventoryQuayDetailVOS) {
//                NewStockPointVO newStockPointVO = stockPointVOMap.get(inventoryQuayDetailVO.getPortName());
//                if (Objects.isNull(newStockPointVO)) continue;
//                MrpSupplyDTO mrpSupplyDTO = createMrpSupply(inventoryQuayDetailVO.getId(),
//                        inventoryQuayDetailVO.getProductCode(),newStockPointVO.getStockPointCode(),
//                        MrpSupplySourceEnum.PORT_INVENTORY.getCode(), inventoryQuayDetailVO.getActualArrivalTime(),
//                        inventoryQuayDetailVO.getActualSentQuantity()
//                                .subtract(portInventoryOccupyGroup.getOrDefault(inventoryQuayDetailVO.getId(),BigDecimal.ZERO)));
//                mrpSupplyDTO.setCarrierCode(inventoryQuayDetailVO.getCarrier());
//                portSupplyList.add(mrpSupplyDTO);
//            }
//        }
//        // 查询港口原片在途库存
//        List<InventoryFloatGlassShippedDetailVO> inventoryFloatGlassShippedDetailVOS =
//                inventoryFloatGlassShippedDetailService.selectByParams(ImmutableMap.of("shipmentMethod","国内船运"));
//        // 将码头在途转换成码头库存供应
//        for (InventoryFloatGlassShippedDetailVO inventoryFloatGlassShippedDetailVO : inventoryFloatGlassShippedDetailVOS) {
//            NewStockPointVO newStockPointVO = stockPointVOMap.get(inventoryFloatGlassShippedDetailVO.getPortName());
//            if (Objects.isNull(inventoryFloatGlassShippedDetailVO.getEstimatedArrivalTime()) || null == newStockPointVO) {
//                continue;
//            }
//            Date arriveTime = DateUtils.getDayFirstTime(inventoryFloatGlassShippedDetailVO.getEstimatedArrivalTime());
//            Date supplyTime = DateUtils.moveDay(arriveTime,1);
//            MrpSupplyDTO mrpSupplyDTO = createMrpSupply(inventoryFloatGlassShippedDetailVO.getId(),
//                    inventoryFloatGlassShippedDetailVO.getProductCode(),newStockPointVO.getStockPointCode(),
//                    MrpSupplySourceEnum.PORT_ON_WAY.getCode(), supplyTime,
//                    inventoryFloatGlassShippedDetailVO.getActualSentQuantity()
//                            .subtract(portInventoryOccupyGroup.getOrDefault(inventoryFloatGlassShippedDetailVO.getId(),BigDecimal.ZERO)));
//            portSupplyList.add(mrpSupplyDTO);
//        }
//
//        portSupplyList.sort(Comparator.comparing(MrpSupplyDTO::getSupplyTime));
//        mrpContextDTO.setPortSupplyList(portSupplyList);
//    }
//    private void setFactorySupplyList(MrpContextDTO mrpContextDTO) {
//        // 原片本厂库存
//        List<MrpSupplyDTO> factorySupplyList = new ArrayList<>();
//
//        String bcStockPointCode = String.join("&&",mrpContextDTO.getBcStockPointCodeList());
//
//        // 本厂现有库存
//        List<InventoryBatchDetailVO> glassInventoryBatchDetailList = dfpFeign.selectAllGlassInventoryBatch();
//        if (CollectionUtils.isNotEmpty(glassInventoryBatchDetailList)){
//            for (InventoryBatchDetailVO inventoryBatchDetailVO : glassInventoryBatchDetailList) {
//                MrpSupplyDTO mrpSupplyDTO = createMrpSupply(inventoryBatchDetailVO.getId(),
//                        inventoryBatchDetailVO.getProductCode(),bcStockPointCode,
//                        MrpSupplySourceEnum.FACTORY_INVENTORY.getCode(), DateUtils.stringToDate(inventoryBatchDetailVO.getAssignedTime(),DateUtils.COMMON_DATE_STR1),
//                        BigDecimal.valueOf(Long.parseLong(inventoryBatchDetailVO.getCurrentQuantity())));
//                factorySupplyList.add(mrpSupplyDTO);
//            }
//        }
//        // 本厂在途库存
//        List<InventoryFloatGlassShippedDetailVO> inventoryFloatGlassShippedDetailVOS =
//                inventoryFloatGlassShippedDetailService.selectByParams(ImmutableMap.of("shipmentMethod","汽车运输"));
//        if (CollectionUtils.isNotEmpty(inventoryFloatGlassShippedDetailVOS)) {
//            for (InventoryFloatGlassShippedDetailVO inventoryFloatGlassShippedDetailVO : inventoryFloatGlassShippedDetailVOS) {
//                if (inventoryFloatGlassShippedDetailVO.getEstimatedArrivalTime() == null) {
//                    continue;
//                }
//                Date arriveTime = DateUtils.getDayFirstTime(inventoryFloatGlassShippedDetailVO.getEstimatedArrivalTime());
//                Date supplyTime = DateUtils.moveDay(arriveTime,1);
//                MrpSupplyDTO mrpSupplyDTO = createMrpSupply(inventoryFloatGlassShippedDetailVO.getId(),
//                        inventoryFloatGlassShippedDetailVO.getProductCode(),bcStockPointCode,
//                        MrpSupplySourceEnum.FACTORY_ON_WAY_FROM_FLOAT.getCode(), supplyTime, inventoryFloatGlassShippedDetailVO.getActualSentQuantity());
//                factorySupplyList.add(mrpSupplyDTO);
//            }
//        }
//        // 调拨到达时间不晚于mrp计算开始时间，且发货地是码头，目的地是本厂的调拨作为第一天的在途数据
//        if (CollectionUtils.isNotEmpty(mrpContextDTO.getMaterialPlanTransferList())) {
//            List<MaterialPlanTransferVO> materialPlanTransferVOList = mrpContextDTO.getMaterialPlanTransferList()
//                    .stream()
//                    .filter(t -> t.getTransferDateArrive().before(DateUtils.moveDay(mrpContextDTO.getMrpCalcDate(),1)))
//                    .filter(t -> mrpContextDTO.getPortStockPointCodeList().contains(t.getStockPointCodeFrom()))
//                    .collect(Collectors.toList());
//            if (CollectionUtils.isNotEmpty(materialPlanTransferVOList)) {
//                for (MaterialPlanTransferVO materialPlanTransferVO : materialPlanTransferVOList) {
//                    MrpSupplyDTO mrpSupplyDTO = createMrpSupply(materialPlanTransferVO.getId(),
//                            materialPlanTransferVO.getProductCode(),materialPlanTransferVO.getStockPointCodeTo(),
//                            MrpSupplySourceEnum.FACTORY_ON_WAY_FROM_PORT.getCode(), mrpContextDTO.getMrpCalcDate(), materialPlanTransferVO.getTransferQuantity());
//                    factorySupplyList.add(mrpSupplyDTO);
//                }
//            }
//        }
//        factorySupplyList.sort(Comparator.comparing(MrpSupplyDTO::getSupplyTime));
//        mrpContextDTO.setFactorySupplyList(factorySupplyList);
//    }
//
//    private void setStockPoint(MrpContextDTO mrpContextDTO) {
//        // 本厂库存点编码
//        List<String> bcStockPointCodes = mrpContextDTO.getStockPointVOList().stream()
//                .filter(t -> StockPointTypeEnum.BC.getCode().equals(t.getStockPointType()))
//                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
//        mrpContextDTO.setBcStockPointCodeList(bcStockPointCodes);
//        // 码头库存点编码
//        List<String> portStockPointCodes = mrpContextDTO.getStockPointVOList().stream()
//                .filter(t->StockPointTypeEnum.MT.getCode().equals(t.getStockPointType()))
//                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
//        mrpContextDTO.setPortStockPointCodeList(portStockPointCodes);
//        // 浮法库存点编码
//        List<String> floatStockPointCodes = mrpContextDTO.getStockPointVOList().stream()
//                .filter(t->StockPointTypeEnum.FF.getCode().equals(t.getStockPointType()))
//                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
//        mrpContextDTO.setFloatStockPointCodeList(floatStockPointCodes);
//
//    }
//
//    private void setTransportRouting(MrpContextDTO mrpContextDTO) {
//        Map<String, String> stockPointCodeMap = mrpContextDTO.getStockPointVOList().stream()
//                .collect(Collectors.toMap(NewStockPointVO::getId, NewStockPointVO::getStockPointCode));
//        List<TransportRoutingVO> transportRoutingList = transportRoutingService.selectAll();
//        transportRoutingList.forEach(t->{
//            t.setDestinStockPointCode(stockPointCodeMap.get(t.getDestinStockPointId()));
//            t.setOriginStockPointCode(stockPointCodeMap.get(t.getOriginStockPointId()));
//        });
//        mrpContextDTO.setTransportRoutingList(transportRoutingList);
//        List<TransportSectionVO> transportSectionList = transportSectionService.selectAll();
//        transportSectionList.forEach(t->{
//            t.setDestinStockPointCode(stockPointCodeMap.get(t.getDestinStockPointId()));
//            t.setOriginStockPointCode(stockPointCodeMap.get(t.getOriginStockPointId()));
//        });
//        // 运输路径
//        Map<String, List<TransportRoutingVO>> transportRoutingGroupMap = transportRoutingList.stream()
//                .collect(Collectors.groupingBy(t->t.getOriginStockPointCode() + "&&" + t.getDestinStockPointCode()));
//        // 运输路径路段
//        Map<String, List<TransportSectionVO>> transportSectionGroupMap = transportSectionList.stream()
//                .collect(Collectors.groupingBy(TransportSectionVO::getRoutingId));
//        // 运输路径路段
//        Map<String, List<TransportSectionVO>> transportSectionGroupMa2 = transportSectionList.stream().collect(Collectors.groupingBy(item -> String.join("&&", item.getOriginStockPointCode(), item.getDestinStockPointCode())));
//
//        mrpContextDTO.setTransportRoutingList(transportRoutingList);
//        mrpContextDTO.setTransportRoutingGroupMap(transportRoutingGroupMap);
//    }
//
//    protected void resetProductCodeByMaterialRelationship(MrpContextDTO mrpContextDTO) {
//        // 原片混合替代
//        List<ProductSubstitutionRelationshipVO> productSubstitutionRelationshipList
//                = mdsFeign.getProductSubstitutionRelationshipVOByParams(ImmutableMap.of("productClassify", "RA.A",
//                "rule", "C"));
//        if (CollectionUtils.isEmpty(productSubstitutionRelationshipList)) {
//            mrpContextDTO.setProductSubstitutionRelationshipVOList(productSubstitutionRelationshipList);
//
//            for (ProductSubstitutionRelationshipVO t : productSubstitutionRelationshipList) {
//                String newProductCode = String.join("&&",t.getRawProductCode(), t.getSubstituteProductCode());
//                // 重置原片需求物料编码
//                if (CollectionUtils.isNotEmpty(mrpContextDTO.getMrpDemandList())) {
//                    for (MrpDemandDTO mrpDemandDTO : mrpContextDTO.getMrpDemandList()) {
//                        if (newProductCode.contains(mrpDemandDTO.getProductCode())) {
//                            mrpDemandDTO.setProductCode(newProductCode);
//                        }
//                    }
//                }
//                // 重置本厂库存供应物料编码
//                if (CollectionUtils.isNotEmpty(mrpContextDTO.getFactorySupplyList())) {
//                    for (MrpSupplyDTO mrpSupplyDTO : mrpContextDTO.getFactorySupplyList()) {
//                        if (newProductCode.contains(mrpSupplyDTO.getProductCode())) {
//                            mrpSupplyDTO.setProductCode(newProductCode);
//                        }
//                    }
//                }
//                // 重置港口库存供应物料编码
//                if (CollectionUtils.isNotEmpty(mrpContextDTO.getPortSupplyList())) {
//                    for (MrpSupplyDTO mrpSupplyDTO : mrpContextDTO.getPortSupplyList()) {
//                        if (newProductCode.contains(mrpSupplyDTO.getProductCode())) {
//                            mrpSupplyDTO.setProductCode(newProductCode);
//                        }
//                    }
//                }
//                // 重置浮法库存供应物料编码
//                if (CollectionUtils.isNotEmpty(mrpContextDTO.getFloatSupplyList())) {
//                    for (MrpSupplyDTO mrpSupplyDTO : mrpContextDTO.getFloatSupplyList()) {
//                        if (newProductCode.contains(mrpSupplyDTO.getProductCode())) {
//                            mrpSupplyDTO.setProductCode(newProductCode);
//                        }
//                    }
//                }
//            }
//        }
//    }
//
//    private void setInventoryPlanReplaceData(MrpContextDTO mrpContextDTO) {
//        // 库存批次可用查找替代计划数据
//        List<InventoryAlternativeRelationshipVO> inventoryAlternativeRelationshipList = inventoryAlternativeRelationshipService.selectAll();
//        if (CollectionUtils.isNotEmpty(inventoryAlternativeRelationshipList)) {
//            inventoryAlternativeRelationshipList.forEach(t -> t.setRemainingQuantity(t.getAlternativeQuantity()));
//            mrpContextDTO.setInventoryAlternativeRelationshipList(inventoryAlternativeRelationshipList);
//        }
//    }
//    private void setMaterialDayTotalDemand(MrpContextDTO mrpContextDTO) {
//        List<MaterialDayTotalDemandDTO> materialDaysDemandList = new ArrayList<>();
//        // mrpDemandList过滤出是物料类型是原片的需求
//        List<MrpDemandDTO> glassDemandList = mrpContextDTO.getMrpDemandList().stream()
//                .filter(t->t.getProductClassify().startsWith("RA.A"))
////                .filter(t->"ABT097516700210G".equals(t.getProductCode()))
//                .collect(Collectors.toList());
//        // 物料需求日期
//        List<String> demandDateStringList = glassDemandList.stream().map(t->DateUtils.dateToString(t.getDemandTime()))
//                .distinct().collect(Collectors.toList());
//        List<Date> demandDateList = demandDateStringList.stream().map(DateUtils::stringToDate)
//                .sorted(Comparator.comparing(Date::getTime)).collect(Collectors.toList());
//        // 根据需求时间分组
//        Map<String, List<MrpDemandDTO>> demandByDay = glassDemandList.stream()
//                .collect(Collectors.groupingBy(t -> DateUtils.dateToString(t.getDemandTime())));
//
//        // 计划替代
//        List<InventoryAlternativeRelationshipVO> inventoryAlternativeRelationshipList = mrpContextDTO.getInventoryAlternativeRelationshipList();
//        for (Date demandDate : demandDateList) {
//            List<MrpDemandDTO> mrpDemandList = demandByDay.get(DateUtils.dateToString(demandDate));
//            // 根据物料编码分组
//            Map<String, List<MrpDemandDTO>> demandByProductCode = mrpDemandList.stream()
//                    .collect(Collectors.groupingBy(MrpDemandDTO::getProductCode));
//            for (List<MrpDemandDTO> productDemandList : demandByProductCode.values()) {
//                String productCode = productDemandList.get(0).getProductCode();
//                MaterialDayTotalDemandDTO materialDayTotalDemand =  new MaterialDayTotalDemandDTO();
//                materialDayTotalDemand.setProductCode(productCode);
//                materialDayTotalDemand.setDemandDate(demandDate);
//                // 标准规格需求
//                BigDecimal standardDemand = productDemandList.stream().map(MrpDemandDTO::getDemandQuantity)
//                        .reduce(BigDecimal.ZERO, BigDecimal::add);
//                materialDayTotalDemand.setStandardDemand(standardDemand);
//                // 被替代的量
//                BigDecimal useReplaceQuantity = BigDecimal.ZERO;
//                // 别的物料替代当前物料
//                if (CollectionUtils.isNotEmpty(inventoryAlternativeRelationshipList)) {
//                    Optional<InventoryAlternativeRelationshipVO>  optional =  inventoryAlternativeRelationshipList.stream()
//                            .filter(t -> t.getDemandProductCode().equals(productCode) && t.getStartTime().before(demandDate))
//                            .findFirst();
//                    if (optional.isPresent()) {
////                        materialDayTotalDemand.setSubstituteProductCode(optional.get().getReplacedProductCode());
//                        useReplaceQuantity = getReplaceQuantity(optional.get(), standardDemand);
//                    }
//                }
//                materialDayTotalDemand.setUseReplaceQuantity(useReplaceQuantity);
//                // 替代规格需求量
//                BigDecimal usedAsReplaceQuantity = BigDecimal.ZERO;
//                if (CollectionUtils.isNotEmpty(inventoryAlternativeRelationshipList)) {
//                    List<InventoryAlternativeRelationshipVO> usedAsReplaceConfigList = inventoryAlternativeRelationshipList.stream()
//                            .filter(t -> t.getReplacedProductCode().equals(productCode) && t.getStartTime().before(demandDate))
//                            .collect(Collectors.toList());
//                    if (CollectionUtils.isNotEmpty(usedAsReplaceConfigList)) {
//                        for (InventoryAlternativeRelationshipVO inventoryAlternativeRelationshipVO : usedAsReplaceConfigList) {
//                            String demandProductCode = inventoryAlternativeRelationshipVO.getDemandProductCode();
//                            List<MrpDemandDTO> standardDemandAtOneDay2 = demandByProductCode.getOrDefault(demandProductCode, new ArrayList<>());
//                            BigDecimal demandQuantity = standardDemandAtOneDay2.stream().map(MrpDemandDTO::getDemandQuantity)
//                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//                            usedAsReplaceQuantity = getReplaceQuantity(inventoryAlternativeRelationshipVO, demandQuantity);
//                        }
//                    }
//                }
//                materialDayTotalDemand.setUsedAsReplaceQuantity(usedAsReplaceQuantity);
//                materialDaysDemandList.add(materialDayTotalDemand);
//            }
//        }
//        mrpContextDTO.setMaterialDayTotalDemandList(materialDaysDemandList);
//    }
//
//    private BigDecimal getReplaceQuantity(InventoryAlternativeRelationshipVO inventoryAlternativeRelationshipVO,
//                                          BigDecimal demandQuantity) {
//        BigDecimal remainingQuantity  = inventoryAlternativeRelationshipVO.getRemainingQuantity();
//        BigDecimal replaceQuantityTemp;
//        if (remainingQuantity.compareTo(demandQuantity) >= 0) {
//            replaceQuantityTemp = demandQuantity;
//        } else {
//            replaceQuantityTemp = remainingQuantity;
//        }
//        inventoryAlternativeRelationshipVO.setRemainingQuantity(remainingQuantity.subtract(replaceQuantityTemp));
//        return replaceQuantityTemp;
//    }
//    private void setFreeStorage(MrpContextDTO mrpContextDTO) {
//        List<ProFreeStoragePO> proFreeStoragePOS = proFreeStorageDao.selectByParams(new HashMap<>());
//        Map<String, Integer> freeStorageMap = proFreeStoragePOS.stream()
//                .collect(Collectors.toMap(t -> String.join("&&", t.getCarrierCode(), t.getPortCode()),
//                        t -> Integer.valueOf(t.getFreeStorage()),(v1, v2)->v1));
//        mrpContextDTO.setPortFreeStorageMap(freeStorageMap);
//    }
//
//    protected void initMrpContextDTO(MrpContextDTO mrpContextDTO) {
//        setStockPoint(mrpContextDTO);
//        // 设置调拨计划
//        setMaterialPlanTransferList(mrpContextDTO);
//        // 设置本厂库存供应（本厂现有库存+计划到柜量+在途）
//        setFactorySupplyList(mrpContextDTO);
//        // 设置港口库存供应
//        setPortSupplyList(mrpContextDTO);
//        // 设置浮法库存供应
//        setFloatSupplyList(mrpContextDTO);
//        // 原片运输路径
//        setTransportRouting(mrpContextDTO);
//        // 根据物料替代关系重置需求和库存供应的物料编码
//        resetProductCodeByMaterialRelationship(mrpContextDTO);
//        // 计划替换配置
//        setInventoryPlanReplaceData(mrpContextDTO);
//        // 根据计划替配置数据计算物料的日总需求
//        setMaterialDayTotalDemand(mrpContextDTO);
//        // 设置免堆期
//        setFreeStorage(mrpContextDTO);
//    }
//
//    protected List<Date> getInventoryShiftDateList(Date date) {
//        // 获取当前日期到12后的日期
//        Date currentDate = DateUtils.getDayFirstTime(date);
////        Date currentDate = DateUtils.stringToDate("2025-01-17");
//        Date moveDate = DateUtils.moveMonth(currentDate, 2);
//        return DateUtils.getIntervalDates(currentDate, moveDate);
//    }
//
//    private Map<String, BigDecimal> getOpeningInventory(MrpContextDTO mrpContextDTO, List<String> supplySourceList) {
//
//        List<MrpSupplyDTO> supplyList = Lists.newArrayList();
//        for (String supplySource : supplySourceList) {
//            if (MrpSupplySourceEnum.FACTORY_INVENTORY.getCode().equals(supplySource)) {
//                for (MrpSupplyDTO mrpSupplyDTO : mrpContextDTO.getFactorySupplyList()) {
//                    if (supplySource.equals(mrpSupplyDTO.getSupplySource())) {
//                        supplyList.add(mrpSupplyDTO);
//                    }
//                }
//            } else if (MrpSupplySourceEnum.PORT_INVENTORY.getCode().equals(supplySource)) {
//                for (MrpSupplyDTO mrpSupplyDTO : mrpContextDTO.getPortSupplyList()) {
//                    if (supplySource.equals(mrpSupplyDTO.getSupplySource())) {
//                        supplyList.add(mrpSupplyDTO);
//                    }
//                }
//            } else if (MrpSupplySourceEnum.FLOAT_INVENTORY.getCode().equals(supplySource)) {
//                supplyList.addAll(mrpContextDTO.getFloatSupplyList());
//            }
//        }
//        if (supplySourceList.size()>1) {
//            return supplyList.stream().collect(Collectors.groupingBy(MrpSupplyDTO::getProductCode,
//                    Collectors.reducing(BigDecimal.ZERO, MrpSupplyDTO::getUnAllocatedQuantity, BigDecimal::add)));
//        }
//        if (supplySourceList.size() == 1 && supplySourceList.contains(MrpSupplySourceEnum.PORT_INVENTORY.getCode())) {
//            return supplyList.stream().collect(Collectors.groupingBy(MrpSupplyDTO::getProductCode,
//                    Collectors.reducing(BigDecimal.ZERO, MrpSupplyDTO::getUnAllocatedQuantity, BigDecimal::add)));
//        }
//        if (supplySourceList.size() == 1 && supplySourceList.contains(MrpSupplySourceEnum.FACTORY_INVENTORY.getCode())) {
//            return supplyList.stream().collect(Collectors.groupingBy(MrpSupplyDTO::getProductCode,
//                    Collectors.reducing(BigDecimal.ZERO, MrpSupplyDTO::getUnAllocatedQuantity, BigDecimal::add)));
//        }
//        return supplyList.stream().collect(Collectors.groupingBy(t -> t.getProductCode() + "&&" + t.getStockPointCode(),
//                Collectors.reducing(BigDecimal.ZERO, MrpSupplyDTO::getUnAllocatedQuantity, BigDecimal::add)));
//    }
//
//    private static List<BigDecimal> splitBigDecimal(BigDecimal bigDecimal, BigDecimal unit) {
//        List<BigDecimal> parts = new ArrayList<>();
//        BigDecimal remainder = bigDecimal;
//        while (remainder.compareTo(unit) >= 0) {
//            parts.add(unit);
//            remainder = remainder.subtract(unit);
//        }
//        if (remainder.compareTo(BigDecimal.ZERO) > 0) {
//            parts.add(remainder);
//        }
//        return parts;
//    }
//
//    private static BigDecimal getInventoryLevel(BigDecimal standardStockDay,
//                                                String productCode,
//                                                Date currentDate,
//                                                Map<String, MaterialDayTotalDemandDTO> mrpDemandOfDate) {
//        BigDecimal standardStockDayInt = (standardStockDay == null) ? BigDecimal.ZERO : standardStockDay;
//        BigDecimal result = BigDecimal.ZERO;
//        if (standardStockDayInt.compareTo(BigDecimal.ZERO) == 0) {
//            return result;
//        }
//        if (mrpDemandOfDate == null || mrpDemandOfDate.size() == 0) {
//            return result;
//        }
//        int a = 1;
//        List<BigDecimal> bigDecimals = splitBigDecimal(standardStockDayInt, BigDecimal.ONE);
//        for (int i = 1; i <= bigDecimals.size(); i++) {
//            Date dateAfter = DateUtils.moveDay(currentDate, i);
//            String key = String.join("&&", productCode, DateUtils.dateToString(dateAfter));
//            MaterialDayTotalDemandDTO materialDayTotalDemandDTO = mrpDemandOfDate.get(key);
//            if (null == materialDayTotalDemandDTO) {
//                continue;
//            }
//            // 总需求 = 标准需求 + 使用替代量 - 使用替代量
//            BigDecimal demandQty = materialDayTotalDemandDTO.getStandardDemand()
//                    .add(materialDayTotalDemandDTO.getUsedAsReplaceQuantity())
//                    .subtract(materialDayTotalDemandDTO.getUseReplaceQuantity());
//            result = result.add(bigDecimals.get(i - 1).multiply(demandQty).setScale(0, RoundingMode.CEILING));
//        }
//        return result;
//    }
//
//    private static BigDecimal getExpectEndingInventory(BigDecimal endingInventory,
//                                                       BigDecimal safetyStockLevelMin,
//                                                       BigDecimal safetyStockLevelStandard,
//                                                       BigDecimal safetyStockLevelMax) {
//        // 期末库存小于最小安全库存，则取最小安全库存
//        if (endingInventory.compareTo(safetyStockLevelMin) < 0) {
//            return safetyStockLevelStandard;
//        }
//        if (endingInventory.compareTo(safetyStockLevelMax) > 0) {
//            return endingInventory;
//        }
//        return endingInventory;
//    }
//
//
//    private static AllocateDTO getStandardAllocateDTO(BigDecimal unfulfilledQuantity,
//                                                      String inventoryType,
//                                                      MrpDemandDTO demand,
//                                                      MrpContextDTO mrpContextDTO) {
//        // 码头标准规格分配
//        AllocateDTO allocateDTO = new AllocateDTO();
//        if (MrpSupplySourceEnum.FACTORY_INVENTORY.getCode().equals(inventoryType)) {
//            allocateDTO.setMrpSupplyList(mrpContextDTO.getFactorySupplyList()
//                    .stream().filter(t->t.getProductCode().equals(demand.getProductCode()))
//                    .collect(Collectors.toList()));
//        } else if (MrpSupplySourceEnum.PORT_INVENTORY.getCode().equals(inventoryType)) {
//            allocateDTO.setMrpSupplyList(mrpContextDTO.getPortSupplyList()
//                    .stream().filter(t->t.getProductCode().equals(demand.getProductCode()))
//                    .collect(Collectors.toList()));
//        } else if (MrpSupplySourceEnum.FLOAT_INVENTORY.getCode().equals(inventoryType)) {
//            allocateDTO.setMrpSupplyList(mrpContextDTO.getFloatSupplyList()
//                    .stream().filter(t->t.getProductCode().equals(demand.getProductCode()))
//                    .collect(Collectors.toList()));
//
//
//        }
//        allocateDTO.setMrpDemand(demand);
//        if (!MrpSupplySourceEnum.FACTORY_INVENTORY.getCode().equals(inventoryType)) {
//            // 运输路径
//            allocateDTO.setTransportRoutingList(mrpContextDTO.getTransportRoutingList());
//        }
//        allocateDTO.setBcStockPointCode(mrpContextDTO.getBcStockPointCodeList().get(0));
//        allocateDTO.setMrpCalDateStart(mrpContextDTO.getMrpCalcDate());
//        return allocateDTO;
//    }
//
//    private void floatTransfer(MrpContextDTO mrpContextDTO,
//                               Map<String, BigDecimal> fixedAdjustQuantityMap,
//                               List<Date> inventoryShiftDateList,
//                               String productCode,
//                               Map<String, BigDecimal> floatOpeningInventory,
//                               Map<String, BigDecimal> factoryPortOpeningInventoryMap,
//                               Map<String, MaterialDayTotalDemandDTO> demandByProductAndDate,
//                               GlassSafetyInventoryVO glassSafetyInventoryVO,
//                               MaterialPlanInventoryShiftPO adjustInventoryShift,
//                               List<MaterialPlanInventoryOccupyPO> materialPlanInventoryOccupyCreateList,
//                               List<MaterialPlanInventoryShiftPO> materialPlanInventoryShiftCreateList) {
//
//
//        // 物料所有浮法厂的期初库存
//        BigDecimal allFloatOpeningInventory = floatOpeningInventory.entrySet().stream()
//                .filter(entry -> entry.getKey().contains(productCode))
//                .map(Map.Entry::getValue)
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//
//        for (Date inventoryShiftDate : inventoryShiftDateList) {
////            log.info("浮法调拨原片：{}库存推移日期：{}计算开始", productCode, inventoryShiftDateStr);
//
//            MaterialPlanInventoryShiftPO materialPlanInventoryShiftPO = new MaterialPlanInventoryShiftPO();
//            materialPlanInventoryShiftPO.setProductCode(productCode);
//            materialPlanInventoryShiftPO.setAllFfInventory(allFloatOpeningInventory);
//            materialPlanInventoryShiftPO.setInventoryDate(inventoryShiftDate);
//            List<String> stockPointCodeList = Lists.newArrayList();
//            stockPointCodeList.addAll(mrpContextDTO.getBcStockPointCodeList());
//            stockPointCodeList.addAll(mrpContextDTO.getPortStockPointCodeList());
//            materialPlanInventoryShiftPO.setStockPointCode(String.join("&&",stockPointCodeList));
//            // 码头本厂期初库存
//            BigDecimal openingInventory = factoryPortOpeningInventoryMap.get(productCode) ==  null
//                    ? BigDecimal.ZERO : factoryPortOpeningInventoryMap.get(productCode);
//            materialPlanInventoryShiftPO.setOpeningInventory(openingInventory);
//            // 总需求
//            String demandKey = String.join("&&", productCode, DateUtils.dateToString(inventoryShiftDate));
//            MaterialDayTotalDemandDTO materialDaysDemandDO = demandByProductAndDate.get(demandKey);
//            // 标准规格需求
//            BigDecimal standardDemand = materialDaysDemandDO == null ? BigDecimal.ZERO : materialDaysDemandDO.getStandardDemand();
//            materialPlanInventoryShiftPO.setDemandQuantity(standardDemand);
//            // 替代规格需求
//            BigDecimal usedAsReplaceQuantity = materialDaysDemandDO == null ? BigDecimal.ZERO : materialDaysDemandDO.getUsedAsReplaceQuantity();
//            materialPlanInventoryShiftPO.setUsedAsReplaceQuantity(usedAsReplaceQuantity);
//            // 被替代的量
//            BigDecimal useReplaceQuantity = materialDaysDemandDO == null ? BigDecimal.ZERO : materialDaysDemandDO.getUseReplaceQuantity();
//            materialPlanInventoryShiftPO.setUseReplaceQuantity(useReplaceQuantity);
//            // 总需求 = 标准规格需求 + 替代规格需求 - 被替代的量
//            BigDecimal totalDemand = standardDemand.add(usedAsReplaceQuantity).subtract(useReplaceQuantity);
//            // 库存缺口 = 总需求 - 期初库存
//            BigDecimal inventoryGap = totalDemand.subtract(openingInventory);
//            inventoryGap = inventoryGap.compareTo(BigDecimal.ZERO) > 0 ? inventoryGap : BigDecimal.ZERO;
//            materialPlanInventoryShiftPO.setSafetyStockGap(inventoryGap);
//            // 本厂在途
//            List<MrpSupplyDTO> onRoadList = mrpContextDTO.getFactorySupplyList().stream()
//                    .filter(t -> DateUtils.moveDay(t.getSupplyTime(), -1).compareTo(inventoryShiftDate) == 0)
//                    .filter(t -> t.getProductCode().equals(productCode))
//                    .filter(t -> t.getSupplySource().startsWith("FACTORY_ON_WAY"))
//                    .collect(Collectors.toList());
//            BigDecimal factoryOnRoadQuantity = onRoadList.stream().map(MrpSupplyDTO::getSupplyQuantity)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//            // 码头在途
//            List<MrpSupplyDTO> portOnRoadList = mrpContextDTO.getPortSupplyList().stream()
//                    .filter(t -> DateUtils.moveDay(t.getSupplyTime(), -1).compareTo(inventoryShiftDate) == 0)
//                    .filter(t -> t.getProductCode().equals(productCode))
//                    .filter(t -> t.getSupplySource().equals(MrpSupplySourceEnum.PORT_ON_WAY.getCode()))
//                    .collect(Collectors.toList());
//            BigDecimal portOnRoadQuantity = portOnRoadList.stream().map(MrpSupplyDTO::getSupplyQuantity)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//            BigDecimal onRoadQuantity = portOnRoadQuantity.add(factoryOnRoadQuantity);
//            materialPlanInventoryShiftPO.setTransitQuantityFromPort(onRoadQuantity);
//            // 浮法计划送柜量(码头本厂的计划到柜量)
//            BigDecimal planInputQuantity = mrpContextDTO.getMaterialPlanTransferList().stream()
//                    .filter(t -> mrpContextDTO.getFloatStockPointCodeList().contains(t.getStockPointCodeFrom())
//                            && t.getProductCode().equals(productCode)
//                            && t.getTransferDateArrive().compareTo(inventoryShiftDate) == 0)
//                    .map(MaterialPlanTransferVO::getTransferQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
//
//            // 期末库存 = 期初库存 + 在途 + 计划送柜量 - 总需求
//            BigDecimal endingInventory = openingInventory.add(onRoadQuantity).add(planInputQuantity).subtract(totalDemand);
//            endingInventory = endingInventory.compareTo(BigDecimal.ZERO) > 0 ? endingInventory : BigDecimal.ZERO;
//
//            // 最小安全库存天数
//            BigDecimal safetyStockDaysMin = glassSafetyInventoryVO.getMinSafetyInventoryDays().add(glassSafetyInventoryVO.getPortInventoryDays());
//            materialPlanInventoryShiftPO.setSafetyStockLevelMin(safetyStockDaysMin);
//            // 最小完全库存水位
//            BigDecimal safetyStockLevelMin = getInventoryLevel(safetyStockDaysMin, productCode, inventoryShiftDate, demandByProductAndDate);
//            materialPlanInventoryShiftPO.setSafetyStockLevelMin(safetyStockLevelMin);
//            // 目标安全库存天数
//            BigDecimal safetyStockDaysStandard = glassSafetyInventoryVO.getStandardSafetyInventoryDays().add(glassSafetyInventoryVO.getPortInventoryDays());
//            materialPlanInventoryShiftPO.setSafetyStockDaysStandard(safetyStockDaysStandard);
//            // 目标安全库存水位
//            BigDecimal safetyStockLevelStandard = getInventoryLevel(safetyStockDaysStandard, productCode, inventoryShiftDate, demandByProductAndDate);
//            materialPlanInventoryShiftPO.setSafetyStockLevelStandard(safetyStockLevelStandard);
//            // 最大安全库存天数
//            BigDecimal safetyStockDaysMax = glassSafetyInventoryVO.getMaxSafetyInventoryDays().add(glassSafetyInventoryVO.getPortInventoryDays());
//            materialPlanInventoryShiftPO.setSafetyStockDaysMax(safetyStockDaysMax);
//            // 最大完全库存水位
//            BigDecimal safetyStockLevelMax = getInventoryLevel(safetyStockDaysMax, productCode, inventoryShiftDate, demandByProductAndDate);
//            materialPlanInventoryShiftPO.setSafetyStockLevelMax(safetyStockLevelMax);
//            // 期望的期末库存
//            BigDecimal expectEndingInventory = getExpectEndingInventory(endingInventory, safetyStockLevelMin, safetyStockLevelStandard, safetyStockLevelMax);
//            BigDecimal demandQuantity = BigDecimal.ZERO;
//
//
//            if (fixedAdjustQuantityMap.get(DateUtils.dateToString(inventoryShiftDate)) != null) {
//                if (adjustInventoryShift.getAdjustQuantityFromPort().compareTo(BigDecimal.ZERO)>0) {
//                    demandQuantity = adjustInventoryShift.getAdjustQuantityFromPort();
//                }
//            } else {
//                if (expectEndingInventory.compareTo(endingInventory) == 0) {
//                    demandQuantity = adjustInventoryShift.getAdjustQuantityFromPort();
//                } else if (expectEndingInventory.compareTo(endingInventory) > 0) {
//                    demandQuantity = expectEndingInventory.subtract(endingInventory);
//                }
//            }
//
//            if (demandQuantity.compareTo(BigDecimal.ZERO) > 0) {
//                MrpDemandDTO portSafeStockDemand = MrpDemandDTO.builder()
//                        .demandQuantity(demandQuantity)
//                        .unFulfillmentQuantity(demandQuantity)
//                        .demandTime(inventoryShiftDate)
//                        .productCode(productCode)
//                        .build();
//
//                AllocateDTO portSateStockAllocateDTO = getStandardAllocateDTO(demandQuantity,
//                        MrpSupplySourceEnum.FLOAT_INVENTORY.getCode(), portSafeStockDemand, mrpContextDTO);
//                MrpAllocateResultDO allocateResult = InventoryAllocator.allocateFloatInventory(portSateStockAllocateDTO);
//                if (CollectionUtils.isNotEmpty(allocateResult.getMaterialPlanInventoryOccupyList())) {
//                    materialPlanInventoryOccupyCreateList.addAll(allocateResult.getMaterialPlanInventoryOccupyList());
//                    // 决策运入数量
//                    BigDecimal newInputQuantity = allocateResult.getMaterialPlanInventoryOccupyList().stream()
//                            .map(MaterialPlanInventoryOccupyPO::getOccupyQuantity)
//                            .reduce(BigDecimal.ZERO, BigDecimal::add);
//                    materialPlanInventoryShiftPO.setAdjustQuantityFromPort(newInputQuantity);
//                    materialPlanInventoryShiftPO.setEndingInventory(endingInventory.add(newInputQuantity));
//                    endingInventory = endingInventory.add(newInputQuantity);
//                    for (MaterialPlanInventoryOccupyPO materialPlanInventoryOccupyPO : allocateResult.getMaterialPlanInventoryOccupyList()) {
//                        // 生成码头调拨库存
//                        if (mrpContextDTO.getPortStockPointCodeList().contains(materialPlanInventoryOccupyPO.getStockPointCodeTo())){
//                            MrpSupplyDTO mrpSupplyDTO = new MrpSupplyDTO();
//                            mrpSupplyDTO.setSupplyId(materialPlanInventoryOccupyPO.getInventoryId());
//                            mrpSupplyDTO.setSupplyTime(DateUtils.moveDay(materialPlanInventoryOccupyPO.getTransportDateEnd(), 1));
//                            mrpSupplyDTO.setProductCode(productCode);
//                            mrpSupplyDTO.setSupplyQuantity(newInputQuantity);
//                            mrpSupplyDTO.setUnAllocatedQuantity(newInputQuantity);
//                            mrpSupplyDTO.setStockPointCode(materialPlanInventoryOccupyPO.getStockPointCodeTo());
//                            mrpSupplyDTO.setSupplySource(MrpSupplySourceEnum.PORT_TRANSPORT_INVENTORY.getCode());
//                            mrpContextDTO.getPortSupplyList().add(mrpSupplyDTO);
//                        }
//                    }
//                } else {
//                    materialPlanInventoryShiftPO.setAdjustQuantityFromPort(BigDecimal.ZERO);
//                    materialPlanInventoryShiftPO.setEndingInventory(endingInventory);
//                }
//            } else {
//                materialPlanInventoryShiftPO.setAdjustQuantityFromPort(BigDecimal.ZERO);
//                materialPlanInventoryShiftPO.setEndingInventory(endingInventory);
//            }
//            materialPlanInventoryShiftCreateList.add(materialPlanInventoryShiftPO);
//            factoryPortOpeningInventoryMap.put(productCode, endingInventory);
//        }
//    }
//
//    private static void createFloatInventoryShift(MrpContextDTO mrpContextDTO,
//                                                  String productCode,
//                                                  List<MaterialPlanInventoryOccupyPO> floatOccupyList,
//                                                  List<Date> inventoryShiftDateList,
//                                                  Map<String, BigDecimal> floatOpeningInventory,
//                                                  List<MaterialPlanInventoryShiftPO> materialPlanInventoryShiftCreateList) {
//        List<MaterialPlanTransferVO> floatTransferList = new ArrayList<>();
//        // 根据物料分组
//        Set<String> floatCodes = new HashSet<>();
//        if (CollectionUtils.isNotEmpty(mrpContextDTO.getMaterialPlanTransferList())) {
//            floatTransferList = mrpContextDTO.getMaterialPlanTransferList().stream()
//                    .filter(t -> mrpContextDTO.getFloatStockPointCodeList().contains(t.getStockPointCodeFrom()))
//                    .collect(Collectors.toList());
//            if (CollectionUtils.isNotEmpty(floatTransferList)) {
//                floatTransferList.forEach(t->floatCodes.add(t.getStockPointCodeFrom()));
//            }
//        }
//        if (CollectionUtils.isNotEmpty(floatOccupyList)) {
//            floatOccupyList.forEach(t->floatCodes.add(t.getStockPointCodeFrom()));
//        }
//
//        for (String floatCode : floatCodes) {
//            for (Date inventoryShiftDate : inventoryShiftDateList) {
//                MaterialPlanInventoryShiftPO materialPlanInventoryShiftPO = new MaterialPlanInventoryShiftPO();
//                materialPlanInventoryShiftPO.setInventoryDate(inventoryShiftDate);
//                materialPlanInventoryShiftPO.setStockPointCode(floatCode);
//                materialPlanInventoryShiftPO.setProductCode(productCode);
//                // 期初库存
//                BigDecimal openingInventory = floatOpeningInventory.get(productCode + "&&" + floatCode);
//                openingInventory = openingInventory == null ? BigDecimal.ZERO : openingInventory;
//                materialPlanInventoryShiftPO.setOpeningInventory(openingInventory);
//                // 计划出柜量
//                BigDecimal outputQuantity = floatTransferList.stream()
//                        .filter(t -> t.getStockPointCodeFrom().equals(floatCode)
//                                && t.getProductCode().equals(productCode)
//                                && t.getTransferDateDepart().compareTo(inventoryShiftDate) == 0)
//                        .map(MaterialPlanTransferVO::getTransferQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
//                materialPlanInventoryShiftPO.setOutputQuantityToBc(outputQuantity);
//                // 决策发出量
//                BigDecimal newOutputQuantity = floatOccupyList.stream()
//                        .filter(t -> t.getStockPointCodeFrom().equals(floatCode)
//                                && DateUtils.getDayFirstTime(t.getOccupyDate()).compareTo(inventoryShiftDate) == 0)
//                        .map(MaterialPlanInventoryOccupyPO::getOccupyQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
//                materialPlanInventoryShiftPO.setDecisionOutputQuantityToBc(newOutputQuantity);
//                // 期末库存 = 期初库存 - 计划出柜量 - 决策发出量
//                BigDecimal endingInventory = openingInventory.subtract(outputQuantity).subtract(newOutputQuantity);
//                materialPlanInventoryShiftPO.setEndingInventory(endingInventory);
//                materialPlanInventoryShiftCreateList.add(materialPlanInventoryShiftPO);
//                floatOpeningInventory.put(productCode + "&&" + floatCode,endingInventory);
//            }
//        }
//    }
//
//    private void portTransfer(MrpContextDTO mrpContextDTO,
//                              List<MaterialPlanInventoryOccupyPO> floatOccupyList,
//                              List<Date> inventoryShiftDateList,
//                              String productCode,
//                              Map<String, BigDecimal> factoryOpeningInventory,
//                              Map<String, MaterialDayTotalDemandDTO> demandByProductAndDate,
//                              List<MaterialPlanInventoryOccupyPO> materialPlanInventoryOccupyCreateList,
//                              GlassSafetyInventoryVO glassSafetyInventoryVO,
//                              List<MaterialPlanInventoryShiftPO> materialPlanInventoryShiftCreateList) {
//        for (Date inventoryShiftDate : inventoryShiftDateList) {
//            String inventoryShiftDateStr = DateUtils.dateToString(inventoryShiftDate);
////                log.info("码头调拨原片：{}库存推移日期：{}计算开始", productCode, inventoryShiftDateStr);
//            // 本厂库存推移
//            MaterialPlanInventoryShiftPO materialPlanInventoryShiftPO = new MaterialPlanInventoryShiftPO();
//            materialPlanInventoryShiftPO.setProductCode(productCode);
//            materialPlanInventoryShiftPO.setInventoryDate(inventoryShiftDate);
//            String stockPointCode = String.join("&&",mrpContextDTO.getBcStockPointCodeList());
//            materialPlanInventoryShiftPO.setStockPointCode(stockPointCode);
//            // 期初库存
//            BigDecimal openingInventory = factoryOpeningInventory.get(productCode) == null ? BigDecimal.ZERO : factoryOpeningInventory.get(productCode);
//            materialPlanInventoryShiftPO.setOpeningInventory(openingInventory);
//            // 总需求
//            String demandKey = String.join("&&", productCode, DateUtils.dateToString(inventoryShiftDate));
//            MaterialDayTotalDemandDTO materialDaysDemandDO = demandByProductAndDate.get(demandKey);
//            // 标准规格需求
//            BigDecimal standardDemand = materialDaysDemandDO == null ? BigDecimal.ZERO : materialDaysDemandDO.getStandardDemand();
//            materialPlanInventoryShiftPO.setDemandQuantity(standardDemand);
//            // 替代规格需求
//            BigDecimal usedAsReplaceQuantity = materialDaysDemandDO == null ? BigDecimal.ZERO : materialDaysDemandDO.getUsedAsReplaceQuantity();
//            materialPlanInventoryShiftPO.setUsedAsReplaceQuantity(usedAsReplaceQuantity);
//            // 被替代的量
//            BigDecimal useReplaceQuantity = materialDaysDemandDO == null ? BigDecimal.ZERO : materialDaysDemandDO.getUseReplaceQuantity();
//            materialPlanInventoryShiftPO.setUseReplaceQuantity(useReplaceQuantity);
//            // 总需求 = 标准规格需求 + 替代规格需求 - 被替代的量
//            BigDecimal totalDemand = standardDemand.add(usedAsReplaceQuantity).subtract(useReplaceQuantity);
//            // 库存缺口 = 总需求 - 期初库存
//            BigDecimal inventoryGap = totalDemand.subtract(openingInventory);
//            inventoryGap = inventoryGap.compareTo(BigDecimal.ZERO) > 0 ? inventoryGap : BigDecimal.ZERO;
//            materialPlanInventoryShiftPO.setSafetyStockGap(inventoryGap);
//            // 计划到货
//            BigDecimal planInputQuantity = mrpContextDTO.getMaterialPlanTransferList().stream()
//                    .filter(t -> stockPointCode.contains(t.getStockPointCodeTo())
//                            && t.getTransferDateArrive().compareTo(inventoryShiftDate) == 0
//                            && t.getProductCode().equals(productCode))
//                    .map(MaterialPlanTransferVO::getTransferQuantity)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//            materialPlanInventoryShiftPO.setInputQuantity(planInputQuantity);
//            // 在途
//            List<MrpSupplyDTO> onRoadList = mrpContextDTO.getFactorySupplyList().stream()
//                    .filter(t -> DateUtils.moveDay(t.getSupplyTime(), -1).compareTo(inventoryShiftDate) == 0)
//                    .filter(t -> t.getProductCode().equals(productCode))
//                    .filter(t -> t.getSupplySource().equals(MrpSupplySourceEnum.FACTORY_ON_WAY_FROM_PORT.getCode()))
//                    .collect(Collectors.toList());
//            BigDecimal onRoadQuantity = onRoadList.stream().map(MrpSupplyDTO::getSupplyQuantity)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//            materialPlanInventoryShiftPO.setTransitQuantityFromPort(onRoadQuantity);
//            // 免堆期运入量
//            BigDecimal newInputQuantity1 = materialPlanInventoryOccupyCreateList.stream()
//                    .filter(t -> t.getSupplyProductCode().equals(productCode)
//                            && t.getTransportDateEnd().compareTo(inventoryShiftDate) == 0
//                            && stockPointCode.contains(t.getStockPointCodeTo()))
//                    .map(MaterialPlanInventoryOccupyPO::getOccupyQuantity)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//            // 浮法汽运调拨
//            BigDecimal newInputQuantity2 = floatOccupyList.stream()
//                    .filter(t -> t.getSupplyProductCode().equals(productCode)
//                            && t.getTransportDateEnd().compareTo(inventoryShiftDate) == 0
//                            && stockPointCode.contains(t.getStockPointCodeTo()))
//                    .map(MaterialPlanInventoryOccupyPO::getOccupyQuantity)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//
//            BigDecimal newInputQuantity = newInputQuantity1.add(newInputQuantity2);
//            // 临时期末库存量
//            BigDecimal endingInventoryTemp = openingInventory.subtract(totalDemand);
//            endingInventoryTemp = endingInventoryTemp.compareTo(BigDecimal.ZERO) > 0 ? endingInventoryTemp : BigDecimal.ZERO;
//
//            // 期末库存 = 临时期末库存 + 在途 + 运入 + 计划入柜量
//            BigDecimal endingInventory = endingInventoryTemp.add(onRoadQuantity).add(newInputQuantity).add(planInputQuantity);
//            endingInventory = endingInventory.compareTo(BigDecimal.ZERO) > 0 ? endingInventory : BigDecimal.ZERO;
//            // 最小安全库存天数
//            materialPlanInventoryShiftPO.setSafetyStockLevelMin(glassSafetyInventoryVO.getMinSafetyInventoryDays() != null ? glassSafetyInventoryVO.getMinSafetyInventoryDays() : BigDecimal.ZERO);
//
//            // 最小安全库存水位
//            materialPlanInventoryShiftPO.setSafetyStockLevelMin(null != glassSafetyInventoryVO.getMinSafetyInventoryDays()
//                    ? getInventoryLevel(glassSafetyInventoryVO.getMinSafetyInventoryDays(), productCode, inventoryShiftDate, demandByProductAndDate)
//                    : getInventoryLevel(BigDecimal.ZERO, productCode, inventoryShiftDate, demandByProductAndDate));
//
//            // 目标安全库存天数
//            materialPlanInventoryShiftPO.setSafetyStockDaysStandard(glassSafetyInventoryVO.getStandardSafetyInventoryDays() != null ? glassSafetyInventoryVO.getStandardSafetyInventoryDays() : BigDecimal.ZERO);
//
//            // 目标安全库存水位
//            materialPlanInventoryShiftPO.setSafetyStockLevelStandard(null != glassSafetyInventoryVO.getStandardSafetyInventoryDays()
//                    ? getInventoryLevel(glassSafetyInventoryVO.getStandardSafetyInventoryDays(), productCode, inventoryShiftDate, demandByProductAndDate)
//                    : getInventoryLevel(BigDecimal.ZERO, productCode, inventoryShiftDate, demandByProductAndDate));
//
//            // 最大安全库存天数
//            materialPlanInventoryShiftPO.setSafetyStockDaysMax(glassSafetyInventoryVO.getMaxSafetyInventoryDays() != null ? glassSafetyInventoryVO.getMaxSafetyInventoryDays() : BigDecimal.ZERO);
//
//            // 最大安全库存水位
//            materialPlanInventoryShiftPO.setSafetyStockLevelMax(null != glassSafetyInventoryVO.getMaxSafetyInventoryDays()
//                    ? getInventoryLevel(glassSafetyInventoryVO.getMaxSafetyInventoryDays(), productCode, inventoryShiftDate, demandByProductAndDate)
//                    : getInventoryLevel(BigDecimal.ZERO, productCode, inventoryShiftDate, demandByProductAndDate));
//
//            // 期望的期末库存
//            BigDecimal expectEndingInventory = getExpectEndingInventory(endingInventory,
//                    materialPlanInventoryShiftPO.getSafetyStockLevelMin(),
//                    materialPlanInventoryShiftPO.getSafetyStockLevelStandard(),
//                    materialPlanInventoryShiftPO.getSafetyStockLevelMax());
//            if (expectEndingInventory.compareTo(endingInventory) == 0) {
//                materialPlanInventoryShiftPO.setEndingInventory(endingInventory);
//                materialPlanInventoryShiftPO.setAdjustQuantityFromPort(newInputQuantity);
//            } else if (expectEndingInventory.compareTo(endingInventory) > 0) {
//                BigDecimal demandQuantity  = expectEndingInventory.subtract(endingInventory);
//                MrpDemandDTO mrpDemandDTO = MrpDemandDTO.builder()
//                        .demandQuantity(demandQuantity)
//                        .unFulfillmentQuantity(demandQuantity)
//                        .demandTime(inventoryShiftDate)
//                        .demandSource(MrpDemandSourceEnum.FACTORY_SAFE_STOCK.getCode())
//                        .productCode(productCode)
//                        .build();
//                // 码头标准规格分配
//                AllocateDTO standardAllocateDTO = getStandardAllocateDTO(demandQuantity, MrpSupplySourceEnum.PORT_INVENTORY.getCode(),
//                        mrpDemandDTO, mrpContextDTO);
//                MrpAllocateResultDO standardPortAllocateResultDO = InventoryAllocator.allocate(standardAllocateDTO);
//                materialPlanInventoryOccupyCreateList.addAll(standardPortAllocateResultDO.getMaterialPlanInventoryOccupyList());
//
//                materialPlanInventoryShiftPO.setEndingInventory(endingInventory.add(standardPortAllocateResultDO.getFulfillmentQuantity()));
//                materialPlanInventoryShiftPO.setAdjustQuantityFromPort(standardPortAllocateResultDO.getFulfillmentQuantity().add(newInputQuantity));
//            }
//            factoryOpeningInventory.put(productCode, materialPlanInventoryShiftPO.getEndingInventory());
//            materialPlanInventoryShiftCreateList.add(materialPlanInventoryShiftPO);
//
//        }
//    }
//
//    private static void createPortInventoryShift(MrpContextDTO mrpContextDTO,
//                                                 String productCode,
//                                                 List<MaterialPlanInventoryOccupyPO> floatOccupyList,
//                                                 List<MaterialPlanInventoryOccupyPO> portOccupyList,
//                                                 List<Date> inventoryShiftDateList,
//                                                 Map<String, BigDecimal> portOpeningInventory,
//                                                 List<MaterialPlanInventoryShiftPO> materialPlanInventoryShiftCreateList) {
//        String portStockPointCode =  String.join("&&", mrpContextDTO.getPortStockPointCodeList());
//        for (Date inventoryShiftDate : inventoryShiftDateList) {
//            MaterialPlanInventoryShiftPO materialPlanInventoryShiftPO = new MaterialPlanInventoryShiftPO();
//            materialPlanInventoryShiftPO.setStockPointCode(portStockPointCode);
//            materialPlanInventoryShiftPO.setProductCode(productCode);
//            materialPlanInventoryShiftPO.setInventoryDate(inventoryShiftDate);
//            // 期初库存
//            BigDecimal openingInventory = portOpeningInventory.get(productCode);
//            openingInventory = openingInventory == null ? BigDecimal.ZERO : openingInventory;
//            materialPlanInventoryShiftPO.setOpeningInventory(openingInventory);
//            // 码头在途
//            BigDecimal transitQuantity = mrpContextDTO.getPortSupplyList().stream()
//                    .filter(t -> portStockPointCode.contains(t.getStockPointCode())
//                            && t.getProductCode().equals(productCode)
//                            && t.getSupplySource().equals(MrpSupplySourceEnum.PORT_ON_WAY.getCode())
//                            && inventoryShiftDate.compareTo(DateUtils.getDayFirstTime(t.getSupplyTime())) == 0)
//                    .map(MrpSupplyDTO::getSupplyQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
//            materialPlanInventoryShiftPO.setTransitQuantityFromPort(transitQuantity);
//            // 计划到柜量
//            BigDecimal inputQuantity = mrpContextDTO.getMaterialPlanTransferList().stream()
//                    .filter(t -> portStockPointCode.contains(t.getStockPointCodeTo())
//                            && t.getProductCode().equals(productCode)
//                            && DateUtils.getDayFirstTime(t.getTransferDateArrive()).compareTo(inventoryShiftDate) == 0)
//                    .map(MaterialPlanTransferVO::getTransferQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
//            materialPlanInventoryShiftPO.setInputQuantity(inputQuantity);
//            // 计划出柜量(本厂的在途)
//            BigDecimal outputQuantity = mrpContextDTO.getMaterialPlanTransferList().stream()
//                    .filter(t -> portStockPointCode.contains(t.getStockPointCodeFrom())
//                            && t.getProductCode().equals(productCode)
//                            && DateUtils.getDayFirstTime(t.getTransferDateDepart()).compareTo(inventoryShiftDate) == 0)
//                    .map(MaterialPlanTransferVO::getTransferQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
//            materialPlanInventoryShiftPO.setOutputQuantityToBc(outputQuantity);
//            // 决策运出量
//            BigDecimal decisionOutputQuantity = portOccupyList.stream()
//                    .filter(t -> portStockPointCode.contains(t.getStockPointCodeFrom())
//                            && t.getSupplyProductCode().equals(productCode)
//                            && DateUtils.getDayFirstTime(t.getOccupyDate()).compareTo(inventoryShiftDate) == 0)
//                    .map(MaterialPlanInventoryOccupyPO::getOccupyQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
//            materialPlanInventoryShiftPO.setDecisionOutputQuantityToBc(decisionOutputQuantity);
//            // 计划调整量
//            BigDecimal adjustQuantity = floatOccupyList.stream()
//                    .filter(t -> portStockPointCode.contains(t.getStockPointCodeTo())
//                            && t.getSupplyProductCode().equals(productCode)
//                            && DateUtils.getDayFirstTime(t.getTransportDateEnd()).compareTo(inventoryShiftDate) == 0)
//                    .map(MaterialPlanInventoryOccupyPO::getOccupyQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
//            materialPlanInventoryShiftPO.setAdjustQuantityFromPort(adjustQuantity);
//
//            // 期末库存 = 期初库存 + 码头在途 + 计划到柜量 + 调整量 - 计划出柜量 - 决策运出量
//            BigDecimal endingInventory = openingInventory.add(transitQuantity).add(inputQuantity)
//                    .add(adjustQuantity).subtract(outputQuantity).subtract(decisionOutputQuantity);
//            materialPlanInventoryShiftPO.setEndingInventory(endingInventory);
//            portOpeningInventory.put(productCode, endingInventory);
//            materialPlanInventoryShiftCreateList.add(materialPlanInventoryShiftPO);
//        }
//    }
//
//    private static void updatePortAndBcInventoryShift(List<MaterialPlanInventoryShiftPO> portAndFactoryInventoryShiftCreateList,
//                                                      List<MaterialPlanInventoryShiftPO> bcInventoryShiftCreateList) {
//        Map<String, MaterialPlanInventoryShiftPO> bcInventoryShiftMap = bcInventoryShiftCreateList
//                .stream().collect(Collectors.toMap(
//                        t->DateUtils.dateToString(t.getInventoryDate()),
//                        Function.identity()
//                ));
//        portAndFactoryInventoryShiftCreateList.forEach(portAndFactoryInventoryShift -> {
//            BigDecimal bcOpeningInventory = bcInventoryShiftMap
//                    .get(DateUtils.dateToString(portAndFactoryInventoryShift.getInventoryDate()))
//                    .getOpeningInventory();
//            portAndFactoryInventoryShift.setBcOpeningInventory(bcOpeningInventory);
//        });
//    }
//
//
//    private static void assemblyMaterialReplace(MrpContextDTO mrpContextDTO,
//                                                List<MaterialPlanInventoryShiftPO> materialPlanInventoryShiftCreateList,
//                                                List<MaterialPlanReplacePO> materialPlanReplaceCreateList) {
//        List<MaterialPlanInventoryShiftPO> useReplaceInventoryShift = materialPlanInventoryShiftCreateList.stream()
//                .filter(data-> null != data.getUsedAsReplaceQuantity())
//                .filter(t -> t.getStockPointCode().equals(mrpContextDTO.getBcStockPointCodeList().get(0)))
//                .filter(t -> t.getUsedAsReplaceQuantity().compareTo(BigDecimal.ZERO) > 0)
//                .collect(Collectors.toList());
//        // 根据物料编码和日期分组
//        Map<String, MaterialPlanInventoryShiftPO> useReplaceInventoryShiftMap = useReplaceInventoryShift.stream()
//                .collect(Collectors.toMap(t -> t.getProductCode() + "&&" +DateUtils.dateToString(t.getInventoryDate()),
//                        Function.identity()));
//
//        for (MaterialDayTotalDemandDTO materialDayTotalDemandDTO : mrpContextDTO.getMaterialDayTotalDemandList()) {
//            if (materialDayTotalDemandDTO.getUseReplaceQuantity().compareTo(BigDecimal.ZERO) <= 0) {
//                continue;
//            }
//            String substituteProductShiftKey = materialDayTotalDemandDTO.getSubstituteProductCode() + "&&"
//                    + DateUtils.dateToString(materialDayTotalDemandDTO.getDemandDate());
//
//            if (!useReplaceInventoryShiftMap.containsKey(substituteProductShiftKey))
//                return;
//
//            // 替代料的期初库存
//            BigDecimal openingInventory = useReplaceInventoryShiftMap.get(substituteProductShiftKey).getOpeningInventory();
//            // 计划替代数量
//            BigDecimal useReplaceQuantity = materialDayTotalDemandDTO.getUseReplaceQuantity();
//            // 实际替代数量
//            BigDecimal actualUseReplaceQuantity = openingInventory.compareTo(useReplaceQuantity) <= 0 ? openingInventory : useReplaceQuantity;
//
//            MaterialPlanReplacePO materialPlanReplacePO = new MaterialPlanReplacePO();
//            materialPlanReplacePO.setReplaceDate(materialDayTotalDemandDTO.getDemandDate());
//            materialPlanReplacePO.setReplaceType(MrpReplaceTypeEnum.PLAN_REPLACE.getCode());
//            materialPlanReplacePO.setMasterProductCode(materialDayTotalDemandDTO.getProductCode());
//            materialPlanReplacePO.setReplaceProductCode(materialDayTotalDemandDTO.getSubstituteProductCode());
//            materialPlanReplacePO.setReplaceQuantity(actualUseReplaceQuantity);
//            materialPlanReplaceCreateList.add(materialPlanReplacePO);
//        }
//    }
//
//
//    @Override
//    public BaseResponse<String> doAdjust(MaterialPlanInventoryShiftDTO materialPlanInventoryShift) {
//
//        MrpContextDTO mrpContextDTO = new MrpContextDTO();
//        List<String> errorMegList = new ArrayList<>();
//        List<NewStockPointVO> stockPointVOList = mdsFeign.selectAllStockPoint(SystemHolder.getScenario());
//        mrpContextDTO.setStockPointVOList(stockPointVOList);
//
//        MaterialPlanInventoryShiftPO adjustInventoryShift = materialPlanInventoryShiftDao.selectByPrimaryKey(materialPlanInventoryShift.getId());
//        adjustInventoryShift.setAdjustQuantityFromPort(materialPlanInventoryShift.getAdjustQuantityFromPort());
//
//        MaterialPlanVersionPO materialPlanVersionPO = materialPlanVersionDao.selectByPrimaryKey(adjustInventoryShift.getMaterialPlanVersionId());
//
//        // 查询相同版本、相同物料、相同库存点库存推移数据
//        Map<String, Object> params = ImmutableMap.of("materialPlanVersionId", adjustInventoryShift.getMaterialPlanVersionId(),
//                "productCode", adjustInventoryShift.getProductCode());
//        Map<String, String> stockPointCodeMap = mrpContextDTO.getStockPointVOList().stream()
//                .collect(Collectors.toMap(NewStockPointVO::getStockPointCode, NewStockPointVO::getStockPointType));
//        List<MaterialPlanInventoryShiftPO> materialPlanInventoryShiftPOS = materialPlanInventoryShiftDao.selectByParams(params)
//                .stream()
//                .filter(t->{
//                    Set<String> stockPointType = new HashSet<>();
//                    Arrays.asList(t.getStockPointCode().split("&&"))
//                    .forEach(stockPointCode -> stockPointType.add(stockPointCodeMap.get(stockPointCode)));
//                    return stockPointType.size() == 2
//                            && stockPointType.contains(StockPointTypeEnum.BC.getCode())
//                            && stockPointType.contains(StockPointTypeEnum.MT.getCode()) ;
//                })
//                .filter(t->!t.getInventoryDate().after(adjustInventoryShift.getInventoryDate()))
//                .collect(Collectors.toList());
//        materialPlanInventoryShiftPOS.sort(Comparator.comparing(MaterialPlanInventoryShiftPO::getInventoryDate));
//
//        Map<String, BigDecimal> fixedAdjustQuantityMap = materialPlanInventoryShiftPOS.stream()
//                .collect(Collectors.toMap(t->DateUtils.dateToString(t.getInventoryDate()),
//                        MaterialPlanInventoryShiftPO::getAdjustQuantityFromPort));
//
//
//        List<NewProductStockPointVO> newProductStockPointVOS = mdsFeign.selectProductStockPointVOByProductCodes(
//                SystemHolder.getScenario(), Lists.newArrayList(adjustInventoryShift.getProductCode()));
//        Map<String, NewProductStockPointVO> newProductStockPointVOMap = newProductStockPointVOS.stream()
//                .collect(Collectors.toMap(NewProductStockPointVO::getId, Function.identity()));
//        mrpContextDTO.setNewProductStockPointVOList(newProductStockPointVOS);
//        mrpContextDTO.setNewProductStockPointVOMapOfId(newProductStockPointVOMap);
//
//
//
//        // 需求
//        List<MrpDemandDTO> glassDemandList = materialPlanInventoryShiftPOS.stream()
//                .filter(t->{
//                    Set<String> stockPointType = new HashSet<>();
//                    Arrays.asList(t.getStockPointCode().split("&&"))
//                            .forEach(stockPointCode -> stockPointType.add(stockPointCodeMap.get(stockPointCode)));
//                    return stockPointType.size() == 1 && stockPointType.contains(StockPointTypeEnum.BC.getCode()) ;
//                })
//                .filter(t -> t.getDemandQuantity() != null  && t.getDemandQuantity().compareTo(BigDecimal.ZERO) > 0)
//                .map(t -> MrpDemandDTO.builder()
//                .productCode(t.getProductCode())
//                .productClassify("RA.A")
//                .demandTime(t.getInventoryDate())
//                .demandQuantity(t.getDemandQuantity())
//                .demandSource(MrpDemandSourceEnum.MCB.getCode())
//                .build()).collect(Collectors.toList());
//        mrpContextDTO.setMrpDemandList(glassDemandList);
//
//
//        Date inventoryFirstDate = materialPlanInventoryShiftPOS.get(0).getInventoryDate();
//        mrpContextDTO.setMrpCalcDate(inventoryFirstDate);
//
//        initMrpContextDTO(mrpContextDTO);
//
//        Map<String, MaterialDayTotalDemandDTO> demandByProductAndDate = new HashMap<>();
//        if (CollectionUtils.isNotEmpty(mrpContextDTO.getMaterialDayTotalDemandList())) {
//            demandByProductAndDate = mrpContextDTO.getMaterialDayTotalDemandList().stream()
//                    .collect(Collectors.toMap(t ->
//                                    String.join("&&", t.getProductCode(), DateUtils.dateToString(t.getDemandDate())),
//                            Function.identity()));
//        }
//
//        // 库存推移日期范围
//        List<Date> inventoryShiftDateList = getInventoryShiftDateList(mrpContextDTO.getMrpCalcDate());
//        // 原片安全库存
//        List<GlassSafetyInventoryVO> glassSafetyInventoryVOList = glassSafetyInventoryService.selectAll();
//        Map<String, GlassSafetyInventoryVO> glassSafetyInventoryVOMap = glassSafetyInventoryVOList.stream()
//                .collect(Collectors.toMap(data -> data.getColor() + "&" + data.getThickness(), Function.identity(), (v1, v2) -> v1));
//        // 获取通用*的安全库存
//        List<GlassSafetyInventoryVO> generalList = glassSafetyInventoryService.selectByParams(ImmutableMap.of("color", "*"));
//        // 库存推移
//        List<MaterialPlanInventoryShiftPO> materialPlanInventoryShiftCreateList = new ArrayList<>(1024*1024);
//        // 码头库存批次占用
//        List<MaterialPlanInventoryOccupyPO> inventoryOccupyCreateList = new ArrayList<>(1024*1024);
//        // 物料替代计划
//        List<MaterialPlanReplacePO> materialPlanReplaceCreateList = new ArrayList<>(1024*1024);
//        // 本厂码头期初库存
//        Map<String, BigDecimal> factoryPortOpeningInventoryMap = getOpeningInventory(mrpContextDTO,
//                Lists.newArrayList(MrpSupplySourceEnum.FACTORY_INVENTORY.getCode(), MrpSupplySourceEnum.PORT_INVENTORY.getCode()));
//        // 本厂期初库存
//        Map<String, BigDecimal> factoryOpeningInventory = getOpeningInventory(mrpContextDTO, Lists.newArrayList(MrpSupplySourceEnum.FACTORY_INVENTORY.getCode()));
//        // 码头期初库存
//        Map<String, BigDecimal> portOpeningInventory = getOpeningInventory(mrpContextDTO, Lists.newArrayList(MrpSupplySourceEnum.PORT_INVENTORY.getCode()));
//        // 浮法期初库存
//        Map<String, BigDecimal> floatOpeningInventory = getOpeningInventory(mrpContextDTO, Lists.newArrayList(MrpSupplySourceEnum.FLOAT_INVENTORY.getCode()));
//
//
//
//        List<MaterialPlanInventoryOccupyPO> floatOccupyList = new ArrayList<>();
//        List<MaterialPlanInventoryShiftPO> portAndFactoryInventoryShiftCreateList = new ArrayList<>(1024);
//        List<MaterialPlanInventoryShiftPO> floatInventoryShiftCreateList = new ArrayList<>(1024);
//        List<MaterialPlanInventoryShiftPO> portInventoryShiftCreateList = new ArrayList<>(1024);
//        List<MaterialPlanInventoryShiftPO> bcInventoryShiftCreateList = new ArrayList<>(1024);
//
//        // 物料数据根据物料编码分组
//        Map<String, NewProductStockPointVO> productCodeProductStockPointVOMap = mrpContextDTO.getNewProductStockPointVOList().stream()
//                .collect(Collectors.toMap(NewProductStockPointVO::getProductCode, Function.identity(), (v1, v2) -> v1));
//
//        String productCode = adjustInventoryShift.getProductCode();
//
//        // 根据物料编码获取物料颜色+厚度去取原片安全库存
//        GlassSafetyInventoryVO glassSafetyInventoryVO = glassSafetyInventoryVOMap.get("*&*");
//        NewProductStockPointVO newProductStockPointVO = productCodeProductStockPointVOMap.get(productCode);
//        if (Objects.nonNull(newProductStockPointVO) && StringUtils.isNotEmpty(newProductStockPointVO.getProductColor()) && null != newProductStockPointVO.getProductThickness()) {
//            String safeInventoryKey = newProductStockPointVO.getProductColor() + "&" + newProductStockPointVO.getProductThickness();
//            if (glassSafetyInventoryVOMap.containsKey(safeInventoryKey)) {
//                glassSafetyInventoryVO = glassSafetyInventoryVOMap.get(safeInventoryKey);
//            } else {
//                errorMegList.add("物料编码" + productCode + "颜色" + newProductStockPointVO.getProductColor() + "厚度" + newProductStockPointVO.getProductThickness() + "在原片安全库存中不存在，取*通用库存");
//            }
//        } else {
//            errorMegList.add("物料编码" + productCode + "未取到颜色厚度");
//        }
//
//        // 浮法调拨
//        floatTransfer(mrpContextDTO, fixedAdjustQuantityMap, inventoryShiftDateList, productCode, floatOpeningInventory, factoryPortOpeningInventoryMap,
//                demandByProductAndDate, glassSafetyInventoryVO, adjustInventoryShift, floatOccupyList, portAndFactoryInventoryShiftCreateList);
//        // 生成浮法库存推移数据
//        createFloatInventoryShift(mrpContextDTO, productCode, floatOccupyList, inventoryShiftDateList, floatOpeningInventory,
//                floatInventoryShiftCreateList);
//        // 码头调拨
//        List<MaterialPlanInventoryOccupyPO> portOccupyList = new ArrayList<>();
//        portTransfer(mrpContextDTO, floatOccupyList, inventoryShiftDateList, productCode, factoryOpeningInventory, demandByProductAndDate,
//                portOccupyList, glassSafetyInventoryVO, bcInventoryShiftCreateList);
//        // 码头的库存推移数据
//        createPortInventoryShift(mrpContextDTO, productCode, floatOccupyList, portOccupyList, inventoryShiftDateList,
//                portOpeningInventory, portInventoryShiftCreateList);
//        // 根据本厂库存推移结果更新码头本厂推移结果
//        updatePortAndBcInventoryShift(portAndFactoryInventoryShiftCreateList, bcInventoryShiftCreateList);
//
//        inventoryOccupyCreateList.addAll(floatOccupyList);
//        inventoryOccupyCreateList.addAll(portOccupyList);
//        materialPlanInventoryShiftCreateList.addAll(portAndFactoryInventoryShiftCreateList);
//        materialPlanInventoryShiftCreateList.addAll(floatInventoryShiftCreateList);
//        materialPlanInventoryShiftCreateList.addAll(portInventoryShiftCreateList);
//        materialPlanInventoryShiftCreateList.addAll(bcInventoryShiftCreateList);
//
//        // 生成替代计划
//        assemblyMaterialReplace(mrpContextDTO, materialPlanInventoryShiftCreateList, materialPlanReplaceCreateList);
//        MrpResultPO mrpResultPO = new MrpResultPO();
//        mrpResultPO.setInventoryShiftPOList(materialPlanInventoryShiftCreateList);
//        mrpResultPO.setReplacePOList(materialPlanReplaceCreateList);
//        mrpResultPO.setMaterialPlanOccupyPOList(inventoryOccupyCreateList);
//        mrpResultPO.setErrorMsgList(errorMegList);
//
//
//        MaterialPlanVersionVO materialPlanVersionVO = MaterialPlanVersionConvertor.INSTANCE.po2Vo(materialPlanVersionPO);
//        MaterialPlanVersionDTO materialPlanVersionDTO = MaterialPlanVersionConvertor.INSTANCE.vo2Dto(materialPlanVersionVO);
//        mrpResultPO.setInsertMaterialPlanVersionDTO(materialPlanVersionDTO);
//        save(mrpResultPO);
//        return BaseResponse.success();
//
//    }
//
//    protected void save(MrpResultPO mrpResultPO) {
//        Date now = new Date();
//        MaterialPlanVersionDTO materialPlanVersionDTO = mrpResultPO.getInsertMaterialPlanVersionDTO();
//        String materialPlanVersionId = materialPlanVersionDTO.getId();
//        // 删除上一次计算结果
//        String productCode = mrpResultPO.getInventoryShiftPOList().get(0).getProductCode();
//
//
//        materialPlanInventoryShiftDao.deleteByVersionIdAndProductCode(materialPlanVersionDTO.getId(),productCode);
//        materialPlanInventoryOccupyDao.deleteByVersionIdAndProductCode(materialPlanVersionDTO.getId(),productCode);
//
//        List<MaterialPlanInventoryShiftPO> inventoryShiftPOList = mrpResultPO.getInventoryShiftPOList();
//        BasePOUtils.insertBatchFiller(inventoryShiftPOList, now);
//        inventoryShiftPOList.forEach(t->t.setMaterialPlanVersionId(materialPlanVersionId));
//
//        List<MaterialPlanInventoryOccupyPO> materialPlanInventoryOccupyPOList = mrpResultPO.getMaterialPlanOccupyPOList();
//        BasePOUtils.insertBatchFiller(materialPlanInventoryOccupyPOList, now);
//        materialPlanInventoryOccupyPOList.forEach(t->t.setMaterialPlanVersionId(materialPlanVersionId));
//
////        List<MaterialPlanReplacePO> materialPlanReplacePOList = mrpResultPO.getReplacePOList();
////        BasePOUtils.insertBatchFiller(materialPlanReplacePOList, now);
////        materialPlanReplacePOList.forEach(t->t.setMaterialPlanVersionId(materialPlanVersionId));
//        Lists.partition(inventoryShiftPOList, 1500)
//                .forEach(list->materialPlanInventoryShiftDao.insertBatchWithPrimaryKey(list));
//        Lists.partition(materialPlanInventoryOccupyPOList, 1500)
//                .forEach(list->materialPlanInventoryOccupyDao.insertBatchWithPrimaryKey(list));
////        Lists.partition(materialPlanReplacePOList, 1500)
////                .forEach(list->materialPlanReplaceDao.insertBatchWithPrimaryKey(list));
//    }
//
//    private List<MaterialPlanTransferVO> getMaterialPlanTransferList() {
//        // 查询调拨开始时间发生在未来的调拨计划
//        Map<String, Object> params = new HashMap<>();
////        params.put("transferDateArriveStart",DateUtils.getDayFirstTime(new Date()));
//        params.put("s", YesOrNoEnum.YES.getCode());
//        params.put("storageFlag", Lists.newArrayList(TransferStatusEnum.PUBLISHED.getCode()));
//        return materialPlanTransferService.selectByParams(params);
//    }
//
//
//    private MrpSupplyDTO createMrpSupply(String supplyId,
//                                         String productCode,
//                                         String stockPointCode,
//                                         String supplySource,
//                                         Date supplyTime,
//                                         BigDecimal supplyQuantity) {
//        MrpSupplyDTO mrpSupplyDTO = new MrpSupplyDTO();
//        mrpSupplyDTO.setSupplyId(supplyId);
//        mrpSupplyDTO.setProductCode(productCode);
//        mrpSupplyDTO.setStockPointCode(stockPointCode);
//        mrpSupplyDTO.setSupplyQuantity(supplyQuantity);
//        mrpSupplyDTO.setUnAllocatedQuantity(supplyQuantity);
//        mrpSupplyDTO.setSupplyTime(supplyTime);
//        mrpSupplyDTO.setSupplySource(supplySource);
//        return mrpSupplyDTO;
//    }
//
//
//
//
//
//
//
//}
