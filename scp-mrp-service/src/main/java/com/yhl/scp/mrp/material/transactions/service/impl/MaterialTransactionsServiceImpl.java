package com.yhl.scp.mrp.material.transactions.service.impl;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpMaterialTransactions;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesReturnedPurchase;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mrp.material.purchase.dto.MaterialReturnedPurchaseDTO;
import com.yhl.scp.mrp.material.purchase.infrastructure.po.MaterialReturnedPurchasePO;
import com.yhl.scp.mrp.material.transactions.convertor.MaterialTransactionsConvertor;
import com.yhl.scp.mrp.material.transactions.domain.entity.MaterialTransactionsDO;
import com.yhl.scp.mrp.material.transactions.domain.service.MaterialTransactionsDomainService;
import com.yhl.scp.mrp.material.transactions.dto.MaterialTransactionsDTO;
import com.yhl.scp.mrp.material.transactions.infrastructure.dao.MaterialTransactionsDao;
import com.yhl.scp.mrp.material.transactions.infrastructure.po.MaterialTransactionsPO;
import com.yhl.scp.mrp.material.transactions.service.MaterialTransactionsService;
import com.yhl.scp.mrp.material.transactions.vo.MaterialTransactionsVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>MaterialTransactionsServiceImpl</code>
 * <p>
 * 物料事务处理查询应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-18 14:45:33
 */
@Slf4j
@Service
public class MaterialTransactionsServiceImpl extends AbstractService implements MaterialTransactionsService {

    @Resource
    private MaterialTransactionsDao materialTransactionsDao;

    @Resource
    private MaterialTransactionsDomainService materialTransactionsDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private MaterialTransactionsService materialTransactionsService;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(MaterialTransactionsDTO materialTransactionsDTO) {
        // 0.数据转换
        MaterialTransactionsDO materialTransactionsDO = MaterialTransactionsConvertor.INSTANCE.dto2Do(materialTransactionsDTO);
        MaterialTransactionsPO materialTransactionsPO = MaterialTransactionsConvertor.INSTANCE.dto2Po(materialTransactionsDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialTransactionsDomainService.validation(materialTransactionsDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(materialTransactionsPO);
        materialTransactionsDao.insert(materialTransactionsPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(MaterialTransactionsDTO materialTransactionsDTO) {
        // 0.数据转换
        MaterialTransactionsDO materialTransactionsDO = MaterialTransactionsConvertor.INSTANCE.dto2Do(materialTransactionsDTO);
        MaterialTransactionsPO materialTransactionsPO = MaterialTransactionsConvertor.INSTANCE.dto2Po(materialTransactionsDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialTransactionsDomainService.validation(materialTransactionsDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(materialTransactionsPO);
        materialTransactionsDao.update(materialTransactionsPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MaterialTransactionsDTO> list) {
        List<MaterialTransactionsPO> newList = MaterialTransactionsConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        materialTransactionsDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<MaterialTransactionsDTO> list) {
        List<MaterialTransactionsPO> newList = MaterialTransactionsConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialTransactionsDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return materialTransactionsDao.deleteBatch(idList);
        }
        return materialTransactionsDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MaterialTransactionsVO selectByPrimaryKey(String id) {
        MaterialTransactionsPO po = materialTransactionsDao.selectByPrimaryKey(id);
        return MaterialTransactionsConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "MATERIAL_TRANSACTIONS")
    public List<MaterialTransactionsVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "MATERIAL_TRANSACTIONS")
    public List<MaterialTransactionsVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MaterialTransactionsVO> dataList = materialTransactionsDao.selectByCondition(sortParam, queryCriteriaParam);
        MaterialTransactionsServiceImpl target = SpringBeanUtils.getBean(MaterialTransactionsServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MaterialTransactionsVO> selectByParams(Map<String, Object> params) {
        List<MaterialTransactionsPO> list = materialTransactionsDao.selectByParams(params);
        return MaterialTransactionsConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MaterialTransactionsVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.MATERIAL_TRANSACTIONS.getCode();
    }

    @Override
    public List<MaterialTransactionsVO> invocation(List<MaterialTransactionsVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public BaseResponse<Void> handleMaterialTransactions(List<ErpMaterialTransactions> list) {
        if (CollectionUtils.isEmpty(list)) {
            return BaseResponse.success();
        }
        List<MaterialTransactionsDTO> insertDtoS = new ArrayList<>();
        List<MaterialTransactionsDTO> updateDtoS = new ArrayList<>();
        List<String> transactionsIdList =   list.stream().map(ErpMaterialTransactions::getTransactionId)
                .distinct().collect(Collectors.toList());
        List<MaterialTransactionsPO> oldPos = materialTransactionsDao.selectByParams(ImmutableMap.of("transactionIdList",
                transactionsIdList));
        Map<String, MaterialTransactionsPO> oldPosMap = CollectionUtils.isEmpty(oldPos) ?
                MapUtil.newHashMap() :
                oldPos.stream().collect(Collectors.toMap(t -> t.getTransactionId(),
                        Function.identity(), (v1, v2) -> v1));
        for (ErpMaterialTransactions erpMaterialTransactions : list) {
            MaterialTransactionsDTO dto =  new MaterialTransactionsDTO();
            String id= erpMaterialTransactions.getTransactionId() ;
            if (oldPosMap.containsKey(id)) {
                // MaterialTransactionsPO oldPo = oldPosMap.get(id);
                // org.springframework.beans.BeanUtils.copyProperties(oldPo, dto);
                // generateDto(dto, erpMaterialTransactions);
                // updateDtoS.add(dto);
            } else {
                generateDto(dto, erpMaterialTransactions);
                insertDtoS.add(dto);
            }

        }
        if (CollectionUtils.isNotEmpty(insertDtoS)) {
            doCreateBatch(insertDtoS);
        }
        // if (CollectionUtils.isNotEmpty(updateDtoS)) {
        //     doUpdateBatch(updateDtoS);
        // }

        return BaseResponse.success();
    }

    @Override
    public BaseResponse<Void> syncMaterialTransactions(String tenantId) {
        Map<String, Object> params = MapUtil.newHashMap();
        newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.ERP.getCode(),
                ApiCategoryEnum.MATERIAL_TRANSACTIONS.getCode(), params);
        return BaseResponse.success("同步成功");
    }

    private void generateDto(MaterialTransactionsDTO dto, ErpMaterialTransactions erpMaterialTransactions) {
        dto.setCompany(erpMaterialTransactions.getCompany());
        dto.setStockPointCode(erpMaterialTransactions.getFactory());
        dto.setProductCode(erpMaterialTransactions.getItemNo());
        dto.setDescription(erpMaterialTransactions.getDesc());
        dto.setBatch(erpMaterialTransactions.getLotNo());
        dto.setCategory(erpMaterialTransactions.getCategory());
        dto.setTransactionType(erpMaterialTransactions.getTransactionType());
        dto.setSourceType(erpMaterialTransactions.getSourceType());
        dto.setSource(erpMaterialTransactions.getSource());
        dto.setSubInventory(erpMaterialTransactions.getSubinventory());
        dto.setLocation(erpMaterialTransactions.getLocation());
        dto.setToOrg(erpMaterialTransactions.getToOrg());
        dto.setToLocation(erpMaterialTransactions.getToLocation());
        dto.setToSubInventory(erpMaterialTransactions.getToSubinventory());
        dto.setTransactionQty(erpMaterialTransactions.getTransactionQty());
        dto.setTransactionUom(erpMaterialTransactions.getTransactionUom());
        dto.setPrimaryQty(erpMaterialTransactions.getPrimaryQty());
        dto.setPrimaryUnit(erpMaterialTransactions.getPrimaryUnit());
        dto.setArea(erpMaterialTransactions.getArea());
        dto.setCost(erpMaterialTransactions.getCost());
        dto.setItemId(erpMaterialTransactions.getItemId());
        dto.setPrimaryUnit(erpMaterialTransactions.getPrimaryUnit());
        dto.setTransactionDate(erpMaterialTransactions.getTransactionDate());
        dto.setLastUpdateDate(erpMaterialTransactions.getLastUpdateDate());
        dto.setTransactionId(erpMaterialTransactions.getTransactionId());

    }
}
