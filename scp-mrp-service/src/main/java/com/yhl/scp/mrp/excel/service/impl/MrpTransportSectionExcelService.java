package com.yhl.scp.mrp.excel.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.common.excel.DataImportInfo;
import com.yhl.scp.common.excel.ImportDataTypeEnum;
import com.yhl.scp.common.excel.model.ImportAnalysisResultHolder;
import com.yhl.scp.common.excel.model.ImportContext;
import com.yhl.scp.common.excel.model.ImportRelatedDataHolder;
import com.yhl.scp.common.excel.service.AbstractExcelService;
import com.yhl.scp.common.vo.SimpleVO;
import com.yhl.scp.dfp.transport.vo.TransportResourceVO;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mrp.transport.convertor.TransportRoutingConvertor;
import com.yhl.scp.mrp.transport.convertor.TransportSectionConvertor;
import com.yhl.scp.mrp.transport.dto.TransportRoutingDTO;
import com.yhl.scp.mrp.transport.dto.TransportSectionBasicDTO;
import com.yhl.scp.mrp.transport.dto.TransportSectionDTO;
import com.yhl.scp.mrp.transport.infrastructure.dao.TransportRoutingDao;
import com.yhl.scp.mrp.transport.infrastructure.dao.TransportSectionDao;
import com.yhl.scp.mrp.transport.infrastructure.po.TransportRoutingBasicPO;
import com.yhl.scp.mrp.transport.infrastructure.po.TransportRoutingPO;
import com.yhl.scp.mrp.transport.infrastructure.po.TransportSectionPO;
import com.yhl.scp.mrp.transport.service.TransportRoutingService;
import com.yhl.scp.mrp.transport.service.TransportSectionService;
import com.yhl.scp.mrp.transport.vo.TransportRoutingVO;
import com.yhl.scp.mrp.transport.vo.TransportSectionVO;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>MrpTransportSectionExcelService</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-11 18:57:55
 */
@Service
public class MrpTransportSectionExcelService extends AbstractExcelService<TransportSectionDTO, TransportSectionPO, TransportSectionVO> {
    @Resource
    private TransportSectionService transportSectionService;

    @Resource
    private TransportSectionDao transportSectionDao;
    @Resource
    private TransportRoutingService transportRoutingService;
    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private IpsFeign ipsFeign;

    @Override
    public BaseDao<TransportSectionPO, TransportSectionVO> getBaseDao() {
        return transportSectionDao;
    }

    @Override
    public Function<TransportSectionDTO, TransportSectionPO> getDTO2POConvertor() {
        return TransportSectionConvertor.INSTANCE::dto2Po;
    }

    @Override
    public Class<TransportSectionDTO> getDTOClass() {
        return TransportSectionDTO.class;
    }

    @Override
    public BaseService<TransportSectionDTO, TransportSectionVO> getBaseService() {
        return transportSectionService;
    }

    @Override
    protected void fillIdForUpdateData(List<TransportSectionDTO> list, Map<String, TransportSectionPO> map) {
        for (TransportSectionDTO dto : list) {
            TransportSectionPO transportSectionPO = map.get(String.join("&", dto.getRoutingId(), dto.getOriginStockPointId(), dto.getDestinStockPointId()));
            if (transportSectionPO != null){
                dto.setId(transportSectionPO.getId());
            }
        }
    }
    @Override
    protected void specialVerification(ImportAnalysisResultHolder<TransportSectionDTO, TransportSectionPO> resultHolder, ImportContext importContext) {
        List<TransportSectionDTO> insertList = resultHolder.getInsertList();
        List<TransportSectionDTO> updateList = resultHolder.getUpdateList();
        verifyPaternity(insertList, resultHolder.getImportLogList());
        verifyPaternity(updateList, resultHolder.getImportLogList());
        resultHolder.setInsertList(insertList);
        resultHolder.setUpdateList(updateList);
    }
    @Override
    protected ImportRelatedDataHolder<TransportSectionPO> prepareData(List<TransportSectionDTO> list) {
        List<String> routingCodeList = list.stream().map(TransportSectionBasicDTO::getRoutingId).distinct().collect(Collectors.toList());
        Map<String, Object> queryMap = MapUtil.newHashMap();
        queryMap.put("enabled", YesOrNoEnum.YES.getCode());
        queryMap.put("routingCodeList", routingCodeList);
        List<TransportRoutingVO> transportRoutingVOS = transportRoutingService.selectByParams(queryMap);
        List<SimpleVO> routingVOS = CollectionUtils.isEmpty(transportRoutingVOS) ?
                Lists.newArrayList() :
                transportRoutingVOS.stream().map(x -> {
                    SimpleVO simpleVO = new SimpleVO();
                    simpleVO.setId(x.getId());
                    simpleVO.setCode(x.getRoutingCode());
                    return simpleVO;
                }).collect(Collectors.toList());
        List<TransportSectionPO> existingData = transportSectionDao.selectByParams(new HashMap<>(2));
        Map<String, TransportSectionPO> codeToPOMap = existingData.stream().collect(Collectors
                .toMap(t-> String.join("&", t.getRoutingId(), t.getOriginStockPointId(), t.getDestinStockPointId()), Function.identity(), (v1, v2) -> v1));
        String scenario = ipsNewFeign.getDefaultScenario(SystemModuleEnum.MDS.getCode(), SystemHolder.getTenantId()).getData();
        Map<String, List<SimpleVO>> foreignDataMap = new HashMap<>();
        List<NewStockPointVO> stockPointVOS = newMdsFeign.selectAllStockPoint(scenario);
        List<SimpleVO> stockVOS = CollectionUtils.isEmpty(stockPointVOS) ?
                Lists.newArrayList() :
                stockPointVOS.stream().map(x -> {
                    SimpleVO simpleVO = new SimpleVO();
                    simpleVO.setId(x.getId());
                    simpleVO.setCode(x.getStockPointName());
                    return simpleVO;
                }).collect(Collectors.toList());
        List<CollectionValueVO> transportTypeList = ipsFeign.getByCollectionCode("TRANSPORT_TYPE");
        List<SimpleVO> transportTypeListVOS = CollectionUtils.isEmpty(transportTypeList) ?
                Lists.newArrayList() :
                transportTypeList.stream().map(x -> {
                    SimpleVO simpleVO = new SimpleVO();
                    simpleVO.setId(x.getCollectionValue());
                    simpleVO.setCode(x.getValueMeaning());
                    return simpleVO;
                }).collect(Collectors.toList());
        List<String> uniqueKeys = ListUtil.of("routingId", "originStockPointId", "destinStockPointId");
        List<String> foreignKeys = ListUtil.of("routingId","originStockPointId", "destinStockPointId", "transportType");
        foreignDataMap.put("routingId", routingVOS);
        foreignDataMap.put("originStockPointId", stockVOS);
        foreignDataMap.put("destinStockPointId", stockVOS);
        foreignDataMap.put("transportType", transportTypeListVOS);

        return ImportRelatedDataHolder.<TransportSectionPO>builder()
                .existingData(existingData)
                .mainKeys(uniqueKeys)
                .foreignKeys(foreignKeys)
                .foreignDataMap(foreignDataMap)
                .existingDataMap(codeToPOMap)
                .build();
    }
    private void verifyPaternity(List<TransportSectionDTO> checkList, List<DataImportInfo> importLogList) {

        String scenario = ipsNewFeign.getDefaultScenario(SystemModuleEnum.MDS.getCode(), SystemHolder.getTenantId()).getData();
        List<NewStockPointVO> stockPointVOS = newMdsFeign.selectAllStockPoint(scenario);
        Map<String, NewStockPointVO> newStockPointVOMap = stockPointVOS.stream()
                .collect(Collectors.toMap(NewStockPointVO::getOrganizeId, Function.identity(), (v1, v2) -> v1));
        List<CollectionValueVO> transportTypeList = ipsFeign.getByCollectionCode("TRANSPORT_TYPE");
        Map<String, CollectionValueVO> transportTypeMap = transportTypeList.stream()
                .collect(Collectors.toMap(CollectionValueVO::getCollectionValue, Function.identity(), (v1, v2) -> v1));

        List<TransportRoutingVO> transportRoutingVOS = transportRoutingService.selectByParams(ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode()));
        Map<String, TransportRoutingVO> transportRoutingMap = transportRoutingVOS.stream()
                .collect(Collectors.toMap(TransportRoutingVO::getId, Function.identity(), (v1, v2) -> v1));


        Iterator<TransportSectionDTO> iterator = checkList.iterator();
        while (iterator.hasNext()) {
            TransportSectionDTO transportSectionDTO = iterator.next();
            if (isFieldEmpty(transportSectionDTO.getRoutingId())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + transportSectionDTO.getRowIndex() + ";[路径代码]未填写");
                dataImportInfo.setDisplayIndex(transportSectionDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                throw new BusinessException("行数：" + transportSectionDTO.getRowIndex() + ";[路径代码]未填写");
            }
            if(!transportRoutingMap.containsKey(transportSectionDTO.getRoutingId())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + transportSectionDTO.getRowIndex() + ";[路径代码]不存在");
                dataImportInfo.setDisplayIndex(transportSectionDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                throw new BusinessException("行数：" + transportSectionDTO.getRowIndex() + ";[路径代码]不存在");

            }

            if (isFieldEmpty(transportSectionDTO.getOriginStockPointId())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + transportSectionDTO.getRowIndex() + ";[起点库存点]未填写");
                dataImportInfo.setDisplayIndex(transportSectionDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                throw new BusinessException("行数：" + transportSectionDTO.getRowIndex() + ";[起点库存点]未填写");

            }
            if(!newStockPointVOMap.containsKey(transportSectionDTO.getOriginStockPointId())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + transportSectionDTO.getRowIndex() + ";[起点库存点]不存在");
                dataImportInfo.setDisplayIndex(transportSectionDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                throw new BusinessException("行数：" + transportSectionDTO.getRowIndex() + ";[起点库存点]不存在");

            }
            if (isFieldEmpty(transportSectionDTO.getDestinStockPointId())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + transportSectionDTO.getRowIndex() + ";[终点库存点]未填写");
                dataImportInfo.setDisplayIndex(transportSectionDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                throw new BusinessException("行数：" + transportSectionDTO.getRowIndex() + ";[终点库存点]未填写");

            }
            if(!newStockPointVOMap.containsKey(transportSectionDTO.getDestinStockPointId())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + transportSectionDTO.getRowIndex() + ";[终点库存点]不存在");
                dataImportInfo.setDisplayIndex(transportSectionDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                throw new BusinessException("行数：" + transportSectionDTO.getRowIndex() + ";[终点库存点]不存在");

            }
            if (Objects.isNull(transportSectionDTO.getTransportDate())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + transportSectionDTO.getRowIndex() + ";[运输时长]未填写");
                dataImportInfo.setDisplayIndex(transportSectionDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                throw new BusinessException("行数：" + transportSectionDTO.getRowIndex() + ";[运输时长]未填写");

            }
            if (isFieldEmpty(transportSectionDTO.getTransportType())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + transportSectionDTO.getRowIndex() + ";[运输方式]未填写");
                dataImportInfo.setDisplayIndex(transportSectionDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                throw new BusinessException("行数：" + transportSectionDTO.getRowIndex() + ";[运输方式]未填写");

            }
            if(!transportTypeMap.containsKey(transportSectionDTO.getTransportType())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + transportSectionDTO.getRowIndex() + ";[运输方式]不存在");
                dataImportInfo.setDisplayIndex(transportSectionDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                throw new BusinessException("行数：" + transportSectionDTO.getRowIndex() + ";[运输方式]不存在");

            }
        }
    }
    private boolean isFieldEmpty(String value) {
        return value == null || value.trim().isEmpty();
    }
}
