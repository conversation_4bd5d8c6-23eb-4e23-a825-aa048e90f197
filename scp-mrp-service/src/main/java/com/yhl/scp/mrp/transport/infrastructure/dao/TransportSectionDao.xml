<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.transport.infrastructure.dao.TransportSectionDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.transport.infrastructure.po.TransportSectionPO">
        <!--@Table mds_tra_transport_section-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="routing_id" jdbcType="VARCHAR" property="routingId"/>
        <result column="group_number" jdbcType="VARCHAR" property="groupNumber"/>
        <result column="origin_stock_point_id" jdbcType="VARCHAR" property="originStockPointId"/>
        <result column="destin_stock_point_id" jdbcType="VARCHAR" property="destinStockPointId"/>
        <result column="priority" jdbcType="INTEGER" property="priority"/>
        <result column="transport_type" jdbcType="VARCHAR" property="transportType"/>
        <result column="lead_time" jdbcType="INTEGER" property="leadTime"/>
        <result column="distance" jdbcType="VARCHAR" property="distance"/>
        <result column="transport_cost" jdbcType="VARCHAR" property="transportCost"/>
        <result column="transport_capacity" jdbcType="VARCHAR" property="transportCapacity"/>
        <result column="lot_size" jdbcType="VARCHAR" property="lotSize"/>
        <result column="max_lot_size" jdbcType="VARCHAR" property="maxLotSize"/>
        <result column="min_lot_size" jdbcType="VARCHAR" property="minLotSize"/>
        <result column="effective_time" jdbcType="TIMESTAMP" property="effectiveTime"/>
        <result column="expiry_time" jdbcType="TIMESTAMP" property="expiryTime"/>
        <result column="supply_id" jdbcType="VARCHAR" property="supplyId"/>
        <result column="transport_date" jdbcType="VARCHAR" property="transportDate"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="counting_unit_id" jdbcType="VARCHAR" property="countingUnitId"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mrp.transport.vo.TransportSectionVO">
        <!-- TODO -->
        <result column="routing_code" jdbcType="VARCHAR" property="routingCode"/>
        <result column="origin_stock_point_code" jdbcType="VARCHAR" property="originStockPointCode"/>
        <result column="origin_stock_point_name" jdbcType="VARCHAR" property="originStockPointName"/>
        <result column="destin_stock_point_code" jdbcType="VARCHAR" property="destinStockPointCode"/>
        <result column="destin_stock_point_name" jdbcType="VARCHAR" property="destinStockPointName"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,routing_id,group_number,origin_stock_point_id,destin_stock_point_id,priority
        ,transport_type,lead_time,distance,transport_cost,transport_capacity,lot_size
        ,max_lot_size,min_lot_size,effective_time,expiry_time,supply_id,transport_date
        ,remark,enabled,creator,create_time,modifier,modify_time,counting_unit_id

    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>,routing_code,origin_stock_point_code,origin_stock_point_name
        ,destin_stock_point_code,destin_stock_point_name
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.routingId != null and params.routingId != ''">
                and routing_id = #{params.routingId,jdbcType=VARCHAR}
            </if>
            <if test="params.groupNumber != null and params.groupNumber != ''">
                and group_number = #{params.groupNumber,jdbcType=VARCHAR}
            </if>
            <if test="params.originStockPointId != null and params.originStockPointId != ''">
                and origin_stock_point_id = #{params.originStockPointId,jdbcType=VARCHAR}
            </if>
            <if test="params.destinStockPointId != null and params.destinStockPointId != ''">
                and destin_stock_point_id = #{params.destinStockPointId,jdbcType=VARCHAR}
            </if>
            <if test="params.priority != null">
                and priority = #{params.priority,jdbcType=INTEGER}
            </if>
            <if test="params.transportType != null and params.transportType != ''">
                and transport_type = #{params.transportType,jdbcType=VARCHAR}
            </if>
            <if test="params.leadTime != null">
                and lead_time = #{params.leadTime,jdbcType=INTEGER}
            </if>
            <if test="params.distance != null">
                and distance = #{params.distance,jdbcType=VARCHAR}
            </if>
            <if test="params.transportCost != null">
                and transport_cost = #{params.transportCost,jdbcType=VARCHAR}
            </if>
            <if test="params.transportCapacity != null and params.transportCapacity != ''">
                and transport_capacity = #{params.transportCapacity,jdbcType=VARCHAR}
            </if>
            <if test="params.lotSize != null">
                and lot_size = #{params.lotSize,jdbcType=VARCHAR}
            </if>
            <if test="params.maxLotSize != null">
                and max_lot_size = #{params.maxLotSize,jdbcType=VARCHAR}
            </if>
            <if test="params.minLotSize != null">
                and min_lot_size = #{params.minLotSize,jdbcType=VARCHAR}
            </if>
            <if test="params.effectiveTime != null">
                and effective_time = #{params.effectiveTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.expiryTime != null">
                and expiry_time = #{params.expiryTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.supplyId != null and params.supplyId != ''">
                and supply_id = #{params.supplyId,jdbcType=VARCHAR}
            </if>
            <if test="params.transportDate != null">
                and transport_date = #{params.transportDate,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.countingUnitId != null and params.countingUnitId != ''">
                and counting_unit_id = #{params.countingUnitId,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_tra_transport_section
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_tra_transport_section
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mds_tra_transport_section
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_tra_transport_section
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.transport.infrastructure.dao.TransportSectionDao">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mds_tra_transport_section(
        id,
        routing_id,
        group_number,
        origin_stock_point_id,
        destin_stock_point_id,
        priority,
        transport_type,
        lead_time,
        distance,
        transport_cost,
        transport_capacity,
        lot_size,
        max_lot_size,
        min_lot_size,
        effective_time,
        expiry_time,
        supply_id,
        transport_date,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        counting_unit_id)
        values (
        #{id,jdbcType=VARCHAR},
        #{routingId,jdbcType=VARCHAR},
        #{groupNumber,jdbcType=VARCHAR},
        #{originStockPointId,jdbcType=VARCHAR},
        #{destinStockPointId,jdbcType=VARCHAR},
        #{priority,jdbcType=INTEGER},
        #{transportType,jdbcType=VARCHAR},
        #{leadTime,jdbcType=INTEGER},
        #{distance,jdbcType=VARCHAR},
        #{transportCost,jdbcType=VARCHAR},
        #{transportCapacity,jdbcType=VARCHAR},
        #{lotSize,jdbcType=VARCHAR},
        #{maxLotSize,jdbcType=VARCHAR},
        #{minLotSize,jdbcType=VARCHAR},
        #{effectiveTime,jdbcType=TIMESTAMP},
        #{expiryTime,jdbcType=TIMESTAMP},
        #{supplyId,jdbcType=VARCHAR},
        #{transportDate,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{countingUnitId,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mrp.transport.infrastructure.dao.TransportSectionDao">
        insert into mds_tra_transport_section(id,
                                              routing_id,
                                              group_number,
                                              origin_stock_point_id,
                                              destin_stock_point_id,
                                              priority,
                                              transport_type,
                                              lead_time,
                                              distance,
                                              transport_cost,
                                              transport_capacity,
                                              lot_size,
                                              max_lot_size,
                                              min_lot_size,
                                              effective_time,
                                              expiry_time,
                                              supply_id,
                                              transport_date,
                                              remark,
                                              enabled,
                                              creator,
                                              create_time,
                                              modifier,
                                              modify_time,
                                              counting_unit_id)
        values (#{id,jdbcType=VARCHAR},
                #{routingId,jdbcType=VARCHAR},
                #{groupNumber,jdbcType=VARCHAR},
                #{originStockPointId,jdbcType=VARCHAR},
                #{destinStockPointId,jdbcType=VARCHAR},
                #{priority,jdbcType=INTEGER},
                #{transportType,jdbcType=VARCHAR},
                #{leadTime,jdbcType=INTEGER},
                #{distance,jdbcType=VARCHAR},
                #{transportCost,jdbcType=VARCHAR},
                #{transportCapacity,jdbcType=VARCHAR},
                #{lotSize,jdbcType=VARCHAR},
                #{maxLotSize,jdbcType=VARCHAR},
                #{minLotSize,jdbcType=VARCHAR},
                #{effectiveTime,jdbcType=TIMESTAMP},
                #{expiryTime,jdbcType=TIMESTAMP},
                #{supplyId,jdbcType=VARCHAR},
                #{transportDate,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{countingUnitId,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mds_tra_transport_section(
        id,
        routing_id,
        group_number,
        origin_stock_point_id,
        destin_stock_point_id,
        priority,
        transport_type,
        lead_time,
        distance,
        transport_cost,
        transport_capacity,
        lot_size,
        max_lot_size,
        min_lot_size,
        effective_time,
        expiry_time,
        supply_id,
        transport_date,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        counting_unit_id)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.routingId,jdbcType=VARCHAR},
            #{entity.groupNumber,jdbcType=VARCHAR},
            #{entity.originStockPointId,jdbcType=VARCHAR},
            #{entity.destinStockPointId,jdbcType=VARCHAR},
            #{entity.priority,jdbcType=INTEGER},
            #{entity.transportType,jdbcType=VARCHAR},
            #{entity.leadTime,jdbcType=INTEGER},
            #{entity.distance,jdbcType=VARCHAR},
            #{entity.transportCost,jdbcType=VARCHAR},
            #{entity.transportCapacity,jdbcType=VARCHAR},
            #{entity.lotSize,jdbcType=VARCHAR},
            #{entity.maxLotSize,jdbcType=VARCHAR},
            #{entity.minLotSize,jdbcType=VARCHAR},
            #{entity.effectiveTime,jdbcType=TIMESTAMP},
            #{entity.expiryTime,jdbcType=TIMESTAMP},
            #{entity.supplyId,jdbcType=VARCHAR},
            #{entity.transportDate,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.countingUnitId,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mds_tra_transport_section(
        id,
        routing_id,
        group_number,
        origin_stock_point_id,
        destin_stock_point_id,
        priority,
        transport_type,
        lead_time,
        distance,
        transport_cost,
        transport_capacity,
        lot_size,
        max_lot_size,
        min_lot_size,
        effective_time,
        expiry_time,
        supply_id,
        transport_date,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        counting_unit_id)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.routingId,jdbcType=VARCHAR},
            #{entity.groupNumber,jdbcType=VARCHAR},
            #{entity.originStockPointId,jdbcType=VARCHAR},
            #{entity.destinStockPointId,jdbcType=VARCHAR},
            #{entity.priority,jdbcType=INTEGER},
            #{entity.transportType,jdbcType=VARCHAR},
            #{entity.leadTime,jdbcType=INTEGER},
            #{entity.distance,jdbcType=VARCHAR},
            #{entity.transportCost,jdbcType=VARCHAR},
            #{entity.transportCapacity,jdbcType=VARCHAR},
            #{entity.lotSize,jdbcType=VARCHAR},
            #{entity.maxLotSize,jdbcType=VARCHAR},
            #{entity.minLotSize,jdbcType=VARCHAR},
            #{entity.effectiveTime,jdbcType=TIMESTAMP},
            #{entity.expiryTime,jdbcType=TIMESTAMP},
            #{entity.supplyId,jdbcType=VARCHAR},
            #{entity.transportDate,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.countingUnitId,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.transport.infrastructure.dao.TransportSectionDao">
        update mds_tra_transport_section
        set routing_id            = #{routingId,jdbcType=VARCHAR},
            group_number          = #{groupNumber,jdbcType=VARCHAR},
            origin_stock_point_id = #{originStockPointId,jdbcType=VARCHAR},
            destin_stock_point_id = #{destinStockPointId,jdbcType=VARCHAR},
            priority              = #{priority,jdbcType=INTEGER},
            transport_type        = #{transportType,jdbcType=VARCHAR},
            lead_time             = #{leadTime,jdbcType=INTEGER},
            distance              = #{distance,jdbcType=VARCHAR},
            transport_cost        = #{transportCost,jdbcType=VARCHAR},
            transport_capacity    = #{transportCapacity,jdbcType=VARCHAR},
            lot_size              = #{lotSize,jdbcType=VARCHAR},
            max_lot_size          = #{maxLotSize,jdbcType=VARCHAR},
            min_lot_size          = #{minLotSize,jdbcType=VARCHAR},
            effective_time        = #{effectiveTime,jdbcType=TIMESTAMP},
            expiry_time           = #{expiryTime,jdbcType=TIMESTAMP},
            supply_id             = #{supplyId,jdbcType=VARCHAR},
            transport_date        = #{transportDate,jdbcType=VARCHAR},
            remark                = #{remark,jdbcType=VARCHAR},
            enabled               = #{enabled,jdbcType=VARCHAR},
            modifier              = #{modifier,jdbcType=VARCHAR},
            modify_time           = #{modifyTime,jdbcType=TIMESTAMP},
            counting_unit_id      = #{countingUnitId,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mrp.transport.infrastructure.dao.TransportSectionDao">
        update mds_tra_transport_section
        <set>
            <if test="item.routingId != null and item.routingId != ''">
                routing_id = #{item.routingId,jdbcType=VARCHAR},
            </if>
            <if test="item.groupNumber != null and item.groupNumber != ''">
                group_number = #{item.groupNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.originStockPointId != null and item.originStockPointId != ''">
                origin_stock_point_id = #{item.originStockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.destinStockPointId != null and item.destinStockPointId != ''">
                destin_stock_point_id = #{item.destinStockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.priority != null">
                priority = #{item.priority,jdbcType=INTEGER},
            </if>
            <if test="item.transportType != null and item.transportType != ''">
                transport_type = #{item.transportType,jdbcType=VARCHAR},
            </if>
            <if test="item.leadTime != null">
                lead_time = #{item.leadTime,jdbcType=INTEGER},
            </if>
            <if test="item.distance != null">
                distance = #{item.distance,jdbcType=VARCHAR},
            </if>
            <if test="item.transportCost != null">
                transport_cost = #{item.transportCost,jdbcType=VARCHAR},
            </if>
            <if test="item.transportCapacity != null and item.transportCapacity != ''">
                transport_capacity = #{item.transportCapacity,jdbcType=VARCHAR},
            </if>
            <if test="item.lotSize != null">
                lot_size = #{item.lotSize,jdbcType=VARCHAR},
            </if>
            <if test="item.maxLotSize != null">
                max_lot_size = #{item.maxLotSize,jdbcType=VARCHAR},
            </if>
            <if test="item.minLotSize != null">
                min_lot_size = #{item.minLotSize,jdbcType=VARCHAR},
            </if>
            <if test="item.effectiveTime != null">
                effective_time = #{item.effectiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.expiryTime != null">
                expiry_time = #{item.expiryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.supplyId != null and item.supplyId != ''">
                supply_id = #{item.supplyId,jdbcType=VARCHAR},
            </if>
            <if test="item.transportDate != null">
                transport_date = #{item.transportDate,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.countingUnitId != null and item.countingUnitId != ''">
                counting_unit_id = #{item.countingUnitId,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mds_tra_transport_section
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="routing_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.routingId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="group_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.groupNumber,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="origin_stock_point_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.originStockPointId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="destin_stock_point_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.destinStockPointId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="priority = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.priority,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="transport_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transportType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="lead_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.leadTime,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="distance = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.distance,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transport_cost = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transportCost,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transport_capacity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transportCapacity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="lot_size = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lotSize,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="max_lot_size = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.maxLotSize,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="min_lot_size = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.minLotSize,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="effective_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.effectiveTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="expiry_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.expiryTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="supply_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplyId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transport_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transportDate,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="counting_unit_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.countingUnitId,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mds_tra_transport_section
            <set>
                <if test="item.routingId != null and item.routingId != ''">
                    routing_id = #{item.routingId,jdbcType=VARCHAR},
                </if>
                <if test="item.groupNumber != null and item.groupNumber != ''">
                    group_number = #{item.groupNumber,jdbcType=VARCHAR},
                </if>
                <if test="item.originStockPointId != null and item.originStockPointId != ''">
                    origin_stock_point_id = #{item.originStockPointId,jdbcType=VARCHAR},
                </if>
                <if test="item.destinStockPointId != null and item.destinStockPointId != ''">
                    destin_stock_point_id = #{item.destinStockPointId,jdbcType=VARCHAR},
                </if>
                <if test="item.priority != null">
                    priority = #{item.priority,jdbcType=INTEGER},
                </if>
                <if test="item.transportType != null and item.transportType != ''">
                    transport_type = #{item.transportType,jdbcType=VARCHAR},
                </if>
                <if test="item.leadTime != null">
                    lead_time = #{item.leadTime,jdbcType=INTEGER},
                </if>
                <if test="item.distance != null">
                    distance = #{item.distance,jdbcType=VARCHAR},
                </if>
                <if test="item.transportCost != null">
                    transport_cost = #{item.transportCost,jdbcType=VARCHAR},
                </if>
                <if test="item.transportCapacity != null and item.transportCapacity != ''">
                    transport_capacity = #{item.transportCapacity,jdbcType=VARCHAR},
                </if>
                <if test="item.lotSize != null">
                    lot_size = #{item.lotSize,jdbcType=VARCHAR},
                </if>
                <if test="item.maxLotSize != null">
                    max_lot_size = #{item.maxLotSize,jdbcType=VARCHAR},
                </if>
                <if test="item.minLotSize != null">
                    min_lot_size = #{item.minLotSize,jdbcType=VARCHAR},
                </if>
                <if test="item.effectiveTime != null">
                    effective_time = #{item.effectiveTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.expiryTime != null">
                    expiry_time = #{item.expiryTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.supplyId != null and item.supplyId != ''">
                    supply_id = #{item.supplyId,jdbcType=VARCHAR},
                </if>
                <if test="item.transportDate != null">
                    transport_date = #{item.transportDate,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.countingUnitId != null and item.countingUnitId != ''">
                    counting_unit_id = #{item.countingUnitId,jdbcType=VARCHAR},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mds_tra_transport_section
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mds_tra_transport_section where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <delete id="deleteBatchByRoutingId" parameterType="java.util.List">
        delete from mds_tra_transport_section where routing_id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
