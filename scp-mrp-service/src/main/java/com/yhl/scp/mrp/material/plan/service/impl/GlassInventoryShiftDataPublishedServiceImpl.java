package com.yhl.scp.mrp.material.plan.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mrp.inventory.dto.GlassInventoryShiftQueryParamDTO;
import com.yhl.scp.mrp.material.plan.convertor.GlassInventoryShiftDataPublishedConvertor;
import com.yhl.scp.mrp.material.plan.domain.entity.GlassInventoryShiftDataPublishedDO;
import com.yhl.scp.mrp.material.plan.domain.service.GlassInventoryShiftDataPublishedDomainService;
import com.yhl.scp.mrp.material.plan.dto.GlassInventoryShiftDataPublishedDTO;
import com.yhl.scp.mrp.material.plan.infrastructure.dao.GlassInventoryShiftDataPublishedDao;
import com.yhl.scp.mrp.material.plan.infrastructure.dao.GlassInventoryShiftDetailPublishedDao;
import com.yhl.scp.mrp.material.plan.infrastructure.po.GlassInventoryShiftDataPublishedPO;
import com.yhl.scp.mrp.material.plan.service.GlassInventoryShiftDataPublishedService;
import com.yhl.scp.mrp.material.plan.service.utils.GlassMrpUtil;
import com.yhl.scp.mrp.material.plan.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>GlassInventoryShiftDataPublishedServiceImpl</code>
 * <p>
 * 物料库存推移应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-02 23:09:03
 */
@Slf4j
@Service
public class GlassInventoryShiftDataPublishedServiceImpl extends AbstractService implements GlassInventoryShiftDataPublishedService {

    @Resource
    private GlassInventoryShiftDataPublishedDao glassInventoryShiftDataPublishedDao;

    @Resource
    private GlassInventoryShiftDetailPublishedDao glassInventoryShiftDetailPublishedDao;

    @Resource
    private GlassInventoryShiftDataPublishedDomainService glassInventoryShiftDataPublishedDomainService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    public BaseResponse<Void> doCreate(GlassInventoryShiftDataPublishedDTO glassInventoryShiftDataPublishedDTO) {
        // 0.数据转换
        GlassInventoryShiftDataPublishedDO glassInventoryShiftDataPublishedDO = GlassInventoryShiftDataPublishedConvertor.INSTANCE.dto2Do(glassInventoryShiftDataPublishedDTO);
        GlassInventoryShiftDataPublishedPO glassInventoryShiftDataPublishedPO = GlassInventoryShiftDataPublishedConvertor.INSTANCE.dto2Po(glassInventoryShiftDataPublishedDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        glassInventoryShiftDataPublishedDomainService.validation(glassInventoryShiftDataPublishedDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(glassInventoryShiftDataPublishedPO);
        glassInventoryShiftDataPublishedDao.insertWithPrimaryKey(glassInventoryShiftDataPublishedPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(GlassInventoryShiftDataPublishedDTO glassInventoryShiftDataPublishedDTO) {
        // 0.数据转换
        GlassInventoryShiftDataPublishedDO glassInventoryShiftDataPublishedDO = GlassInventoryShiftDataPublishedConvertor.INSTANCE.dto2Do(glassInventoryShiftDataPublishedDTO);
        GlassInventoryShiftDataPublishedPO glassInventoryShiftDataPublishedPO = GlassInventoryShiftDataPublishedConvertor.INSTANCE.dto2Po(glassInventoryShiftDataPublishedDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        glassInventoryShiftDataPublishedDomainService.validation(glassInventoryShiftDataPublishedDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(glassInventoryShiftDataPublishedPO);
        glassInventoryShiftDataPublishedDao.update(glassInventoryShiftDataPublishedPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<GlassInventoryShiftDataPublishedDTO> list) {
        List<GlassInventoryShiftDataPublishedPO> newList = GlassInventoryShiftDataPublishedConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        glassInventoryShiftDataPublishedDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<GlassInventoryShiftDataPublishedDTO> list) {
        List<GlassInventoryShiftDataPublishedPO> newList = GlassInventoryShiftDataPublishedConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        glassInventoryShiftDataPublishedDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return glassInventoryShiftDataPublishedDao.deleteBatch(idList);
        }
        return glassInventoryShiftDataPublishedDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public GlassInventoryShiftDataPublishedVO selectByPrimaryKey(String id) {
        GlassInventoryShiftDataPublishedPO po = glassInventoryShiftDataPublishedDao.selectByPrimaryKey(id);
        return GlassInventoryShiftDataPublishedConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mrp_glass_inventory_shift_data_published")
    public List<GlassInventoryShiftDataPublishedVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mrp_glass_inventory_shift_data_published")
    public List<GlassInventoryShiftDataPublishedVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<GlassInventoryShiftDataPublishedVO> dataList = glassInventoryShiftDataPublishedDao.selectByCondition(sortParam, queryCriteriaParam);
        GlassInventoryShiftDataPublishedServiceImpl target = SpringBeanUtils.getBean(GlassInventoryShiftDataPublishedServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<GlassInventoryShiftDataPublishedVO> selectByParams(Map<String, Object> params) {
        List<GlassInventoryShiftDataPublishedPO> list = glassInventoryShiftDataPublishedDao.selectByParams(params);
        return GlassInventoryShiftDataPublishedConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<GlassInventoryShiftDataPublishedVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public PageInfo<GlassInventoryShiftPublishedPageVO> pageCustom(GlassInventoryShiftQueryParamDTO dto) {
        List<String> dynamicColumnParam = Lists.newArrayList("product_code");
        Map<String, Object> queryParams = new HashMap<>();
        //            queryParams.put("materialPlanner", SystemHolder.getUserId());
        queryParams.put("materialPlanner", "8d62ab0f-018d-72b118fe-8a87cdd8-0029");
        FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder()
                .dynamicColumnParam(dynamicColumnParam)
                .queryParam(queryParams).build();

        List<NewProductStockPointVO> newProductStockPointVOList = newMdsFeign.selectProductListByParamOnDynamicColumns(SystemHolder.getScenario(), feignDynamicParam);
        if (com.yhl.platform.common.utils.CollectionUtils.isEmpty(newProductStockPointVOList)){
            throw new BusinessException("用户没有物料权限");
        }
        List<String> productCodeList = newProductStockPointVOList.stream().map(NewProductStockPointVO::getProductCode)
                .distinct().collect(Collectors.toList());
        if (StringUtils.isNotEmpty(dto.getProductCode()) && !productCodeList.contains(dto.getProductCode())) {
            throw new BusinessException("用户该物料权限");
        }
        Map<String, Object> params = new HashMap<>();
        params.put("materialPlanPublishedVersionId", dto.getMaterialPlanPublishedVersionId());
        if (StringUtils.isNotEmpty(dto.getProductCode())) {
            if (dto.getProductCode().contains("*")) {
                params.put("productCode", dto.getProductCode());
            } else {
                List<String> ypProductCodes = newMdsFeign.getYpProductCodes(SystemHolder.getScenario());
                Map<String, List<String>> substitutionProductGroup = GlassMrpUtil.getMixProductCodeGroup(ypProductCodes);
                params.put("productCode", GlassMrpUtil.getNewProductCode(dto.getProductCode(), substitutionProductGroup));
            }

        }
        params.put("stockPointType", dto.getStockPointType());
        params.put("productColor", dto.getProductColor());
        params.put("productThickness", dto.getProductThickness());
        params.put("warningRemindStart", dto.getWarningRemindStart());
        params.put("warningRemindEnd", dto.getWarningRemindEnd());

        dto.setPageSize(10);
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<GlassInventoryShiftDataPublishedVO> glassInventoryShiftDataPublishedVOList = 
                glassInventoryShiftDataPublishedDao.selectVOByParams(params);
        PageInfo<GlassInventoryShiftDataPublishedVO> glassInventoryShiftDataPublishedVOPageInfo = new PageInfo<>(glassInventoryShiftDataPublishedVOList);
        Map<String, GlassInventoryShiftDataPublishedVO> glassInventoryShiftDataPublishedVOMap = glassInventoryShiftDataPublishedVOList.stream()
                .collect(Collectors.toMap(GlassInventoryShiftDataPublishedVO::getId, Function.identity()));

        List<String> inventoryShiftDataIds = glassInventoryShiftDataPublishedVOList.stream()
                .map(BaseVO::getId)
                .collect(Collectors.toList());
        Map<String,List<GlassInventoryShiftDetailPublishedVO>> glassInventoryShiftDetailGroup = glassInventoryShiftDetailPublishedDao
                .selectVOByParams(ImmutableMap.of("inventoryShiftDataIds", inventoryShiftDataIds))
                .stream().collect(Collectors.groupingBy(GlassInventoryShiftDetailPublishedVO::getInventoryShiftDataId));
        List<GlassInventoryShiftPublishedPageVO> glassInventoryShiftPageVOList = new ArrayList<>();


        for (List<GlassInventoryShiftDetailPublishedVO> glassInventoryShiftDetailVOList : glassInventoryShiftDetailGroup.values()) {
            Map<String, List<GlassInventoryShiftDetailPublishedVO>> collect = glassInventoryShiftDetailVOList.stream()
                    .collect(Collectors.groupingBy(t -> String.join("&&", t.getStockPointCode(), t.getStockPointType())));
            List<String> stockPointCodes = glassInventoryShiftDetailVOList.stream()
                    .map(GlassInventoryShiftDetailPublishedVO::getStockPointCode)
                    .distinct()
                    .collect(Collectors.toList());
            for (String stockPointType : Lists.newArrayList("BC", "MT", "BCMT", "FF")) {
                for (String stockPointCode : stockPointCodes) {
                    String key = String.join("&&", stockPointCode, stockPointType);
                    List<GlassInventoryShiftDetailPublishedVO> glassInventoryShiftDetailVOS = collect.get(key);
                    if (CollectionUtils.isEmpty(glassInventoryShiftDetailVOS)) {
                        continue;
                    }
                    String inventoryShiftDataId = glassInventoryShiftDetailVOS.get(0).getInventoryShiftDataId();
                    GlassInventoryShiftPublishedPageVO glassInventoryShiftPublishedPageVO = new GlassInventoryShiftPublishedPageVO();
                    glassInventoryShiftPublishedPageVO.setProductCode(glassInventoryShiftDataPublishedVOMap.get(inventoryShiftDataId).getProductCode());
                    glassInventoryShiftPublishedPageVO.setProductFactoryCode(glassInventoryShiftDataPublishedVOMap.get(inventoryShiftDataId).getProductFactoryCode());
                    glassInventoryShiftPublishedPageVO.setProductThickness(glassInventoryShiftDataPublishedVOMap.get(inventoryShiftDataId).getProductThickness());
                    glassInventoryShiftPublishedPageVO.setProductColor(glassInventoryShiftDataPublishedVOMap.get(inventoryShiftDataId).getProductColor());
                    glassInventoryShiftPublishedPageVO.setVehicleModelCode(glassInventoryShiftDataPublishedVOMap.get(inventoryShiftDataId).getVehicleModelCode());
                    glassInventoryShiftPublishedPageVO.setStockPointCode(glassInventoryShiftDetailVOS.get(0).getStockPointCode());
                    glassInventoryShiftPublishedPageVO.setStockPointType(stockPointType);

                    Map<Date, GlassInventoryShiftDetailPublishedVO> inventoryDateMap = glassInventoryShiftDetailVOS.stream()
                            .filter(data -> null != data.getInventoryDate())
                            .collect(Collectors.toMap(data -> DateUtils.formatDate(data.getInventoryDate(), "yyyy-MM-dd"), Function.identity()));
                    List<GlassInventoryShiftDetailPublishedVO> list = new ArrayList<>();
                    list.addAll(getDayMaterialPlanInventoryShiftList(inventoryDateMap, dto.getStartDate()));
                    list.addAll(getWeekMaterialPlanInventoryShiftList(glassInventoryShiftDetailVOS, list, dto.getEndDate()));

                    glassInventoryShiftPublishedPageVO.setGlassInventoryShiftDetailList(list);
                    glassInventoryShiftPageVOList.add(glassInventoryShiftPublishedPageVO);

                }
            }
        }

        PageInfo<GlassInventoryShiftPublishedPageVO> glassInventoryShiftPublishedPageVOPageInfo = new PageInfo<>(glassInventoryShiftPageVOList);
        glassInventoryShiftPublishedPageVOPageInfo.setTotal(glassInventoryShiftDataPublishedVOPageInfo.getTotal());
        glassInventoryShiftPublishedPageVOPageInfo.setPageSize(glassInventoryShiftDataPublishedVOPageInfo.getPageSize());
        return glassInventoryShiftPublishedPageVOPageInfo;
    }

    private List<GlassInventoryShiftDetailPublishedVO> getWeekMaterialPlanInventoryShiftList(List<GlassInventoryShiftDetailPublishedVO> inventoryShiftList,
                                                                                    List<GlassInventoryShiftDetailPublishedVO> list,
                                                                                    Date customEndDate) {

        List<GlassInventoryShiftDetailPublishedVO> result = new ArrayList<>();

        // 获取推移天后的最后一个日期
        Date inventoryDate = list.get(list.size() - 1).getInventoryDate();
        Date customStartDate = DateUtils.moveCalendar(inventoryDate, Calendar.DATE, 1);

        // 获取周维度
        List<String> weekRangeGroups = getWeekRangeGroups(customStartDate, customEndDate);
        for (String weekRangeGroup : weekRangeGroups) {
            // 解析 weekRangeGroup 获取开始日期和结束日期
            String[] dates = weekRangeGroup.split("~");
            Date startDate = DateUtils.stringToDate(dates[0]);
            Date endDate = DateUtils.stringToDate(dates[1]);

            // 汇总推移
            GlassInventoryShiftDetailPublishedVO vo = summarizeInventoryShift(inventoryShiftList, startDate, endDate);
            vo.setInventoryDateDimension(weekRangeGroup);
            result.add(vo);
        }

        return result;
    }
    public static GlassInventoryShiftDetailPublishedVO summarizeInventoryShift(List<GlassInventoryShiftDetailPublishedVO> inventoryShiftList,
                                                                      Date startDate,
                                                                      Date endDate) {
        if (inventoryShiftList == null || inventoryShiftList.isEmpty()) {
            return new GlassInventoryShiftDetailPublishedVO();
        }

        List<GlassInventoryShiftDetailPublishedVO> filteredList = inventoryShiftList.stream()
                .filter(data -> data.getInventoryDate() != null)
                .filter(data -> !data.getInventoryDate().before(startDate) && !data.getInventoryDate().after(endDate))
                .sorted(Comparator.comparing(GlassInventoryShiftDetailPublishedVO::getInventoryDate))
                .collect(Collectors.toList());

        if (filteredList.isEmpty()) {
            return new GlassInventoryShiftDetailPublishedVO();
        }

        GlassInventoryShiftDetailPublishedVO result = new GlassInventoryShiftDetailPublishedVO();
        result.setId(filteredList.get(0).getId());
        result.setOpeningInventory(filteredList.get(0).getOpeningInventory());
        result.setEndingInventory(filteredList.get(filteredList.size() - 1).getEndingInventory());

        // 计算安全库存平均值
        result.setSafetyStockLevelMin(calculateAverage(filteredList, GlassInventoryShiftDetailPublishedVO::getSafetyStockLevelMin));
        result.setSafetyStockLevelStandard(calculateAverage(filteredList, GlassInventoryShiftDetailPublishedVO::getSafetyStockLevelStandard));
        result.setSafetyStockLevelMax(calculateAverage(filteredList, GlassInventoryShiftDetailPublishedVO::getSafetyStockLevelMax));

        // 累加其他属性
        result.setDemandQuantity(sumBigDecimals(filteredList, GlassInventoryShiftDetailPublishedVO::getDemandQuantity));
        result.setUsedAsReplaceQuantity(sumBigDecimals(filteredList, GlassInventoryShiftDetailPublishedVO::getUsedAsReplaceQuantity));
        result.setUseReplaceQuantity(sumBigDecimals(filteredList, GlassInventoryShiftDetailPublishedVO::getUseReplaceQuantity));
        result.setAdjustQuantityFromPort(sumBigDecimals(filteredList, GlassInventoryShiftDetailPublishedVO::getAdjustQuantityFromPort));
        result.setInputQuantity(sumBigDecimals(filteredList, GlassInventoryShiftDetailPublishedVO::getInputQuantity));
        result.setOutputQuantityToBc(sumBigDecimals(filteredList, GlassInventoryShiftDetailPublishedVO::getOutputQuantityToBc));
        result.setTransitQuantityFromPort(sumBigDecimals(filteredList, GlassInventoryShiftDetailPublishedVO::getTransitQuantityFromPort));
        result.setSafetyStockGap(sumBigDecimals(filteredList, GlassInventoryShiftDetailPublishedVO::getSafetyStockGap));
        result.setAdjustQuantityFromFloat(sumBigDecimals(filteredList, GlassInventoryShiftDetailPublishedVO::getAdjustQuantityFromFloat));
        result.setDecisionOutputQuantityToBc(sumBigDecimals(filteredList, GlassInventoryShiftDetailPublishedVO::getDecisionOutputQuantityToBc));
        result.setDecisionOutputQuantityToPort(sumBigDecimals(filteredList, GlassInventoryShiftDetailPublishedVO::getDecisionOutputQuantityToPort));
        result.setTransitQuantityFromFloat(sumBigDecimals(filteredList, GlassInventoryShiftDetailPublishedVO::getTransitQuantityFromFloat));
        result.setOutputQuantityToPort(sumBigDecimals(filteredList, GlassInventoryShiftDetailPublishedVO::getOutputQuantityToPort));
        return result;
    }

    private static BigDecimal sumBigDecimals(List<GlassInventoryShiftDetailPublishedVO> list, Function<GlassInventoryShiftDetailPublishedVO, BigDecimal> mapper) {
        return list.stream()
                .map(mapper)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private static BigDecimal calculateAverage(List<GlassInventoryShiftDetailPublishedVO> list,
                                               Function<GlassInventoryShiftDetailPublishedVO, BigDecimal> mapper) {
        // 检查列表是否为空，如果为空则无需计算，直接返回 0
        if (list.isEmpty()) {
            return BigDecimal.ZERO;
        }
        BigDecimal sum = list.stream()
                .map(mapper)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        // 将累加的总和除以列表的大小，得到平均值
        // 使用 divide 方法时，指定保留两位小数，并采用四舍五入的方式进行处理
        return sum.divide(BigDecimal.valueOf(list.size()), 2, RoundingMode.HALF_UP);
    }

    public static List<String> getWeekRangeGroups(Date customStartDate, Date customEndDate) {
        List<String> result = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();

        // 处理开始日期
        Date startDate = (customStartDate != null) ? customStartDate : calendar.getTime();

        // 处理结束日期
        if (customEndDate == null) {
            calendar.setTime(startDate);
            calendar.add(Calendar.YEAR, 1);
        }
        Date endDate = (customEndDate != null) ? customEndDate : calendar.getTime();

        Calendar currentGroupStart = Calendar.getInstance();
        currentGroupStart.setTime(startDate);
        Calendar currentGroupEnd = Calendar.getInstance();

        while (currentGroupStart.getTime().before(endDate)) {
            currentGroupEnd.setTime(currentGroupStart.getTime());
            // 计算每组的结束日期，加 6 天保证每组 7 天
            currentGroupEnd.add(Calendar.DAY_OF_MONTH, 6);

            if (currentGroupEnd.getTime().after(endDate)) {
                currentGroupEnd.setTime(endDate);
            }

            result.add(sdf.format(currentGroupStart.getTime()) + "~" + sdf.format(currentGroupEnd.getTime()));

            // 移动到下一组的开始日期
            currentGroupStart.setTime(currentGroupEnd.getTime());
            currentGroupStart.add(Calendar.DAY_OF_MONTH, 1);
        }

        return result;
    }


    private List<GlassInventoryShiftDetailPublishedVO> getDayMaterialPlanInventoryShiftList(Map<Date, GlassInventoryShiftDetailPublishedVO> inventoryDateMap,
                                                                                   Date startDate) {
        List<GlassInventoryShiftDetailPublishedVO> result = new ArrayList<>();
        // 默认当前日期
        if (startDate == null) {
            startDate = new Date();
        }

        // 获取所需筛选时间
        List<Date> nextNDaysList = DateUtils.getIntervalDates(startDate, DateUtils.moveDay(startDate, 29));

        // 遍历接下来的日期，处理库存数据
        for (Date date : nextNDaysList) {
            GlassInventoryShiftDetailPublishedVO vo = new GlassInventoryShiftDetailPublishedVO();
            GlassInventoryShiftDetailPublishedVO materialPlanInventoryShiftVO = inventoryDateMap.get(date);
            if (Objects.nonNull(materialPlanInventoryShiftVO)){
                vo = materialPlanInventoryShiftVO;
            }

            vo.setInventoryDate(date);
            vo.setInventoryDateDimension(DateUtils.dateToString(date));
            result.add(vo);
        }
        return result;
    }
    

    @Override
    public String getObjectType() {
        return null /*ObjectTypeEnum.GLASS_INVENTORY_SHIFT_DATA_PUBLISHED.getCode()*/;
    }

    @Override
    public List<GlassInventoryShiftDataPublishedVO> invocation(List<GlassInventoryShiftDataPublishedVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
