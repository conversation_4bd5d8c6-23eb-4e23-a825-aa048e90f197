package com.yhl.scp.mrp.material.plan.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mrp.enums.TransferStatusEnum;
import com.yhl.scp.mrp.inventory.dto.InventoryFloatGlassShippedDetailDTO;
import com.yhl.scp.mrp.inventory.dto.InventoryQuayDetailDTO;
import com.yhl.scp.mrp.inventory.service.InventoryFloatGlassDetailService;
import com.yhl.scp.mrp.inventory.service.InventoryFloatGlassShippedDetailService;
import com.yhl.scp.mrp.inventory.service.InventoryOceanFreightService;
import com.yhl.scp.mrp.inventory.service.InventoryQuayDetailService;
import com.yhl.scp.mrp.inventory.vo.InventoryFloatGlassShippedDetailVO;
import com.yhl.scp.mrp.inventory.vo.InventoryQuayDetailVO;
import com.yhl.scp.mrp.material.plan.convertor.MaterialPlanTransferConvertor;
import com.yhl.scp.mrp.material.plan.domain.entity.MaterialPlanTransferDO;
import com.yhl.scp.mrp.material.plan.domain.service.MaterialPlanTransferDomainService;
import com.yhl.scp.mrp.material.plan.dto.MaterialPlanTransferDTO;
import com.yhl.scp.mrp.material.plan.dto.MaterialPlanTransferInventoryDetailDTO;
import com.yhl.scp.mrp.material.plan.infrastructure.dao.MaterialPlanTransferDao;
import com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanTransferPO;
import com.yhl.scp.mrp.material.plan.service.MaterialPlanTransferInventoryDetailService;
import com.yhl.scp.mrp.material.plan.service.MaterialPlanTransferService;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanTransferVO;
import com.yhl.scp.mrp.published.service.MaterialPlanInventoryOccupyPublishedService;
import com.yhl.scp.mrp.published.vo.MaterialPlanInventoryOccupyPublishedVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>MaterialPlanTransferServiceImpl</code>
 * <p>
 * 调拨计划应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-31 21:15:16
 */
@Slf4j
@Service
public class MaterialPlanTransferServiceImpl extends AbstractService implements MaterialPlanTransferService {

    @Resource
    private MaterialPlanTransferDao materialPlanTransferDao;

    @Resource
    private MaterialPlanTransferDomainService materialPlanTransferDomainService;

    @Resource
    private InventoryQuayDetailService inventoryQuayDetailService;

    @Resource
    private InventoryFloatGlassDetailService inventoryFloatGlassDetailService;

    @Resource
    private InventoryFloatGlassShippedDetailService inventoryFloatGlassShippedDetailService;


    @Resource
    private MaterialPlanTransferInventoryDetailService materialPlanTransferInventoryDetailService;

    @Resource
    private MaterialPlanInventoryOccupyPublishedService materialPlanInventoryOccupyPublishedService;

    @Resource
    private InventoryOceanFreightService inventoryOceanFreightService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    public BaseResponse<Void> doCreate(MaterialPlanTransferDTO materialPlanTransferDTO) {
        // 0.数据转换
        MaterialPlanTransferDO materialPlanTransferDO = MaterialPlanTransferConvertor.INSTANCE.dto2Do(materialPlanTransferDTO);
        MaterialPlanTransferPO materialPlanTransferPO = MaterialPlanTransferConvertor.INSTANCE.dto2Po(materialPlanTransferDTO);
        // 1.数据校验
        materialPlanTransferDomainService.validation(materialPlanTransferDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(materialPlanTransferPO);
        materialPlanTransferDao.insertWithPrimaryKey(materialPlanTransferPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(MaterialPlanTransferDTO materialPlanTransferDTO) {
        // 0.数据转换
        MaterialPlanTransferDO materialPlanTransferDO = MaterialPlanTransferConvertor.INSTANCE.dto2Do(materialPlanTransferDTO);
        MaterialPlanTransferPO materialPlanTransferPO = MaterialPlanTransferConvertor.INSTANCE.dto2Po(materialPlanTransferDTO);
        // 1.数据校验
        materialPlanTransferDomainService.validation(materialPlanTransferDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(materialPlanTransferPO);
        materialPlanTransferDao.update(materialPlanTransferPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MaterialPlanTransferDTO> list) {
        List<MaterialPlanTransferPO> newList = MaterialPlanTransferConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        materialPlanTransferDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doCreateBatch02(List<MaterialPlanTransferDTO> list) {
        List<MaterialPlanTransferPO> newList = MaterialPlanTransferConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        materialPlanTransferDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<MaterialPlanTransferDTO> list) {
        List<MaterialPlanTransferPO> newList = MaterialPlanTransferConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialPlanTransferDao.updateBatch(newList);
    }

    @Override
    public void doUpdateBatchSelective(List<MaterialPlanTransferDTO> list) {
        List<MaterialPlanTransferPO> newList = MaterialPlanTransferConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialPlanTransferDao.updateBatchSelective(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return materialPlanTransferDao.deleteBatch(idList);
        }
        return materialPlanTransferDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MaterialPlanTransferVO selectByPrimaryKey(String id) {
        MaterialPlanTransferPO po = materialPlanTransferDao.selectByPrimaryKey(id);
        return MaterialPlanTransferConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mrp_material_plan_transfer")
    public List<MaterialPlanTransferVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    public List<MaterialPlanTransferVO> pageMt(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return materialPlanTransferDao.selectMtByCondition(sortParam, queryCriteriaParam);
    }



    @Override
    @Expression(value = "v_mrp_material_plan_transfer")
    public List<MaterialPlanTransferVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MaterialPlanTransferVO> dataList = materialPlanTransferDao.selectByCondition(sortParam, queryCriteriaParam);
        MaterialPlanTransferServiceImpl target = SpringBeanUtils.getBean(MaterialPlanTransferServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MaterialPlanTransferVO> selectByParams(Map<String, Object> params) {
        List<MaterialPlanTransferPO> list = materialPlanTransferDao.selectByParams(params);
        return MaterialPlanTransferConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MaterialPlanTransferVO> selectVOByParams(Map<String, Object> params) {
        return materialPlanTransferDao.selectVOByParams(params);
    }

    @Override
    public List<LabelValue<String>> stockPointNameFromDropDown(String stockPointNameFrom) {
        Map<String, Object> params = new HashMap<>();
        if (StringUtils.isNotEmpty(stockPointNameFrom)) {
            params.put("stockPointNameFrom", stockPointNameFrom);
        }
        List<MaterialPlanTransferVO> materialPlanTransferVOList = this.selectVOByParams(params);
        return materialPlanTransferVOList.stream()
                .map(item -> new LabelValue<>(item.getStockPointNameFrom(), item.getStockPointCodeFrom()))
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public BaseResponse<Void> doCancelled(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)){
            throw new BusinessException("选择数据不能为空");
        }

        List<MaterialPlanTransferVO> list = this.selectVOByParams(ImmutableMap.of("ids", ids));

        // 校验未发布状态
        list.forEach(data -> {
            if (!data.getTransferStatus().equals(TransferStatusEnum.PUBLISHED.getCode())) {
                throw new BusinessException("已发布状态才允许取消");
            }
        });

        // 收集码头调拨
        List<MaterialPlanTransferVO> mtTransferList = list.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getStockPointTypeFrom()))
                .filter(data -> data.getStockPointTypeFrom().equals(StockPointTypeEnum.MT.getCode()))
                .collect(Collectors.toList());
        // 收集非码头调拨
        List<MaterialPlanTransferVO> noMtTransferList = list.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getStockPointTypeFrom()))
                .filter(data -> !data.getStockPointTypeFrom().equals(StockPointTypeEnum.MT.getCode()))
                .collect(Collectors.toList());

        // 处理取消（码头）的数据
        handleCancelledMtData(mtTransferList);

        // 处理取消（非码头）的数据
        handleCancelledNoMtData(noMtTransferList);

        // 删除对应调拨库存批次明细
        materialPlanTransferInventoryDetailService.deleteByMaterialPlanTransferId(ids);


//        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
//            @Override
//            public void afterCommit() {
//                // 需要提交之后执行的代码
//                log.info("原片MRP计算");
//            }
//        });

        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    /**
     * 处理 取消 调拨计划为库存点类型码头数据
     *
     * @param list                                      调拨计划
     */
    private void handleCancelledMtData(List<MaterialPlanTransferVO> list) {

        if (CollectionUtils.isEmpty(list)) return;

        // 收集调拨计划柜号
        List<String> cabinetNos = list.stream()
                .map(MaterialPlanTransferVO::getCabinetNo).filter(StringUtils::isNotEmpty).collect(Collectors.toList());

        // 根据柜号获取调拨计划
        List<MaterialPlanTransferVO> materialPlanTransferVOS = this.selectByParams(ImmutableMap.of("cabinetNos", cabinetNos));
        // 收集id用于修改调拨计划调拨状态
        List<String> idList = materialPlanTransferVOS.stream().map(BaseVO::getId).collect(Collectors.toList());

        // 获取码头库存明细并分组
        List<InventoryQuayDetailVO> inventoryDetails =
                inventoryQuayDetailService.selectByParams(ImmutableMap.of("containerNumberList", cabinetNos));
        // 修改码头库存批次明细的送柜时间
        List<InventoryQuayDetailDTO> updateInventoryQuayDetailDTOList = new ArrayList<>();
        for (InventoryQuayDetailVO vo : inventoryDetails) {
            InventoryQuayDetailDTO inventoryQuayDetailDTO = new InventoryQuayDetailDTO();
            BeanUtils.copyProperties(vo,inventoryQuayDetailDTO);
            inventoryQuayDetailDTO.setContainerDeliveryTime(null);
            updateInventoryQuayDetailDTOList.add(inventoryQuayDetailDTO);
        }
        if (CollectionUtils.isNotEmpty(updateInventoryQuayDetailDTOList)){
            inventoryQuayDetailService.doUpdateBatch(updateInventoryQuayDetailDTOList);
        }

        // 修改浮法已发运库存批次明细的送柜时间
        List<InventoryFloatGlassShippedDetailVO> inventoryFloatGlassShippedDetailVOList =
                inventoryFloatGlassShippedDetailService.selectByParams(ImmutableMap.of("lotNumbers", cabinetNos));
        List<InventoryFloatGlassShippedDetailDTO> updateInventoryFloatGlassShippedDetailDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(inventoryFloatGlassShippedDetailVOList)){
            for (InventoryFloatGlassShippedDetailVO vo : inventoryFloatGlassShippedDetailVOList) {
                InventoryFloatGlassShippedDetailDTO inventoryFloatGlassShippedDetailDTO = new InventoryFloatGlassShippedDetailDTO();
                BeanUtils.copyProperties(vo,inventoryFloatGlassShippedDetailDTO);
                inventoryFloatGlassShippedDetailDTO.setContainerDeliveryTime(null);
                updateInventoryFloatGlassShippedDetailDTOList.add(inventoryFloatGlassShippedDetailDTO);
            }
        }
        if (CollectionUtils.isNotEmpty(inventoryFloatGlassShippedDetailVOList)){
            inventoryFloatGlassShippedDetailService.doUpdateBatch(updateInventoryFloatGlassShippedDetailDTOList);
        }

        // 修改调拨计划状态为取消
        List<MaterialPlanTransferDTO> updateMaterialPlanTransferDTOList = new ArrayList<>();
        for (String id : idList) {
            MaterialPlanTransferDTO dto = MaterialPlanTransferDTO.builder()
                            .id(id)
                            .transferStatus(TransferStatusEnum.UN_PUBLISHED.getCode())
                            .transferredQuantity(BigDecimal.ZERO)
                            .build();
            updateMaterialPlanTransferDTOList.add(dto);
        }
        this.doUpdateBatchSelective(updateMaterialPlanTransferDTOList);
    }

    /**
     * 处理 取消 调拨计划为库存点类型非码头数据
     *
     * @param list 调拨计划
     */
    private void handleCancelledNoMtData(List<MaterialPlanTransferVO> list) {

        if (CollectionUtils.isEmpty(list)) return;

        // 收集id，用于修改调拨计划发布状态
        List<String> idList = list.stream().map(BaseVO::getId).collect(Collectors.toList());

        // 修改调拨计划状态为取消
        List<MaterialPlanTransferDTO> updateMaterialPlanTransferDTOList = new ArrayList<>();
        for (String id : idList) {
            MaterialPlanTransferDTO dto = MaterialPlanTransferDTO.builder()
                    .id(id).transferStatus(TransferStatusEnum.UN_PUBLISHED.getCode()).build();
            updateMaterialPlanTransferDTOList.add(dto);
        }
        this.doUpdateBatchSelective(updateMaterialPlanTransferDTOList);
    }

    @Override
    public List<MaterialPlanTransferVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return "";
    }

    @Override
    public List<MaterialPlanTransferVO> invocation(List<MaterialPlanTransferVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

    @Override
    public List<MaterialPlanTransferVO> getLatestPublisheData() {
        return null;
    }

    @Override
    public List<LabelValue<String>> containerNumberDown(String id) {
        MaterialPlanTransferVO materialPlanTransferVO = this.selectVOByParams(ImmutableMap.of("id",id)).get(0);
        Set<String> containerNumberSet = new HashSet<>();

        if (Objects.nonNull(materialPlanTransferVO) &&
                StringUtils.isNotEmpty(materialPlanTransferVO.getStockPointTypeFrom())) {
            String productCode = materialPlanTransferVO.getProductCode();

            if (StockPointTypeEnum.MT.getCode().equals(materialPlanTransferVO.getStockPointTypeFrom())) {
                //获取码头的所有柜号并添加到 Set 中
                Optional.ofNullable(inventoryQuayDetailService.selectByParams(ImmutableMap.of("productCode", productCode)))
                        .ifPresent(quayDetails -> containerNumberSet.addAll(quayDetails.stream()
                                .map(InventoryQuayDetailVO::getContainerNumber)
                                .collect(Collectors.toSet())));
            } else if (StockPointTypeEnum.FF.getCode().equals(materialPlanTransferVO.getStockPointTypeFrom())) {
                //获取浮法已发运的所有柜号并添加到 Set 中
                Optional.ofNullable(inventoryFloatGlassShippedDetailService.selectByParams(ImmutableMap.of("productCode", productCode)))
                        .ifPresent(floatGlassDetails -> containerNumberSet.addAll(floatGlassDetails.stream()
                                .map(InventoryFloatGlassShippedDetailVO::getContainerNumber)
                                .collect(Collectors.toSet())));
            }
        }

        return containerNumberSet.stream()
                .map(item -> new LabelValue<>(item, item))
                .collect(Collectors.toList());
    }

    @Override
    public BaseResponse<Void> publish(List<String> ids) {
        // 获取所有对应的 MaterialPlanTransferPO 对象
        List<MaterialPlanTransferVO> list = this.selectVOByParams(ImmutableMap.of("ids", ids));

        MaterialPlanTransferServiceImpl target = springBeanUtils.getBean(MaterialPlanTransferServiceImpl.class);
        // 处理库存点类型为码头的数据
        target.handleMtData(list.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getStockPointTypeFrom()))
                .filter(data -> data.getStockPointTypeFrom().equals(StockPointTypeEnum.MT.getCode()))
                .collect(Collectors.toList()));

        // 处理非码头的数据
        target.handleNoMtData(list.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getStockPointTypeFrom()))
                .filter(data -> !data.getStockPointTypeFrom().equals(StockPointTypeEnum.MT.getCode()))
                .collect(Collectors.toList()));

        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    /**
     * 处理调拨计划为库存点类型码头数据
     *
     * @param list 调拨计划
     */
    @BusinessMonitorLog(businessCode = "码头、浮法汽运调拨计划发布", moduleCode = "MRP", businessFrequency = "DAY")
    public void handleMtData(List<MaterialPlanTransferVO> list) {
        // 预检查空列表
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        // 校验码头类型数据必须包含柜号
        validateCabinetNumberPresence(list);
        // 收集调拨计划柜号
        List<String> cabinetNos = list.stream().map(MaterialPlanTransferVO::getCabinetNo).filter(StringUtils::isNotEmpty).collect(Collectors.toList());

//        // 获取调拨计划中最早调拨开始时间为送柜时间
//        Date containerDeliveryDate = list.stream()
//                .map(MaterialPlanTransferVO::getTransferDateDepart)
//                .filter(Objects::nonNull).min(Comparator.comparing(data -> data)).orElse(new Date());

        // 根据柜号分组调拨到达日期
        Map<String, Date> containerNumberMap = list.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getCabinetNo()))
                .filter(data -> null != data.getTransferDateArrive())
                .collect(Collectors.toMap(MaterialPlanTransferVO::getCabinetNo, MaterialPlanTransferVO::getTransferDateArrive, (v1, v2) -> v1));

        // 获取码头库存明细并分组
        List<InventoryQuayDetailVO> inventoryDetails =
                inventoryQuayDetailService.selectByParams(ImmutableMap.of("containerNumberList", cabinetNos));

        // 修改码头库存批次明细的送柜时间
        List<InventoryQuayDetailDTO> updateInventoryQuayDetailDTOList = new ArrayList<>();
        for (InventoryQuayDetailVO vo : inventoryDetails) {
            InventoryQuayDetailDTO inventoryQuayDetailDTO = new InventoryQuayDetailDTO();
            inventoryQuayDetailDTO.setId(vo.getId());
            if (containerNumberMap.containsKey(vo.getContainerNumber())){
                inventoryQuayDetailDTO.setContainerDeliveryTime(containerNumberMap.get(vo.getContainerNumber()));
            }
            updateInventoryQuayDetailDTOList.add(inventoryQuayDetailDTO);
        }
        if (CollectionUtils.isNotEmpty(updateInventoryQuayDetailDTOList)){
            inventoryQuayDetailService.doUpdateBatchSelective(updateInventoryQuayDetailDTOList);
        }

        // 根据柜号查询浮法已发运数据（查出后也需更新送柜时间）
        List<InventoryFloatGlassShippedDetailVO> inventoryFloatGlassShippedDetailVOList =
                inventoryFloatGlassShippedDetailService.selectByParams(ImmutableMap.of("lotNumbers", cabinetNos));
        List<InventoryFloatGlassShippedDetailDTO> updateInventoryFloatGlassShippedDetailDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(inventoryFloatGlassShippedDetailVOList)){
            for (InventoryFloatGlassShippedDetailVO vo : inventoryFloatGlassShippedDetailVOList) {
                InventoryFloatGlassShippedDetailDTO inventoryFloatGlassShippedDetailDTO = new InventoryFloatGlassShippedDetailDTO();
                inventoryFloatGlassShippedDetailDTO.setId(vo.getId());
                if (containerNumberMap.containsKey(vo.getContainerNumber())){
                    inventoryFloatGlassShippedDetailDTO.setContainerDeliveryTime(containerNumberMap.get(vo.getContainerNumber()));
                }
                updateInventoryFloatGlassShippedDetailDTOList.add(inventoryFloatGlassShippedDetailDTO);
            }
        }
        if (CollectionUtils.isNotEmpty(inventoryFloatGlassShippedDetailVOList)){
            inventoryFloatGlassShippedDetailService.doUpdateBatchSelective(updateInventoryFloatGlassShippedDetailDTOList);
        }

        // 执行更新码头库存批次PO操作
        try {
            inventoryOceanFreightService.syncAutoCreatPo(SystemHolder.getTenantId(),
                    ImmutableMap.of("ids", inventoryDetails.stream()
                            .map(InventoryQuayDetailVO::getId).distinct().collect(Collectors.toList())));
        } catch (Exception e) {
            throw new BusinessException("执行PO更新失败,{0}", e.getLocalizedMessage());
        }

        // 获取码头库存明细并分组（再查一遍，这时候包含PO了）
        inventoryDetails = inventoryQuayDetailService.selectByParams(ImmutableMap.of("containerNumberList", cabinetNos));

        // 构建调拨计划映射（柜号 -> 调拨计划）
        Map<String, MaterialPlanTransferVO> planByCabinet = list.stream()
                .filter(plan -> StringUtils.isNotEmpty(plan.getCabinetNo()))
                .collect(Collectors.toMap(
                        MaterialPlanTransferVO::getCabinetNo,
                        Function.identity(),
                        (existing, replacement) -> existing));
        // 删除旧数据（根据柜号删除）
        materialPlanTransferDao.deleteByCabinetNo(cabinetNos);

        // 构建新调拨计划
        List<MaterialPlanTransferDTO> newPlans = inventoryDetails.stream()
                .map(detail -> buildTransferDTO(detail, planByCabinet.get(detail.getContainerNumber())))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<MaterialPlanTransferDTO> result = new ArrayList<>();
        Map<String, List<MaterialPlanTransferDTO>> materialPlanTransferDTOMap = new HashMap<>();

        // 分组并处理
        Map<String, List<MaterialPlanTransferDTO>> groupedPlans = newPlans.stream()
                .collect(Collectors.groupingBy(MaterialPlanTransferDTO::getPlanTransferNo));
        for (Map.Entry<String, List<MaterialPlanTransferDTO>> entry : groupedPlans.entrySet()) {
            List<MaterialPlanTransferDTO> value = entry.getValue();
            MaterialPlanTransferDTO dto = value.get(0);
            // 创建新对象用于存储累加结果
            MaterialPlanTransferDTO newDto = new MaterialPlanTransferDTO();
            BeanUtils.copyProperties(dto, newDto);
            newDto.setId(UUID.randomUUID().toString());
            newDto.setPlanTransferNo(dto.getPlanTransferNo());

            // 将原始对象列表存入 materialPlanTransferDTOMap
            materialPlanTransferDTOMap.put(newDto.getId(), value);

            // 累加调拨数量
            BigDecimal totalTransferQuantity = value.stream()
                    .map(MaterialPlanTransferDTO::getTransferQuantity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            newDto.setTransferredQuantity(totalTransferQuantity);
            newDto.setTransferQuantity(totalTransferQuantity);

            // 将新对象添加到结果列表
            result.add(newDto);
        }

        // 批量添加（调拨计划）
        Lists.partition(result, 500).forEach(this::doCreateBatch02);

        // 删除已存在调拨计划库存批次明细（根据柜号）
        materialPlanTransferInventoryDetailService.deleteByCabinetNo(cabinetNos);

        List<MaterialPlanTransferInventoryDetailDTO> materialPlanTransferInventoryDetailDTOList = new ArrayList<>();
        // 根据调拨计划id，添加对应的批次明细
        for (Map.Entry<String, List<MaterialPlanTransferDTO>> entry : materialPlanTransferDTOMap.entrySet()) {
            for (MaterialPlanTransferDTO dto : entry.getValue()) {
                materialPlanTransferInventoryDetailDTOList.add( MaterialPlanTransferInventoryDetailDTO.builder()
                        .materialPlanTransferId(entry.getKey())
                        .inventoryId(dto.getInventoryId())
                        .inventoryType(StockPointTypeEnum.MT.getCode())
                        .cabinetNo(dto.getCabinetNo())
                        .productCode(dto.getProductCode())
                        .transferQuantity(dto.getTransferQuantity())
                        .build());
            }
        }
        // 批量添加（调拨计划库存批次明细）
        Lists.partition(materialPlanTransferInventoryDetailDTOList, 500).forEach(data -> materialPlanTransferInventoryDetailService.doCreateBatch(data));
    }

    /**
     * 校验码头类型调拨计划必须包含柜号
     */
    private void validateCabinetNumberPresence(List<MaterialPlanTransferVO> plans) {
        plans.forEach(plan -> {
            if (plan.getStockPointTypeFrom() != null && StockPointTypeEnum.MT.getCode().equals(plan.getStockPointTypeFrom())
                    && StringUtils.isEmpty(plan.getCabinetNo())) {
                throw new BusinessException("调拨计划号" + plan.getPlanTransferNo() + "缺少柜号信息");
            }
        });
    }

    /**
     * 构建调拨计划DTO
     */
    private MaterialPlanTransferDTO buildTransferDTO(InventoryQuayDetailVO detail, MaterialPlanTransferVO plan) {
        if (plan == null) {
            log.warn("柜号{}无对应的调拨计划", detail.getContainerNumber());
            return null;
        }

        MaterialPlanTransferDTO dto = new MaterialPlanTransferDTO();
        BeanUtils.copyProperties(plan, dto);
        dto.setProductCode(detail.getProductCode());
        dto.setTransferQuantity(detail.getActualSentQuantity());
        dto.setTransferStatus(TransferStatusEnum.PUBLISHED.getCode());
        dto.setInventoryId(detail.getId());
        dto.setContainerDeliveryTime(detail.getContainerDeliveryTime());
        dto.setPurchaseOrderCode(detail.getPo());
        dto.setPurchaseOrderLineCode(detail.getPoNumber());

        // 生成唯一调拨单号
        dto.setPlanTransferNo(String.join("-",
                detail.getProductCode(),
                plan.getStockPointCodeFrom(),
                DateUtils.dateToString(plan.getTransferDateDepart(), "yyyy-MM-dd"),
                detail.getContainerNumber()));
        return dto;
    }

    /**
     * 处理调拨计划为库存点类型非码头数据
     *
     * @param list 调拨计划
     */
    @BusinessMonitorLog(businessCode = "浮法海运调拨计划发布", moduleCode = "MRP", businessFrequency = "WEEK")
    public void handleNoMtData(List<MaterialPlanTransferVO> list) {
        // 用于存储未发布过的对象
        List<MaterialPlanTransferDTO> toUpdateList = new ArrayList<>();

        // 遍历查询结果，检查每个对象的状态
        for (MaterialPlanTransferVO vo : list) {
            if (StringUtils.isNotEmpty(vo.getTransferStatus()) && vo.getTransferStatus().equals(TransferStatusEnum.PUBLISHED.getCode())) {
                // 如果已经是已发布状态，跳过并可以记录提示信息
                log.info("ID {} 已经是已发布状态，跳过发布", vo.getId());
                continue;
            }
            // 如果没有发布过，则设置为已发布状态
            MaterialPlanTransferDTO dto = new MaterialPlanTransferDTO();
            dto.setId(vo.getId());
            dto.setTransferStatus(TransferStatusEnum.PUBLISHED.getCode());
            toUpdateList.add(dto);
        }

        // 批量更新未发布的对象
        if (CollectionUtils.isNotEmpty(toUpdateList)) {
            Lists.partition(toUpdateList, 500).forEach(this::doUpdateBatchSelective);
        }

        // 收集调拨计划id
        List<String> materialPlanTransferIds = list.stream().map(BaseVO::getId).distinct().collect(Collectors.toList());
        // 获取对应的库存占用明细
        List<MaterialPlanInventoryOccupyPublishedVO> materialPlanInventoryOccupyVOList = materialPlanInventoryOccupyPublishedService.selectByParams(ImmutableMap.of("materialPlanTransferIds", materialPlanTransferIds));
        // 根据调拨计划分组
        Map<String, List<MaterialPlanInventoryOccupyPublishedVO>> materialPlanInventoryOccupyMap = materialPlanInventoryOccupyVOList.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getMaterialPlanTransferId()))
                .collect(Collectors.groupingBy(MaterialPlanInventoryOccupyPublishedVO::getMaterialPlanTransferId));

        // 查询浮法库存批次明细
        List<MaterialPlanTransferInventoryDetailDTO> materialPlanTransferInventoryDetailDTOList = new ArrayList<>();
        // 根据调拨计划id，添加对应的批次明细
        for (Map.Entry<String, List<MaterialPlanInventoryOccupyPublishedVO>> entry : materialPlanInventoryOccupyMap.entrySet()) {
            for (MaterialPlanInventoryOccupyPublishedVO vo : entry.getValue()) {
                String[] split = vo.getInventoryId().split("&&");
                materialPlanTransferInventoryDetailDTOList.add(MaterialPlanTransferInventoryDetailDTO.builder()
                            .materialPlanTransferId(entry.getKey())
                            .inventoryId(vo.getInventoryId())
                            .inventoryType(StockPointTypeEnum.FF.getCode())
                            .productCode(split[0])
                            .transferQuantity(vo.getOccupyQuantity())
                            .build());
            }
        }
        // 批量添加（调拨计划库存批次明细）
        Lists.partition(materialPlanTransferInventoryDetailDTOList, 500).forEach(data -> materialPlanTransferInventoryDetailService.doCreateBatch(data));
    }


}
