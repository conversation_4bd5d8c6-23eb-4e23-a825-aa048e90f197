package com.yhl.scp.mrp.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mrp.material.plan.dto.MrpParamDTO;
import com.yhl.scp.mrp.material.plan.service.impl.GlassMrpServiceImpl;
import com.yhl.scp.mrp.material.plan.service.impl.NoGlassMrpServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @ClassName MaterialPlanJob
 * @Description TODO
 * @Date 2025-03-25 11:48:47
 * <AUTHOR>
 * @Copyright rzz
 * @Version 1.0
 */
@Component
@Slf4j
public class MaterialPlanJob {

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private NoGlassMrpServiceImpl noGlassMrpService;

    @Resource
    private GlassMrpServiceImpl glassMrpService;

    @XxlJob("mrpCalcJobHandler")
    @BusinessMonitorLog(businessCode = "材料、原片MRP自动计算", moduleCode = "MRP", businessFrequency = "DAY")
    public ReturnT<String> mrpCalcJobHandler() {
        XxlJobHelper.log("MRP计算Job开始执行");
        List<Scenario> mrpScenarios = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.MRP.getCode()).getData();
        if (CollectionUtils.isEmpty(mrpScenarios)) {
            XxlJobHelper.log("租户下不存在MRP模块信息");
            return ReturnT.SUCCESS;
        }

        String jobParam = XxlJobHelper.getJobParam();
        XxlJobHelper.log("接收調度中心参数...[{}]", jobParam);
        for (Scenario scenario : mrpScenarios) {
            MrpParamDTO mrpParamDTO = new MrpParamDTO();
            mrpParamDTO.setCalcType(null);
            mrpParamDTO.setScenario(scenario.getDataBaseName());
            mrpParamDTO.setWhetherAutomatic(Boolean.TRUE);
            mrpParamDTO.setCalcStartTime(DateUtils.getDayFirstTime(new Date()));
            // 材料MRP计算
            DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());
            XxlJobHelper.log("材料MRP计算开始,数据源:{}", scenario.getDataBaseName());
            noGlassMrpService.doRunMrp(mrpParamDTO);
            XxlJobHelper.log("材料MRP计算结束,数据源:{}", scenario.getDataBaseName());
            // 原片MRP计算
            XxlJobHelper.log("原片MRP计算开始,数据源:{}", scenario.getDataBaseName());
            glassMrpService.doRunMrp(mrpParamDTO);
            XxlJobHelper.log("原片MRP计算结束,数据源:{}", scenario.getDataBaseName());
            DynamicDataSourceContextHolder.clearDataSource();
        }
        XxlJobHelper.log("MRP计算Job执行结束");
        return ReturnT.SUCCESS;
    }

}
