package com.yhl.scp.mrp.report.glassInventoryDynamic.convertor;

import com.yhl.scp.mrp.report.glassInventoryDynamic.domain.entity.GlassInventoryDynamicDataReportDO;
import com.yhl.scp.mrp.report.glassInventoryDynamic.dto.GlassInventoryDynamicDataReportDTO;
import com.yhl.scp.mrp.report.glassInventoryDynamic.infrastructure.po.GlassInventoryDynamicDataReportPO;
import com.yhl.scp.mrp.report.glassInventoryDynamic.vo.GlassInventoryDynamicDataReportVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>GlassInventoryDynamicDataReportConvertor</code>
 * <p>
 * 原片库存动态表转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-12 22:05:06
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GlassInventoryDynamicDataReportConvertor {

    GlassInventoryDynamicDataReportConvertor INSTANCE = Mappers.getMapper(GlassInventoryDynamicDataReportConvertor.class);

    GlassInventoryDynamicDataReportDO dto2Do(GlassInventoryDynamicDataReportDTO obj);

    GlassInventoryDynamicDataReportDTO do2Dto(GlassInventoryDynamicDataReportDO obj);

    List<GlassInventoryDynamicDataReportDO> dto2Dos(List<GlassInventoryDynamicDataReportDTO> list);

    List<GlassInventoryDynamicDataReportDTO> do2Dtos(List<GlassInventoryDynamicDataReportDO> list);

    GlassInventoryDynamicDataReportVO do2Vo(GlassInventoryDynamicDataReportDO obj);

    GlassInventoryDynamicDataReportVO po2Vo(GlassInventoryDynamicDataReportPO obj);

    List<GlassInventoryDynamicDataReportVO> po2Vos(List<GlassInventoryDynamicDataReportPO> list);

    GlassInventoryDynamicDataReportPO do2Po(GlassInventoryDynamicDataReportDO obj);

    GlassInventoryDynamicDataReportDO po2Do(GlassInventoryDynamicDataReportPO obj);

    GlassInventoryDynamicDataReportPO dto2Po(GlassInventoryDynamicDataReportDTO obj);

    List<GlassInventoryDynamicDataReportPO> dto2Pos(List<GlassInventoryDynamicDataReportDTO> obj);

}
