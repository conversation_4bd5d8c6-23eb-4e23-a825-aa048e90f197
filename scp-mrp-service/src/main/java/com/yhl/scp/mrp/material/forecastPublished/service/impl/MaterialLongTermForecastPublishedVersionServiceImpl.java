package com.yhl.scp.mrp.material.forecastPublished.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mrp.material.forecastPublished.convertor.MaterialLongTermForecastPublishedVersionConvertor;
import com.yhl.scp.mrp.material.forecastPublished.domain.entity.MaterialLongTermForecastPublishedVersionDO;
import com.yhl.scp.mrp.material.forecastPublished.domain.service.MaterialLongTermForecastPublishedVersionDomainService;
import com.yhl.scp.mrp.material.forecastPublished.dto.MaterialLongTermForecastPublishedVersionDTO;
import com.yhl.scp.mrp.material.forecastPublished.infrastructure.dao.MaterialLongTermForecastPublishedVersionDao;
import com.yhl.scp.mrp.material.forecastPublished.infrastructure.po.MaterialLongTermForecastPublishedVersionPO;
import com.yhl.scp.mrp.material.forecastPublished.service.MaterialLongTermForecastPublishedVersionService;
import com.yhl.scp.mrp.material.forecastPublished.vo.MaterialLongTermForecastPublishedVersionVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mrp.published.infrastructure.po.MaterialPlanPublishedVersionPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>MaterialLongTermForecastPublishedVersionServiceImpl</code>
 * <p>
 * 材料长期预测版本表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-07 14:52:40
 */
@Slf4j
@Service
public class MaterialLongTermForecastPublishedVersionServiceImpl extends AbstractService implements MaterialLongTermForecastPublishedVersionService {

    @Resource
    private MaterialLongTermForecastPublishedVersionDao materialLongTermForecastPublishedVersionDao;

    @Resource
    private MaterialLongTermForecastPublishedVersionDomainService materialLongTermForecastPublishedVersionDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(MaterialLongTermForecastPublishedVersionDTO materialLongTermForecastPublishedVersionDTO) {
        // 0.数据转换
        MaterialLongTermForecastPublishedVersionDO materialLongTermForecastPublishedVersionDO = MaterialLongTermForecastPublishedVersionConvertor.INSTANCE.dto2Do(materialLongTermForecastPublishedVersionDTO);
        MaterialLongTermForecastPublishedVersionPO materialLongTermForecastPublishedVersionPO = MaterialLongTermForecastPublishedVersionConvertor.INSTANCE.dto2Po(materialLongTermForecastPublishedVersionDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialLongTermForecastPublishedVersionDomainService.validation(materialLongTermForecastPublishedVersionDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(materialLongTermForecastPublishedVersionPO);
        materialLongTermForecastPublishedVersionDao.insertWithPrimaryKey(materialLongTermForecastPublishedVersionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(MaterialLongTermForecastPublishedVersionDTO materialLongTermForecastPublishedVersionDTO) {
        // 0.数据转换
        MaterialLongTermForecastPublishedVersionDO materialLongTermForecastPublishedVersionDO = MaterialLongTermForecastPublishedVersionConvertor.INSTANCE.dto2Do(materialLongTermForecastPublishedVersionDTO);
        MaterialLongTermForecastPublishedVersionPO materialLongTermForecastPublishedVersionPO = MaterialLongTermForecastPublishedVersionConvertor.INSTANCE.dto2Po(materialLongTermForecastPublishedVersionDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialLongTermForecastPublishedVersionDomainService.validation(materialLongTermForecastPublishedVersionDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(materialLongTermForecastPublishedVersionPO);
        materialLongTermForecastPublishedVersionDao.update(materialLongTermForecastPublishedVersionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MaterialLongTermForecastPublishedVersionDTO> list) {
        List<MaterialLongTermForecastPublishedVersionPO> newList = MaterialLongTermForecastPublishedVersionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        materialLongTermForecastPublishedVersionDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<MaterialLongTermForecastPublishedVersionDTO> list) {
        List<MaterialLongTermForecastPublishedVersionPO> newList = MaterialLongTermForecastPublishedVersionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialLongTermForecastPublishedVersionDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return materialLongTermForecastPublishedVersionDao.deleteBatch(idList);
        }
        return materialLongTermForecastPublishedVersionDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MaterialLongTermForecastPublishedVersionVO selectByPrimaryKey(String id) {
        MaterialLongTermForecastPublishedVersionPO po = materialLongTermForecastPublishedVersionDao.selectByPrimaryKey(id);
        return MaterialLongTermForecastPublishedVersionConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "MATERIAL_LONG_TERM_FORECAST_PUBLISHED_VERSION")
    public List<MaterialLongTermForecastPublishedVersionVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "MATERIAL_LONG_TERM_FORECAST_PUBLISHED_VERSION")
    public List<MaterialLongTermForecastPublishedVersionVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MaterialLongTermForecastPublishedVersionVO> dataList = materialLongTermForecastPublishedVersionDao.selectByCondition(sortParam, queryCriteriaParam);
        MaterialLongTermForecastPublishedVersionServiceImpl target = SpringBeanUtils.getBean(MaterialLongTermForecastPublishedVersionServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MaterialLongTermForecastPublishedVersionVO> selectByParams(Map<String, Object> params) {
        List<MaterialLongTermForecastPublishedVersionPO> list = materialLongTermForecastPublishedVersionDao.selectByParams(params);
        return MaterialLongTermForecastPublishedVersionConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MaterialLongTermForecastPublishedVersionVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<MaterialLongTermForecastPublishedVersionVO> invocation(List<MaterialLongTermForecastPublishedVersionVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public String getVersionCode() {
        String dateStr = DateUtils.dateToString(new Date(), "yyyyMMdd");
        String versionCode;
        MaterialLongTermForecastPublishedVersionPO materialLongTermForecastPublishedVersionPO = materialLongTermForecastPublishedVersionDao.selectLastVersion();
        if (null == materialLongTermForecastPublishedVersionPO){
            versionCode = dateStr + SystemHolder.getUserName() + "001";
        }else {
            String lastVersionCode = materialLongTermForecastPublishedVersionPO.getVersionCode().substring(materialLongTermForecastPublishedVersionPO.getVersionCode().length() - 3);
            int newVersionNumber = Integer.parseInt(lastVersionCode) + 1;
            versionCode = dateStr + SystemHolder.getUserName() + String.format("%03d", newVersionNumber);
        }
        return versionCode;
    }
}
