package com.yhl.scp.mrp.material.plan.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <code>MaterialInventoryDemandDO</code>
 * <p>
 *  原片日总需求
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-01 23:27:40
 */
@Data
public class MaterialDayTotalDemandDTO {
    // 物料编码
    private String productCode;
    // 需求日期
    private Date demandDate;
    // 标准规格需求
    private BigDecimal standardDemand;
    // 替代规格需求
    private BigDecimal usedAsReplaceQuantity;
    // 被替代的量
    private BigDecimal useReplaceQuantity;
    // 替代料使用明细
    private List<ReplaceDetailDTO> useReplaceDetailList;
    // 被作为替代料使用明细
    private Map<String, BigDecimal> useAsReplaceDetail;
    // 本厂编码
    private String productFactoryCode;
    // 车型编码
    private String vehicleModelCode;
    // 已满足标准规格需求
    private BigDecimal satisfyStandardDemand = BigDecimal.ZERO;
    // 主料编码
    private String mainProductCode;

    @Data
    public static class ReplaceDetailDTO{
        // 代料编码
        private String productCode;
        // 替代料数量
        private BigDecimal replaceQuantity;

        private String remark;

        private String inventoryAlternativeRelationshipId;
    }
}
