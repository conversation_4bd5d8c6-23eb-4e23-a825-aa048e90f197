package com.yhl.scp.mrp.published.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.mrp.published.dto.MaterialPlanInventoryShiftPublishedDTO;
import com.yhl.scp.mrp.published.vo.MaterialPlanInventoryShiftPublishedVO;

import java.util.List;

/**
 * <code>MaterialPlanInventoryShiftPublishedService</code>
 * <p>
 * 物料库存推移发布表应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-21 14:29:02
 */
public interface MaterialPlanInventoryShiftPublishedService extends BaseService<MaterialPlanInventoryShiftPublishedDTO, MaterialPlanInventoryShiftPublishedVO> {

    /**
     * 查询所有
     *
     * @return list {@link MaterialPlanInventoryShiftPublishedVO}
     */
    List<MaterialPlanInventoryShiftPublishedVO> selectAll();

}
