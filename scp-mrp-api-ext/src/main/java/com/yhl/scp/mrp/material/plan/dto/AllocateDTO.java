package com.yhl.scp.mrp.material.plan.dto;

import com.yhl.scp.mds.substitution.vo.ProductSubstitutionRelationshipVO;
import com.yhl.scp.mrp.inventory.vo.FulfillmentManualVO;
import com.yhl.scp.mrp.inventory.vo.InventoryAlternativeRelationshipVO;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanTransferVO;
import com.yhl.scp.mrp.transport.vo.TransportRoutingVO;
import com.yhl.scp.mrp.transport.vo.TransportSectionVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <code>AllocateDTO</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-09 14:40:56
 */
@Data
public class AllocateDTO {
    // 系统时间
    private Date mrpCalDateStart = new Date();
    // 港口库存点
    private List<String>  mtStockPointCodeList;
    // 浮法厂库存点
    private List<String>  ffStockPointCodeList;
    // 运输路径
    List<TransportRoutingVO> transportRoutingList;
    // 需求
    private MrpDemandDTO mrpDemand;
    // 供应
    private List<MrpSupplyDTO> mrpSupplyList;
    // 目的地是否是本厂
    private boolean bcDestination;
    // 供应库存点类型
    private String supplyStockPointType;
    // 原片混合替代关系
    private Map<String, List<String>> mixSubstitution;
    // 原片需求按照物料来分组
    Map<String, List<MaterialDayTotalDemandDTO>> demandGroupByProduct;
    // 对应推移模式的所有供应
    List<MrpSupplyDTO> mrpSupplyAllList;
    // 被替代使用量
    Map<String, BigDecimal> usedAsReplaceQuantityMap;
    // 是否使用替代
    boolean whetherUseSubstitution = false;
}
