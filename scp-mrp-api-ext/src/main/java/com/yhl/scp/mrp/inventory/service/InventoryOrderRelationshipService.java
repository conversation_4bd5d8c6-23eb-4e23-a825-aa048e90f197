package com.yhl.scp.mrp.inventory.service;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.mds.bom.vo.ProductBomVO;
import com.yhl.scp.mds.bom.vo.ProductBomVersionVO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mrp.inventory.dto.InventoryAlternativeRelationshipDTO;
import com.yhl.scp.mrp.inventory.dto.InventoryOrderRelationshipDTO;
import com.yhl.scp.mrp.inventory.vo.InventoryAlternativeRelationshipVO;
import com.yhl.scp.mrp.inventory.vo.InventoryOrderRelationshipVO;

import java.util.List;
import java.util.Map;

/**
 * <code>InventoryOrderRelationshipService</code>
 * <p>
 * 库存与订单关系应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-04 10:31:57
 */
public interface InventoryOrderRelationshipService {

    PageInfo<InventoryOrderRelationshipVO> pageCustom(InventoryOrderRelationshipDTO dto);

}
