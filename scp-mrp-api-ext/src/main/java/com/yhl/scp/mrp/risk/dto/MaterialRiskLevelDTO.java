package com.yhl.scp.mrp.risk.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>MaterialRiskLevelDTO</code>
 * <p>
 * 材料风险等级DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-23 17:21:30
 */

@ApiModel(value = "材料风险等级DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MaterialRiskLevelDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 271061476189675707L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 库存点编码
     */
    @ApiModelProperty(value = "库存点编码")
    private String stockPointCode;
    /**
     * 材料编码
     */
    @ApiModelProperty(value = "材料编码")
    private String materialCode;
    /**
     * 材料风险等级
     */
    @ApiModelProperty(value = "材料风险等级")
    private String materialRiskLevel;
    /**
     * 安全库存上限
     */
    @ApiModelProperty(value = "安全库存上限")
    private BigDecimal safeUpperAlarm;
    /**
     * 安全库存下限
     */
    @ApiModelProperty(value = "安全库存下限")
    private BigDecimal safeLowerAlarm;
    /**
     * 风险等级规则id
     */
    @ApiModelProperty(value = "风险等级规则id")
    private String materialRiskLevelRuleId;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String modifier;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

}
