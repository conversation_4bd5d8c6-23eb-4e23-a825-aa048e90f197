package com.yhl.scp.mrp.inventory.dto;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <code>InventoryOrderRelationshipDTO</code>
 * <p>
 * 库存与订单关系DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-25 10:31:57
 */
@ApiModel(value = "库存与订单关系DTO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InventoryOrderRelationshipDTO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -8564324098238229604L;

    /**
     * 版本id
     */
    @ApiModelProperty(value = "版本id")
    private String versionId;
    /**
     * 库存点编码
     */
    @ApiModelProperty(value = "库存点编码")
    private String stockPointCode;
    /**
     * 车型编码
     */
    @ApiModelProperty(value = "车型编码")
    private String vehicleModelCode;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    private String productCode;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    private String oemCode;
    /**
     * 零件号
     */
    @ApiModelProperty(value = "零件号")
    private String partNumber;

    @ApiModelProperty(value = "每页大小")
    private int pageSize = 10;

    @ApiModelProperty(value = "第几页")
    private int pageNum = 1;

    @ApiModelProperty("半品库存")
    private List<BigDecimal> stepInventories;

    @ApiModelProperty("本票需求缺口")
    private BigDecimal demandGap;

    @Override
    public void clean() {

    }
}
