package com.yhl.scp.mrp.material.plan.dto;

import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedVO;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepInputVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingVO;
import com.yhl.scp.mds.extension.supplier.vo.SupplierPurchaseRatioVO;
import com.yhl.scp.mds.extension.supplier.vo.SupplierVO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.capacityBalance.vo.CapacityBalanceVersionVO;
import com.yhl.scp.mps.plan.vo.MasterPlanIssuedDataVO;
import com.yhl.scp.mps.plan.vo.MasterPlanVersionVO;
import com.yhl.scp.mrp.inventory.vo.GlassSafetyInventoryVO;
import com.yhl.scp.mrp.inventory.vo.InventoryAlternativeRelationshipVO;
import com.yhl.scp.mrp.inventory.vo.SafetyInventoryVO;
import com.yhl.scp.mrp.material.arrival.dto.MaterialArrivalTrackingDTO;
import com.yhl.scp.mrp.material.plan.vo.*;
import com.yhl.scp.mrp.substitutionRelationship.vo.GlassSubstitutionRelationshipVO;
import com.yhl.scp.mrp.supplier.vo.MaterialSupplierPurchaseVO;
import com.yhl.scp.mrp.transport.vo.TransportRoutingVO;
import lombok.Data;

import java.util.*;

/**
 * <code>MrpContextDTO</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-30 17:25:39
 */
@Data
public class MrpContextDTO {
    // mrp计算日期
    private Date mrpCalcDate;
    // 库存点数据
    private List<NewStockPointVO> stockPointVOList;
    // 库存点物品数据
    private List<NewProductStockPointVO> newProductStockPointVOList = new ArrayList<>();
    // 库存点物品Map, key为ID
    private Map<String,NewProductStockPointVO> newProductStockPointVOMapOfId;
    // 库存点物品Map, key为productCode
    private Map<String,NewProductStockPointVO> newProductStockPointVOMapOfCode;
    // 工艺路径数据
    private List<RoutingVO> routingVOList = new ArrayList<>();
    // 工艺路径步骤数据
    private List<RoutingStepVO> routingStepVOList = new ArrayList<>();
    // 工艺路径步骤输入数据
    private List<RoutingStepInputVO> routingStepInputVOList = new ArrayList<>();
    // 工艺路径步骤输入数据Map, key为routingStepId
    Map<String, List<RoutingStepInputVO>> routingStepInputVOGroup;
    // 工艺路径步骤输出数据
//    private List<RoutingStepOutputVO> routingStepOutputVOList;
    // 主计划版本数据
    private MasterPlanVersionVO masterPlanVersionVO;
    // 发货计划详情数据
    private List<DeliveryPlanPublishedVO> deliveryPlanDetailVOList;
    // 主计划发布结果数据
    private List<MasterPlanIssuedDataVO> masterPlanIssuedDataVOList;
    // 最新已发布的产能版本
    private CapacityBalanceVersionVO capacityBalanceVersionVO;
    // 月度产能平衡数据
//    private List<CapacitySupplyRelationshipVO> capacitySupplyRelationshipVOList;
    // 需求数据
    private List<MrpDemandDTO> mrpDemandList;

    /**
     *  非原片供应数据
     */
    // 计划采购供应
    private List<MrpSupplyDTO> planPurchaseSupplyList;
    // 订单在途供应
    private List<MrpSupplyDTO> transitOrderSupplyList;

    /**
     *  原片供应数据
     */
    // 原片安全库存管理信息
    Map<String, GlassSafetyInventoryVO> glassSafetyInventoryMap;
    // 原片运输路径
    private List<TransportRoutingVO> transportRoutingList;
    private Map<String, List<TransportRoutingVO>> transportRoutingGroupMap;
    // 原片混合替代关系
    private Map<String, List<String>> mixSubstitution;
    // 原片对应本厂编码、车型信息
    private Map<String, List<NewProductStockPointVO>> glassOfFactoryCodeMap;
    // 计划替代规则
    private List<InventoryAlternativeRelationshipVO> inventoryAlternativeRelationshipList;
    // 调拨计划
    private List<MaterialPlanTransferVO> materialPlanTransferList;
    // 调拨计划占用库存批次信息
    private List<MaterialPlanTransferInventoryDetailVO> materialPlanTransferInventoryDetailList;
    // 原片本厂库存
    private List<MrpSupplyDTO> factorySupplyList;
    // 原片港口库存
    private List<MrpSupplyDTO> portSupplyList;
    // 原片浮法厂库存
    private List<MrpSupplyDTO> floatSupplyList;
    // 本厂库存点
    private List<String> bcStockPointCodeList;
    // 码头库存点
    private List<String> portStockPointCodeList;
    // 浮法库存点
    private List<String> floatStockPointCodeList;
    // 码头免堆期
    Map<String, Integer> portFreeStorageMap;
    // 原片日总需求
    List<MaterialDayTotalDemandDTO> materialDayTotalDemandList;
    // 场景
    private String scenario;
    //原片MRP刷新类型
    private String glassMrpRefreshType;

    // 生产需求获取规则(生产计划计算；发货计划计算)
    private String mpsDemandRule;

    // 过滤出发货计划物品对应的主生产计划制造订单，计算出能覆盖到的发货计划的日期
    Map<String, Date> lastDeliveryPlanTimeMapOfProductCode;

    // 辅料子库存
    private List<String> auxiliarySubInventoryList;

    // 材料计划员权限下的材料
    private List<String> planUserProductCodeList = new ArrayList<>();
    // 材料计划员权限下的半成品和成品
    private List<String> planUserFactoryCodeList = new ArrayList<>();


    private boolean bcAdjust;

    // 被调整库存推移主表
    private GlassInventoryShiftDataDTO adjustInventoryShiftData;
    // 指定库存推移明细对象
    private GlassInventoryShiftDetailDTO adjustInventoryShiftDetail;

    private List<GlassInventoryShiftDetailVO> inventoryShiftDetailList;
    // 本厂库存推移
    private List<GlassInventoryShiftDetailVO> bcGlassInventoryShiftDetailList;
    // 码头库存推移
    private List<GlassInventoryShiftDetailVO> mtGlassInventoryShiftDetailList;
    // 浮法库存推移
    private List<GlassInventoryShiftDetailVO> ffGlassInventoryShiftDetailList;
    // 库存批次占用信息
    private List<MaterialPlanInventoryOccupyVO> materialPlanInventoryOccupyList;

    // 是否自动
    private Boolean whetherAutomatic = Boolean.FALSE;

    // 用户ID
    private String userId;

    // 最新毛需求版本ID
    private String materialGrossDemandVersionId;

    // 最新毛需求版本Code
    private String materialGrossDemandVersionCode;

    // 计算的物品代码列表
    private List<String> computeProductCodeList;

    // 材料与供应商关系
    private Map<String, MaterialSupplierPurchaseVO> supplierPurchaseVOMapOfMaterialCode;

    // 供应商的采购比例
    private Map<String, List<SupplierPurchaseRatioVO>> supplierPurchaseRatioVOGroup;

    // 供应商Map,key为ID
    private Map<String, SupplierVO> supplierVOMapOfId;

    // 非原片的安全库存List
    private List<SafetyInventoryVO> safetyInventoryVOList;

    // 非原片的安全库存Map
    private Map<String, SafetyInventoryVO> safetyInventoryVOMapProductCode;

    // 非原片的期初库存
    private Map<String, List<InventoryBatchDetailVO>> openingInventoryMap;

    // 原片需求按照物料来分组
    private Map<String, List<MaterialDayTotalDemandDTO>> demandGroupByProduct;

    // 需要修改的到货跟踪数据
    private List<MaterialArrivalTrackingDTO> updateMaterialArrivalTrackingList;

    // 原片替代映射
    private List<GlassSubstitutionRelationshipVO> glassSubstitutionRelationshipList;

    // 原片替代映射Map
    private Map<String, List<GlassSubstitutionRelationshipVO>> glassSubstitutionRelationshipMapOfRawProductCode;

    // 原片替代映射Map
    private Map<String, List<GlassSubstitutionRelationshipVO>> glassSubstitutionRelationshipMapOfProductionCode;

}
