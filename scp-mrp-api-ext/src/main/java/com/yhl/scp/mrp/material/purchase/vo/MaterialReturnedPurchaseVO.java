package com.yhl.scp.mrp.material.purchase.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>MaterialReturnedPurchaseVO</code>
 * <p>
 * 采购退货VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-08 15:17:53
 */
@ApiModel(value = "采购退货VO")
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MaterialReturnedPurchaseVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -72159126530513105L;

    /**
     * 退货数量
     */
    @ApiModelProperty(value = "退货数量")
    @FieldInterpretation(value = "退货数量")
    private String returnQty;
    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    @FieldInterpretation(value = "物料编码")
    private String productCode;
    /**
     * 退货单号
     */
    @ApiModelProperty(value = "退货单号")
    @FieldInterpretation(value = "退货单号")
    private String documentNum;
    /**
     * 订单行号
     */
    @ApiModelProperty(value = "订单行号")
    @FieldInterpretation(value = "订单行号")
    private String lineNum;
    /**
     * 工厂ID
     */
    @ApiModelProperty(value = "工厂ID")
    @FieldInterpretation(value = "工厂ID")
    private String orgId;
    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    @FieldInterpretation(value = "订单号")
    private String poNumber;
    /**
     * 退货日期
     */
    @ApiModelProperty(value = "退货日期")
    @FieldInterpretation(value = "退货日期")
    private Date creationDate;

    /**
     * 是否匹配
     */
    @ApiModelProperty(value = "是否匹配")
    @FieldInterpretation(value = "是否匹配")
    private String whetherMatches;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @FieldInterpretation(value = "版本")
    private Integer versionValue;

    /**
     * 物品分类
     */
    @ApiModelProperty(value = "物品分类")
    @FieldInterpretation(value = "物品分类")
    private String productClassify;

    @Override
    public void clean() {

    }

}
