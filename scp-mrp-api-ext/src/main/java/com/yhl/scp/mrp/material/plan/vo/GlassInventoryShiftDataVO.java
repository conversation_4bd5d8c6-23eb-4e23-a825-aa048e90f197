package com.yhl.scp.mrp.material.plan.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <code>GlassInventoryShiftDataVO</code>
 * <p>
 * 物料库存推移VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-02 11:29:07
 */
@ApiModel(value = "原片物料库存推移VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class GlassInventoryShiftDataVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -32458931877382931L;

    /**
     * 物料计划版本id
     */
    @ApiModelProperty(value = "物料计划版本id")
    @FieldInterpretation(value = "物料计划版本id")
    private String materialPlanVersionId;
    /**
     * 物品编码
     */
    @ApiModelProperty(value = "物品编码")
    @FieldInterpretation(value = "物品编码")
    private String productCode;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @FieldInterpretation(value = "本厂编码")
    private String productFactoryCode;
    /**
     * 车型编码
     */
    @ApiModelProperty(value = "车型编码")
    @FieldInterpretation(value = "车型编码")
    private String vehicleModelCode;
    /**
     * 最小安全库存天数
     */
    @ApiModelProperty(value = "最小安全库存天数")
    @FieldInterpretation(value = "最小安全库存天数")
    private BigDecimal safetyStockDaysMin;
    /**
     * 目标安全库存天数
     */
    @ApiModelProperty(value = "目标安全库存天数")
    @FieldInterpretation(value = "目标安全库存天数")
    private BigDecimal safetyStockDaysStandard;
    /**
     * 最大安全库存天数
     */
    @ApiModelProperty(value = "最大安全库存天数")
    @FieldInterpretation(value = "最大安全库存天数")
    private BigDecimal safetyStockDaysMax;
    /**
     * 码头备库天数
     */
    @ApiModelProperty(value = "码头备库天数")
    @FieldInterpretation(value = "码头备库天数")
    private BigDecimal portInventoryDays;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @FieldInterpretation(value = "备注")
    private String remark;

    @ApiModelProperty(value = "库存推移明细")
    @FieldInterpretation(value = "库存推移明细")
    List<GlassInventoryShiftDetailVO> glassInventoryShiftDetailList;

    @ApiModelProperty(value = "物料厚度")
    @FieldInterpretation(value = "物料厚度")
    private BigDecimal productThickness;


    @ApiModelProperty(value = "物料颜色")
    @FieldInterpretation(value = "物料颜色")
    private String productColor;
    /**
     * 长
     */
    @ApiModelProperty(value = "长")
    @FieldInterpretation(value = "长")
    private BigDecimal productLength;
    /**
     * 宽
     */
    @ApiModelProperty(value = "宽")
    @FieldInterpretation(value = "宽")
    private BigDecimal productWidth;

    @Override
    public void clean() {

    }

}
