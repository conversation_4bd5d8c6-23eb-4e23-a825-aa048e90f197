package com.yhl.scp.mrp.material.plan.dto;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @ClassName MrpDemandData
 * @Description TODO
 * @Date 2024-10-25 16:20:59
 * <AUTHOR>
 * @Copyright 瑞之泽
 * @Version 1.0
 */
@Data
@Builder
public class MrpDemandDTO implements Serializable {

    private static final long serialVersionUID = -75616598839960265L;

    /**
     * 主键
     */
    private String id;
    /**
     * 工单号
     */
    private String workOrderCode;

    /**
     * 库存点编码
     */
    private String stockPointCode;

    /**
     * 本厂编码
     */
    private String productCode;

    /**
     * 产品分类
     */
    private String productClassify;

    /**
     * 需求时间
     */
    private Date demandTime;

    /**
     * 需求数量
     */
    private BigDecimal demandQuantity;
    /**
     * 未分配数量
     */
    private BigDecimal unFulfillmentQuantity;

    /**
     * 工序代码
     */
    private String operationCode;

    /**
     * 供应方式(本厂/委外)
     */
    private String supplyModel;

    /**
     * 需求来源
     */
    private String demandSource;

    /**
     * 工序输入ID
     */
    private String operationInputId;

    /**
     * 路径步骤ID
     */
    private String routingStepId;

    /**
     * 颜色
     */
    private String productColor;

    /**
     * 厚度
     */
    private BigDecimal productThickness;

    /**
     *  本厂编码
     */
    private String productFactoryCode;

    /**
     *  车型编码
     */
    private String vehicleModeCode;

    /**
     *  含替代料
     */
    private List<String> demandProductCodeList;
}
