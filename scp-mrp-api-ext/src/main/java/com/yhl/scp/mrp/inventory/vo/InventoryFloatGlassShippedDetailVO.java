package com.yhl.scp.mrp.inventory.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 * <code>InventoryFloatGlassShippedDetailVO</code>
 * <p>
 * 原片浮法已发运库存批次明细VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-30 20:31:57
 */
@ApiModel(value = "原片浮法已发运库存批次明细VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InventoryFloatGlassShippedDetailVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 129001803423845100L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @FieldInterpretation(value = "主键id")
    private String id;
    /**
     * 物品id
     */
    @ApiModelProperty(value = "物品id")
    @FieldInterpretation(value = "物品id")
    private String productId;
    /**
     * 物品编码
     */
    @ApiModelProperty(value = "物品编码")
    @FieldInterpretation(value = "物品编码")
    @ExcelProperty(value = "物品编码")
    private String productCode;
    /**
     * 物品规格
     */
    @ApiModelProperty(value = "物品规格")
    @FieldInterpretation(value = "物品规格")
    private String productSpec;
    /**
     * 库存点编码
     */
    @ApiModelProperty(value = "库存点编码")
    @FieldInterpretation(value = "库存点编码")
    private String stockPointCode;
    /**
     * 等级
     */
    @ApiModelProperty(value = "等级")
    @FieldInterpretation(value = "等级")
    private String level;
    /**
     * 批次等级代码
     */
    @ApiModelProperty(value = "批次等级代码")
    @FieldInterpretation(value = "批次等级代码")
    private String lotLevelCode;
    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    @FieldInterpretation(value = "批次号")
    private String lotNumber;
    /**
     * 片/箱
     */
    @ApiModelProperty(value = "片/箱")
    @FieldInterpretation(value = "片/箱")
    private BigDecimal perBox;
    /**
     * 箱数
     */
    @ApiModelProperty(value = "箱数")
    @FieldInterpretation(value = "箱数")
    private BigDecimal box;
    /**
     * 实发片数
     */
    @ApiModelProperty(value = "实发片数")
    @FieldInterpretation(value = "实发片数")
    private BigDecimal actualSentQuantity;
    /**
     * 面积
     */
    @ApiModelProperty(value = "面积")
    @FieldInterpretation(value = "面积")
    private BigDecimal area;
    /**
     * 吨数
     */
    @ApiModelProperty(value = "吨数")
    @FieldInterpretation(value = "吨数")
    private BigDecimal weight;
    /**
     * 包装方式
     */
    @ApiModelProperty(value = "包装方式")
    @FieldInterpretation(value = "包装方式")
    private String packageType;
    /**
     * PO
     */
    @ApiModelProperty(value = "PO")
    @FieldInterpretation(value = "PO")
    private String po;
    /**
     * 柜号
     */
    @ApiModelProperty(value = "柜号")
    @FieldInterpretation(value = "柜号")
    private String containerNumber;
    /**
     * 厂家
     */
    @ApiModelProperty(value = "厂家")
    @FieldInterpretation(value = "厂家")
    private String manufacturer;
    /**
     * 发货方式
     */
    @ApiModelProperty(value = "发货方式")
    @FieldInterpretation(value = "发货方式")
    private String deliveryMethod;
    /**
     * 发货时间
     */
    @ApiModelProperty(value = "发货时间")
    @FieldInterpretation(value = "发货时间")
    @ExcelProperty(value = "发货时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date deliveryTime;
    /**
     * 要求到港时间
     */
    @ApiModelProperty(value = "要求到港时间")
    @FieldInterpretation(value = "要求到港时间")
    @ExcelProperty(value = "要求到港时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date requiredArrivalTime;
    /**
     * 预计到港时间
     */
    @ApiModelProperty(value = "预计到港时间")
    @FieldInterpretation(value = "预计到港时间")
    private Date estimatedArrivalTime;
    /**
     * 实际到港时间
     */
    @ApiModelProperty(value = "实际到港时间")
    @FieldInterpretation(value = "实际到港时间")
    private Date actualArrivalTime;
    /**
     * 超期时间
     */
    @ApiModelProperty(value = "超期时间")
    @FieldInterpretation(value = "超期时间")
    private Date overdueTime;
    /**
     * 送柜时间
     */
    @ApiModelProperty(value = "送柜时间")
    @FieldInterpretation(value = "送柜时间")
    private Date containerDeliveryTime;
    /**
     * 港口
     */
    @ApiModelProperty(value = "港口")
    @FieldInterpretation(value = "港口")
    private String portName;
    /**
     * 承运商
     */
    @ApiModelProperty(value = "承运商")
    @FieldInterpretation(value = "承运商")
    private String carrier;
    /**
     * 切裁率
     */
    @ApiModelProperty(value = "切裁率")
    @FieldInterpretation(value = "切裁率")
    private BigDecimal cuttingRate;
    /**
     * 厚度
     */
    @ApiModelProperty(value = "厚度")
    @FieldInterpretation(value = "厚度")
    private BigDecimal productThickness;
    /**
     * 颜色
     */
    @ApiModelProperty(value = "颜色")
    @FieldInterpretation(value = "颜色")
    private String productColor;
    /**
     * 分类说明
     */
    @ApiModelProperty(value = "分类说明")
    @FieldInterpretation(value = "分类说明")
    private String classifyDesc;
    /**
     * 数据记录ID
     */
    @ApiModelProperty(value = "数据记录ID")
    @FieldInterpretation(value = "数据记录ID")
    private String planNumber;

    /**
     * 行记录id
     */
    @ApiModelProperty(value = "行记录id")
    @FieldInterpretation(value = "数据记录ID")
    private String lineId;
    /**
     * 提单号
     */
    @ApiModelProperty(value = "提单号")
    @FieldInterpretation(value = "提单号")
    private String billNo;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @FieldInterpretation(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    @FieldInterpretation(value = "是否生效（生效/失效）")
    private String enabled;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @FieldInterpretation(value = "创建人")
    private String creator;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @FieldInterpretation(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @FieldInterpretation(value = "修改人")
    private String modifier;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @FieldInterpretation(value = "修改时间")
    private Date modifyTime;
    /**
     * 发运方式
     */
    @ApiModelProperty(value = "发运方式")
    @FieldInterpretation(value = "发运方式")
    private String shipmentMethod;
    /**
     * 发运方式
     */
    @ApiModelProperty(value = "库存点名称")
    @FieldInterpretation(value = "库存点名称")
    private String stockPointName;
    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间")
    @FieldInterpretation(value = "最后更新时间")
    private Date lastUpdateDate;
    /**
     * 是否入库
     */
    @ApiModelProperty(value = "是否入库")
    @FieldInterpretation(value = "是否入库")
    private String storageFlag;
    /**
     * 价格
     */
    @ApiModelProperty(value = "价格")
    @FieldInterpretation(value = "价格")
    private BigDecimal orderPrice;
    /**
     * 采购单号
     */
    @ApiModelProperty(value = "采购单号")
    @FieldInterpretation(value = "采购单号")
    private String poNumber;
    /**
     * 调拨单号
     */
    @ApiModelProperty(value = "调拨单号")
    @FieldInterpretation(value = "调拨单号")
    private String allocateNo;

    /**
     * 物料长度
     */
    @ApiModelProperty(value = "物料长度")
    @FieldInterpretation(value = "物料长度")
    private BigDecimal productLength;
    /**
     * 物料宽度
     */
    @ApiModelProperty(value = "物料宽度")
    @FieldInterpretation(value = "物料宽度")
    private BigDecimal productWidth;
    @Override
    public void clean() {

    }

}
