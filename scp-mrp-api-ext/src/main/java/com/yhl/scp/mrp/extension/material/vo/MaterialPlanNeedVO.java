package com.yhl.scp.mrp.extension.material.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>MaterialPlanNeedVO</code>
 * <p>
 * 要货计划VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-31 21:11:00
 */
@ApiModel(value = "要货计划VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MaterialPlanNeedVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 651871142964393756L;

    /**
     * 库存推移id
     */
    @ApiModelProperty(value = "库存推移id")
    @FieldInterpretation(value = "库存推移id")
    private String materialPlanInventoryShiftId;
    /**
     * 要货计划单号
     */
    @ApiModelProperty(value = "要货计划单号")
    @FieldInterpretation(value = "要货计划单号")
    private String materialPlanNeedNo;
    /**
     * 上版数量
     */
    @ApiModelProperty(value = "上版数量")
    @FieldInterpretation(value = "上版数量")
    private BigDecimal preVersionNeedQuantity;
    /**
     * 要货数量
     */
    @ApiModelProperty(value = "要货数量")
    @FieldInterpretation(value = "要货数量")
    private BigDecimal needQuantity;
    /**
     * 要货日期
     */
    @ApiModelProperty(value = "要货日期")
    @FieldInterpretation(value = "要货日期")
    private Date needDate;
    /**
     * 材料需求发布日期
     */
    @ApiModelProperty(value = "需求发布日期")
    @FieldInterpretation(value = "需求发布日期")
    private Date requirementReleaseDate;
    /**
     * 采购单号
     */
    @ApiModelProperty(value = "采购单号")
    @FieldInterpretation(value = "采购单号")
    private String purchaseOrderCode;
    /**
     * 采购单行号
     */
    @ApiModelProperty(value = "采购单行号")
    @FieldInterpretation(value = "采购单行号")
    private String purchaseOrderLineCode;
    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @FieldInterpretation(value = "供应商编码")
    private String supplierCode;
    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @FieldInterpretation(value = "供应商名称")
    private String supplierName;
    /**
     * 承诺到货时间
     */
    @ApiModelProperty(value = "承诺到货时间")
    @FieldInterpretation(value = "承诺到货时间")
    private Date expectedArrivalTime;
    /**
     * 承诺到货数量
     */
    @ApiModelProperty(value = "承诺到货数量")
    @FieldInterpretation(value = "承诺到货数量")
    private BigDecimal expectedArrivalQuantity;
    /**
     * 剩余未发货数量
     */
    @ApiModelProperty(value = "剩余未发货数量")
    @FieldInterpretation(value = "剩余未发货数量")
    private BigDecimal unshippedQuantity;
    /**
     * 供应数量
     */
    @ApiModelProperty(value = "供应数量")
    @FieldInterpretation(value = "供应数量")
    private BigDecimal supplyQuantity;
    /**
     * 订单数量
     */
    @ApiModelProperty(value = "订单数量")
    @FieldInterpretation(value = "订单数量")
    private BigDecimal orderQuantity;
    /**
     * 发布状态
     */
    @ApiModelProperty(value = "发布状态")
    @FieldInterpretation(value = "发布状态")
    private String publishStatus;
    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    @FieldInterpretation(value = "发布时间")
    private Date publishTime;
    /**
     * 物品大类（物料分类）
     */
    @ApiModelProperty(value = "物品大类")
    @FieldInterpretation(value = "物品大类")
    private String productCategory;
    /**
     * 供应类型（物料属性）
     */
    @ApiModelProperty(value = "供应类型")
    @FieldInterpretation(value = "供应类型")
    private String supplyType;
    /**
     * 库存点编码
     */
    @ApiModelProperty(value = "库存点编码")
    @FieldInterpretation(value = "库存点编码")
    private String stockPointCode;
    /**
     * 库存点名称
     */
    @ApiModelProperty(value = "库存点名称")
    @FieldInterpretation(value = "库存点名称")
    private String stockPointName;
    /**
     * 物品id
     */
    @ApiModelProperty(value = "物品id")
    @FieldInterpretation(value = "物品id")
    private String productId;
    /**
     * 物品代码
     */
    @ApiModelProperty(value = "物品代码")
    @FieldInterpretation(value = "物品代码")
    private String productCode;
    /**
     * 物品名称
     */
    @ApiModelProperty(value = "物品名称")
    @FieldInterpretation(value = "物品名称")
    private String productName;
    /**
     * 计量单位
     */
    @ApiModelProperty(value = "计量单位")
    @FieldInterpretation(value = "计量单位")
    private String measurementUnit;
    /**
     * 厚度
     */
    @ApiModelProperty(value = "厚度")
    @ExcelProperty(value = "厚度")
    private BigDecimal productThickness;
    /**
     * 颜色
     */
    @ApiModelProperty(value = "颜色")
    @ExcelProperty(value = "颜色")
    private String productColor;
    /**
     * 要货计划锁定期
     */
    @ApiModelProperty(value = "要货计划锁定期")
    @FieldInterpretation(value = "要货计划锁定期")
    private BigDecimal requestCargoPlanLockDay;
    /**
     * 制造订单号
     */
    @ApiModelProperty(value = "制造订单号")
    @FieldInterpretation(value = "制造订单号")
    private String orderNo;
    /**
     * 组织ID
     */
    @ApiModelProperty(value = "组织ID")
    @FieldInterpretation(value = "组织ID")
    private String organizeId;
    /**
     * 发布人
     */
    @ApiModelProperty(value = "发布人")
    @FieldInterpretation(value = "发布人")
    private String publishUser;
    /**
     * 父级id（拆单）
     */
    @ApiModelProperty(value = "父级id（拆单）")
    @FieldInterpretation(value = "父级id（拆单）")
    private String parentId;
    /**
     * 推移版本代码
     */
    @ApiModelProperty(value = "推移版本代码")
    @FieldInterpretation(value = "推移版本代码")
    private String inventoryShiftVersionCode;
    /**
     * 数据来源
     */
    @ApiModelProperty(value = "数据来源")
    @FieldInterpretation(value = "数据来源")
    private String dataSource;

    @Override
    public void clean() {

    }

}
