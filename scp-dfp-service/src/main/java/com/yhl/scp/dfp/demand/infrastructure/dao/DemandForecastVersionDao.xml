<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.demand.infrastructure.dao.DemandForecastVersionDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.demand.infrastructure.po.DemandForecastVersionPO">
        <!--@Table fdp_demand_forecast_version-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="plan_period" jdbcType="VARCHAR" property="planPeriod"/>
        <result column="plan_horizon" jdbcType="INTEGER" property="planHorizon"/>
        <result column="plan_granularity" jdbcType="VARCHAR" property="planGranularity"/>
        <result column="version_code" jdbcType="VARCHAR" property="versionCode"/>
        <result column="parent_version_id" jdbcType="VARCHAR" property="parentVersionId"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="rolling_version_id" jdbcType="VARCHAR" property="rollingVersionId"/>
        <result column="algorithm_version_id" jdbcType="VARCHAR" property="algorithmVersionId"/>
        <result column="generate_type" jdbcType="VARCHAR" property="generateType"/>
        <result column="version_status" jdbcType="VARCHAR" property="versionStatus"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.dfp.demand.vo.DemandForecastVersionVO">
        <result column="oem_name" jdbcType="VARCHAR" property="oemName"/>
        <result column="rolling_version_code" jdbcType="VARCHAR" property="rollingVersionCode"/>
        <result column="algorithm_version_code" jdbcType="VARCHAR" property="algorithmVersionCode"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,plan_period,plan_horizon,plan_granularity,version_code,parent_version_id,oem_code,rolling_version_id,
        algorithm_version_id,generate_type,version_status,remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <include refid="Base_Column_List"/>,oem_name,rolling_version_code,algorithm_version_code
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.versionIdList != null and params.versionIdList.size() > 0">
                and id in
                <foreach collection="params.versionIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.planPeriod != null and params.planPeriod != ''">
                and plan_period = #{params.planPeriod,jdbcType=VARCHAR}
            </if>
            <if test="params.planHorizon != null">
                and plan_horizon = #{params.planHorizon,jdbcType=INTEGER}
            </if>
            <if test="params.planGranularity != null and params.planGranularity != ''">
                and plan_granularity = #{params.planGranularity,jdbcType=VARCHAR}
            </if>
            <if test="params.versionCode != null and params.versionCode != ''">
                and version_code = #{params.versionCode,jdbcType=VARCHAR}
            </if>
            <if test="params.parentVersionId != null and params.parentVersionId != ''">
                and parent_version_id = #{params.parentVersionId,jdbcType=VARCHAR}
            </if>
            <if test="params.oemCode != null and params.oemCode != ''">
                and oem_code = #{params.oemCode,jdbcType=VARCHAR}
            </if>
            <if test="params.oemCodes != null and params.oemCodes.size() > 0">
                and oem_code in
                <foreach collection="params.oemCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.rollingVersionId != null and params.rollingVersionId != ''">
                and rolling_version_id = #{params.rollingVersionId,jdbcType=VARCHAR}
            </if>
            <if test="params.algorithmVersionId != null and params.algorithmVersionId != ''">
                and algorithm_version_id = #{params.algorithmVersionId,jdbcType=VARCHAR}
            </if>
            <if test="params.generateType != null and params.generateType != ''">
                and generate_type = #{params.generateType,jdbcType=VARCHAR}
            </if>
            <if test="params.versionStatus != null and params.versionStatus != ''">
                and version_status = #{params.versionStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.parentVersionIdList != null and params.parentVersionIdList.size() > 0">
                and parent_version_id in
                <foreach collection="params.parentVersionIdList" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.algorithmVersionId != null">
                and algorithm_version_id = #{params.algorithmVersionId,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_demand_forecast_version
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_demand_forecast_version
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_fdp_demand_forecast_version
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_demand_forecast_version
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_fdp_demand_forecast_version
        <include refid="Base_Where_Condition"/>
    </select>
    <select id="selectLatestVersionId" resultType="java.lang.String">
        select id
        from fdp_demand_forecast_version a
        where exists(select id
                     from fdp_demand_forecast_version b
                     where b.parent_version_id is null
                       and b.id = a.parent_version_id)
        order by create_time desc limit 1
    </select>
    <select id="selectForecastVersion" resultMap="VOResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_demand_forecast_version
        where version_code = #{versionCode,jdbcType=VARCHAR}
        and parent_version_id is not null
        order by create_time desc
        limit 1
    </select>
    <select id="selectLatestVersionCode" resultType="java.lang.String">
        select version_code
        from fdp_demand_forecast_version a
        where exists(select id
                     from fdp_demand_forecast_version b
                     where b.parent_version_id is null
                       and b.id = a.parent_version_id)
        order by create_time desc limit 1
    </select>
    <select id="selectVersionAndOemCode" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_fdp_demand_forecast_version
        <where>
            <if test="versionCode != null and versionCode != ''">
                and version_code = #{versionCode,jdbcType=VARCHAR}
            </if>
            and parent_version_id is not null
        </where>
        order by create_time desc
    </select>
    <select id="selectDemandForecastVersion" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_demand_forecast_version a
        where exists(select id from fdp_demand_forecast_version b where b.parent_version_id is null and b.id =
        a.parent_version_id)
        order by create_time desc
    </select>
    <select id="selectNewVersionCode" resultType="java.lang.String">
        select version_code
        from fdp_demand_forecast_version
        <where>
            <if test="params.planPeriod != null and params.planPeriod != ''">
                and plan_period = #{params.planPeriod,jdbcType=VARCHAR}
            </if>
            <if test="params.oemCodeList != null and params.oemCodeList.size() > 0">
                and oem_code in
                <foreach collection="params.oemCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
        order by create_time desc
        limit 1
    </select>
    <select id="selectOneLevelDetail" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_demand_forecast_version
        where
        plan_period = #{planPeriod,jdbcType=VARCHAR}
        and parent_version_id is null
    </select>
    <select id="selectTwoLevelDetail" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_demand_forecast_version
        where
        version_code = #{newVersionCode,jdbcType=VARCHAR}
        and oem_code is null and parent_version_id is not null limit 1
    </select>
    <select id="selectAllVersion" resultMap="BaseResultMap">
        SELECT
			a.id,
			a.plan_period,
			a.plan_horizon,
			a.plan_granularity,
			a.version_code,
			a.parent_version_id,
			a.oem_code,
			a.rolling_version_id,
			a.algorithm_version_id,
			a.generate_type,
			a.version_status,
			a.remark,
			a.enabled,
			a.creator,
			a.create_time,
			a.modifier,
			a.modify_time,
			a.version_value 
		FROM
			fdp_demand_forecast_version a
		WHERE
			EXISTS ( SELECT id FROM fdp_demand_forecast_version b WHERE b.parent_version_id IS NULL AND b.id = a.parent_version_id ) 
		ORDER BY
			a.create_time DESC
    </select>

    <select id="selectDistinctVersionCodes" resultType="java.util.Map">
        select distinct version_code as versionCode, id
        from fdp_demand_forecast_version
        where 1 = 1
          and version_status = #{publishStatus,jdbcType=VARCHAR}
          and version_code is not null
          and oem_code is null
        order by version_code desc
    </select>

    <select id="selectPublishVersionDetail" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_demand_forecast_version
        where parent_version_id is not null and version_status = #{publishStatus,jdbcType=VARCHAR}
    </select>

    <select id="selectOemCodeListByVersionId" resultType="java.lang.String">
        SELECT DISTINCT oem_code
        FROM fdp_demand_forecast_version
        WHERE parent_version_id = #{versionId,jdbcType=VARCHAR}
    </select>
    <select id="selectMaxVersionCode" resultType="java.lang.String">
        SELECT
        MAX(version_code)
        FROM
        fdp_demand_forecast_version
        WHERE
        plan_period = #{planPeriod,jdbcType=VARCHAR}
        and oem_code in
        <foreach collection="oemCodeList" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="selectFirstVersionInfoByPlanPeriod" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_demand_forecast_version
        where plan_period = #{planPeriod,jdbcType=VARCHAR}
        and oem_code is null and parent_version_id is null
        order by create_time desc
        limit 1
    </select>
    <select id="selectSecondVersionInfoByVersionCode"
            resultType="com.yhl.scp.dfp.demand.infrastructure.po.DemandForecastVersionPO">
        select
        <include refid="Base_Column_List"/>
        from fdp_demand_forecast_version a
        where version_code = #{newVersionCode,jdbcType=VARCHAR}
        and exists(select id from fdp_demand_forecast_version b where b.parent_version_id is null and b.id =
        a.parent_version_id)
        order by create_time desc
        limit 1
    </select>
    <select id="selectLastVersionCodes" resultType="java.util.Map">
        select distinct version_code as versionCode, id
        from fdp_demand_forecast_version
        where version_code is not null and oem_code is null
        and plan_period in
        <foreach collection="planPeriods" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        order by version_code desc
    </select>
    <select id="selectLatestSecondVersionByPlanPeriod" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_demand_forecast_version
        where plan_period = #{planPeriod,jdbcType=VARCHAR}
        and oem_code is null
        and parent_version_id is not null
        order by create_time desc limit 1
    </select>
    <select id="selectLatestVersionIdIgnoreCurrent" resultType="java.lang.String">
        select id
        from fdp_demand_forecast_version a
        where id != #{currentVersionId,jdbcType=VARCHAR}
          and exists (select id
            from fdp_demand_forecast_version b
            where b.parent_version_id is null
          and b.id = a.parent_version_id)
        order by create_time desc limit 1
    </select>
    <select id="selectVersionsNewByParams" resultType="com.yhl.scp.dfp.demand.vo.DemandForecastVersionVO">
        SELECT
            t1.id,
            t1.version_code
        FROM
            fdp_demand_forecast_version t1
        WHERE
            t1.version_status = #{params.versionStatus,jdbcType=INTEGER}
          and t1.oem_code is null
        order by t1.modify_time desc
            limit 1
    </select>
    <select id="selectByRollingVersionId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select id, parent_version_id,rolling_version_id, version_code
        from fdp_demand_forecast_version
        where parent_version_id is not null
        and rolling_version_id = #{rollingVersionId,jdbcType=VARCHAR}
    </select>

    <select id="selectByLikeVersionCodeSerialNumber" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_demand_forecast_version
        where parent_version_id is not null
        and oem_code is null
        and version_code like concat('%', #{versionCodeSerialNumber,jdbcType=VARCHAR})
    </select>

    <select id="selectLatestVersionIdByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_demand_forecast_version
        where parent_version_id is not null
        and oem_code is null
        <if test="params.versionStatus != null">
            and version_status = #{versionStatus,jdbcType=VARCHAR}
        </if>
        order by create_time desc
        limit 1
    </select>

    <select id="selectLastVersionByPlanPeriod" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_demand_forecast_version
        where parent_version_id is not null
        <if test="planPeriod != null and planPeriod != ''">
            and plan_period = #{planPeriod,jdbcType=VARCHAR}
        </if>
        order by create_time desc
        limit 1
    </select>

    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.demand.infrastructure.po.DemandForecastVersionPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into fdp_demand_forecast_version(
        id,
        plan_period,
        plan_horizon,
        plan_granularity,
        version_code,
        parent_version_id,
        oem_code,
        rolling_version_id,
        algorithm_version_id,
        generate_type,
        version_status,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{planPeriod,jdbcType=VARCHAR},
        #{planHorizon,jdbcType=INTEGER},
        #{planGranularity,jdbcType=VARCHAR},
        #{versionCode,jdbcType=VARCHAR},
        #{parentVersionId,jdbcType=VARCHAR},
        #{oemCode,jdbcType=VARCHAR},
        #{rollingVersionId,jdbcType=VARCHAR},
        #{algorithmVersionId,jdbcType=VARCHAR},
        #{generateType,jdbcType=VARCHAR},
        #{versionStatus,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.dfp.demand.infrastructure.po.DemandForecastVersionPO">
        insert into fdp_demand_forecast_version(id,
                                                plan_period,
                                                plan_horizon,
                                                plan_granularity,
                                                version_code,
                                                parent_version_id,
                                                oem_code,
                                                rolling_version_id,
                                                algorithm_version_id,
                                                generate_type,
                                                version_status,
                                                remark,
                                                enabled,
                                                creator,
                                                create_time,
                                                modifier,
                                                modify_time,
                                                version_value)
        values (#{id,jdbcType=VARCHAR},
                #{planPeriod,jdbcType=VARCHAR},
                #{planHorizon,jdbcType=INTEGER},
                #{planGranularity,jdbcType=VARCHAR},
                #{versionCode,jdbcType=VARCHAR},
                #{parentVersionId,jdbcType=VARCHAR},
                #{oemCode,jdbcType=VARCHAR},
                #{rollingVersionId,jdbcType=VARCHAR},
                #{algorithmVersionId,jdbcType=VARCHAR},
                #{generateType,jdbcType=VARCHAR},
                #{versionStatus,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into fdp_demand_forecast_version(
        id,
        plan_period,
        plan_horizon,
        plan_granularity,
        version_code,
        parent_version_id,
        oem_code,
        rolling_version_id,
        algorithm_version_id,
        generate_type,
        version_status,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.planPeriod,jdbcType=VARCHAR},
            #{entity.planHorizon,jdbcType=INTEGER},
            #{entity.planGranularity,jdbcType=VARCHAR},
            #{entity.versionCode,jdbcType=VARCHAR},
            #{entity.parentVersionId,jdbcType=VARCHAR},
            #{entity.oemCode,jdbcType=VARCHAR},
            #{entity.rollingVersionId,jdbcType=VARCHAR},
            #{entity.algorithmVersionId,jdbcType=VARCHAR},
            #{entity.generateType,jdbcType=VARCHAR},
            #{entity.versionStatus,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into fdp_demand_forecast_version(
        id,
        plan_period,
        plan_horizon,
        plan_granularity,
        version_code,
        parent_version_id,
        oem_code,
        rolling_version_id,
        algorithm_version_id,
        generate_type,
        version_status,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.planPeriod,jdbcType=VARCHAR},
            #{entity.planHorizon,jdbcType=INTEGER},
            #{entity.planGranularity,jdbcType=VARCHAR},
            #{entity.versionCode,jdbcType=VARCHAR},
            #{entity.parentVersionId,jdbcType=VARCHAR},
            #{entity.oemCode,jdbcType=VARCHAR},
            #{entity.rollingVersionId,jdbcType=VARCHAR},
            #{entity.algorithmVersionId,jdbcType=VARCHAR},
            #{entity.generateType,jdbcType=VARCHAR},
            #{entity.versionStatus,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.demand.infrastructure.po.DemandForecastVersionPO">
        update fdp_demand_forecast_version
        set plan_period          = #{planPeriod,jdbcType=VARCHAR},
            plan_horizon         = #{planHorizon,jdbcType=INTEGER},
            plan_granularity     = #{planGranularity,jdbcType=VARCHAR},
            version_code         = #{versionCode,jdbcType=VARCHAR},
            parent_version_id    = #{parentVersionId,jdbcType=VARCHAR},
            oem_code             = #{oemCode,jdbcType=VARCHAR},
            rolling_version_id   = #{rollingVersionId,jdbcType=VARCHAR},
            algorithm_version_id = #{algorithmVersionId,jdbcType=VARCHAR},
            generate_type        = #{generateType,jdbcType=VARCHAR},
            version_status       = #{versionStatus,jdbcType=VARCHAR},
            remark               = #{remark,jdbcType=VARCHAR},
            enabled              = #{enabled,jdbcType=VARCHAR},
            modifier             = #{modifier,jdbcType=VARCHAR},
            modify_time          = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER};
        update fdp_demand_forecast_version
        set version_value = version_value + 1
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.dfp.demand.infrastructure.po.DemandForecastVersionPO">
        update fdp_demand_forecast_version
        <set>
            <if test="item.planPeriod != null and item.planPeriod != ''">
                plan_period = #{item.planPeriod,jdbcType=VARCHAR},
            </if>
            <if test="item.planHorizon != null">
                plan_horizon = #{item.planHorizon,jdbcType=INTEGER},
            </if>
            <if test="item.planGranularity != null and item.planGranularity != ''">
                plan_granularity = #{item.planGranularity,jdbcType=VARCHAR},
            </if>
            <if test="item.versionCode != null and item.versionCode != ''">
                version_code = #{item.versionCode,jdbcType=VARCHAR},
            </if>
            <if test="item.parentVersionId != null and item.parentVersionId != ''">
                parent_version_id = #{item.parentVersionId,jdbcType=VARCHAR},
            </if>
            <if test="item.oemCode != null and item.oemCode != ''">
                oem_code = #{item.oemCode,jdbcType=VARCHAR},
            </if>
            <if test="item.rollingVersionId != null and item.rollingVersionId != ''">
                rolling_version_id = #{item.rollingVersionId,jdbcType=VARCHAR},
            </if>
            <if test="item.algorithmVersionId != null and item.algorithmVersionId != ''">
                algorithm_version_id = #{item.algorithmVersionId,jdbcType=VARCHAR},
            </if>
            <if test="item.generateType != null and item.generateType != ''">
                generate_type = #{item.generateType,jdbcType=VARCHAR},
            </if>
            <if test="item.versionStatus != null and item.versionStatus != ''">
                version_status = #{item.versionStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
        and version_value = #{item.versionValue,jdbcType=INTEGER};
        update fdp_demand_forecast_version set
        version_value = version_value + 1
        where id = #{item.id,jdbcType=VARCHAR}
        and version_value = #{item.versionValue,jdbcType=INTEGER}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update fdp_demand_forecast_version
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="plan_period = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.planPeriod,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="plan_horizon = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.planHorizon,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="plan_granularity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.planGranularity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="parent_version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.parentVersionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="oem_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oemCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="rolling_version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.rollingVersionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="algorithm_version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.algorithmVersionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="generate_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.generateType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
        and version_value in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.versionValue,jdbcType=INTEGER}
        </foreach>;
        update fdp_demand_forecast_version set
        version_value = version_value + 1
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
        and version_value in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update fdp_demand_forecast_version
            <set>
                <if test="item.planPeriod != null and item.planPeriod != ''">
                    plan_period = #{item.planPeriod,jdbcType=VARCHAR},
                </if>
                <if test="item.planHorizon != null">
                    plan_horizon = #{item.planHorizon,jdbcType=INTEGER},
                </if>
                <if test="item.planGranularity != null and item.planGranularity != ''">
                    plan_granularity = #{item.planGranularity,jdbcType=VARCHAR},
                </if>
                <if test="item.versionCode != null and item.versionCode != ''">
                    version_code = #{item.versionCode,jdbcType=VARCHAR},
                </if>
                <if test="item.parentVersionId != null and item.parentVersionId != ''">
                    parent_version_id = #{item.parentVersionId,jdbcType=VARCHAR},
                </if>
                <if test="item.oemCode != null and item.oemCode != ''">
                    oem_code = #{item.oemCode,jdbcType=VARCHAR},
                </if>
                <if test="item.rollingVersionId != null and item.rollingVersionId != ''">
                    rolling_version_id = #{item.rollingVersionId,jdbcType=VARCHAR},
                </if>
                <if test="item.algorithmVersionId != null and item.algorithmVersionId != ''">
                    algorithm_version_id = #{item.algorithmVersionId,jdbcType=VARCHAR},
                </if>
                <if test="item.generateType != null and item.generateType != ''">
                    generate_type = #{item.generateType,jdbcType=VARCHAR},
                </if>
                <if test="item.versionStatus != null and item.versionStatus != ''">
                    version_status = #{item.versionStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER};
            update fdp_demand_forecast_version set
            version_value = version_value + 1
            where id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <update id="publishVersion" parameterType="com.yhl.scp.dfp.demand.infrastructure.po.DemandForecastVersionPO">
        update fdp_demand_forecast_version
        set version_status = #{versionStatus,jdbcType=VARCHAR},
            modifier       = #{modifier,jdbcType=VARCHAR},
            modify_time    = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
           or parent_version_id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from fdp_demand_forecast_version
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from fdp_demand_forecast_version where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <!-- 批量id+版本删除 -->
    <delete id="deleteBatchVersion" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            delete from fdp_demand_forecast_version where
            id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </delete>
    
    <select id="selectMaxDemandForecastVersionId" resultType="java.lang.String">
        select
        id
        from fdp_demand_forecast_version a
        where exists(select id from fdp_demand_forecast_version b where b.parent_version_id is null and b.id =
        a.parent_version_id)
        order by create_time desc
    	limit 1
    </select>
</mapper>