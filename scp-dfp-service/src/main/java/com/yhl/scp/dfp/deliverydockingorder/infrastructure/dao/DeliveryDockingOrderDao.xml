<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.deliverydockingorder.infrastructure.dao.DeliveryDockingOrderDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.deliverydockingorder.infrastructure.po.DeliveryDockingOrderPO">
        <!--@Table fdp_delivery_docking_order-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="delivery_docking_number" jdbcType="VARCHAR" property="deliveryDockingNumber"/>
        <result column="customer_code" jdbcType="VARCHAR" property="customerCode"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="customer_address" jdbcType="VARCHAR" property="customerAddress"/>
        <result column="transport_mode" jdbcType="VARCHAR" property="transportMode"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="mes_tally_number" jdbcType="VARCHAR" property="mesTallyNumber"/>
        <result column="oem_address" jdbcType="VARCHAR" property="oemAddress"/>
        <result column="harvest_address" jdbcType="VARCHAR" property="harvestAddress"/>
        <result column="transfer_warehouse" jdbcType="VARCHAR" property="transferWarehouse"/>
        <result column="customer" jdbcType="VARCHAR" property="customer"/>
        <result column="line_code" jdbcType="VARCHAR" property="lineCode"/>
        <result column="oem_name" jdbcType="VARCHAR" property="oemName"/>
        <result column="kid" jdbcType="VARCHAR" property="kid"/>
        <result column="cabinet_type" jdbcType="VARCHAR" property="cabinetType"/>
        <result column="container_number" jdbcType="VARCHAR" property="containerNumber"/>
        <result column="vehicle_length" jdbcType="VARCHAR" property="vehicleLength"/>
        <result column="delivery_transport_code" jdbcType="VARCHAR" property="deliveryTransportCode"/>
        <result column="expected_arrive_time" jdbcType="TIMESTAMP" property="expectedArriveTime"/>
        <result column="exit_bar_code" jdbcType="VARCHAR" property="exitBarCode"/>
        <result column="mes_tally_sheet_status" jdbcType="VARCHAR" property="mesTallySheetStatus"/>
        <result column="transport_direction" jdbcType="VARCHAR" property="transportDirection"/>
        <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime"/>
        <result column="overage_report" jdbcType="VARCHAR" property="overageReport"/>
        <result column="business_type" jdbcType="VARCHAR" property="businessType"/>
        <result column="market_type" jdbcType="VARCHAR" property="marketType"/>
        <result column="version_code" jdbcType="VARCHAR" property="versionCode"/>
        <result column="data_sources_id" jdbcType="VARCHAR" property="dataSourcesId"/>
        <result column="data_sources" jdbcType="VARCHAR" property="dataSources"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="sth_auth_no" jdbcType="VARCHAR" property="sthAuthNo"/>
        <result column="sth_route_no" jdbcType="VARCHAR" property="sthRouteNo"/>
        <result column="mrn_no" jdbcType="VARCHAR" property="mrnNo"/>
        <result column="contact" jdbcType="VARCHAR" property="contact"/>
        <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone"/>
        <result column="std_contract" jdbcType="VARCHAR" property="stdContract"/>
        <result column="sth_carr_id" jdbcType="VARCHAR" property="sthCarrId"/>
        <result column="sth_freight_terms" jdbcType="VARCHAR" property="sthFreightTerms"/>
        <result column="sth_equip_no" jdbcType="VARCHAR" property="sthEquipNo"/>
        <result column="actual_arrive_time" jdbcType="TIMESTAMP" property="actualArriveTime"/>
        <result column="vehicle_number" jdbcType="INTEGER" property="vehicleNumber"/>
        <result column="vehicle_info" jdbcType="INTEGER" property="vehicleInfo"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.dfp.deliverydockingorder.vo.DeliveryDockingOrderVO">
        <!-- TODO -->
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,delivery_docking_number,customer_code,oem_code,customer_address,transport_mode,status,mes_tally_number,oem_address
        ,harvest_address,transfer_warehouse,customer,line_code,oem_name,kid,cabinet_type,container_number,vehicle_length
        ,delivery_transport_code,expected_arrive_time,exit_bar_code,mes_tally_sheet_status,transport_direction,delivery_time
        ,overage_report,business_type,market_type,version_code,data_sources_id,data_sources,version_value,remark,enabled,creator
        ,create_time,modifier,modify_time,sth_auth_no,sth_route_no,mrn_no,contact,contact_phone,std_contract,sth_carr_id
        ,sth_freight_terms,sth_equip_no,actual_arrive_time,vehicle_number, vehicle_info
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />,customer_name
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.deliveryDockingNumber != null and params.deliveryDockingNumber != ''">
                and delivery_docking_number = #{params.deliveryDockingNumber,jdbcType=VARCHAR}
            </if>
            <if test="params.deliveryDockingNumbers != null and params.deliveryDockingNumbers != ''">
                and delivery_docking_number in
                <foreach collection="params.deliveryDockingNumbers" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.customerCode != null and params.customerCode != ''">
                and customer_code = #{params.customerCode,jdbcType=VARCHAR}
            </if>
            <if test="params.oemCode != null and params.oemCode != ''">
                and oem_code = #{params.oemCode,jdbcType=VARCHAR}
            </if>
            <if test="params.customerAddress != null and params.customerAddress != ''">
                and customer_address = #{params.customerAddress,jdbcType=VARCHAR}
            </if>
            <if test="params.transportMode != null and params.transportMode != ''">
                and transport_mode = #{params.transportMode,jdbcType=VARCHAR}
            </if>
            <if test="params.status != null and params.status != ''">
                and status = #{params.status,jdbcType=VARCHAR}
            </if>
            <if test="params.mesTallyNumber != null and params.mesTallyNumber != ''">
                and mes_tally_number = #{params.mesTallyNumber,jdbcType=VARCHAR}
            </if>
            <if test="params.oemAddress != null and params.oemAddress != ''">
                and oem_address = #{params.oemAddress,jdbcType=VARCHAR}
            </if>
            <if test="params.harvestAddress != null and params.harvestAddress != ''">
                and harvest_address = #{params.harvestAddress,jdbcType=VARCHAR}
            </if>
            <if test="params.transferWarehouse != null and params.transferWarehouse != ''">
                and transfer_warehouse = #{params.transferWarehouse,jdbcType=VARCHAR}
            </if>
            <if test="params.customer != null and params.customer != ''">
                and customer = #{params.customer,jdbcType=VARCHAR}
            </if>
            <if test="params.lineCode != null and params.lineCode != ''">
                and line_code = #{params.lineCode,jdbcType=VARCHAR}
            </if>
            <if test="params.oemName != null and params.oemName != ''">
                and oem_name = #{params.oemName,jdbcType=VARCHAR}
            </if>
            <if test="params.kid != null and params.kid != ''">
                and kid = #{params.kid,jdbcType=VARCHAR}
            </if>
            <if test="params.cabinetType != null and params.cabinetType != ''">
                and cabinet_type = #{params.cabinetType,jdbcType=VARCHAR}
            </if>
            <if test="params.containerNumber != null and params.containerNumber != ''">
                and container_number = #{params.containerNumber,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleLength != null and params.vehicleLength != ''">
                and vehicle_length = #{params.vehicleLength,jdbcType=VARCHAR}
            </if>
            <if test="params.deliveryTransportCode != null and params.deliveryTransportCode != ''">
                and delivery_transport_code = #{params.deliveryTransportCode,jdbcType=VARCHAR}
            </if>
            <if test="params.expectedArriveTime != null">
                and expected_arrive_time = #{params.expectedArriveTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.exitBarCode != null and params.exitBarCode != ''">
                and exit_bar_code = #{params.exitBarCode,jdbcType=VARCHAR}
            </if>
            <if test="params.mesTallySheetStatus != null and params.mesTallySheetStatus != ''">
                and mes_tally_sheet_status = #{params.mesTallySheetStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.transportDirection != null and params.transportDirection != ''">
                and transport_direction = #{params.transportDirection,jdbcType=VARCHAR}
            </if>
            <if test="params.deliveryTime != null">
                and delivery_time = #{params.deliveryTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.overageReport != null and params.overageReport != ''">
                and overage_report = #{params.overageReport,jdbcType=VARCHAR}
            </if>
            <if test="params.businessType != null and params.businessType != ''">
                and business_type = #{params.businessType,jdbcType=VARCHAR}
            </if>
            <if test="params.marketType != null and params.marketType != ''">
                and market_type = #{params.marketType,jdbcType=VARCHAR}
            </if>
            <if test="params.versionCode != null and params.versionCode != ''">
                and version_code = #{params.versionCode,jdbcType=VARCHAR}
            </if>
            <if test="params.dataSourcesId != null and params.dataSourcesId != ''">
                and data_sources_id = #{params.dataSourcesId,jdbcType=VARCHAR}
            </if>
            <if test="params.dataSources != null and params.dataSources != ''">
                and data_sources = #{params.dataSources,jdbcType=VARCHAR}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.sthAuthNo != null and params.sthAuthNo != ''">
                and sth_auth_no = #{params.sthAuthNo,jdbcType=VARCHAR}
            </if>
            <if test="params.sthRouteNo != null and params.sthRouteNo != ''">
                and sth_route_no = #{params.sthRouteNo,jdbcType=VARCHAR}
            </if>
            <if test="params.mrnNo != null and params.mrnNo != ''">
                and mrn_no = #{params.mrnNo,jdbcType=VARCHAR}
            </if>
            <if test="params.contact != null and params.contact != ''">
                and contact = #{params.contact,jdbcType=VARCHAR}
            </if>
            <if test="params.contactPhone != null and params.contactPhone != ''">
                and contact_phone = #{params.contactPhone,jdbcType=VARCHAR}
            </if>
            <if test="params.stdContract != null and params.stdContract != ''">
                and std_contract = #{params.stdContract,jdbcType=VARCHAR}
            </if>
            <if test="params.sthCarrId != null and params.sthCarrId != ''">
                and sth_carr_id = #{params.sthCarrId,jdbcType=VARCHAR}
            </if>
            <if test="params.sthFreightTerms != null and params.sthFreightTerms != ''">
                and sth_freight_terms = #{params.sthFreightTerms,jdbcType=VARCHAR}
            </if>
            <if test="params.sthEquipNo != null and params.sthEquipNo != ''">
                and sth_equip_no = #{params.sthEquipNo,jdbcType=VARCHAR}
            </if>
            <if test="params.actualArriveTime != null">
                and actual_arrive_time = #{params.actualArriveTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.vehicleNumber != null">
                and vehicle_number = #{params.vehicleNumber,jdbcType=INTEGER}
            </if>
            <if test="params.vehicleInfo != null">
                and vehicle_info = #{params.vehicleInfo,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_delivery_docking_order
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_delivery_docking_order
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_fdp_delivery_docking_order
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_delivery_docking_order
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List" />
        from v_fdp_delivery_docking_order
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.deliverydockingorder.infrastructure.po.DeliveryDockingOrderPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into fdp_delivery_docking_order(
        id,
        delivery_docking_number,
        customer_code,
        oem_code,
        customer_address,
        transport_mode,
        status,
        mes_tally_number,
        oem_address,
        harvest_address,
        transfer_warehouse,
        customer,
        line_code,
        oem_name,
        kid,
        cabinet_type,
        container_number,
        vehicle_length,
        delivery_transport_code,
        expected_arrive_time,
        exit_bar_code,
        mes_tally_sheet_status,
        transport_direction,
        delivery_time,
        overage_report,
        business_type,
        market_type,
        version_code,
        data_sources_id,
        data_sources,
        version_value,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        sth_auth_no,
        sth_route_no,
        mrn_no,
        contact,
        contact_phone,
        std_contract,
        sth_carr_id,
        sth_freight_terms,
        sth_equip_no,
        actual_arrive_time,
        vehicle_number,
        vehicle_info)
        values (
        #{id,jdbcType=VARCHAR},
        #{deliveryDockingNumber,jdbcType=VARCHAR},
        #{customerCode,jdbcType=VARCHAR},
        #{oemCode,jdbcType=VARCHAR},
        #{customerAddress,jdbcType=VARCHAR},
        #{transportMode,jdbcType=VARCHAR},
        #{status,jdbcType=VARCHAR},
        #{mesTallyNumber,jdbcType=VARCHAR},
        #{oemAddress,jdbcType=VARCHAR},
        #{harvestAddress,jdbcType=VARCHAR},
        #{transferWarehouse,jdbcType=VARCHAR},
        #{customer,jdbcType=VARCHAR},
        #{lineCode,jdbcType=VARCHAR},
        #{oemName,jdbcType=VARCHAR},
        #{kid,jdbcType=VARCHAR},
        #{cabinetType,jdbcType=VARCHAR},
        #{containerNumber,jdbcType=VARCHAR},
        #{vehicleLength,jdbcType=VARCHAR},
        #{deliveryTransportCode,jdbcType=VARCHAR},
        #{expectedArriveTime,jdbcType=TIMESTAMP},
        #{exitBarCode,jdbcType=VARCHAR},
        #{mesTallySheetStatus,jdbcType=VARCHAR},
        #{transportDirection,jdbcType=VARCHAR},
        #{deliveryTime,jdbcType=TIMESTAMP},
        #{overageReport,jdbcType=VARCHAR},
        #{businessType,jdbcType=VARCHAR},
        #{marketType,jdbcType=VARCHAR},
        #{versionCode,jdbcType=VARCHAR},
        #{dataSourcesId,jdbcType=VARCHAR},
        #{dataSources,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{sthAuthNo,jdbcType=VARCHAR},
        #{sthRouteNo,jdbcType=VARCHAR},
        #{mrnNo,jdbcType=VARCHAR},
        #{contact,jdbcType=VARCHAR},
        #{contactPhone,jdbcType=VARCHAR},
        #{stdContract,jdbcType=VARCHAR},
        #{sthCarrId,jdbcType=VARCHAR},
        #{sthFreightTerms,jdbcType=VARCHAR},
        #{sthEquipNo,jdbcType=VARCHAR},
        #{actualArriveTime,jdbcType=TIMESTAMP},
        #{vehicleNumber,jdbcType=INTEGER},
        #{vehicleInfo,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.dfp.deliverydockingorder.infrastructure.po.DeliveryDockingOrderPO">
        insert into fdp_delivery_docking_order(
        id,
        delivery_docking_number,
        customer_code,
        oem_code,
        customer_address,
        transport_mode,
        status,
        mes_tally_number,
        oem_address,
        harvest_address,
        transfer_warehouse,
        customer,
        line_code,
        oem_name,
        kid,
        cabinet_type,
        container_number,
        vehicle_length,
        delivery_transport_code,
        expected_arrive_time,
        exit_bar_code,
        mes_tally_sheet_status,
        transport_direction,
        delivery_time,
        overage_report,
        business_type,
        market_type,
        version_code,
        data_sources_id,
        data_sources,
        version_value,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        sth_auth_no,
        sth_route_no,
        mrn_no,
        contact,
        contact_phone,
        std_contract,
        sth_carr_id,
        sth_freight_terms,
        sth_equip_no,
        actual_arrive_time,
        vehicle_number,
        vehicle_info)
        values (
        #{id,jdbcType=VARCHAR},
        #{deliveryDockingNumber,jdbcType=VARCHAR},
        #{customerCode,jdbcType=VARCHAR},
        #{oemCode,jdbcType=VARCHAR},
        #{customerAddress,jdbcType=VARCHAR},
        #{transportMode,jdbcType=VARCHAR},
        #{status,jdbcType=VARCHAR},
        #{mesTallyNumber,jdbcType=VARCHAR},
        #{oemAddress,jdbcType=VARCHAR},
        #{harvestAddress,jdbcType=VARCHAR},
        #{transferWarehouse,jdbcType=VARCHAR},
        #{customer,jdbcType=VARCHAR},
        #{lineCode,jdbcType=VARCHAR},
        #{oemName,jdbcType=VARCHAR},
        #{kid,jdbcType=VARCHAR},
        #{cabinetType,jdbcType=VARCHAR},
        #{containerNumber,jdbcType=VARCHAR},
        #{vehicleLength,jdbcType=VARCHAR},
        #{deliveryTransportCode,jdbcType=VARCHAR},
        #{expectedArriveTime,jdbcType=TIMESTAMP},
        #{exitBarCode,jdbcType=VARCHAR},
        #{mesTallySheetStatus,jdbcType=VARCHAR},
        #{transportDirection,jdbcType=VARCHAR},
        #{deliveryTime,jdbcType=TIMESTAMP},
        #{overageReport,jdbcType=VARCHAR},
        #{businessType,jdbcType=VARCHAR},
        #{marketType,jdbcType=VARCHAR},
        #{versionCode,jdbcType=VARCHAR},
        #{dataSourcesId,jdbcType=VARCHAR},
        #{dataSources,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{sthAuthNo,jdbcType=VARCHAR},
        #{sthRouteNo,jdbcType=VARCHAR},
        #{mrnNo,jdbcType=VARCHAR},
        #{contact,jdbcType=VARCHAR},
        #{contactPhone,jdbcType=VARCHAR},
        #{stdContract,jdbcType=VARCHAR},
        #{sthCarrId,jdbcType=VARCHAR},
        #{sthFreightTerms,jdbcType=VARCHAR},
        #{sthEquipNo,jdbcType=VARCHAR},
        #{actualArriveTime,jdbcType=TIMESTAMP},
        #{vehicleNumber,jdbcType=INTEGER},
        #{vehicleInfo,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into fdp_delivery_docking_order(
        id,
        delivery_docking_number,
        customer_code,
        oem_code,
        customer_address,
        transport_mode,
        status,
        mes_tally_number,
        oem_address,
        harvest_address,
        transfer_warehouse,
        customer,
        line_code,
        oem_name,
        kid,
        cabinet_type,
        container_number,
        vehicle_length,
        delivery_transport_code,
        expected_arrive_time,
        exit_bar_code,
        mes_tally_sheet_status,
        transport_direction,
        delivery_time,
        overage_report,
        business_type,
        market_type,
        version_code,
        data_sources_id,
        data_sources,
        version_value,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        sth_auth_no,
        sth_route_no,
        mrn_no,
        contact,
        contact_phone,
        std_contract,
        sth_carr_id,
        sth_freight_terms,
        sth_equip_no,
        actual_arrive_time,
        vehicle_number,
        vehicle_info)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.deliveryDockingNumber,jdbcType=VARCHAR},
        #{entity.customerCode,jdbcType=VARCHAR},
        #{entity.oemCode,jdbcType=VARCHAR},
        #{entity.customerAddress,jdbcType=VARCHAR},
        #{entity.transportMode,jdbcType=VARCHAR},
        #{entity.status,jdbcType=VARCHAR},
        #{entity.mesTallyNumber,jdbcType=VARCHAR},
        #{entity.oemAddress,jdbcType=VARCHAR},
        #{entity.harvestAddress,jdbcType=VARCHAR},
        #{entity.transferWarehouse,jdbcType=VARCHAR},
        #{entity.customer,jdbcType=VARCHAR},
        #{entity.lineCode,jdbcType=VARCHAR},
        #{entity.oemName,jdbcType=VARCHAR},
        #{entity.kid,jdbcType=VARCHAR},
        #{entity.cabinetType,jdbcType=VARCHAR},
        #{entity.containerNumber,jdbcType=VARCHAR},
        #{entity.vehicleLength,jdbcType=VARCHAR},
        #{entity.deliveryTransportCode,jdbcType=VARCHAR},
        #{entity.expectedArriveTime,jdbcType=TIMESTAMP},
        #{entity.exitBarCode,jdbcType=VARCHAR},
        #{entity.mesTallySheetStatus,jdbcType=VARCHAR},
        #{entity.transportDirection,jdbcType=VARCHAR},
        #{entity.deliveryTime,jdbcType=TIMESTAMP},
        #{entity.overageReport,jdbcType=VARCHAR},
        #{entity.businessType,jdbcType=VARCHAR},
        #{entity.marketType,jdbcType=VARCHAR},
        #{entity.versionCode,jdbcType=VARCHAR},
        #{entity.dataSourcesId,jdbcType=VARCHAR},
        #{entity.dataSources,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.sthAuthNo,jdbcType=VARCHAR},
        #{entity.sthRouteNo,jdbcType=VARCHAR},
        #{entity.mrnNo,jdbcType=VARCHAR},
        #{entity.contact,jdbcType=VARCHAR},
        #{entity.contactPhone,jdbcType=VARCHAR},
        #{entity.stdContract,jdbcType=VARCHAR},
        #{entity.sthCarrId,jdbcType=VARCHAR},
        #{entity.sthFreightTerms,jdbcType=VARCHAR},
        #{entity.sthEquipNo,jdbcType=VARCHAR}),
        #{entity.actualArriveTime,jdbcType=TIMESTAMP},
        #{entity.vehicleNumber,jdbcType=INTEGER},
        #{entity.vehicleInfo,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into fdp_delivery_docking_order(
        id,
        delivery_docking_number,
        customer_code,
        oem_code,
        customer_address,
        transport_mode,
        status,
        mes_tally_number,
        oem_address,
        harvest_address,
        transfer_warehouse,
        customer,
        line_code,
        oem_name,
        kid,
        cabinet_type,
        container_number,
        vehicle_length,
        delivery_transport_code,
        expected_arrive_time,
        exit_bar_code,
        mes_tally_sheet_status,
        transport_direction,
        delivery_time,
        overage_report,
        business_type,
        market_type,
        version_code,
        data_sources_id,
        data_sources,
        version_value,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        sth_auth_no,
        sth_route_no,
        mrn_no,
        contact,
        contact_phone,
        std_contract,
        sth_carr_id,
        sth_freight_terms,
        sth_equip_no,
        actual_arrive_time,
        vehicle_number,
        vehicle_info)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.deliveryDockingNumber,jdbcType=VARCHAR},
        #{entity.customerCode,jdbcType=VARCHAR},
        #{entity.oemCode,jdbcType=VARCHAR},
        #{entity.customerAddress,jdbcType=VARCHAR},
        #{entity.transportMode,jdbcType=VARCHAR},
        #{entity.status,jdbcType=VARCHAR},
        #{entity.mesTallyNumber,jdbcType=VARCHAR},
        #{entity.oemAddress,jdbcType=VARCHAR},
        #{entity.harvestAddress,jdbcType=VARCHAR},
        #{entity.transferWarehouse,jdbcType=VARCHAR},
        #{entity.customer,jdbcType=VARCHAR},
        #{entity.lineCode,jdbcType=VARCHAR},
        #{entity.oemName,jdbcType=VARCHAR},
        #{entity.kid,jdbcType=VARCHAR},
        #{entity.cabinetType,jdbcType=VARCHAR},
        #{entity.containerNumber,jdbcType=VARCHAR},
        #{entity.vehicleLength,jdbcType=VARCHAR},
        #{entity.deliveryTransportCode,jdbcType=VARCHAR},
        #{entity.expectedArriveTime,jdbcType=TIMESTAMP},
        #{entity.exitBarCode,jdbcType=VARCHAR},
        #{entity.mesTallySheetStatus,jdbcType=VARCHAR},
        #{entity.transportDirection,jdbcType=VARCHAR},
        #{entity.deliveryTime,jdbcType=TIMESTAMP},
        #{entity.overageReport,jdbcType=VARCHAR},
        #{entity.businessType,jdbcType=VARCHAR},
        #{entity.marketType,jdbcType=VARCHAR},
        #{entity.versionCode,jdbcType=VARCHAR},
        #{entity.dataSourcesId,jdbcType=VARCHAR},
        #{entity.dataSources,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.sthAuthNo,jdbcType=VARCHAR},
        #{entity.sthRouteNo,jdbcType=VARCHAR},
        #{entity.mrnNo,jdbcType=VARCHAR},
        #{entity.contact,jdbcType=VARCHAR},
        #{entity.contactPhone,jdbcType=VARCHAR},
        #{entity.stdContract,jdbcType=VARCHAR},
        #{entity.sthCarrId,jdbcType=VARCHAR},
        #{entity.sthFreightTerms,jdbcType=VARCHAR},
        #{entity.sthEquipNo,jdbcType=VARCHAR},
        #{entity.actualArriveTime,jdbcType=TIMESTAMP},
        #{entity.vehicleNumber,jdbcType=INTEGER},
        #{entity.vehicleInfo,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.deliverydockingorder.infrastructure.po.DeliveryDockingOrderPO">
        update fdp_delivery_docking_order set
        delivery_docking_number = #{deliveryDockingNumber,jdbcType=VARCHAR},
        customer_code = #{customerCode,jdbcType=VARCHAR},
        oem_code = #{oemCode,jdbcType=VARCHAR},
        customer_address = #{customerAddress,jdbcType=VARCHAR},
        transport_mode = #{transportMode,jdbcType=VARCHAR},
        status = #{status,jdbcType=VARCHAR},
        mes_tally_number = #{mesTallyNumber,jdbcType=VARCHAR},
        oem_address = #{oemAddress,jdbcType=VARCHAR},
        harvest_address = #{harvestAddress,jdbcType=VARCHAR},
        transfer_warehouse = #{transferWarehouse,jdbcType=VARCHAR},
        customer = #{customer,jdbcType=VARCHAR},
        line_code = #{lineCode,jdbcType=VARCHAR},
        oem_name = #{oemName,jdbcType=VARCHAR},
        kid = #{kid,jdbcType=VARCHAR},
        cabinet_type = #{cabinetType,jdbcType=VARCHAR},
        container_number = #{containerNumber,jdbcType=VARCHAR},
        vehicle_length = #{vehicleLength,jdbcType=VARCHAR},
        delivery_transport_code = #{deliveryTransportCode,jdbcType=VARCHAR},
        expected_arrive_time = #{expectedArriveTime,jdbcType=TIMESTAMP},
        exit_bar_code = #{exitBarCode,jdbcType=VARCHAR},
        mes_tally_sheet_status = #{mesTallySheetStatus,jdbcType=VARCHAR},
        transport_direction = #{transportDirection,jdbcType=VARCHAR},
        delivery_time = #{deliveryTime,jdbcType=TIMESTAMP},
        overage_report = #{overageReport,jdbcType=VARCHAR},
        business_type = #{businessType,jdbcType=VARCHAR},
        market_type = #{marketType,jdbcType=VARCHAR},
        version_code = #{versionCode,jdbcType=VARCHAR},
        data_sources_id = #{dataSourcesId,jdbcType=VARCHAR},
        data_sources = #{dataSources,jdbcType=VARCHAR},
        version_value = #{versionValue,jdbcType=INTEGER},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        sth_auth_no = #{sthAuthNo,jdbcType=VARCHAR},
        sth_route_no = #{sthRouteNo,jdbcType=VARCHAR},
        mrn_no = #{mrnNo,jdbcType=VARCHAR},
        contact = #{contact,jdbcType=VARCHAR},
        contact_phone = #{contactPhone,jdbcType=VARCHAR},
        std_contract = #{stdContract,jdbcType=VARCHAR},
        sth_carr_id = #{sthCarrId,jdbcType=VARCHAR},
        sth_freight_terms = #{sthFreightTerms,jdbcType=VARCHAR},
        sth_equip_no = #{sthEquipNo,jdbcType=VARCHAR},
        actual_arrive_time = #{actualArriveTime,jdbcType=TIMESTAMP},
        vehicle_number = #{vehicleNumber,jdbcType=INTEGER},
        vehicle_info = #{vehicleInfo,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.dfp.deliverydockingorder.infrastructure.po.DeliveryDockingOrderPO">
        update fdp_delivery_docking_order
        <set>
            <if test="item.deliveryDockingNumber != null and item.deliveryDockingNumber != ''">
                delivery_docking_number = #{item.deliveryDockingNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.customerCode != null and item.customerCode != ''">
                customer_code = #{item.customerCode,jdbcType=VARCHAR},
            </if>
            <if test="item.oemCode != null and item.oemCode != ''">
                oem_code = #{item.oemCode,jdbcType=VARCHAR},
            </if>
            <if test="item.customerAddress != null and item.customerAddress != ''">
                customer_address = #{item.customerAddress,jdbcType=VARCHAR},
            </if>
            <if test="item.transportMode != null and item.transportMode != ''">
                transport_mode = #{item.transportMode,jdbcType=VARCHAR},
            </if>
            <if test="item.status != null and item.status != ''">
                status = #{item.status,jdbcType=VARCHAR},
            </if>
            <if test="item.mesTallyNumber != null and item.mesTallyNumber != ''">
                mes_tally_number = #{item.mesTallyNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.oemAddress != null and item.oemAddress != ''">
                oem_address = #{item.oemAddress,jdbcType=VARCHAR},
            </if>
            <if test="item.harvestAddress != null and item.harvestAddress != ''">
                harvest_address = #{item.harvestAddress,jdbcType=VARCHAR},
            </if>
            <if test="item.transferWarehouse != null and item.transferWarehouse != ''">
                transfer_warehouse = #{item.transferWarehouse,jdbcType=VARCHAR},
            </if>
            <if test="item.customer != null and item.customer != ''">
                customer = #{item.customer,jdbcType=VARCHAR},
            </if>
            <if test="item.lineCode != null and item.lineCode != ''">
                line_code = #{item.lineCode,jdbcType=VARCHAR},
            </if>
            <if test="item.oemName != null and item.oemName != ''">
                oem_name = #{item.oemName,jdbcType=VARCHAR},
            </if>
            <if test="item.kid != null and item.kid != ''">
                kid = #{item.kid,jdbcType=VARCHAR},
            </if>
            <if test="item.cabinetType != null and item.cabinetType != ''">
                cabinet_type = #{item.cabinetType,jdbcType=VARCHAR},
            </if>
            <if test="item.containerNumber != null and item.containerNumber != ''">
                container_number = #{item.containerNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.vehicleLength != null and item.vehicleLength != ''">
                vehicle_length = #{item.vehicleLength,jdbcType=VARCHAR},
            </if>
            <if test="item.deliveryTransportCode != null and item.deliveryTransportCode != ''">
                delivery_transport_code = #{item.deliveryTransportCode,jdbcType=VARCHAR},
            </if>
            <if test="item.expectedArriveTime != null">
                expected_arrive_time = #{item.expectedArriveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.exitBarCode != null and item.exitBarCode != ''">
                exit_bar_code = #{item.exitBarCode,jdbcType=VARCHAR},
            </if>
            <if test="item.mesTallySheetStatus != null and item.mesTallySheetStatus != ''">
                mes_tally_sheet_status = #{item.mesTallySheetStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.transportDirection != null and item.transportDirection != ''">
                transport_direction = #{item.transportDirection,jdbcType=VARCHAR},
            </if>
            <if test="item.deliveryTime != null">
                delivery_time = #{item.deliveryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.overageReport != null and item.overageReport != ''">
                overage_report = #{item.overageReport,jdbcType=VARCHAR},
            </if>
            <if test="item.businessType != null and item.businessType != ''">
                business_type = #{item.businessType,jdbcType=VARCHAR},
            </if>
            <if test="item.marketType != null and item.marketType != ''">
                market_type = #{item.marketType,jdbcType=VARCHAR},
            </if>
            <if test="item.versionCode != null and item.versionCode != ''">
                version_code = #{item.versionCode,jdbcType=VARCHAR},
            </if>
            <if test="item.dataSourcesId != null and item.dataSourcesId != ''">
                data_sources_id = #{item.dataSourcesId,jdbcType=VARCHAR},
            </if>
            <if test="item.dataSources != null and item.dataSources != ''">
                data_sources = #{item.dataSources,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.sthAuthNo != null and item.sthAuthNo != ''">
                sth_auth_no = #{item.sthAuthNo,jdbcType=VARCHAR},
            </if>
            <if test="item.sthRouteNo != null and item.sthRouteNo != ''">
                sth_route_no = #{item.sthRouteNo,jdbcType=VARCHAR},
            </if>
            <if test="item.mrnNo != null and item.mrnNo != ''">
                mrn_no = #{item.mrnNo,jdbcType=VARCHAR},
            </if>
            <if test="item.contact != null and item.contact != ''">
                contact = #{item.contact,jdbcType=VARCHAR},
            </if>
            <if test="item.contactPhone != null and item.contactPhone != ''">
                contact_phone = #{item.contactPhone,jdbcType=VARCHAR},
            </if>
            <if test="item.stdContract != null and item.stdContract != ''">
                std_contract = #{item.stdContract,jdbcType=VARCHAR},
            </if>
            <if test="item.sthCarrId != null and item.sthCarrId != ''">
                sth_carr_id = #{item.sthCarrId,jdbcType=VARCHAR},
            </if>
            <if test="item.sthFreightTerms != null and item.sthFreightTerms != ''">
                sth_freight_terms = #{item.sthFreightTerms,jdbcType=VARCHAR},
            </if>
            <if test="item.sthEquipNo != null and item.sthEquipNo != ''">
                sth_equip_no = #{item.sthEquipNo,jdbcType=VARCHAR},
            </if>
            <if test="item.actualArriveTime != null">
                actual_arrive_time = #{item.actualArriveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.vehicleNumber != null">
                vehicle_number = #{item.vehicleNumber,jdbcType=INTEGER},
            </if>
            <if test="item.vehicleInfo != null">
                vehicle_info = #{item.vehicleInfo,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update fdp_delivery_docking_order
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="delivery_docking_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.deliveryDockingNumber,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="customer_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.customerCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="oem_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oemCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="customer_address = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.customerAddress,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transport_mode = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transportMode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.status,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mes_tally_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.mesTallyNumber,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="oem_address = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oemAddress,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="harvest_address = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.harvestAddress,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transfer_warehouse = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transferWarehouse,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="customer = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.customer,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="line_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lineCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="oem_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oemName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="kid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.kid,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cabinet_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.cabinetType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="container_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.containerNumber,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="vehicle_length = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.vehicleLength,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="delivery_transport_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.deliveryTransportCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="expected_arrive_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.expectedArriveTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="exit_bar_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.exitBarCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mes_tally_sheet_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.mesTallySheetStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transport_direction = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transportDirection,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="delivery_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.deliveryTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="overage_report = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.overageReport,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="business_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.businessType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="market_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.marketType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="data_sources_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.dataSourcesId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="data_sources = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.dataSources,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="sth_auth_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sthAuthNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sth_route_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sthRouteNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mrn_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.mrnNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="contact = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.contact,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="contact_phone = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.contactPhone,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="std_contract = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stdContract,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sth_carr_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sthCarrId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sth_freight_terms = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sthFreightTerms,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sth_equip_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sthEquipNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="actual_arrive_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.actualArriveTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
         	<trim prefix="vehicle_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.vehicleNumber,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="vehicle_info = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.vehicleInfo,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update fdp_delivery_docking_order 
        <set>
            <if test="item.deliveryDockingNumber != null and item.deliveryDockingNumber != ''">
                delivery_docking_number = #{item.deliveryDockingNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.customerCode != null and item.customerCode != ''">
                customer_code = #{item.customerCode,jdbcType=VARCHAR},
            </if>
            <if test="item.oemCode != null and item.oemCode != ''">
                oem_code = #{item.oemCode,jdbcType=VARCHAR},
            </if>
            <if test="item.customerAddress != null and item.customerAddress != ''">
                customer_address = #{item.customerAddress,jdbcType=VARCHAR},
            </if>
            <if test="item.transportMode != null and item.transportMode != ''">
                transport_mode = #{item.transportMode,jdbcType=VARCHAR},
            </if>
            <if test="item.status != null and item.status != ''">
                status = #{item.status,jdbcType=VARCHAR},
            </if>
            <if test="item.mesTallyNumber != null and item.mesTallyNumber != ''">
                mes_tally_number = #{item.mesTallyNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.oemAddress != null and item.oemAddress != ''">
                oem_address = #{item.oemAddress,jdbcType=VARCHAR},
            </if>
            <if test="item.harvestAddress != null and item.harvestAddress != ''">
                harvest_address = #{item.harvestAddress,jdbcType=VARCHAR},
            </if>
            <if test="item.transferWarehouse != null and item.transferWarehouse != ''">
                transfer_warehouse = #{item.transferWarehouse,jdbcType=VARCHAR},
            </if>
            <if test="item.customer != null and item.customer != ''">
                customer = #{item.customer,jdbcType=VARCHAR},
            </if>
            <if test="item.lineCode != null and item.lineCode != ''">
                line_code = #{item.lineCode,jdbcType=VARCHAR},
            </if>
            <if test="item.oemName != null and item.oemName != ''">
                oem_name = #{item.oemName,jdbcType=VARCHAR},
            </if>
            <if test="item.kid != null and item.kid != ''">
                kid = #{item.kid,jdbcType=VARCHAR},
            </if>
            <if test="item.cabinetType != null and item.cabinetType != ''">
                cabinet_type = #{item.cabinetType,jdbcType=VARCHAR},
            </if>
            <if test="item.containerNumber != null and item.containerNumber != ''">
                container_number = #{item.containerNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.vehicleLength != null and item.vehicleLength != ''">
                vehicle_length = #{item.vehicleLength,jdbcType=VARCHAR},
            </if>
            <if test="item.deliveryTransportCode != null and item.deliveryTransportCode != ''">
                delivery_transport_code = #{item.deliveryTransportCode,jdbcType=VARCHAR},
            </if>
            <if test="item.expectedArriveTime != null">
                expected_arrive_time = #{item.expectedArriveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.exitBarCode != null and item.exitBarCode != ''">
                exit_bar_code = #{item.exitBarCode,jdbcType=VARCHAR},
            </if>
            <if test="item.mesTallySheetStatus != null and item.mesTallySheetStatus != ''">
                mes_tally_sheet_status = #{item.mesTallySheetStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.transportDirection != null and item.transportDirection != ''">
                transport_direction = #{item.transportDirection,jdbcType=VARCHAR},
            </if>
            <if test="item.deliveryTime != null">
                delivery_time = #{item.deliveryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.overageReport != null and item.overageReport != ''">
                overage_report = #{item.overageReport,jdbcType=VARCHAR},
            </if>
            <if test="item.businessType != null and item.businessType != ''">
                business_type = #{item.businessType,jdbcType=VARCHAR},
            </if>
            <if test="item.marketType != null and item.marketType != ''">
                market_type = #{item.marketType,jdbcType=VARCHAR},
            </if>
            <if test="item.versionCode != null and item.versionCode != ''">
                version_code = #{item.versionCode,jdbcType=VARCHAR},
            </if>
            <if test="item.dataSourcesId != null and item.dataSourcesId != ''">
                data_sources_id = #{item.dataSourcesId,jdbcType=VARCHAR},
            </if>
            <if test="item.dataSources != null and item.dataSources != ''">
                data_sources = #{item.dataSources,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.sthAuthNo != null and item.sthAuthNo != ''">
                sth_auth_no = #{item.sthAuthNo,jdbcType=VARCHAR},
            </if>
            <if test="item.sthRouteNo != null and item.sthRouteNo != ''">
                sth_route_no = #{item.sthRouteNo,jdbcType=VARCHAR},
            </if>
            <if test="item.mrnNo != null and item.mrnNo != ''">
                mrn_no = #{item.mrnNo,jdbcType=VARCHAR},
            </if>
            <if test="item.contact != null and item.contact != ''">
                contact = #{item.contact,jdbcType=VARCHAR},
            </if>
            <if test="item.contactPhone != null and item.contactPhone != ''">
                contact_phone = #{item.contactPhone,jdbcType=VARCHAR},
            </if>
            <if test="item.stdContract != null and item.stdContract != ''">
                std_contract = #{item.stdContract,jdbcType=VARCHAR},
            </if>
            <if test="item.sthCarrId != null and item.sthCarrId != ''">
                sth_carr_id = #{item.sthCarrId,jdbcType=VARCHAR},
            </if>
            <if test="item.sthFreightTerms != null and item.sthFreightTerms != ''">
                sth_freight_terms = #{item.sthFreightTerms,jdbcType=VARCHAR},
            </if>
            <if test="item.sthEquipNo != null and item.sthEquipNo != ''">
                sth_equip_no = #{item.sthEquipNo,jdbcType=VARCHAR},
            </if>
            <if test="item.actualArriveTime != null">
                actual_arrive_time = #{item.actualArriveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.vehicleNumber != null">
                vehicle_number = #{item.vehicleNumber,jdbcType=INTEGER},
            </if>
            <if test="item.vehicleInfo != null">
                vehicle_info = #{item.vehicleInfo,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from fdp_delivery_docking_order where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from fdp_delivery_docking_order where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
