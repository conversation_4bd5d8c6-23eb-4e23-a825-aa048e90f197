package com.yhl.scp.dfp.stock.convertor;

import com.yhl.scp.dfp.stock.domain.entity.FdpInventoryBatchDetailLogDO;
import com.yhl.scp.dfp.stock.dto.FdpInventoryBatchDetailLogDTO;
import com.yhl.scp.dfp.stock.infrastructure.po.FdpInventoryBatchDetailLogPO;
import com.yhl.scp.dfp.stock.vo.FdpInventoryBatchDetailLogVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>FdpInventoryBatchDetailLogConvertor</code>
 * <p>
 * 转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-18 22:55:12
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface FdpInventoryBatchDetailLogConvertor {

    FdpInventoryBatchDetailLogConvertor INSTANCE = Mappers.getMapper(FdpInventoryBatchDetailLogConvertor.class);

    FdpInventoryBatchDetailLogDO dto2Do(FdpInventoryBatchDetailLogDTO obj);

    FdpInventoryBatchDetailLogDTO do2Dto(FdpInventoryBatchDetailLogDO obj);

    List<FdpInventoryBatchDetailLogDO> dto2Dos(List<FdpInventoryBatchDetailLogDTO> list);

    List<FdpInventoryBatchDetailLogDTO> do2Dtos(List<FdpInventoryBatchDetailLogDO> list);

    FdpInventoryBatchDetailLogVO do2Vo(FdpInventoryBatchDetailLogDO obj);

    FdpInventoryBatchDetailLogVO po2Vo(FdpInventoryBatchDetailLogPO obj);

    List<FdpInventoryBatchDetailLogVO> po2Vos(List<FdpInventoryBatchDetailLogPO> list);

    FdpInventoryBatchDetailLogPO do2Po(FdpInventoryBatchDetailLogDO obj);

    FdpInventoryBatchDetailLogDO po2Do(FdpInventoryBatchDetailLogPO obj);

    FdpInventoryBatchDetailLogPO dto2Po(FdpInventoryBatchDetailLogDTO obj);

    List<FdpInventoryBatchDetailLogPO> dto2Pos(List<FdpInventoryBatchDetailLogDTO> obj);

}
