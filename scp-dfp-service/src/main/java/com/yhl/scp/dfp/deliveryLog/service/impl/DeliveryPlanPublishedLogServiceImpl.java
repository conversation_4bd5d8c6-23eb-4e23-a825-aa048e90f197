package com.yhl.scp.dfp.deliveryLog.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.dfp.deliveryLog.convertor.DeliveryPlanPublishedLogConvertor;
import com.yhl.scp.dfp.deliveryLog.domain.entity.DeliveryPlanPublishedLogDO;
import com.yhl.scp.dfp.deliveryLog.domain.service.DeliveryPlanPublishedLogDomainService;
import com.yhl.scp.dfp.deliveryLog.dto.DeliveryPlanPublishedLogDTO;
import com.yhl.scp.dfp.deliveryLog.infrastructure.dao.DeliveryPlanPublishedLogDao;
import com.yhl.scp.dfp.deliveryLog.infrastructure.po.DeliveryPlanPublishedLogPO;
import com.yhl.scp.dfp.deliveryLog.service.DeliveryPlanPublishedLogService;
import com.yhl.scp.dfp.deliveryLog.vo.DeliveryPlanPublishedLogVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>DeliveryPlanPublishedLogServiceImpl</code>
 * <p>
 * 发货计划发布追踪表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-20 17:21:53
 */
@Slf4j
@Service
public class DeliveryPlanPublishedLogServiceImpl extends AbstractService implements DeliveryPlanPublishedLogService {

    @Resource
    private DeliveryPlanPublishedLogDao deliveryPlanPublishedLogDao;

    @Resource
    private DeliveryPlanPublishedLogDomainService deliveryPlanPublishedLogDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(DeliveryPlanPublishedLogDTO deliveryPlanPublishedLogDTO) {
        // 0.数据转换
        DeliveryPlanPublishedLogDO deliveryPlanPublishedLogDO = DeliveryPlanPublishedLogConvertor.INSTANCE.dto2Do(deliveryPlanPublishedLogDTO);
        DeliveryPlanPublishedLogPO deliveryPlanPublishedLogPO = DeliveryPlanPublishedLogConvertor.INSTANCE.dto2Po(deliveryPlanPublishedLogDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        deliveryPlanPublishedLogDomainService.validation(deliveryPlanPublishedLogDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(deliveryPlanPublishedLogPO);
        deliveryPlanPublishedLogDao.insert(deliveryPlanPublishedLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(DeliveryPlanPublishedLogDTO deliveryPlanPublishedLogDTO) {
        // 0.数据转换
        DeliveryPlanPublishedLogDO deliveryPlanPublishedLogDO = DeliveryPlanPublishedLogConvertor.INSTANCE.dto2Do(deliveryPlanPublishedLogDTO);
        DeliveryPlanPublishedLogPO deliveryPlanPublishedLogPO = DeliveryPlanPublishedLogConvertor.INSTANCE.dto2Po(deliveryPlanPublishedLogDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        deliveryPlanPublishedLogDomainService.validation(deliveryPlanPublishedLogDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(deliveryPlanPublishedLogPO);
        deliveryPlanPublishedLogDao.update(deliveryPlanPublishedLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<DeliveryPlanPublishedLogDTO> list) {
        List<DeliveryPlanPublishedLogPO> newList = DeliveryPlanPublishedLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        deliveryPlanPublishedLogDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<DeliveryPlanPublishedLogDTO> list) {
        List<DeliveryPlanPublishedLogPO> newList = DeliveryPlanPublishedLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        deliveryPlanPublishedLogDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return deliveryPlanPublishedLogDao.deleteBatch(idList);
        }
        return deliveryPlanPublishedLogDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public DeliveryPlanPublishedLogVO selectByPrimaryKey(String id) {
        DeliveryPlanPublishedLogPO po = deliveryPlanPublishedLogDao.selectByPrimaryKey(id);
        return DeliveryPlanPublishedLogConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "DELIVERY_PLAN_PUBLISHED_LOG")
    public List<DeliveryPlanPublishedLogVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "DELIVERY_PLAN_PUBLISHED_LOG")
    public List<DeliveryPlanPublishedLogVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<DeliveryPlanPublishedLogVO> dataList = deliveryPlanPublishedLogDao.selectByCondition(sortParam, queryCriteriaParam);
        DeliveryPlanPublishedLogServiceImpl target = SpringBeanUtils.getBean(DeliveryPlanPublishedLogServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<DeliveryPlanPublishedLogVO> selectByParams(Map<String, Object> params) {
        List<DeliveryPlanPublishedLogPO> list = deliveryPlanPublishedLogDao.selectByParams(params);
        return DeliveryPlanPublishedLogConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<DeliveryPlanPublishedLogVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<DeliveryPlanPublishedLogVO> invocation(List<DeliveryPlanPublishedLogVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
