package com.yhl.scp.dfp.deliveryLog.domain.factory;

import com.yhl.scp.dfp.deliveryLog.domain.entity.DeliveryPlanPublishedLogDO;
import com.yhl.scp.dfp.deliveryLog.dto.DeliveryPlanPublishedLogDTO;
import com.yhl.scp.dfp.deliveryLog.infrastructure.dao.DeliveryPlanPublishedLogDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>DeliveryPlanPublishedLogFactory</code>
 * <p>
 * 发货计划发布追踪表领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-20 17:22:06
 */
@Component
public class DeliveryPlanPublishedLogFactory {

    @Resource
    private DeliveryPlanPublishedLogDao deliveryPlanPublishedLogDao;

    DeliveryPlanPublishedLogDO create(DeliveryPlanPublishedLogDTO dto) {
        // TODO
        return null;
    }

}
