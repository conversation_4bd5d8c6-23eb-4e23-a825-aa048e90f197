<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.projectForecast.infrastructure.dao.ProjectForecastPresentationDao">
    <resultMap id="BaseResultMap"
               type="com.yhl.scp.dfp.projectForecast.infrastructure.po.ProjectForecastPresentationPO">
        <!--@Table fdp_project_forecast_presentation-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="version_id" jdbcType="VARCHAR" property="versionId"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="part_code" jdbcType="VARCHAR" property="partCode"/>
        <result column="demand_type" jdbcType="VARCHAR" property="demandType"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.dfp.projectForecast.vo.ProjectForecastPresentationVO">
        <result column="oem_name" jdbcType="VARCHAR" property="oemName"/>
    </resultMap>
    <resultMap id="OverviewVOMap" type="com.yhl.scp.dfp.demand.vo.DemandForecastOverViewVO">
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="oem_name" jdbcType="VARCHAR" property="oemName"/>
        <result column="risk_level" jdbcType="VARCHAR" property="riskLevel"/>
    </resultMap>
    <resultMap id="DetailVOMap" type="com.yhl.scp.dfp.demand.vo.DemandForecastEstablishmentVehicleVO">
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
        <result column="access_position" jdbcType="VARCHAR" property="accessPosition"/>
    </resultMap>
    <resultMap id="EstablishmentVOMap" type="com.yhl.scp.dfp.demand.vo.DemandForecastEstablishmentVO">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="forecast_version_id" jdbcType="VARCHAR" property="forecastVersionId"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
        <result column="access_position" jdbcType="VARCHAR" property="accessPosition"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="forecast_date" jdbcType="VARCHAR" property="forecastDate"/>
        <result column="customer_forecast" jdbcType="VARCHAR" property="customerForecast"/>
        <result column="demand_forecast" jdbcType="VARCHAR" property="demandForecast"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="LineChartMap" type="com.yhl.scp.dfp.release.dto.ReleaseLineChartDTO">
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="forecast_time" jdbcType="VARCHAR" property="forecastTime"/>
        <result column="demand_forecast" jdbcType="INTEGER" property="demandForecast"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,version_id,oem_code,product_code,part_code,demand_type,vehicle_model_code,remark,
        enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>,oem_name
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.versionId != null and params.versionId != ''">
                and version_id = #{params.versionId,jdbcType=VARCHAR}
            </if>
            <if test="params.versionIds != null and params.versionIds.size() > 0">
                and version_id in
                <foreach collection="params.versionIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.oemCode != null and params.oemCode != ''">
                and oem_code = #{params.oemCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.partCode != null and params.partCode != ''">
                and part_code = #{params.partCode,jdbcType=VARCHAR}
            </if>
            <if test="params.demandType != null and params.demandType != ''">
                and demand_type = #{params.demandType,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleModelCode != null and params.vehicleModelCode != ''">
                and vehicle_model_code = #{params.vehicleModelCode,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_project_forecast_presentation
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_project_forecast_presentation
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_fdp_project_forecast_presentation
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_project_forecast_presentation
        <include refid="Base_Where_Condition"/>
    </select>

    <select id="selectDataPermissionByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_project_forecast_presentation fdfe
        <include refid="Base_Where_Condition"/>
    </select>

    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_fdp_project_forecast_presentation
        <include refid="Base_Where_Condition"/>
    </select>
    <select id="selectOemWithRisk" resultMap="OverviewVOMap">
        select distinct fdfe.oem_code, mo.oem_name, forl.risk_level
        from fdp_project_forecast_presentation fdfe
        left join mds_oem mo on mo.oem_code = fdfe.oem_code
        left join fdp_oem_risk_level forl on forl.oem_code = fdfe.oem_code and forl.estimate_time =
        #{planPeriod,jdbcType=VARCHAR}
        where 1 = 1
        and fdfe.version_id = #{versionId,jdbcType=VARCHAR}
        <if test="oemCodeList != null and oemCodeList.size() > 0">
            and fdfe.oem_code in
            <foreach collection="oemCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="riskLevelSet != null and riskLevelSet.size() > 0">
            and fdfe.risk_level in
            <foreach collection="riskLevelSet" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="selectDataByCondition" resultMap="DetailVOMap">
        select distinct
        vehicle_model_code,
        access_position
        from v_fdp_project_forecast_presentation_vehicle
        where version_id = #{param.versionId}
        and oem_code = #{param.oemCode}
        <if test="param.productCodeList != null and param.productCodeList.size() > 0">
            and product_code in
            <foreach collection="param.productCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="param.vehicleModelCodeList != null and param.vehicleModelCodeList.size() > 0">
            and vehicle_model_code in
            <foreach collection="param.vehicleModelCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="selectProductDataByCondition" resultMap="DetailVOMap">
        select distinct mpsp.product_code,
                        mpsp.product_name
        from mds_product_stock_point mpsp
        where 1 = 1
          and exists (select 1
                      from fdp_project_forecast_presentation fpfp
                      where fpfp.version_id = #{param.versionId}
                        and fpfp.oem_code = #{param.oemCode}
                        and fpfp.product_code = mpsp.product_code)
    </select>
    <select id="selectEstablishmentByParams" resultMap="EstablishmentVOMap">
        select * from v_fdp_project_forecast_presentation_establishment
        where 1 = 1
        <if test="param.forecastVersionId != null and param.forecastVersionId != ''">
            and forecast_version_id = #{param.forecastVersionId,jdbcType=VARCHAR}
        </if>
        <if test="params.oemCode != null and params.oemCode != ''">
            and oem_code = #{param.oemCode,jdbcType=VARCHAR}
        </if>
        <if test="param.productCodeList != null and param.productCodeList.size() > 0">
            and product_code in
            <foreach collection="param.productCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="param.ids != null and param.ids.size() > 0">
            and id in
            <foreach collection="param.ids" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="selectByVersionAndOem" resultType="com.yhl.scp.dfp.release.vo.ReleaseVO">
        select id,
               product_code        as productCode,
               core_process        as coreProcess,
               product_special     as productSpecial,
               vehicle_model_code  as vehicleModelCode,
               material_risk_level as materialRiskLevel,
               forecast_time       as forecastTime,
               demand_forecast     as quantity,
               version_value       as versionValue
        from v_fdp_project_forecast_presentation_product_detail
        where 1 = 1
          and parent_version_id = #{versionId,jdbcType=VARCHAR}
          and oem_code = #{oemCode,jdbcType=VARCHAR}
    </select>
    <select id="selectLineChartByYearAndOem" resultMap="LineChartMap">
        select *
        from v_fdp_project_forecast_line_chart
        where forecast_time like concat(#{calcYear}, '%')
          and oem_code = #{oemCode,jdbcType=VARCHAR}
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.projectForecast.infrastructure.po.ProjectForecastPresentationPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into fdp_project_forecast_presentation(
        id,
        version_id,
        oem_code,
        product_code,
        part_code,
        demand_type,
        vehicle_model_code,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{versionId,jdbcType=VARCHAR},
        #{oemCode,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{partCode,jdbcType=VARCHAR},
        #{demandType,jdbcType=VARCHAR},
        #{vehicleModelCode,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.dfp.projectForecast.infrastructure.po.ProjectForecastPresentationPO">
        insert into fdp_project_forecast_presentation(id,
                                                      version_id,
                                                      oem_code,
                                                      product_code,
                                                      part_code,
                                                      demand_type,
                                                      vehicle_model_code,
                                                      remark,
                                                      enabled,
                                                      creator,
                                                      create_time,
                                                      modifier,
                                                      modify_time,
                                                      version_value)
        values (#{id,jdbcType=VARCHAR},
                #{versionId,jdbcType=VARCHAR},
                #{oemCode,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{partCode,jdbcType=VARCHAR},
                #{demandType,jdbcType=VARCHAR},
                #{vehicleModelCode,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into fdp_project_forecast_presentation(
        id,
        version_id,
        oem_code,
        product_code,
        part_code,
        demand_type,
        vehicle_model_code,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.versionId,jdbcType=VARCHAR},
            #{entity.oemCode,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.partCode,jdbcType=VARCHAR},
            #{entity.demandType,jdbcType=VARCHAR},
            #{entity.vehicleModelCode,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into fdp_project_forecast_presentation(
        id,
        version_id,
        oem_code,
        product_code,
        part_code,
        demand_type,
        vehicle_model_code,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.versionId,jdbcType=VARCHAR},
            #{entity.oemCode,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.partCode,jdbcType=VARCHAR},
            #{entity.demandType,jdbcType=VARCHAR},
            #{entity.vehicleModelCode,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.projectForecast.infrastructure.po.ProjectForecastPresentationPO">
        update fdp_project_forecast_presentation
        set version_id         = #{versionId,jdbcType=VARCHAR},
            oem_code           = #{oemCode,jdbcType=VARCHAR},
            product_code       = #{productCode,jdbcType=VARCHAR},
            part_code          = #{partCode,jdbcType=VARCHAR},
            demand_type        = #{demandType,jdbcType=VARCHAR},
            vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
            remark             = #{remark,jdbcType=VARCHAR},
            enabled            = #{enabled,jdbcType=VARCHAR},
            modifier           = #{modifier,jdbcType=VARCHAR},
            modify_time        = #{modifyTime,jdbcType=TIMESTAMP},
            version_value      = version_value + 1
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER};
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.dfp.projectForecast.infrastructure.po.ProjectForecastPresentationPO">
        update fdp_project_forecast_presentation
        <set>
            <if test="item.versionId != null and item.versionId != ''">
                version_id = #{item.versionId,jdbcType=VARCHAR},
            </if>
            <if test="item.oemCode != null and item.oemCode != ''">
                oem_code = #{item.oemCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.partCode != null and item.partCode != ''">
                part_code = #{item.partCode,jdbcType=VARCHAR},
            </if>
            <if test="item.demandType != null and item.demandType != ''">
                demand_type = #{item.demandType,jdbcType=VARCHAR},
            </if>
            <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            version_value = version_value + 1
        </set>
        where id = #{id,jdbcType=VARCHAR}
        and version_value = #{versionValue,jdbcType=INTEGER};
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update fdp_project_forecast_presentation
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="oem_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oemCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="part_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.partCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="vehicle_model_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.vehicleModelCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
        and version_value in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.versionValue,jdbcType=INTEGER}
        </foreach>;
        update fdp_project_forecast_presentation set
        version_value = version_value + 1
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
        and version_value in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update fdp_project_forecast_presentation
            <set>
                <if test="item.versionId != null and item.versionId != ''">
                    version_id = #{item.versionId,jdbcType=VARCHAR},
                </if>
                <if test="item.oemCode != null and item.oemCode != ''">
                    oem_code = #{item.oemCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.partCode != null and item.partCode != ''">
                    part_code = #{item.partCode,jdbcType=VARCHAR},
                </if>
                <if test="item.demandType != null and item.demandType != ''">
                    demand_type = #{item.demandType,jdbcType=VARCHAR},
                </if>
                <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                    vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
                version_value = version_value + 1
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER};
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from fdp_project_forecast_presentation
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from fdp_project_forecast_presentation where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <!-- 批量id+版本删除 -->
    <delete id="deleteBatchVersion" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            delete from fdp_project_forecast_presentation where
            id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </delete>
    <delete id="deleteVersionId">
        delete
        from fdp_project_forecast_presentation
        where version_id = #{versionId,jdbcType=VARCHAR}
    </delete>
</mapper>