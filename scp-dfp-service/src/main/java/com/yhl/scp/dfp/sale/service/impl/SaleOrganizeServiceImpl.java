package com.yhl.scp.dfp.sale.service.impl;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.enums.ObjectTypeEnum;
import com.yhl.scp.dfp.sale.convertor.SaleOrganizeConvertor;
import com.yhl.scp.dfp.sale.domain.entity.SaleOrganizeDO;
import com.yhl.scp.dfp.sale.domain.service.SaleOrganizeDomainService;
import com.yhl.scp.dfp.sale.dto.SaleOrganizeDTO;
import com.yhl.scp.dfp.sale.infrastructure.dao.SaleOrganizeDao;
import com.yhl.scp.dfp.sale.infrastructure.po.SaleOrganizePO;
import com.yhl.scp.dfp.sale.service.SaleOrganizeService;
import com.yhl.scp.dfp.sale.vo.SaleOrganizeVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <code>SaleOrganizeServiceImpl</code>
 * <p>
 * 销售组织应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:48:09
 */
@Slf4j
@Service
public class SaleOrganizeServiceImpl extends AbstractService implements SaleOrganizeService {

    @Resource
    private SaleOrganizeDao saleOrganizeDao;

    @Resource
    private SaleOrganizeDomainService saleOrganizeDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private NewDcpFeign newDcpFeign;


    @Override
    public BaseResponse<Void> doCreate(SaleOrganizeDTO saleOrganizeDTO) {
        // 0.数据转换
        SaleOrganizeDO saleOrganizeDO = SaleOrganizeConvertor.INSTANCE.dto2Do(saleOrganizeDTO);
        SaleOrganizePO saleOrganizePO = SaleOrganizeConvertor.INSTANCE.dto2Po(saleOrganizeDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        saleOrganizeDomainService.validation(saleOrganizeDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(saleOrganizePO);
        saleOrganizeDao.insertWithPrimaryKey(saleOrganizePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(SaleOrganizeDTO saleOrganizeDTO) {
        // 0.数据转换
        SaleOrganizeDO saleOrganizeDO = SaleOrganizeConvertor.INSTANCE.dto2Do(saleOrganizeDTO);
        SaleOrganizePO saleOrganizePO = SaleOrganizeConvertor.INSTANCE.dto2Po(saleOrganizeDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        saleOrganizeDomainService.validation(saleOrganizeDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(saleOrganizePO);
        saleOrganizeDao.update(saleOrganizePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<SaleOrganizeDTO> list) {
        List<SaleOrganizePO> newList = SaleOrganizeConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        saleOrganizeDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<SaleOrganizeDTO> list) {
        List<SaleOrganizePO> newList = SaleOrganizeConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        saleOrganizeDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return saleOrganizeDao.deleteBatch(idList);
        }
        return saleOrganizeDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public SaleOrganizeVO selectByPrimaryKey(String id) {
        SaleOrganizePO po = saleOrganizeDao.selectByPrimaryKey(id);
        return SaleOrganizeConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mds_sale_organize")
    public List<SaleOrganizeVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mds_sale_organize")
    public List<SaleOrganizeVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<SaleOrganizeVO> dataList = saleOrganizeDao.selectByCondition(sortParam, queryCriteriaParam);
        SaleOrganizeServiceImpl target = springBeanUtils.getBean(SaleOrganizeServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<SaleOrganizeVO> selectByParams(Map<String, Object> params) {
        List<SaleOrganizePO> list = saleOrganizeDao.selectByParams(params);
        return SaleOrganizeConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<SaleOrganizeVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public int deleteBatchVersion(List<RemoveVersionDTO> removeVersionDTOS) {
        if (CollectionUtils.isEmpty(removeVersionDTOS)) {
            return 0;
        }
        saleOrganizeDomainService.checkDelete(removeVersionDTOS);
        return saleOrganizeDao.deleteBatchVersion(removeVersionDTOS);
    }

    /**
     * 同步销售组织
     *
     * @return
     */
    @Override
    public BaseResponse<Void> syncSaleOrganize() {
        Map<String, Object> params = MapUtil.newHashMap();
        params.put("orgType", "OU");
        //调用远程的销售组织信息
        newDcpFeign.callExternalApi(SystemHolder.getTenantCode(), ApiSourceEnum.ERP.getCode(),
                ApiCategoryEnum.SALE_ORGANIZE.getCode(), params);
        return BaseResponse.success("同步成功");
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.SALE_ORGANIZE.getCode();
    }

    @Override
    public List<SaleOrganizeVO> invocation(List<SaleOrganizeVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
