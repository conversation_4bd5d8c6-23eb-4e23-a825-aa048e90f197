package com.yhl.scp.dfp.consistence.service.impl;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.basic.forecast.enums.VersionStatusEnum;
import com.yhl.scp.dfp.common.constants.DfpConstants;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.common.enums.GenerateTypeEnum;
import com.yhl.scp.dfp.common.enums.PublishStatusEnum;
import com.yhl.scp.dfp.consistence.convertor.ConsistenceDemandForecastVersionConvertor;
import com.yhl.scp.dfp.consistence.domain.entity.ConsistenceDemandForecastVersionDO;
import com.yhl.scp.dfp.consistence.domain.service.ConsistenceDemandForecastVersionDomainService;
import com.yhl.scp.dfp.consistence.dto.ConsistenceDemandForecastDataDTO;
import com.yhl.scp.dfp.consistence.dto.ConsistenceDemandForecastDataDetailDTO;
import com.yhl.scp.dfp.consistence.dto.ConsistenceDemandForecastVersionDTO;
import com.yhl.scp.dfp.consistence.dto.ExecutionMonitorDTO;
import com.yhl.scp.dfp.consistence.infrastructure.dao.ConsistenceDemandForecastVersionDao;
import com.yhl.scp.dfp.consistence.infrastructure.po.ConsistenceDemandForecastVersionPO;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastDataDetailService;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastDataService;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastVersionService;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastVersionNewVO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastVersionVO;
import com.yhl.scp.dfp.demand.service.DemandForecastEstablishmentService;
import com.yhl.scp.dfp.demand.service.DemandForecastVersionService;
import com.yhl.scp.dfp.demand.vo.DemandForecastEstablishmentVO;
import com.yhl.scp.dfp.demand.vo.DemandForecastVersionVO;
import com.yhl.scp.dfp.enums.ObjectTypeEnum;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mps.feign.MpsFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>ConsistenceDemandForecastVersionServiceImpl</code>
 * <p>
 * 一致性业务预测版本应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:46:57
 */
@Slf4j
@Service
public class ConsistenceDemandForecastVersionServiceImpl extends AbstractService implements ConsistenceDemandForecastVersionService {

    @Resource
    private ConsistenceDemandForecastVersionDao consistenceDemandForecastVersionDao;

    @Resource
    private ConsistenceDemandForecastVersionDomainService consistenceDemandForecastVersionDomainService;

    @Resource
    private OemService oemService;

    @Resource
    private ConsistenceDemandForecastDataDetailService consistenceDemandForecastDataDetailService;

    @Resource
    private DemandForecastVersionService demandForecastVersionService;
    
    @Resource
    private DemandForecastEstablishmentService demandForecastEstablishmentService;
    
    @Resource
    private ConsistenceDemandForecastDataService consistenceDemandForecastDataService;
    
    @Resource
    private MpsFeign mpsFeign;
    
    @Override
    public BaseResponse<Void> doCreate(ConsistenceDemandForecastVersionDTO consistenceDemandForecastVersionDTO) {
        // 0.数据转换
        ConsistenceDemandForecastVersionDO consistenceDemandForecastVersionDO =
                ConsistenceDemandForecastVersionConvertor.INSTANCE.dto2Do(consistenceDemandForecastVersionDTO);
        ConsistenceDemandForecastVersionPO consistenceDemandForecastVersionPO =
                ConsistenceDemandForecastVersionConvertor.INSTANCE.dto2Po(consistenceDemandForecastVersionDTO);
        // 1.数据校验
        consistenceDemandForecastVersionDomainService.validation(consistenceDemandForecastVersionDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(consistenceDemandForecastVersionPO);
        consistenceDemandForecastVersionDao.insertWithPrimaryKey(consistenceDemandForecastVersionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(ConsistenceDemandForecastVersionDTO consistenceDemandForecastVersionDTO) {
        // 0.数据转换
        ConsistenceDemandForecastVersionDO consistenceDemandForecastVersionDO =
                ConsistenceDemandForecastVersionConvertor.INSTANCE.dto2Do(consistenceDemandForecastVersionDTO);
        ConsistenceDemandForecastVersionPO consistenceDemandForecastVersionPO =
                ConsistenceDemandForecastVersionConvertor.INSTANCE.dto2Po(consistenceDemandForecastVersionDTO);
        // 1.数据校验
        consistenceDemandForecastVersionDomainService.validation(consistenceDemandForecastVersionDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(consistenceDemandForecastVersionPO);
        consistenceDemandForecastVersionDao.update(consistenceDemandForecastVersionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<ConsistenceDemandForecastVersionDTO> list) {
        List<ConsistenceDemandForecastVersionPO> newList =
                ConsistenceDemandForecastVersionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        consistenceDemandForecastVersionDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<ConsistenceDemandForecastVersionDTO> list) {
        List<ConsistenceDemandForecastVersionPO> newList =
                ConsistenceDemandForecastVersionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        consistenceDemandForecastVersionDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return consistenceDemandForecastVersionDao.deleteBatch(idList);
        }
        return consistenceDemandForecastVersionDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public ConsistenceDemandForecastVersionVO selectByPrimaryKey(String id) {
        ConsistenceDemandForecastVersionPO po = consistenceDemandForecastVersionDao.selectByPrimaryKey(id);
        return ConsistenceDemandForecastVersionConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "fdp_consistence_demand_forecast_version")
    public List<ConsistenceDemandForecastVersionVO> selectByPage(Pagination pagination, String sortParam,
                                                                 String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "fdp_consistence_demand_forecast_version")
    public List<ConsistenceDemandForecastVersionVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<ConsistenceDemandForecastVersionVO> dataList =
                consistenceDemandForecastVersionDao.selectByCondition(sortParam, queryCriteriaParam);
        ConsistenceDemandForecastVersionServiceImpl target =
                SpringBeanUtils.getBean(ConsistenceDemandForecastVersionServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<ConsistenceDemandForecastVersionVO> selectByParams(Map<String, Object> params) {
        List<ConsistenceDemandForecastVersionPO> list = consistenceDemandForecastVersionDao.selectByParams(params);
        return ConsistenceDemandForecastVersionConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<ConsistenceDemandForecastVersionVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    /**
     * 用于一致性业务预测版本获取关联业务预测版本号
     *
     * @return {@link ConsistenceDemandForecastVersionVO}
     */
    public List<ConsistenceDemandForecastVersionVO> selectNewAll() {
        List<ConsistenceDemandForecastVersionNewVO> newVos = consistenceDemandForecastVersionDao.selectNewAll();
        ArrayList<ConsistenceDemandForecastVersionVO> vos = new ArrayList<>();
        for (ConsistenceDemandForecastVersionNewVO newVO : newVos) {
            ConsistenceDemandForecastVersionVO build = ConsistenceDemandForecastVersionVO.builder()
                    .id(newVO.getId())
                    .planPeriod(newVO.getPlanPeriod())
                    .planHorizon(newVO.getPlanHorizon())
                    .planGranularity(newVO.getPlanGranularity())
                    .versionCode(newVO.getVersionCode())
                    .parentVersionId(newVO.getParentVersionId())
                    .oemCode(newVO.getOemCode())
                    .oemName(newVO.getOemName())
                    .demandForecastVersionId(newVO.getDemandForecastVersionId())
                    .demandForecastVersionCode(newVO.getDemandForecastVersionCode())
                    .generateType(newVO.getGenerateType())
                    .versionStatus(newVO.getVersionStatus())
                    .remark(newVO.getRemark())
                    .enabled(newVO.getEnabled())
                    .creator(newVO.getCreator())
                    .createTime(newVO.getCreateTime())
                    .modifier(newVO.getModifier())
                    .modifyTime(newVO.getModifyTime())
                    .reviewVersionFlag(newVO.getReviewVersionFlag())
                    .versionValue(newVO.getVersionValue()).build();
            vos.add(build);
        }
        return vos;

    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.CONSISTENCE_DEMAND_FORECAST_VERSION.getCode();
    }

    @Override
    public List<ConsistenceDemandForecastVersionVO> invocation(List<ConsistenceDemandForecastVersionVO> dataList,
                                                               Map<String, Object> params, String invocation) {
        return dataList;
    }

    @Override
    public int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList) {
        if (CollectionUtils.isNotEmpty(versionDTOList)) {
            return consistenceDemandForecastVersionDao.deleteBatchVersion(versionDTOList);
        }
        return 0;
    }

    @Override
    public List<ConsistenceDemandForecastVersionVO> treeQuery() {
        // 获取要返回的需求版本数据
        List<ConsistenceDemandForecastVersionVO> demandVersionVOS = this.selectNewAll();
        // 获取首层原始需求版本集合
        Map<String, ConsistenceDemandForecastVersionVO> consistenceDemandForecastVersionVOMapOfId =
                demandVersionVOS.stream().collect(Collectors.toMap(ConsistenceDemandForecastVersionVO::getId,
                        Function.identity()));
        // 获取首层原始需求版本集合
        List<ConsistenceDemandForecastVersionVO> firstDemandVersionList =
                demandVersionVOS.stream().filter(k -> StringUtils.isEmpty(k.getParentVersionId())).collect(Collectors.toList());
        // 其他层根据父级版本号分组
        Map<String, List<ConsistenceDemandForecastVersionVO>> childVersionGroup =
                demandVersionVOS.stream().filter(k -> StringUtils.isNotEmpty(k.getParentVersionId())).collect(Collectors.groupingBy(ConsistenceDemandForecastVersionVO::getParentVersionId));
        // 组装为树结构返回前端
        List<ConsistenceDemandForecastVersionVO> versionTreeVOS = new ArrayList<>();
        for (ConsistenceDemandForecastVersionVO originDemandVersionVO : firstDemandVersionList) {
            toDemandVersionTreeVO(childVersionGroup, originDemandVersionVO, consistenceDemandForecastVersionVOMapOfId);
            versionTreeVOS.add(originDemandVersionVO);
        }
        return versionTreeVOS;
    }

    @Override
    public List<ConsistenceDemandForecastVersionVO> getVersionCodes(String planPeriod) {
        Map<String, Object> queryMap = new HashMap<>();
        if (StringUtils.isNotEmpty(planPeriod)) {
            queryMap.put("planPeriod", planPeriod);
        }
        List<ConsistenceDemandForecastVersionVO> demandVersionVOS = this.selectByParams(queryMap);
        if (CollectionUtils.isNotEmpty(demandVersionVOS)) {
            // 获取二级版本号集合
            return demandVersionVOS.stream().filter(k -> 
                    StringUtils.isEmpty(k.getOemCode()) && StringUtils.isNotEmpty(k.getParentVersionId()))
                    .sorted(Comparator.comparing(ConsistenceDemandForecastVersionVO::getModifyTime,
                            Comparator.reverseOrder())).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * 获取目标主机厂数据
     *
     * @param versionId 版本ID
     * @return java.util.List<java.lang.String>
     */
    public List<String> getOemCodeOrigin(String versionId) {
        // 如果目标一致性业务预测版本为空则默认查所有主机厂编码
        List<String> codeOriginList;
        if (StringUtils.isEmpty(versionId)) {
            List<OemVO> oemVOS = oemService.selectAll();
            codeOriginList = oemVOS.stream().map(OemVO::getOemCode).distinct().collect(Collectors.toList());
        } else {
            // 根据版本号查询主机厂信息
            List<ConsistenceDemandForecastVersionVO> demandVersionVOS = this.selectByParams(ImmutableMap.of(
                    "parentVersionId", versionId));
            codeOriginList =
                    demandVersionVOS.stream().map(ConsistenceDemandForecastVersionVO::getOemCode).distinct().collect(Collectors.toList());
        }
        return codeOriginList;
    }

    @Override
    public void publishVersion(String versionId) {
        // 更新版本数据为已发布
        List<ConsistenceDemandForecastVersionVO> secondVersionVOS = this.selectByParams(ImmutableMap.of("id",
                versionId));
        List<ConsistenceDemandForecastVersionVO> thirdVersionVOS = this.selectByParams(ImmutableMap.of(
                "parentVersionId", versionId));
        secondVersionVOS.addAll(thirdVersionVOS);
        secondVersionVOS.forEach(x -> x.setVersionStatus(VersionStatusEnum.PUBLISHED.getCode()));
        List<ConsistenceDemandForecastVersionDTO> consistenceDemandForecastVersionDTOS =
                ConsistenceDemandForecastVersionConvertor.INSTANCE.vo2Dtos(secondVersionVOS);
        this.doUpdateBatch(consistenceDemandForecastVersionDTOS);
        String scenario = SystemHolder.getScenario();
        Integer planHorizon = secondVersionVOS.stream().map(ConsistenceDemandForecastVersionVO::getPlanHorizon).distinct().findFirst().orElse(null);
        String planPeriod = secondVersionVOS.stream().map(ConsistenceDemandForecastVersionVO::getPlanPeriod).distinct().findFirst().orElse(null);
        String capacityPeriod = planHorizon == null ? null : String.valueOf(planHorizon);
        CompletableFuture.runAsync(() -> {
            try {
                mpsFeign.doRefreshCapacityBalance(scenario, planPeriod, capacityPeriod);
            } catch (Exception e) {
                log.error("产能平衡计算失败", e);
            }
        });
    }

    @Override
    public List<ConsistenceDemandForecastVersionVO> selectForecastVersionALlPlanPeriodNew() {
        return ConsistenceDemandForecastVersionConvertor.INSTANCE.po2Vos(consistenceDemandForecastVersionDao.selectForecastVersionALlPlanPeriodNew());
    }

    @Override
    public List<ConsistenceDemandForecastVersionVO> selectPublishVersionDetail() {
        List<ConsistenceDemandForecastVersionPO> demandForecastVersionPOS =
                consistenceDemandForecastVersionDao.selectPublishVersionDetail(PublishStatusEnum.PUBLISHED.getCode());
        return ConsistenceDemandForecastVersionConvertor.INSTANCE.po2Vos(demandForecastVersionPOS);
    }

    @Override
    public List<DemandForecastEstablishmentVO> selectMaxVersionCodeData(String demandCategory, String oemCode,
                                                                        List<String> productCodes) {
        return consistenceDemandForecastVersionDao.selectMaxVersionCodeData(demandCategory, oemCode, productCodes);
    }

    @Override
    public List<ConsistenceDemandForecastVersionVO> selectMaxVersionByParams(Map<String, Object> params) {
        return consistenceDemandForecastVersionDao.selectMaxVersionByParams(params);
    }

    @Override
    public BaseResponse<List<LabelValue<String>>> dropDownVersion() {
        List<LabelValue<String>> result = Lists.newArrayList();
        Map<String, Object> queryMap = MapUtil.newHashMap();
        List<ConsistenceDemandForecastVersionVO> demandVersionVOS = this.selectByParams(queryMap);
        if (CollectionUtils.isEmpty(demandVersionVOS)) {
            return BaseResponse.success(result);
        }
        // 获取二级版本号集合
        demandVersionVOS.stream().filter(k -> org.apache.commons.lang3.StringUtils.isBlank(k.getOemCode()) &&
                        org.apache.commons.lang3.StringUtils.isNotBlank(k.getParentVersionId())).
                sorted(Comparator.comparing(ConsistenceDemandForecastVersionVO::getVersionCode).reversed()).
                forEach(x -> {
                    LabelValue<String> labelValue = new LabelValue<>();
                    labelValue.setLabel(x.getVersionCode());
                    labelValue.setValue(x.getId());
                    result.add(labelValue);
                });
        return BaseResponse.success(result);
    }

    @Override
    public String getLastVersionId(String versionId) {
        List<LabelValue<String>> result = Lists.newArrayList();
        List<ConsistenceDemandForecastVersionVO> demandVersions = this.selectByParams(new HashMap<>());
        if (CollectionUtils.isEmpty(demandVersions)) {
            return null;
        }
        // 获取二级版本号集合
        demandVersions.stream().filter(k -> org.apache.commons.lang3.StringUtils.isBlank(k.getOemCode()) &&
                        org.apache.commons.lang3.StringUtils.isNotBlank(k.getParentVersionId())).
                sorted(Comparator.comparing(ConsistenceDemandForecastVersionVO::getVersionCode).reversed()).
                forEach(x -> {
                    LabelValue<String> labelValue = new LabelValue<>();
                    labelValue.setLabel(x.getVersionCode());
                    labelValue.setValue(x.getId());
                    result.add(labelValue);
                });
        for (int i = 0; i < result.size(); i++) {
            String value = result.get(i).getValue();
            if (StringUtils.equals(value, versionId) && i+1 < result.size()){
                    return result.get(i+1).getValue();
                }

        }
        return null;
    }

    /**
     * 添加子节点
     *
     * @param childVersionGroup 子节点分组
     * @param demandVersionVO   父节点
     */
    private void toDemandVersionTreeVO(Map<String, List<ConsistenceDemandForecastVersionVO>> childVersionGroup,
                                       ConsistenceDemandForecastVersionVO demandVersionVO, Map<String,
            ConsistenceDemandForecastVersionVO> consistenceDemandForecastVersionVOMapOfId) {
        if (childVersionGroup.containsKey(demandVersionVO.getId())) {
            List<ConsistenceDemandForecastVersionVO> demandVersionVOList = new ArrayList<>();
            for (ConsistenceDemandForecastVersionVO versionVO : childVersionGroup.get(demandVersionVO.getId())) {
                if (org.apache.commons.lang3.StringUtils.isNotBlank(versionVO.getDemandForecastVersionId()) && consistenceDemandForecastVersionVOMapOfId.containsKey(versionVO.getDemandForecastVersionId())) {
                    versionVO.setDemandForecastVersionCode(consistenceDemandForecastVersionVOMapOfId.get(versionVO.getDemandForecastVersionId()).getVersionCode());
                }
                toDemandVersionTreeVO(childVersionGroup, versionVO, consistenceDemandForecastVersionVOMapOfId);
                demandVersionVOList.add(versionVO);
            }
            demandVersionVO.setDemandVersionVOList(demandVersionVOList);
        }
    }

    @Override
    public List<ConsistenceDemandForecastVersionVO> selectVersionInfoByCapacityBalance() {
        return consistenceDemandForecastVersionDao.selectVersionInfo();
    }

    @Override
    public List<ConsistenceDemandForecastVersionVO> selectByPlanPeriodList(List<String> planPeriodList, String publishStatus) {
        return consistenceDemandForecastVersionDao.selectByPlanPeriodList(planPeriodList, publishStatus);
    }

	@Override
	public String getMaxVersionCodes(String planPeriod) {
		Map<String, Object> queryMap = new HashMap<>();
        if (StringUtils.isNotEmpty(planPeriod)) {
            queryMap.put("planPeriod", planPeriod);
        }
        List<ConsistenceDemandForecastVersionVO> demandVersionVOS = this.selectByParams(queryMap);
        String prefix = DfpConstants.CONSISTENCE_DEMAND_FORECAST_VERSION_CODE_PRE + planPeriod;
        if (CollectionUtils.isEmpty(demandVersionVOS)) {
        	return prefix + DfpConstants.CONSISTENCE_DEMAND_FORECAST_VERSION_CODE;
        }
        demandVersionVOS = demandVersionVOS.stream().filter(k -> StringUtils.isEmpty(k.getOemCode()) && StringUtils.isNotEmpty(k.getParentVersionId())).
                    sorted(Comparator.comparing(ConsistenceDemandForecastVersionVO::getModifyTime,
                            Comparator.reverseOrder())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(demandVersionVOS)) {
        	return prefix + DfpConstants.CONSISTENCE_DEMAND_FORECAST_VERSION_CODE;
        }else {
        	String versionCode = consistenceDemandForecastVersionDao.selectMaxVersionCodeByOemCodeList(planPeriod, null);
            return prefix + getNewVersionCode(versionCode);
        }
	}
	
	/**
     * 最新版本号加一
     *
     * @param targetVersionCode 目标版本号
     * @return java.lang.String
     */
    protected String getNewVersionCode(String targetVersionCode) {
        String newVersionCode;
        int number = 1;
        if (StringUtils.isNotEmpty(targetVersionCode)) {
            if (targetVersionCode.length() < 5) {
                throw new IllegalArgumentException("The version code must be at least 5 characters long.");
            }
            String lastFive = targetVersionCode.substring(targetVersionCode.length() - 5);
            number = Integer.parseInt(lastFive);
            // 数字加1
            number++;
        }
        // 格式化为五位数，不足五位补零
        newVersionCode = String.format("%05d", number);
        return newVersionCode;
    }

	@Override
	public BaseResponse<Void> doCreateVersion(ConsistenceDemandForecastVersionDTO dto) {
		//1.新建一致性需求预测版本
		String versionCode = dto.getVersionCode();
		String maxVersionCodes = this.getMaxVersionCodes(dto.getPlanPeriod());
		if(!Objects.equals(versionCode, maxVersionCodes)) {
			throw new BusinessException("当前一致性需求预测已创建,请重新创建!");
		}
		//获取父亲本版数据
		ConsistenceDemandForecastVersionPO parentVersion = consistenceDemandForecastVersionDao.selectFirstVersionInfoByPlanPeriod(dto.getPlanPeriod());
		if(parentVersion == null) {
			//新增版本
			parentVersion = new ConsistenceDemandForecastVersionPO();
			parentVersion.setId(UUIDUtil.getUUID());
			parentVersion.setPlanPeriod(dto.getPlanPeriod());
	        BasePOUtils.insertFiller(parentVersion);
	        consistenceDemandForecastVersionDao.insertWithPrimaryKey(parentVersion);
		}
		//获取需求预测版本号数据
		DemandForecastVersionVO demandForecastVersionVO = demandForecastVersionService
				.selectByPrimaryKey(dto.getDemandForecastVersionId());
		ConsistenceDemandForecastVersionPO consistenceDemandForecastVersionPO = new ConsistenceDemandForecastVersionPO();
		consistenceDemandForecastVersionPO.setId(UUIDUtil.getUUID());
		consistenceDemandForecastVersionPO.setPlanPeriod(dto.getPlanPeriod());
		consistenceDemandForecastVersionPO.setPlanHorizon(demandForecastVersionVO.getPlanHorizon());
		consistenceDemandForecastVersionPO.setPlanGranularity(demandForecastVersionVO.getPlanGranularity());
		consistenceDemandForecastVersionPO.setParentVersionId(parentVersion.getId());
		consistenceDemandForecastVersionPO.setDemandForecastVersionId(dto.getDemandForecastVersionId());
		consistenceDemandForecastVersionPO.setGenerateType(GenerateTypeEnum.MANUAL.getCode());
		consistenceDemandForecastVersionPO.setVersionStatus(PublishStatusEnum.UNPUBLISH.getCode());
		consistenceDemandForecastVersionPO.setVersionCode(dto.getVersionCode());
		consistenceDemandForecastVersionPO.setReviewVersionFlag(dto.getReviewVersionFlag());
        BasePOUtils.insertFiller(consistenceDemandForecastVersionPO);
        consistenceDemandForecastVersionDao.insertWithPrimaryKey(consistenceDemandForecastVersionPO);
		//2.拷贝一致性需求预测数据
        HashMap<String, Object> params = MapUtil.of("forecastVersionId", dto.getDemandForecastVersionId());
        params.put("enabled", YesOrNoEnum.YES.getCode());
        List<DemandForecastEstablishmentVO> establishmentList = demandForecastEstablishmentService.selectByParams(params);
		//按照车型编码，本厂编码进行分组
        Map<String, List<DemandForecastEstablishmentVO>> establishmentMap = establishmentList
        		.stream().collect(Collectors.groupingBy(e -> e.getOemCode() + "_" 
        + e.getVehicleModelCode() + "_" + e.getProductCode()+ "_" + e.getDemandCategory()));
        List<ConsistenceDemandForecastDataDTO> batchDataList = new ArrayList<>();
        List<ConsistenceDemandForecastDataDetailDTO> batchDataDetailList = new ArrayList<>();
        for (List<DemandForecastEstablishmentVO> establishmentValueList : establishmentMap.values()) {
            ConsistenceDemandForecastDataDTO dataAdd = assembleConsistenceDemandForecastDataDTO(establishmentValueList,
                    consistenceDemandForecastVersionPO);
            batchDataList.add(dataAdd);
        	// 3.拷贝一致性需求预测数据明细
        	for (DemandForecastEstablishmentVO valueDetail : establishmentValueList) {
        		ConsistenceDemandForecastDataDetailDTO dataDetailAdd = new ConsistenceDemandForecastDataDetailDTO();
        		dataDetailAdd.setId(UUIDUtil.getUUID());
        		dataDetailAdd.setConsistenceDemandForecastDataId(dataAdd.getId());
        		dataDetailAdd.setForecastTime(valueDetail.getForecastTime());
        		dataDetailAdd.setForecastQuantity(valueDetail.getDemandForecast());
        		dataDetailAdd.setCustomerForecastsQuantity(valueDetail.getCustomerForecast());
        		dataDetailAdd.setAlgorithmForecastsQuantity(valueDetail.getAlgorithmForecast() == null ?
                        BigDecimal.ZERO : valueDetail.getAlgorithmForecast());
        		dataDetailAdd.setActualShipmentQuantity(valueDetail.getDeliveryNum());
        		dataDetailAdd.setAdjustQuantity(valueDetail.getChangeValue());
        		dataDetailAdd.setAdjustType(valueDetail.getChangeType());
        		batchDataDetailList.add(dataDetailAdd);
			}
		}
        if(CollectionUtils.isNotEmpty(batchDataList)) {
        	consistenceDemandForecastDataService.doCreateBatch(batchDataList);
        }
        if(CollectionUtils.isNotEmpty(batchDataDetailList)) {
        	consistenceDemandForecastDataDetailService.doCreateBatch(batchDataDetailList);
        }
		return BaseResponse.success(BaseResponse.OP_SUCCESS);
	}

    private static ConsistenceDemandForecastDataDTO assembleConsistenceDemandForecastDataDTO(List<DemandForecastEstablishmentVO> establishmentValueList, ConsistenceDemandForecastVersionPO consistenceDemandForecastVersionPO) {
        DemandForecastEstablishmentVO demandForecastEstablishmentVO = establishmentValueList.get(0);
        ConsistenceDemandForecastDataDTO dataAdd = new ConsistenceDemandForecastDataDTO();
        dataAdd.setId(UUIDUtil.getUUID());
        dataAdd.setVersionId(consistenceDemandForecastVersionPO.getId());
        dataAdd.setDemandCategory(demandForecastEstablishmentVO.getDemandCategory());
        dataAdd.setOemCode(demandForecastEstablishmentVO.getOemCode());
        dataAdd.setVehicleModelCode(demandForecastEstablishmentVO.getVehicleModelCode());
        dataAdd.setProductCode(demandForecastEstablishmentVO.getProductCode());
        dataAdd.setForecastType(com.yhl.scp.dfp.common.enums.DemandTypeEnum.LOADING_DEMAND.getCode());
        dataAdd.setVersionStatus(PublishStatusEnum.UNPUBLISH.getCode());
        return dataAdd;
    }

    @Override
    public ExecutionMonitorDTO getExecutionMonitorDTO() {
        ExecutionMonitorDTO executionMonitorDTO = new ExecutionMonitorDTO();
        ConsistenceDemandForecastVersionPO newData = consistenceDemandForecastVersionDao.getNewData();
        executionMonitorDTO.setSaleDate(newData.getPlanPeriod());
        executionMonitorDTO.setVersionId(newData.getId());
        return executionMonitorDTO;
    }

    @Override
    public ConsistenceDemandForecastVersionVO selectConsistenceDemandForecastVersionLatestPublished() {
        ConsistenceDemandForecastVersionPO newData = consistenceDemandForecastVersionDao.getNewData();
        return ConsistenceDemandForecastVersionConvertor.INSTANCE.po2Vo(newData);
    }

	@Override
	public ConsistenceDemandForecastVersionVO selectOneMaxVersionByParams(Map<String, String> params) {
		return consistenceDemandForecastVersionDao.selectOneMaxVersionByParams(params);
	}

	@Override
	public List<String> selectHistoryVersionByPlanPeriods(List<String> planPeriods) {
		return consistenceDemandForecastVersionDao.selectHistoryVersionByPlanPeriods(planPeriods);
	}
}