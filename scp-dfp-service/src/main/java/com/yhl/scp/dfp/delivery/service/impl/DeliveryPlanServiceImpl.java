package com.yhl.scp.dfp.delivery.service.impl;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.PlatformUser;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.common.constants.Constants;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.dcp.apiLog.vo.ExtApiLogVO;
import com.yhl.scp.dfp.basic.forecast.enums.VersionStatusEnum;
import com.yhl.scp.dfp.calendar.service.DfpResourceCalendarService;
import com.yhl.scp.dfp.calendar.vo.ResourceCalendarVO;
import com.yhl.scp.dfp.clean.vo.CleanDemandDataDetailVO;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum;
import com.yhl.scp.dfp.common.enums.PublishStatusEnum;
import com.yhl.scp.dfp.common.enums.SwitchRelationModeEnum;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastDataDetailService;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastDataService;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastVersionService;
import com.yhl.scp.dfp.consistence.vo.*;
import com.yhl.scp.dfp.delivery.calc.DeliveryPlanCalcSpace;
import com.yhl.scp.dfp.delivery.calc.InventoryShiftCalcVO;
import com.yhl.scp.dfp.delivery.convertor.DeliveryPlanConvertor;
import com.yhl.scp.dfp.delivery.convertor.DeliveryPlanDateUtils;
import com.yhl.scp.dfp.delivery.convertor.DeliveryPlanDetailConvertor;
import com.yhl.scp.dfp.delivery.convertor.DeliveryPlanPublishedConvertor;
import com.yhl.scp.dfp.delivery.domain.entity.DeliveryPlanDO;
import com.yhl.scp.dfp.delivery.domain.service.DeliveryPlanDomainService;
import com.yhl.scp.dfp.delivery.dto.*;
import com.yhl.scp.dfp.delivery.infrastructure.dao.DeliveryPlanDao;
import com.yhl.scp.dfp.delivery.infrastructure.dao.DeliveryPlanDetailDao;
import com.yhl.scp.dfp.delivery.infrastructure.dao.DeliveryPlanPublishedDao;
import com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanDetailPO;
import com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanPO;
import com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanPublishedPO;
import com.yhl.scp.dfp.delivery.service.*;
import com.yhl.scp.dfp.delivery.vo.*;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanLockConfigService;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanPublishedCompareService;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanPublishedService;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanRecordService;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanReplenishConfigService;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanService;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanVersionService;
import com.yhl.scp.dfp.excel.handler.CustomColumnWidthHandler;
import com.yhl.scp.dfp.material.service.PartRiskLevelService;
import com.yhl.scp.dfp.material.vo.PartRiskLevelVO;
import com.yhl.scp.dfp.oem.enums.OemBusinessTypeEnum;
import com.yhl.scp.dfp.oem.enums.OemPickUpTypeEnum;
import com.yhl.scp.dfp.oem.infrastructure.dao.OemProductLineMapDao;
import com.yhl.scp.dfp.oem.infrastructure.po.OemProductLineMapPO;
import com.yhl.scp.dfp.oem.service.OemInventorySubmissionService;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.service.OemStockPointMapService;
import com.yhl.scp.dfp.oem.service.OemTransportTimeService;
import com.yhl.scp.dfp.oem.vo.OemInventorySubmissionVO;
import com.yhl.scp.dfp.oem.vo.OemStockPointMapVO;
import com.yhl.scp.dfp.oem.vo.OemTransportTimeVO;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.safety.service.SafetyStockLevelService;
import com.yhl.scp.dfp.safety.vo.SafetyStockLevelVO;
import com.yhl.scp.dfp.stock.convertor.InventoryShiftConvertor;
import com.yhl.scp.dfp.stock.infrastructure.dao.InventoryShiftDao;
import com.yhl.scp.dfp.stock.infrastructure.po.InventoryShiftPO;
import com.yhl.scp.dfp.stock.service.InventoryBatchDetailService;
import com.yhl.scp.dfp.stock.service.InventoryShiftService;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.dfp.stock.vo.InventoryShiftVO;
import com.yhl.scp.dfp.switchrelation.service.DfpSwitchRelationBetweenProductService;
import com.yhl.scp.dfp.switchrelation.vo.DfpSwitchRelationBetweenProductVO;
import com.yhl.scp.dfp.switchrelation.vo.SwitchRelationVO;
import com.yhl.scp.dfp.transport.service.TransportRoutingService;
import com.yhl.scp.dfp.transport.vo.TransportRoutingVO;
import com.yhl.scp.dfp.utils.EasyExcelUtil;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseRecordService;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseToWarehouseService;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordMonthVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseMonthVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.rbac.entity.User;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.ips.warning.vo.WarningSqlSettingVO;
import com.yhl.scp.mds.box.vo.BoxInfoVO;
import com.yhl.scp.mds.customer.vo.CustomerVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.enums.ProductTypeEnum;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.feign.MdsFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.product.vo.ProductCandidateResourceTimeVO;
import com.yhl.scp.mds.productBox.vo.ProductBoxRelationVO;
import com.yhl.scp.mds.routing.vo.ProductCandidateResourceVO;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.feign.MpsFeign;
import com.yhl.scp.mps.plan.req.MasterPlanReq;
import com.yhl.scp.mps.plan.vo.DeliveryPlanGeneralViewDetailVO;
import com.yhl.scp.mps.plan.vo.DeliveryPlanGeneralViewVO;
import com.yhl.scp.mps.plan.vo.MasterPlanWorkOrderBodyVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.time.format.DateTimeFormatter.ISO_LOCAL_DATE;

/**
 * <code>DeliveryPlanServiceImpl</code>
 * <p>
 * 发货计划表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-21 13:51:24
 */
@Slf4j
@Service
public class DeliveryPlanServiceImpl extends AbstractService implements DeliveryPlanService {

    private static final BigDecimal NORMAL_STRATEGY_DEFAULT = BigDecimal.valueOf(0.5);

    public static final String SUB_INVENTORY_CPSJ = "CPSJ";

    @Resource
    OemInventorySubmissionService oemInventorySubmissionService;
    @Resource
    DeliveryPlanVersionService deliveryPlanVersionService;
    @Resource
    OemService oemService;
    @Resource
    DeliveryPlanLockConfigService deliveryPlanLockConfigService;
    @Resource
    DeliveryPlanReplenishConfigService deliveryPlanReplenishConfigService;
    @Resource
    SafetyStockLevelService safetyStockLevelService;
    @Resource
    TransportRoutingService transportRoutingService;
    @Resource
    private DeliveryPlanDao deliveryPlanDao;
    @Resource
    private DeliveryPlanDetailDao deliveryPlanDetailDao;
    @Resource
    private InventoryShiftDao inventoryShiftDao;
    @Resource
    private DeliveryPlanDomainService deliveryPlanDomainService;
    @Resource
    private NewMdsFeign newMdsFeign;
    @Resource
    private MdsFeign mdsFeign;
    @Resource
    private PartRiskLevelService partRiskLevelService;
    @Resource
    private DeliveryPlanPublishedService deliveryPlanPublishedService;
    @Resource
    private DeliveryPlanPublishedDao deliveryPlanPublishedDao;
    @Resource
    private DeliveryPlanPublishedCompareService deliveryPlanPublishedCompareService;
    @Resource
    private OemTransportTimeService oemTransportTimeService;
    @Resource
    private OemStockPointMapService oemStockPointMapService;
    @Resource
    private OemProductLineMapDao oemProductLineMapDao;
    @Resource
    private WarehouseReleaseToWarehouseService warehouseReleaseToWarehouseService;
    @Resource
    private NewDcpFeign newDcpFeign;
    @Resource
    private InventoryShiftService inventoryShiftService;
    @Resource
    private DeliveryPlanService deliveryPlanService;
    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private DfpSwitchRelationBetweenProductService dfpSwitchRelationBetweenProductService;
    @Resource
    private MpsFeign mpsFeign;
    @Resource
    private InventoryBatchDetailService inventoryBatchDetailService;

    @Resource
    private DeliveryPlanRecordService deliveryPlanRecordService;
    
    @Resource
    private DfpResourceCalendarService dfpResourceCalendarService;
    @Resource
    private DeliveryPlanPublishedCompareDayService deliveryPlanPublishedCompareDayService;

    @Resource
    private WarehouseReleaseRecordService warehouseReleaseRecordService;

    @Resource
    private ConsistenceDemandForecastVersionService consistenceDemandForecastVersionService;

    @Resource
    private ConsistenceDemandForecastDataService consistenceDemandForecastDataService;

    @Resource
    private ConsistenceDemandForecastDataDetailService consistenceDemandForecastDataDetailService;

    /**
     * 根据期末库存天数获取标准安全库存水位值
     *
     * @param inventoryShiftPOS
     * @param i
     */
    @SuppressWarnings("unused")
    private static void setTargetEndingInventory(List<InventoryShiftPO> inventoryShiftPOS, int i) {
        // 获取覆盖的总天数
        BigDecimal totalDay = inventoryShiftPOS.get(i).getTargetEndingInventoryDays().setScale(0, RoundingMode.UP);
        BigDecimal integerPart = inventoryShiftPOS.get(i).getTargetEndingInventoryDays().setScale(0, RoundingMode.DOWN);
        // 获取不满足天数的百分比
        BigDecimal decimalPart = inventoryShiftPOS.get(i).getTargetEndingInventoryDays().subtract(integerPart);
        int tempCount = 0;
        // 获取不满足天数的百分比
        for (int j = i + 1; j < i + 1 + totalDay.intValue() && j < inventoryShiftPOS.size(); j++) {
            if (j < i + 1 + integerPart.intValue()) {
                tempCount += inventoryShiftPOS.get(j).getCustomerDemand();
            } else {
                tempCount += new BigDecimal(inventoryShiftPOS.get(j).getCustomerDemand()).multiply(decimalPart).setScale(0, RoundingMode.UP).intValue();
            }
        }
        inventoryShiftPOS.get(i).setTargetEndingInventory(tempCount);
    }

    private void calculateDemandDateList(int supplyDays, List<Date> dateList) {
        if (supplyDays > 0) {
            Date lastDate = dateList.get(dateList.size() - 1);
            for (int i = 1; i <= supplyDays; i++) {
                dateList.add(DateUtils.moveDay(lastDate, i));
            }
        }
    }

    private List<Date> initEndingInventoryDateList(List<Date> dateList) {
        List<Date> result = new ArrayList<>(dateList);
        Date lastDate = dateList.get(dateList.size() - 1);
        for (int i = 1; i <= 31; i++) {
            result.add(DateUtils.moveDay(lastDate, i));
        }
        return result;
    }

    private static int getExpectEndingInventory(List<Date> dateList,
                                                Map<String, Integer> demandQuantityGroupByDay,
                                                int i,
                                                BigDecimal expectEndingInventoryDays) {
        int expectEndingInventory = 0;

        if (expectEndingInventoryDays.compareTo(BigDecimal.ZERO) == 0) {
            return expectEndingInventory;
        }
        expectEndingInventoryDays = expectEndingInventoryDays.setScale(1);


        int endIndex = i + BigDecimal.valueOf(expectEndingInventoryDays.doubleValue())
                .setScale(0, RoundingMode.UP).intValue();
        String str = expectEndingInventoryDays.toPlainString();
        int decimalIndex = str.indexOf('.');
        String scale = "0";
        if (decimalIndex != -1) {
            scale = str.substring(str.indexOf('.'));
        }

        Date dateBefore = dateList.get(i);
        int a = 1;
        for (int j = i + 1; j <= endIndex; j++) {
            Date dateAfter = DateUtils.moveDay(dateBefore, a++);
            int customerDemandAfter = demandQuantityGroupByDay.get(DateUtils.dateToString(dateAfter)) == null ? 0 :
                    demandQuantityGroupByDay.get(DateUtils.dateToString(dateAfter));
            if (".0".equalsIgnoreCase(scale)) {
                expectEndingInventory = expectEndingInventory + customerDemandAfter;
            } else {
                if (j < endIndex) {
                    expectEndingInventory = expectEndingInventory + customerDemandAfter;
                } else {
                    expectEndingInventory = expectEndingInventory + BigDecimal.valueOf(customerDemandAfter)
                            .multiply(BigDecimal.valueOf(Double.parseDouble("0" + scale))).intValue();
                }
            }
        }
        return expectEndingInventory;
    }

    private static LinkedList<BigDecimal> getExpectEndingInventoryDays(DeliveryPlanLockConfigVO deliveryPlanLockConfigVO,
                                                                       DeliveryPlanReplenishConfigVO deliveryPlanReplenishConfigVO,
                                                                       BigDecimal endingInventoryDays) {
        // 根据锁定配置和安全库存获取库存天数上下限，锁定天数
        BigDecimal stockDaysMinLimit = deliveryPlanLockConfigVO.getStockDaysMinLimit();
        BigDecimal stockDaysMaxLimit = deliveryPlanLockConfigVO.getStockDaysMaxLimit();

        BigDecimal expectEndingInventoryDays = BigDecimal.ZERO;
        LinkedList<BigDecimal> expectEndingInventoryDaysList = new LinkedList<>();

        // 常规补货
        if (endingInventoryDays.compareTo(stockDaysMinLimit) >= 0 && endingInventoryDays.compareTo(stockDaysMaxLimit) < 0) {
            expectEndingInventoryDays = endingInventoryDays.add(null == deliveryPlanReplenishConfigVO ?
                    NORMAL_STRATEGY_DEFAULT : deliveryPlanReplenishConfigVO.getNormalStrategy());
            expectEndingInventoryDays = expectEndingInventoryDays.compareTo(stockDaysMaxLimit) >= 0 ?
                    stockDaysMaxLimit : expectEndingInventoryDays;
            expectEndingInventoryDaysList.add(expectEndingInventoryDays);
        }
        // 紧急补货
        else if (endingInventoryDays.compareTo(stockDaysMinLimit) < 0) {
            if (null == deliveryPlanReplenishConfigVO
                    || "MIN".equalsIgnoreCase(deliveryPlanReplenishConfigVO.getEmergencyStrategy())) {
                expectEndingInventoryDays = stockDaysMinLimit;
                expectEndingInventoryDaysList.add(expectEndingInventoryDays);
                int maxTimes = 100;
                for (int i = 1; i < maxTimes/*Integer.MAX_VALUE*/; i++) {
                    BigDecimal expectEndingInventoryDaysAfter =
                            expectEndingInventoryDays.add(null == deliveryPlanReplenishConfigVO ?
                                    NORMAL_STRATEGY_DEFAULT.multiply(BigDecimal.valueOf(i)) :
                                    deliveryPlanReplenishConfigVO.getNormalStrategy().multiply(BigDecimal.valueOf(i)));
                    if (expectEndingInventoryDaysAfter.compareTo(stockDaysMaxLimit) >= 0) {
                        expectEndingInventoryDaysList.add(stockDaysMaxLimit);
                        break;
                    } else {
                        expectEndingInventoryDaysList.add(expectEndingInventoryDaysAfter);
                    }
                }
            } else if ("STANDARD".equalsIgnoreCase(deliveryPlanReplenishConfigVO.getEmergencyStrategy())) {
                expectEndingInventoryDays = stockDaysMaxLimit;
                expectEndingInventoryDaysList.add(expectEndingInventoryDays);
            }
        } else {
            expectEndingInventoryDaysList.add(expectEndingInventoryDays);
        }
        return expectEndingInventoryDaysList;
    }

    private static BigDecimal getManualEndingInventoryDays(List<Date> dateList,
                                                           Integer endingInventory,
                                                           int i,
                                                           Map<String, Integer> inventoryShiftPOMap) {
        BigDecimal endingInventoryDays = BigDecimal.ZERO;


        if (endingInventory == 0 && i + 1 < dateList.size()) {
            Date dateAfter = dateList.get(i + 1);
            int customerDemandAfter = inventoryShiftPOMap.get(DateUtils.dateToString(dateAfter)) == null
                    ? 0 : inventoryShiftPOMap.get(DateUtils.dateToString(dateAfter));
            if (customerDemandAfter == 0) {
                return BigDecimal.ONE;
            } else {
                return endingInventoryDays;
            }
        }

        if (endingInventory > 0) {
            for (int j = i + 1; j < dateList.size(); j++) {
                Date dateAfterOne = dateList.get(j);
                int customerDemandAfterOne = inventoryShiftPOMap.get(DateUtils.dateToString(dateAfterOne)) == null
                        ? 0 : inventoryShiftPOMap.get(DateUtils.dateToString(dateAfterOne));
                endingInventory = endingInventory - customerDemandAfterOne;
                if (j == i + 1) {
                    if (endingInventory < 0) {
                        break;
                    }
                    endingInventoryDays = endingInventoryDays.add(BigDecimal.ONE);
                    Date dateAfterTwo = DateUtils.moveDay(dateAfterOne, 1);
                    int customerDemandAfterTwo = inventoryShiftPOMap.get(DateUtils.dateToString(dateAfterTwo)) == null
                            ? 0 : inventoryShiftPOMap.get(DateUtils.dateToString(dateAfterTwo));
                    if (customerDemandAfterTwo == 0 && endingInventory <= 0) {
                        return endingInventoryDays;
                    }
                } else {
                    if (endingInventory < 0) {
                        endingInventoryDays = endingInventoryDays.add(BigDecimal.valueOf(endingInventory)
                                .add(BigDecimal.valueOf(customerDemandAfterOne))
                                .divide(BigDecimal.valueOf(customerDemandAfterOne), 1, RoundingMode.HALF_UP));
                        break;
                    } else {
                        endingInventoryDays = endingInventoryDays.add(BigDecimal.ONE);
                    }
                }
            }
        }
        return endingInventoryDays;
    }

    private static BigDecimal getEndingInventoryDays(List<Date> dateList,
                                                     Map<String, Integer> demandSumGroupByDay,
                                                     Integer endingInventory,
                                                     int i, List<String> oemHolidayList) {
        BigDecimal endingInventoryDays = BigDecimal.ZERO;
//        if (endingInventory == 0 && i + 1 < dateList.size()) {
//            Date dateAfter = dateList.get(i + 1);
//            int customerDemandAfter = demandSumGroupByDay.get(DateUtils.dateToString(dateAfter)) == null
//                    ? 0 : demandSumGroupByDay.get(DateUtils.dateToString(dateAfter));
//            if (customerDemandAfter == 0) {
//                return BigDecimal.ONE;
//            } else {
//                return endingInventoryDays;
//            }
//        }
//        if (endingInventory > 0) {
//            for (int j = i + 1; j < dateList.size(); j++) {
//                Date dateAfterOne = dateList.get(j);
//                int customerDemandAfterOne = demandSumGroupByDay.get(DateUtils.dateToString(dateAfterOne)) == null
//                        ? 0 : demandSumGroupByDay.get(DateUtils.dateToString(dateAfterOne));
//                endingInventory = endingInventory - customerDemandAfterOne;
//                if (j == i + 1) {
//                    if (endingInventory < 0) {
//                        break;
//                    }
//                    endingInventoryDays = endingInventoryDays.add(BigDecimal.ONE);
//                } else {
//                    if (endingInventory < 0) {
//                        endingInventoryDays = endingInventoryDays.add(
//                                BigDecimal.valueOf(endingInventory).add(BigDecimal.valueOf(customerDemandAfterOne))
//                                        .divide(BigDecimal.valueOf(customerDemandAfterOne), 1, RoundingMode.HALF_UP));
//                        break;
//                    } else {
//                        endingInventoryDays = endingInventoryDays.add(BigDecimal.ONE);
//                    }
//                }
//            }
//        }
        
        for (int j = i + 1; j < dateList.size(); j++) {
            Date dateAfterOne = dateList.get(j);
            String dateAfterOneStr = DateUtils.dateToString(dateAfterOne);
            //如果这一天放假，那么这天直接跳过，不计算期末库存天数
            if(oemHolidayList.contains(dateAfterOneStr)) {
            	continue;
            }
            int customerDemandAfterOne = demandSumGroupByDay.get(DateUtils.dateToString(dateAfterOne)) == null
                    ? 0 : demandSumGroupByDay.get(DateUtils.dateToString(dateAfterOne));
            endingInventory = endingInventory - customerDemandAfterOne;
            if (j == i + 1) {
                if (endingInventory < 0) {
                    break;
                }
                endingInventoryDays = endingInventoryDays.add(BigDecimal.ONE);
            } else {
                if (endingInventory < 0 && customerDemandAfterOne != 0) {
                    endingInventoryDays = endingInventoryDays.add(
                            BigDecimal.valueOf(endingInventory).add(BigDecimal.valueOf(customerDemandAfterOne))
                                    .divide(BigDecimal.valueOf(customerDemandAfterOne), 1, RoundingMode.HALF_UP));
                    break;
                } else {
                    endingInventoryDays = endingInventoryDays.add(BigDecimal.ONE);
                }
            }
        }
        return endingInventoryDays;
    }
    
    /**
     * 获取需要补库的数量
     * @param dateList
     * @param demandSumGroupByDay
     * @param addEndingInventoryDays
     * @param demandQuantity
     * @param i
     * @return
     */
    private static Integer getNeedaddDemandQuantity(List<Date> dateList,
            Map<String, Integer> demandSumGroupByDay,
            BigDecimal addEndingInventoryDays,
            Integer demandQuantity, 
            int i, List<String> oemHolidayList) {
		BigDecimal endingInventoryDays = BigDecimal.ZERO;
		Integer demandQty = 0;
        for (int j = i + 1; j < dateList.size(); j++) {
        	Date dateAfterOne = dateList.get(j);
        	String dateAfterOneStr = DateUtils.dateToString(dateAfterOne);
        	if(oemHolidayList.contains(dateAfterOneStr)) {
        		continue;
        	}
        	int customerDemandAfterOne = demandSumGroupByDay.get(DateUtils.dateToString(dateAfterOne)) == null
                    ? 0 : demandSumGroupByDay.get(DateUtils.dateToString(dateAfterOne));
        	if(customerDemandAfterOne == 0) {
        		endingInventoryDays = endingInventoryDays.add(BigDecimal.ONE);
        	}else {
        		if(endingInventoryDays.add(BigDecimal.ONE).compareTo(addEndingInventoryDays) < 0) {
        			endingInventoryDays = endingInventoryDays.add(BigDecimal.ONE);
        			demandQty = demandQty + customerDemandAfterOne;
        		}else if(endingInventoryDays.add(BigDecimal.ONE).compareTo(addEndingInventoryDays) == 0){
        			//只能加一部分
        			endingInventoryDays = endingInventoryDays.add(BigDecimal.ONE);
        			demandQty = demandQty + customerDemandAfterOne;
        			break;
        		}else {
        			Integer addQty = addEndingInventoryDays.subtract(endingInventoryDays).multiply(BigDecimal.valueOf(customerDemandAfterOne))
        					.setScale(0, BigDecimal.ROUND_UP).intValue();
        			demandQty = demandQty + addQty;
        			break;
        		}
        	}
        }
		return demandQty - demandQuantity;
	}

    /**
     * 计算运输天数
     *
     * @param hours 总小时数
     * @return 运输天数
     */
    public static int calculateTransportDays(double hours) {
        // 小于等于4小时，运输天数为0
        if (hours <= 4) {
            return 0;
        }
        // 每24小时增加一天
        int days = (int) Math.ceil((hours) / 24.0);
        // 如果结果为0，则返回1天
        return days == 0 ? 1 : days;
    }

    @Override
    public BaseResponse<Void> doCreate(DeliveryPlanDTO deliveryPlanDTO) {
        // 0.数据转换
        DeliveryPlanDO deliveryPlanDO = DeliveryPlanConvertor.INSTANCE.dto2Do(deliveryPlanDTO);
        DeliveryPlanPO deliveryPlanPO = DeliveryPlanConvertor.INSTANCE.dto2Po(deliveryPlanDTO);
        // 1.数据校验
        deliveryPlanDomainService.validation(deliveryPlanDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(deliveryPlanPO);
        deliveryPlanDao.insertWithPrimaryKey(deliveryPlanPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(DeliveryPlanDTO deliveryPlanDTO) {
        // 0.数据转换
        DeliveryPlanDO deliveryPlanDO = DeliveryPlanConvertor.INSTANCE.dto2Do(deliveryPlanDTO);
        DeliveryPlanPO deliveryPlanPO = DeliveryPlanConvertor.INSTANCE.dto2Po(deliveryPlanDTO);
        // 1.数据校验
        deliveryPlanDomainService.validation(deliveryPlanDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(deliveryPlanPO);
        deliveryPlanDao.update(deliveryPlanPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<DeliveryPlanDTO> list) {
        List<DeliveryPlanPO> newList = DeliveryPlanConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        deliveryPlanDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<DeliveryPlanDTO> list) {
        List<DeliveryPlanPO> newList = DeliveryPlanConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        deliveryPlanDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return deliveryPlanDao.deleteBatch(idList);
        }
        return deliveryPlanDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public DeliveryPlanVO selectByPrimaryKey(String id) {
        DeliveryPlanPO po = deliveryPlanDao.selectByPrimaryKey(id);
        return DeliveryPlanConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "DELIVERY_PLAN")
    public List<DeliveryPlanVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "DELIVERY_PLAN")
    public List<DeliveryPlanVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<DeliveryPlanVO> dataList = deliveryPlanDao.selectByCondition(sortParam, queryCriteriaParam);
        DeliveryPlanServiceImpl target = SpringBeanUtils.getBean(DeliveryPlanServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<DeliveryPlanVO> invocation(List<DeliveryPlanVO> dataList, Map<String, Object> params,
                                           String invocation) {
        if (CollectionUtils.isEmpty(dataList))
            return dataList;
        List<String> deliveryPlanIds = dataList.stream().map(DeliveryPlanVO::getId).collect(Collectors.toList());
        List<DeliveryPlanDetailPO> deliveryPlanDetails = deliveryPlanDetailDao.selectByDeliveryPlanIds(deliveryPlanIds);
        Map<String, List<DeliveryPlanDetailPO>> detailMap =
                deliveryPlanDetails.stream().collect(Collectors.groupingBy(DeliveryPlanDetailPO::getDeliveryPlanDataId));


        Date lastArrivalDate = deliveryPlanDetails.stream().map(DeliveryPlanDetailPO::getDemandTime).max(Date::compareTo).get();

        //查询新旧物料关系
        List<String> oemCodes = dataList.stream().map(DeliveryPlanVO::getOemCode).distinct().collect(Collectors.toList());
        List<DfpSwitchRelationBetweenProductVO> betweenProductList = dfpSwitchRelationBetweenProductService
				.selectByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(),
    			"oemCodes" , oemCodes));
        Map<String, String> betweenProductMap = new HashMap<>();
        betweenProductList.forEach( e -> {
        	betweenProductMap.put(e.getOemCode() + "&" + e.getNewProductCode(),
        			e.getOldProductCode() + "&" + e.getNewProductCode());
        	betweenProductMap.put(e.getOemCode() + "&" + e.getOldProductCode(),
        			e.getOldProductCode() + "&" + e.getNewProductCode());
        });
        //查询库存推移数据
        List<InventoryShiftPO> inventoryShiftList = inventoryShiftDao.selectByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(),
    			"versionId", dataList.get(0).getVersionId(),
    			"oemCodes" , oemCodes));
        Map<String, List<InventoryShiftPO>> inventoryShiftMap = inventoryShiftList.stream()
                .collect(Collectors.groupingBy(e-> e.getOemCode() + "&" + e.getProductCode(),
                    Collectors.collectingAndThen(Collectors.toList(), list -> list.stream()
                        .sorted(Comparator.comparing(InventoryShiftPO::getPlannedDate))
                        .collect(Collectors.toList()))));

        //查实时库存(销售组织成品库存)
        String scenario = SystemHolder.getScenario();
        if(StringUtils.isEmpty(scenario) && params != null && params.containsKey("scenario")) {
        	scenario = params.get("scenario").toString();
        }
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(scenario,
                "SALE_ORGANIZATION",
                "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        List<String> productCodes = dataList.stream().map(DeliveryPlanVO::getProductCode)
        		.distinct().collect(Collectors.toList());
        List<InventoryBatchDetailVO> InventoryBatchDetailList = inventoryBatchDetailService.selectByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(),
    			"stockPointCode", rangeData,
    			"subinventory", SUB_INVENTORY_CPSJ,
    			"productCodes" , productCodes));
        Map<String, BigDecimal> currentQuantityMap = new HashMap<>();
        for (InventoryBatchDetailVO inventoryBatchDetail : InventoryBatchDetailList) {
        	String productCode = inventoryBatchDetail.getProductCode();
        	BigDecimal currentQuantity = StringUtils.isEmpty(inventoryBatchDetail.getCurrentQuantity())
        			? BigDecimal.ZERO : new BigDecimal(inventoryBatchDetail.getCurrentQuantity());
        	currentQuantityMap.put(productCode,
        			currentQuantityMap.getOrDefault(productCode, BigDecimal.ZERO).add(currentQuantity));
        }


        //获取主机厂运输时间
        List<OemTransportTimeVO> oemTransportTimeVOList = oemTransportTimeService.selectByParams(ImmutableMap.of(
                "oemCodes", oemCodes));
				// 根据主机厂分组
        Map<String, List<OemTransportTimeVO>> oemTransportTimeVOGroup = oemTransportTimeVOList.stream()
        		.collect(Collectors.groupingBy(OemTransportTimeVO::getOemCode));

        for (DeliveryPlanVO data : dataList) {
        	String unkey = data.getOemCode() + "&" + data.getProductCode();
        	List<DeliveryPlanDetailVO> detailList = DeliveryPlanDetailConvertor.INSTANCE
        			.po2Vos(detailMap.get(data.getId()));
        	//1.维护发货计划详情
        	data.setDetailList(detailList);

        	//2.成品库库存
        	data.setFinishedInventory(currentQuantityMap.getOrDefault(data.getProductCode(), BigDecimal.ZERO));

        	//3.期初库存(厂外),在途数量,主机厂期初库存,成品库库存
        	if(betweenProductMap.containsKey(unkey)) {
        		unkey = betweenProductMap.get(unkey);
        	}
        	List<InventoryShiftPO> inventoryShifts = inventoryShiftMap.get(unkey);
        	if(CollectionUtils.isEmpty(inventoryShifts)){
        		continue;
        	}
        	InventoryShiftPO inventoryShiftPO = inventoryShifts.get(0);
        	Integer openingInventory = inventoryShiftPO.getOpeningInventory() == null 
        			? 0 : inventoryShiftPO.getOpeningInventory();
        	Integer oemOpeningInventory = inventoryShiftPO.getOemOpeningInventory() == null 
        			? 0 : inventoryShiftPO.getOemOpeningInventory();
        	Integer receive = inventoryShifts.stream()
        			.filter( e-> e.getReceive() != null)
                    .reduce(0, (sum, e) -> sum + e.getReceive(), Integer::sum);
        	data.setOpeningInventory(openingInventory);
        	data.setOemOpeningInventory(oemOpeningInventory);
        	data.setReceive(receive);

        	//获取运输时间和每日到货数量
        	int oemTransportSuppiyDays = getOemTransportSuppiyDays(oemTransportTimeVOGroup, data);
        	Map<String, Integer> arrivalQty =  detailList.stream().collect(Collectors
        			.toMap( e-> DateUtils.dateToString(DateUtils.moveDay(e.getDemandTime(), oemTransportSuppiyDays)),
        					DeliveryPlanDetailVO::getDemandQuantity,(v1, v2) -> v1));
        	//库存剩余量
        	BigDecimal availableStock = BigDecimal.valueOf(openingInventory)
        			.add(BigDecimal.valueOf(oemOpeningInventory))
        			.add(BigDecimal.valueOf(receive));
        	//4.客户需求数量
        	List<DeliveryPlanCustomerDemandVO> customerDemandList = new ArrayList<>();
        	for (int i = 0; i < inventoryShifts.size(); i++) {
        		InventoryShiftPO inventoryShift = inventoryShifts.get(i);
        		DeliveryPlanCustomerDemandVO customerDemandVO = new DeliveryPlanCustomerDemandVO();
        		customerDemandVO.setCustomerDemand(inventoryShift.getCustomerDemand());
        		customerDemandVO.setPlannedDate(inventoryShift.getPlannedDate());
        		customerDemandList.add(customerDemandVO);

        		Date plannedDate = inventoryShift.getPlannedDate();
        		availableStock = availableStock.add(BigDecimal
        				.valueOf(arrivalQty.getOrDefault(DateUtils.dateToString(plannedDate), 0)));
        		BigDecimal customerDemand = BigDecimal.valueOf(inventoryShift.getCustomerDemand());
        		if(availableStock.compareTo(customerDemand) >= 0) {
        			customerDemandVO.setBackColor("GREEN");
        		}else {
        			//判断未来是否能满足
        			int dateInterval = DateUtils.getDateInterval(plannedDate, lastArrivalDate).intValue();
        			BigDecimal futureStock = availableStock;
        			boolean canMeetLater = false;
        			for (int j = 0; j <= dateInterval; j++) {
        				BigDecimal futureDayStock = BigDecimal.valueOf(arrivalQty
        						.getOrDefault(DateUtils.dateToString(DateUtils.moveDay(plannedDate, j)), 0));
        				futureStock = futureStock.add(futureDayStock);
        				if(futureStock.compareTo(customerDemand) >= 0) {
        					canMeetLater = true;
        					break;
        				}
					}
        			if (canMeetLater) {
        				//满足黄色
        				customerDemandVO.setBackColor("YELLOW");
        			} else {
        				//不满足，红色
        				customerDemandVO.setBackColor("RED");
        			}
        		}
        		//库存扣减
        		availableStock = availableStock.subtract(customerDemand);
			}
        	data.setCustomerDemandList(customerDemandList);

		}
        return dataList;
    }

    @Override
    public List<DeliveryPlanVO> selectByParams(Map<String, Object> params) {
        List<DeliveryPlanPO> list = deliveryPlanDao.selectByParams(params);
        List<DeliveryPlanVO> deliveryPlanVOS = DeliveryPlanConvertor.INSTANCE.po2Vos(list);
        this.queryDeliveryPlanDetail(deliveryPlanVOS);
        return DeliveryPlanConvertor.INSTANCE.po2Vos(list);
    }
    
    
    public List<DeliveryPlanVO> queryDeliveryPlanDetail(List<DeliveryPlanVO> dataList) {
        if (CollectionUtils.isEmpty(dataList))
            return dataList;
        List<String> deliveryPlanIds = dataList.stream().map(DeliveryPlanVO::getId).collect(Collectors.toList());
        List<DeliveryPlanDetailPO> deliveryPlanDetails = deliveryPlanDetailDao.selectByDeliveryPlanIds(deliveryPlanIds);
        Map<String, List<DeliveryPlanDetailPO>> detailMap =
                deliveryPlanDetails.stream().collect(Collectors.groupingBy(DeliveryPlanDetailPO::getDeliveryPlanDataId));
        dataList.forEach(data -> data.setDetailList(DeliveryPlanDetailConvertor
                .INSTANCE.po2Vos(detailMap.get(data.getId()))));
        return dataList;
    }

    @Override
    public List<DeliveryPlanVO> selectAll() {
        List<DeliveryPlanVO> deliveryPlanVOS = this.selectByParams(new HashMap<>(2));
        this.invocation(deliveryPlanVOS, null, null);
        return deliveryPlanVOS;
    }

    @Override
    public List<DeliveryPlanVO> queryCopy() {
        List<DeliveryPlanPO> deliveryPlanPOS = deliveryPlanDao.selectCopy();
        List<DeliveryPlanVO> deliveryPlanVOS = DeliveryPlanConvertor.INSTANCE.po2Vos(deliveryPlanPOS);
        List<String> deliveryPlanIds = deliveryPlanVOS.stream().map(DeliveryPlanVO::getId).collect(Collectors.toList());
        List<DeliveryPlanDetailPO> deliveryPlanDetails =
                deliveryPlanDetailDao.selectByDeliveryPlanIds2(deliveryPlanIds);
        Map<String, List<DeliveryPlanDetailPO>> detailMap =
                deliveryPlanDetails.stream().collect(Collectors.groupingBy(DeliveryPlanDetailPO::getDeliveryPlanDataId));
        deliveryPlanVOS.forEach(data -> data.setDetailList(DeliveryPlanDetailConvertor
                .INSTANCE.po2Vos(detailMap.get(data.getId()))));
        return deliveryPlanVOS;
    }

    @Override
    public List<LabelValue<String>> queryOemDropdownInfo(String versionCode) {
        return deliveryPlanDao.selectOemLabelValue(versionCode);
    }

    @Override
    public DeliveryPlanDoPublishVO doPublish(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException("请勾选需要发布的条目");
        }
        List<DeliveryPlanPO> deliveryPlanPOS = deliveryPlanDao.selectByPrimaryKeys(ids);
        // deliveryPlanPOS转map
        Map<String, DeliveryPlanPO> deliveryPlanPOMap = deliveryPlanPOS.stream().collect(Collectors
                .toMap(DeliveryPlanPO::getId, Function.identity(), (t1, t2) -> t2));

        List<DeliveryPlanDetailPO> deliveryPlanDetailPOS = deliveryPlanDetailDao.selectByDeliveryPlanIds(ids);
        if (CollectionUtils.isEmpty(deliveryPlanDetailPOS)) {
            throw new BusinessException("没有明细数据，不能发布");
        }
        String minDeliveryTimeStr = DateUtils.dateToString(deliveryPlanDetailPOS.stream()
        	    .map(DeliveryPlanDetailPO::getDemandTime)
        	    .min(Date::compareTo).get());
        List<String> oemCodes = deliveryPlanPOS.stream().map(DeliveryPlanPO::getOemCode)
                .distinct().collect(Collectors.toList());
        List<String> productCodes = deliveryPlanPOS.stream().map(DeliveryPlanPO::getProductCode)
                .distinct().collect(Collectors.toList());
        Date now = new Date();
        List<DeliveryPlanPublishedVO> deliveryPlanPublishedVOS = deliveryPlanPublishedService.selectByParams(
                ImmutableMap.of("oemCodes", oemCodes, "productCodes", productCodes, "demandTimeStart", minDeliveryTimeStr));
        Map<String, DeliveryPlanPublishedVO> deliveryPlanPublishedVOMap =
                deliveryPlanPublishedVOS.stream().collect(Collectors.toMap(t -> String.join("&",
                                t.getOemCode(), t.getProductCode(), DateUtils.dateToString(t.getDemandTime()),
                                t.getDemandCategory()), Function.identity(),
                        (t1, t2) -> t2));

        List<DeliveryPlanPublishedDTO> oldDeliveryPlanPublishedDTOS = DeliveryPlanPublishedConvertor.INSTANCE.vo2Dtos(deliveryPlanPublishedVOS);

        //获取所有主机厂数据信息（获取主机厂是否自提属性信息）
        List<OemVO> oemList = oemService.selectByParams(ImmutableMap.of("oemCodes", oemCodes));
        Map<String, OemVO> oemPictUpTypeMap = oemList.stream()
                .collect(Collectors.toMap(OemVO::getOemCode, Function.identity(), (t1, t2) -> t2));

        List<DeliveryPlanPublishedPO> insertList = new ArrayList<>();
        List<DeliveryPlanPublishedPO> updateList = new ArrayList<>();
        Map<String, List<DeliveryPlanDetailPO>> detailMap = deliveryPlanDetailPOS.stream()
                .collect(Collectors.groupingBy(DeliveryPlanDetailPO::getDeliveryPlanDataId));
        for (String k : detailMap.keySet()) {
            List<DeliveryPlanDetailPO> v = detailMap.get(k);
            v.sort(Comparator.comparing(DeliveryPlanDetailPO::getDemandTime));
            DeliveryPlanPO deliveryPlanPO = deliveryPlanPOMap.get(k);
            for (DeliveryPlanDetailPO deliveryPlanDetail : v) {
                String key = String.join("&", deliveryPlanPO.getOemCode(), deliveryPlanPO.getProductCode(),
                        DateUtils.dateToString(deliveryPlanDetail.getDemandTime()), deliveryPlanPO.getDemandCategory());
                DeliveryPlanPublishedPO deliveryPlanPublished = new DeliveryPlanPublishedPO();

                if (deliveryPlanPublishedVOMap.containsKey(key)) {
                    DeliveryPlanPublishedVO deliveryPlanPublishedVO = deliveryPlanPublishedVOMap.get(key);
                    BeanUtils.copyProperties(deliveryPlanPublishedVO, deliveryPlanPublished);
                    updateList.add(deliveryPlanPublished);
                } else {
                    deliveryPlanPublished.setProductCode(deliveryPlanPO.getProductCode());
                    deliveryPlanPublished.setOemCode(deliveryPlanPO.getOemCode());
                    deliveryPlanPublished.setDemandCategory(deliveryPlanPO.getDemandCategory());
                    insertList.add(deliveryPlanPublished);
                }
                //需求时间处理,主机厂出口/国内自提设置早上8点，主机厂是国内非自提晚八点
                OemVO oemVO = oemPictUpTypeMap.get(deliveryPlanPO.getOemCode());
                Date newDemandTime;
                if("出口".equals(oemVO.getMarketType()) 
                		|| ("国内".equals(oemVO.getMarketType()) 
                				&& OemPickUpTypeEnum.PICK_UP.getCode().equals(oemVO.getPickUpType()))) {
                	newDemandTime =
                            DateUtils.stringToDate(DateUtils.dateToString(deliveryPlanDetail.getDemandTime())
                                    + " 08:00:00", DateUtils.COMMON_DATE_STR1);
                }else {
                	newDemandTime =
                            DateUtils.stringToDate(DateUtils.dateToString(deliveryPlanDetail.getDemandTime())
                                    + " 20:00:00", DateUtils.COMMON_DATE_STR1);
                }
                deliveryPlanPublished.setDemandTime(newDemandTime);
                deliveryPlanPublished.setDeliveryPlanDataId(deliveryPlanDetail.getDeliveryPlanDataId());
                deliveryPlanPublished.setDeliveryVersionId(deliveryPlanPO.getVersionId());
                deliveryPlanPublished.setDemandQuantity(deliveryPlanDetail.getDemandQuantity());
                deliveryPlanPublished.setPublishTime(new Date());
                deliveryPlanPublished.setPublisher(SystemHolder.getUserId());
                deliveryPlanPublished.setBoxQuantity(deliveryPlanDetail.getBoxQuantity());
            }
        }

        if (CollectionUtils.isNotEmpty(insertList)) {
            BasePOUtils.insertBatchFiller(insertList, new Date());
            deliveryPlanPublishedDao.insertBatchWithPrimaryKey(insertList);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            BasePOUtils.updateBatchFiller(updateList, new Date());
            deliveryPlanPublishedDao.updateBatch(updateList);
        }

        deliveryPlanPOS.forEach(t -> t.setPublishStatus(PublishStatusEnum.PUBLISHED.getCode()));

        List<DeliveryPlanPublishedPO> allList = new ArrayList<>(insertList);
        allList.addAll(updateList);
        // 下发MES
        BaseResponse<List<String>> baseResponse = publish2MES(allList);
        List<String> data = baseResponse.getData();
        if (baseResponse.getSuccess() == Boolean.FALSE && CollectionUtils.isNotEmpty(data)) {
            deliveryPlanPOS.forEach(t -> {
                if (data.contains(t.getProductCode())) {
                    t.setPublishStatus(PublishStatusEnum.UNPUBLISH.getCode());
                }
            });
        }

        if (CollectionUtils.isNotEmpty(deliveryPlanPOS)) {
            BasePOUtils.updateBatchFiller(deliveryPlanPOS, new Date());
            deliveryPlanDao.updateBatch(deliveryPlanPOS);
        }
        DeliveryPlanDoPublishVO doPublishResult = new DeliveryPlanDoPublishVO();
        doPublishResult.setSyncMesResp(baseResponse);
        doPublishResult.setOldDeliveryPlanPublishedDTOS(oldDeliveryPlanPublishedDTOS);
        return doPublishResult;
    }

    public BaseResponse<List<String>> publish2MES(List<DeliveryPlanPublishedPO> publishedList) {
        // 查询主机厂
        List<String> oemCodes =
                publishedList.stream().map(DeliveryPlanPublishedPO::getOemCode).distinct().collect(Collectors.toList());
        List<OemVO> oemList = oemService.selectByParams(ImmutableMap.of("oemCodes", oemCodes));
        Map<String, OemVO> oemMap = oemList.stream().filter(x -> StringUtils.isNotBlank(x.getSaleOrgId())
                        && StringUtils.isNotBlank(x.getCustomerCode()))
                .collect(Collectors.toMap(OemVO::getOemCode, Function.identity(), (t1, t2) -> t2));
        // 查询销售组织信息
        List<String> saleOrgIds = oemList.stream().map(OemVO::getSaleOrgId).distinct().collect(Collectors.toList());
        Map<String, String> saleOrgId2CodeMap = newMdsFeign.selectStockPointByParams(SystemHolder.getScenario(),
                        ImmutableMap.of("organizeIds", saleOrgIds)).stream()
                .filter(x -> StringUtils.isNotBlank(x.getOrganizeId())
                        && StringUtils.isNotBlank(x.getStockPointCode())).collect(Collectors
                        .toMap(NewStockPointVO::getOrganizeId, NewStockPointVO::getStockPointCode, (t1, t2) -> t1));

        // 查询客户信息
        List<String> customerCodes =
                oemList.stream().map(OemVO::getCustomerCode).distinct().collect(Collectors.toList());
        HashMap<String, Object> customerQueryParams = new HashMap<>();
        customerQueryParams.put("customerCodes", customerCodes);
        Map<String, String> customerCode2IdMap = newMdsFeign.selectCustomerByParams(customerQueryParams).stream()
                .collect(Collectors.toMap(CustomerVO::getCustomerCode, CustomerVO::getCustomerId, (t1, t2) -> t1));
        // 查询库存点物品
        List<String> productCodes = publishedList.stream().map(DeliveryPlanPublishedPO::getProductCode)
                .distinct().collect(Collectors.toList());
        List<NewProductStockPointVO> productList = newMdsFeign.selectProductStockPointByProducts(productCodes);

        // 根据产品编码分组并筛选出organize_type为SALE_ORGANIZATION的数据
        Map<String, List<NewProductStockPointVO>> productToStockPointsMap = productList.stream()
                .collect(Collectors.groupingBy(NewProductStockPointVO::getProductCode));
        Map<String, NewProductStockPointVO> productStockPointMap = new HashMap<>();
        for (Map.Entry<String, List<NewProductStockPointVO>> entry : productToStockPointsMap.entrySet()) {
            String productCode = entry.getKey();
            List<NewProductStockPointVO> stockPoints = entry.getValue();
            // 获取所有涉及的stock_point_code
            List<String> stockPointCodes = stockPoints.stream().map(NewProductStockPointVO::getStockPointCode).collect(Collectors.toList());
            Map<String, Object> params = new HashMap<>();
            params.put("stockPointCodes", stockPointCodes);
            List<NewStockPointVO> stockPointDetails = newMdsFeign.selectStockPointByParams(SystemHolder.getScenario(), params).stream().collect(Collectors.toList());

            Map<String, String> stockPointOrganizeTypeMap = stockPointDetails.stream().collect(Collectors.toMap(NewStockPointVO::getStockPointCode, NewStockPointVO::getOrganizeType));
            Optional<NewProductStockPointVO> matchedStockPoint = stockPoints.stream()
                    .filter(stockPoint -> "SALE_ORGANIZATION".equals(stockPointOrganizeTypeMap.get(stockPoint.getStockPointCode())))
                    .findFirst();

            if (matchedStockPoint.isPresent()) {
                NewProductStockPointVO selectedStockPoint = matchedStockPoint.get();
                // 处理 PoCategory，仅保留 . 后面的数据
                String poCategory = selectedStockPoint.getPoCategory();
                String[] poCategoryParts = poCategory.split("\\.", 2);
                selectedStockPoint.setPoCategory(poCategoryParts.length > 1 ? poCategoryParts[1] : poCategory);
                productStockPointMap.put(productCode, selectedStockPoint);
            }
        }
        // 查询库存点
        List<String> stockPointCodes =
                productList.stream().filter(x -> StringUtils.isNotBlank(x.getPoCategory())).map(x -> {
                    String poCategory = x.getPoCategory();
                    String[] poCategoryParts = poCategory.split("\\.", 2);
                    return poCategoryParts.length > 1 ? poCategoryParts[1] : poCategory;
                }).distinct().collect(Collectors.toList());
        HashMap<String, Object> subCategoryMap = new HashMap<>();
        subCategoryMap.put("stockPointCodes", stockPointCodes);
        Map<String, String> stockPointCode2IdMap =
                newMdsFeign.selectStockPointBySubCategory(subCategoryMap).stream().collect(Collectors
                        .toMap(NewStockPointVO::getStockPointCode, NewStockPointVO::getOrganizeId, (t1, t2) -> t1));

        // 查询库库存推移
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("oemCodes", oemCodes);
        queryParams.put("productCodes", productCodes);
        String versionId = publishedList.get(0).getDeliveryVersionId();
        queryParams.put("versionId", versionId);
        Map<String, List<InventoryShiftVO>> inventoryShiftGroup =
                inventoryShiftService.selectByParams(queryParams).stream().collect(Collectors
                        .groupingBy(x -> String.join(Constants.DELIMITER, x.getVersionId(), x.getOemCode(),
                                x.getProductCode())));
        // 查询发货计划
        Map<String, List<DeliveryPlanVO>> deliveryPlanGroup =
                deliveryPlanService.selectByParams(queryParams).stream().collect(Collectors
                        .groupingBy(x -> String.join(Constants.DELIMITER, x.getVersionId(), x.getOemCode(),
                                x.getProductCode())));
        // 查询用户姓名列表
        Map<String, String> userId2UserNameMap = ipsNewFeign.userList().stream().collect(Collectors
                .toMap(User::getId, User::getCnName, (t1, t2) -> t2));

        // 查询用户工号列表
        Map<String, String> userId2UserNameMap1 = ipsNewFeign.userList().stream().collect(Collectors
                .toMap(User::getId, User::getUserName, (t1, t2) -> t2));

        //查询发货计划版本表
        DeliveryPlanVersionVO versionVO =
                deliveryPlanVersionService.selectByPrimaryKey(versionId);
        if (Objects.isNull(versionVO) || StringUtils.isBlank(versionVO.getVersionCode())) {
            throw new BusinessException("发货计划版本未找到");
        }

        String billNo = versionVO.getVersionCode();
        // 提取version_code的后半部分，例如"OD2025021100001"
        int lastDashIndex = billNo.lastIndexOf("-");
        if (lastDashIndex != -1 && lastDashIndex < billNo.length() - 1) {
            billNo = billNo.substring(lastDashIndex + 1);
        } else {
            throw new BusinessException("发货计划版本编码格式有误");
        }
        // 数据组装前的合并逻辑
        Map<String, MesInterfaceDTO> mergeMap = new HashMap<>();
        // // 数据组装
        // List<MesInterfaceDTO> syncList = new ArrayList<>();
        for (DeliveryPlanPublishedPO datum : publishedList) {
            // 判断 KID 是否为空
            if (ObjectUtils.isEmpty(datum.getKID())) {
                log.error("KID 为空，跳过该条数据，kid: {}", datum.getKID());
                continue;
            }
            // 获取主机厂编码并处理相关逻辑，现在基于每个发货计划单实例
            String oemCode = datum.getOemCode();
            if (StringUtils.isEmpty(oemCode)) {
                log.error("缺少主机厂编码数据，无法关联到主机厂表，oemCode: {}", oemCode);
                continue;
            }
            // 查找 Oem 表的数据
            if (!oemMap.containsKey(oemCode)) {
                log.error("该数据的主机厂编码是{}，但是在主机厂表中匹配不到数据。", oemCode);
                continue;
            }
            OemVO matchedOem = oemMap.get(oemCode);

            MesInterfaceDTO mesInterfaceDTO = new MesInterfaceDTO();
            mesInterfaceDTO.setSalePlantId(matchedOem.getSaleOrgId());
            mesInterfaceDTO.setCustomerCode(matchedOem.getCustomerCode());

            String saleOrgId = matchedOem.getSaleOrgId();
            if (!saleOrgId2CodeMap.containsKey(saleOrgId)) {
                log.error("该数据的销售组织ID是{}，但是在销售组织表中匹配不到数据。", saleOrgId);
                continue;
            }
            mesInterfaceDTO.setSalePlantCode(saleOrgId2CodeMap.get(saleOrgId));

            String customerCode = matchedOem.getCustomerCode();
            if (!customerCode2IdMap.containsKey(customerCode)) {
                log.error("该数据的客户编码是{}，但是在客户表中匹配不到数据。", customerCode);
                continue;
            }
            mesInterfaceDTO.setCustomerId(customerCode2IdMap.get(customerCode));

            // 设置 itemNum 字段, 拼接而成
            String itemNum = mesInterfaceDTO.getSalePlantCode() + "PC" + mesInterfaceDTO.getCustomerCode();
            mesInterfaceDTO.setLineNum(itemNum);

            // 在库存推移表里查找对应的在途发运，中转库库存
            String uniqueKey = String.join(Constants.DELIMITER, datum.getDeliveryVersionId(), oemCode,
                    datum.getProductCode());
            List<InventoryShiftVO> inventoryShifts = inventoryShiftGroup.get(uniqueKey);

            mesInterfaceDTO.setOnWayDelivery(0);
            mesInterfaceDTO.setWarehouseStock(0);
            Date demandTime = DateUtils.truncateTimeOfDate(datum.getDemandTime());
            if (CollectionUtils.isNotEmpty(inventoryShifts)) {
                boolean matchFlag = false;
                for (InventoryShiftVO inventoryShift : inventoryShifts) {
                    Date plannedDate = inventoryShift.getPlannedDate();
                    // 如果需求时间和计划日期相同，则使用该条记录的数据
                    if (demandTime.equals(plannedDate)) {
                        mesInterfaceDTO.setOnWayDelivery(Objects.isNull(inventoryShift.getReceive())
                                ? 0 : inventoryShift.getReceive());
                        mesInterfaceDTO.setWarehouseStock(Objects.isNull(inventoryShift.getOpeningInventory())
                                ? 0 : inventoryShift.getOpeningInventory());
                        matchFlag = true;
                        break;
                    }
                }
                if (!matchFlag) {
                    log.warn("该数据的主机厂编码，物料编码，版本id是{}, 但是在库存推移表中未找到与需求时间匹配的数据。", oemCode);
                }
            } else {
                log.warn("该数据的主机厂编码，物料编码，版本id是{}，但是在库存推移表中匹配不到数据。", oemCode);
            }

            // 在发货计划表里查找对应的每箱片数
            List<DeliveryPlanVO> deliveryPlans = deliveryPlanGroup.get(uniqueKey);
            if (CollectionUtils.isNotEmpty(deliveryPlans)) {
                DeliveryPlanVO matchedDeliveryPlan = deliveryPlans.get(0);
                mesInterfaceDTO.setPerBoxQTY(matchedDeliveryPlan.getPiecePerBox());
            } else {
                log.warn("该数据的主机厂编码，物料编码，版本id是{}，但是在发货计划表中匹配不到数据。", oemCode);
            }

            //  获取本厂编码，在物料表里查找对应数据
            String productCode = datum.getProductCode();
            if (StringUtils.isEmpty(productCode)) {
                log.error("缺少本厂编码数据，无法关联到物料表");
                continue;
            }

            if (!productStockPointMap.containsKey(productCode)) {
                log.error("该数据的本厂编码是{}, 但是在物料表中匹配不到数据。", productCode);
                continue;
            }

            NewProductStockPointVO matchedProduct = productStockPointMap.get(productCode);
            // 处理 PoCategory，仅保留 . 后面的数据
            String poCategory = matchedProduct.getPoCategory();
            String[] poCategoryParts = poCategory.split("\\.", 2);
            String subCategory = poCategoryParts.length > 1 ? poCategoryParts[1] : poCategory;

            mesInterfaceDTO.setItemId(matchedProduct.getInventoryItemId());
            mesInterfaceDTO.setStatus(matchedProduct.getMaterialDemandStatus());
            // 设置处理后的子类别
            mesInterfaceDTO.setPlantCode(subCategory);

            //  根据处理后的销售组织Code去库存表里查找对应的id
            if (stockPointCode2IdMap.containsKey(subCategory)) {
                mesInterfaceDTO.setPlantId(stockPointCode2IdMap.get(subCategory));
            } else {
                log.warn("未能在库存表中找到与组织Code {} 对应的 stock_point_code 数据", productCode);
            }

            String deliveryVersionId = datum.getDeliveryVersionId();
            if (StringUtils.isEmpty(deliveryVersionId)) {
                log.error("缺少版本id数据，无法关联到发货计划版本表，deliveryVersionId: {}", deliveryVersionId);
                continue;
            }

            // 设置BillNo为提取出的部分
            mesInterfaceDTO.setBillNo(billNo);

            // 用户工号 根据创建人判断工号
            if (StringUtils.isNotEmpty(datum.getCreator())) {
                String creator = datum.getCreator();
                if (userId2UserNameMap1.containsKey(creator)) {
                    mesInterfaceDTO.setCreatedBy(userId2UserNameMap1.get(creator));
                } else {
                    log.error("用户表中匹配不到该数据的创建人{}", creator);
                    continue;
                }
            } else {
                log.error("缺少数据创建人，请先补充数据并保存。");
                continue;
            }

            if (StringUtils.isNotEmpty(datum.getModifier())) {
                String modifier = datum.getModifier();
                if (userId2UserNameMap1.containsKey(modifier)) {
                    mesInterfaceDTO.setLastUpdatedBy(userId2UserNameMap1.get(modifier));
                } else {
                    log.error("用户表中匹配不到该数据的修改人{}", modifier);
                    continue;
                }
            } else {
                log.error("缺少数据更新人，请先补充数据并保存。");
                continue;
            }

            if (StringUtils.isNotEmpty(datum.getModifier())) {
                String modifier = datum.getModifier();
                if (userId2UserNameMap.containsKey(modifier)) {
                    mesInterfaceDTO.setSaler(userId2UserNameMap.get(modifier));
                } else {
                    log.error("用户表中匹配不到该数据的修改人{}", modifier);
                    continue;
                }
            } else {
                log.error("缺少数据更新人，请先补充数据并保存。");
                continue;
            }

            LocalDate demandLocalDate = demandTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            // 获取当前日期
            LocalDate today = LocalDate.now();
            // 如果发货日期小于当前日期，则过滤掉该条数据
            if (demandLocalDate.isBefore(today)) {
                continue;
            }
            // 将 LocalDate 转换回 Date，只保留日期部分
            mesInterfaceDTO.setDemandDate(DateUtils.dateToString(demandTime, DateUtils.COMMON_DATE_STR3));

            // 设置其他字段
            // 创建时间
            mesInterfaceDTO.setCreationDate(DateUtils.dateToString(datum.getCreateTime(), DateUtils.COMMON_DATE_STR1));
            // 需求数量
            mesInterfaceDTO.setDemandQTY(datum.getDemandQuantity());
            // 本厂编码
            mesInterfaceDTO.setItemCode(datum.getProductCode());
            // kid
            mesInterfaceDTO.setKid(String.valueOf(datum.getKID()));
            // 修改时间
            mesInterfaceDTO.setLastUpdateDate(DateUtils.dateToString(datum.getModifyTime(), DateUtils.COMMON_DATE_STR1));
            // 备注
            mesInterfaceDTO.setRemark(datum.getRemark());
            // 厂内安全库存，默认为0
            mesInterfaceDTO.setSafeStock(0);

            // 创建合并键
            String key = mesInterfaceDTO.getItemCode() + "_" + mesInterfaceDTO.getCustomerCode() + "_" +
                    mesInterfaceDTO.getDemandDate();

            if (mergeMap.containsKey(key)) {
                MesInterfaceDTO existingDto = mergeMap.get(key);
                existingDto.setDemandQTY(existingDto.getDemandQTY() + mesInterfaceDTO.getDemandQTY());
                if (Long.parseLong(String.valueOf(datum.getKID())) > Long.parseLong(existingDto.getKid())) {
                    existingDto.setKid(String.valueOf(datum.getKID()));
                }
            } else {
                mesInterfaceDTO.setKid(String.valueOf(datum.getKID())); // 设置初始KID
                mergeMap.put(key, mesInterfaceDTO); // 将新项添加到合并映射
            }
            // syncList.add(mesInterfaceDTO);
        }
        // 将合并结果转换为列表
        List<MesInterfaceDTO> syncList = new ArrayList<>(mergeMap.values());

        if (syncList.isEmpty()) {
            log.warn("所有发货计划单处理后没有生成有效的同步数据");
            return BaseResponse.success("所有发货计划单已处理，但无可以发布数据");
        }

        PlatformUser user = SystemHolder.getUser();
        String planner = user.getUserName();
        Date deliveryDate = DateUtils.truncateTimeOfDate(new Date());
        List<DeliveryPlanRecordVO> records = deliveryPlanRecordService.selectByParams(ImmutableMap
                        .of("planner", planner, "versionId", versionId, "deliveryDate", deliveryDate))
                .stream().sorted((v1, v2) -> v2.getCreateTime()
                        .compareTo(v1.getCreateTime())).collect(Collectors.toList());

        String version;
        boolean initialVersion = CollectionUtils.isEmpty(records);
        String batchNo;
        String oldBatchNo;
        String timestamp = DateUtils.dateToString(new Date(), "yyMMddHHmmss");
        if (initialVersion) {
            version = planner + timestamp;
            batchNo = billNo + "@" + version;
            oldBatchNo = batchNo;
        } else {
            version = planner + timestamp;
            batchNo = billNo + "@" + version;
            oldBatchNo = records.get(0).getBatchNo();
        }
        // 组装发货计划下发记录并持久化
        DeliveryPlanRecordDTO build =
                DeliveryPlanRecordDTO.builder().id(UUIDUtil.getUUID()).deliveryDate(deliveryDate).billNo(billNo)
                        .deliveryVersion(version).batchNo(batchNo).planner(planner).versionId(versionId)
                        .enabled(YesOrNoEnum.YES.getCode()).build();
        deliveryPlanRecordService.doCreate(build);

        List<MesInterfaceDTO> batchDataList = new ArrayList<>();
        for (MesInterfaceDTO mesInterfaceDTO : syncList) {
            mesInterfaceDTO.setVersion(version);
            mesInterfaceDTO.setKid(mesInterfaceDTO.getKid() + "." + timestamp);
            batchDataList.add(mesInterfaceDTO);
        }

        try {
            // 将批量数据列表封装进一个单独的Map中
            Map<String, Object> apiParams = new HashMap<>();
            // 直接将batchDataList放入apiParams中
            apiParams.put("dataList", batchDataList);
            apiParams.put("apiParams", oldBatchNo);

            // 调用外部API，传递封装后的参数
            BaseResponse<String> response = newDcpFeign.callExternalApi(TenantCodeEnum.FYQB.getCode(),
                    ApiSourceEnum.MES.getCode(),
                    ApiCategoryEnum.DELIVERY_PLAN_MES.getCode(), apiParams);
            if (!response.getSuccess()) {
                return BaseResponse.error(response.getMsg());
            }
            Integer resolveCount = 0;
            Integer applyCount = 0;
            ExtApiLogVO extApiLog = newDcpFeign.getExtApiLog(response.getData());
            resolveCount += extApiLog.getResolveCount();
            applyCount += extApiLog.getApplyCount();
            String remark = extApiLog.getRemark();

            String result = String.format("共下发%d条数据，成功%d条，失败%d条", resolveCount, applyCount, resolveCount - applyCount);
            if (!Objects.equals(resolveCount, applyCount) && StringUtils.isNotBlank(remark)) {
                boolean hasIllegalStatusError = remark.contains("STATUS状态不合法");

                String formattedFailures;

                if (hasIllegalStatusError) {
                    formattedFailures = Arrays.stream(remark.split("<br/>"))
                            .map(line -> {
                                String[] parts = line.split(": ", 2);
                                String id = parts[0];
                                return id + ": “数据校验失败！物料状态未维护，请到物料主数据页面进行维护”@";
                            })
                            .collect(Collectors.joining("<br/>"));
                } else {
                    formattedFailures = remark;
                }

                result += "，失败信息如下：<br/>" + formattedFailures;
            }
            if (Objects.equals(applyCount, resolveCount)) {
                return BaseResponse.success(result);
            } else {
                // 解析异常日志
                if (StringUtils.isNotBlank(remark)) {
                    List<String> errorDataList = new ArrayList<>();

                    // 判断是否需要替换错误信息
                    boolean hasIllegalStatusError = remark.contains("STATUS状态不合法");
                    if (hasIllegalStatusError) {
                        errorDataList = Arrays.stream(remark.split("<br/>"))
                                .map(line -> {
                                    String[] parts = line.split(": ", 2);
                                    String id = parts[0];
                                    return id + ": “数据校验失败！物料状态未维护，请到物料主数据页面进行维护”";
                                })
                                .collect(Collectors.toList());
                    } else {
                        errorDataList = Arrays.asList(remark.split("<br/>"));
                    }

                    return BaseResponse.error(errorDataList, result);
                }
                return BaseResponse.error(result);
            }
        } catch (Exception e) {
            log.error("批量发布发货计划单报错，{}", e.getMessage());
            throw new BusinessException("批量发布发货计划单报错：" + e.getMessage());
        }
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.DELIVERY_PLAN.getCode();
    }

    @Override
    public int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList) {
        if (CollectionUtils.isNotEmpty(versionDTOList)) {
            return deliveryPlanDao.deleteBatchVersion(versionDTOList);
        }
        return 0;
    }

    @Override
    public List<DeliveryPlanVO2> selectVO2ByPlanPeriod(String planPeriod, String startTime, String endTime) {
        return deliveryPlanDao.selectVO2ByPlanPeriod(planPeriod, startTime, endTime);
    }

    @Override
    public List<DeliveryPlanVO2> selectVO2ByDeliveryVersionId(String deliveryVersionId) {
        return deliveryPlanDao.selectVO2ByDeliveryVersionId(deliveryVersionId);
    }

    @Override
    public BaseResponse<Void> doDeliveryPlanSave(List<DeliveryPlanDTO> deliveryPlans) {
    	if (CollectionUtils.isEmpty(deliveryPlans)) {
            return BaseResponse.error("请更新发货计划数据");
        }
    	for (DeliveryPlanDTO deliveryPlanDTO : deliveryPlans) {
    		if (CollectionUtils.isEmpty(deliveryPlanDTO.getDetailList())) {
                return BaseResponse.error("请更新发货计划数据");
            }
		}
        // 根据版本ID获取配送计划版本信息
    	String versionId = deliveryPlans.get(0).getVersionId();
        DeliveryPlanVersionVO deliveryPlanVersionVO =
                deliveryPlanVersionService.selectByPrimaryKey(versionId);
        // 检查版本状态，如果已发布，则抛出异常
        if (StringUtils.equals(deliveryPlanVersionVO.getVersionStatus(),
                VersionStatusEnum.PUBLISHED.getCode())) {
            throw new BusinessException("该版本已发布，不能编辑保存");
        }
        for (DeliveryPlanDTO deliveryPlan : deliveryPlans) {
        	List<DeliveryPlanDetailDTO> detailList = deliveryPlan.getDetailList();
        	detailList.forEach(t -> {
                t.setSavedSign(YesOrNoEnum.YES.getCode());
            });
		}
        List<String> productCodes = deliveryPlans.stream().map(DeliveryPlanDTO::getProductCode).distinct().collect(Collectors.toList());
        List<String> deliveryPlanIds = deliveryPlans.stream().map(DeliveryPlanDTO::getId).collect(Collectors.toList());
        // 将DTO转换为POJO
        List<DeliveryPlanPO> copyDeliveryPlanPOs = DeliveryPlanConvertor.INSTANCE.dto2Pos(deliveryPlans);
        //处理相同产品不同主机厂数据
        List<DeliveryPlanDTO> deliveryPlanList = new ArrayList<>();
        deliveryPlanList.addAll(deliveryPlans);
        List<DeliveryPlanPO> queryDeliveryPlanList = deliveryPlanDao
                .selectByParams(ImmutableMap.of("versionId", versionId,
                        "productCodes", productCodes))
                .stream().filter(e -> !deliveryPlanIds.contains(e.getId()))
                .collect(Collectors.toList());
        for (DeliveryPlanPO deliveryPlan : queryDeliveryPlanList) {
            DeliveryPlanDTO add = new DeliveryPlanDTO();
            add.setDetailList(new ArrayList<>());
            add.setId(deliveryPlan.getId());
            add.setOemCode(deliveryPlan.getOemCode());
            add.setProductCode(deliveryPlan.getProductCode());
            deliveryPlanList.add(add);
            ;
        }
        // 刷新库存推移数据
        DeliveryPlanCalculateDTO deliveryPlanCalculateDTO = new DeliveryPlanCalculateDTO();
        deliveryPlanCalculateDTO.setVersionId(versionId);
        deliveryPlanCalculateDTO.setDeliveryPlanList(deliveryPlanList);
        deliveryPlanCalculateDTO.setVersionCode(deliveryPlanVersionVO.getVersionCode());
        BaseResponse<Void> response = doDeliveryPlanCalc(deliveryPlanCalculateDTO, true);
        //refreshInventoryShift(deliveryPlanPO, newList);
        // 设置配送计划的发布状态为未发布
        copyDeliveryPlanPOs.forEach( e->{
        	e.setPublishStatus(PublishStatusEnum.UNPUBLISH.getCode());
        	BasePOUtils.updateFiller(e, new Date());
            // 更新配送计划
            deliveryPlanDao.update(e);
        });
        // 填充更新时间等基础信息
        return response;
    }

    public void doDeliveryPlanSaveCopy(DeliveryPlanDTO deliveryPlanDTO) {
        // 根据版本ID获取配送计划版本信息
        DeliveryPlanVersionVO deliveryPlanVersionVO =
                deliveryPlanVersionService.selectByPrimaryKey(deliveryPlanDTO.getVersionId());
        // 检查版本状态，如果已发布，则抛出异常
        if (StringUtils.equals(deliveryPlanVersionVO.getVersionStatus(),
                VersionStatusEnum.PUBLISHED.getCode())) {
            throw new BusinessException("该版本已发布，不能编辑保存");
        }
        // 获取当前数据源
        String mdsScenario = DynamicDataSourceContextHolder.getDataSource();
        // 获取配送计划详情列表
        List<DeliveryPlanDetailDTO> detailList = deliveryPlanDTO.getDetailList();
        // 将DTO转换为POJO
        DeliveryPlanPO deliveryPlanPO = DeliveryPlanConvertor.INSTANCE.dto2Po(deliveryPlanDTO);
        deliveryPlanPO.setDemandCategory(deliveryPlanDTO.getDemandCategory());
        // 将详情DTO列表转换为POJO列表
        List<DeliveryPlanDetailPO> newList = DeliveryPlanDetailConvertor.INSTANCE.dto2Pos(detailList);
        // 填充更新时间等基础信息
        BasePOUtils.updateBatchFiller(newList);
        // 获取产品代码列表
        List<String> productCodeList = Lists.newArrayList(deliveryPlanDTO.getProductCode());
        // 初始化映射，用于存储产品风险等级、产品与成品箱关系和箱体信息
        Map<String, PartRiskLevelVO> materialRiskLevelVOMapOfJoinKey = new HashMap<>();
        Map<String, ProductBoxRelationVO> productBoxRelationVOMapOfProductCode = new HashMap<>();
        Map<String, BoxInfoVO> boxInfoVOMapOfId = new HashMap<>();
        // 如果产品代码列表不为空，则查询相关的产品风险等级、产品与成品箱关系和箱体信息
        if (CollectionUtils.isNotEmpty(productCodeList)) {
            List<PartRiskLevelVO> partRiskLevelVOS = partRiskLevelService.selectByProductCodeList(productCodeList);
            // 根据主机厂+零件号分组
            materialRiskLevelVOMapOfJoinKey = partRiskLevelVOS.stream().collect(Collectors.toMap(item -> String.join(
                    "&", item.getOemCode(), item.getProductCode()), Function.identity(), (t1, t2) -> t2));

            // 查询零件与成品箱关系
            List<ProductBoxRelationVO> productBoxRelationVOList =
                    newMdsFeign.selectProductBoxRelationVOByProductCodeList(mdsScenario, productCodeList);
            productBoxRelationVOMapOfProductCode =
                    productBoxRelationVOList.stream().collect(Collectors.toMap(ProductBoxRelationVO::getProductCode,
                            Function.identity(), (t1, t2) -> t2));
            // 查询箱体信息
            if (CollectionUtils.isNotEmpty(productBoxRelationVOList)) {
                List<String> boxIdList =
                        productBoxRelationVOList.stream().map(ProductBoxRelationVO::getBoxId).distinct().collect(Collectors.toList());
                List<BoxInfoVO> boxInfoVOList = newMdsFeign.selectBoxInfoVOByBoxIdList(mdsScenario, boxIdList);
                boxInfoVOMapOfId = boxInfoVOList.stream().collect(Collectors.toMap(BoxInfoVO::getId,
                        Function.identity(), (t1, t2) -> t2));
            }
        }
        // 将映射赋值给最终变量，用于后续的计算
        Map<String, BoxInfoVO> finalBoxInfoVOMapOfId = boxInfoVOMapOfId;
        Map<String, PartRiskLevelVO> finalMaterialRiskLevelVOMapOfJoinKey = materialRiskLevelVOMapOfJoinKey;
        Map<String, ProductBoxRelationVO> finalProductBoxRelationVOMapOfProductCode =
                productBoxRelationVOMapOfProductCode;

        // 遍历详情列表，设置每个详情的补货计划数量
        newList.forEach(
                deliveryPlanDetailPO -> setReplenishPlanQty4Save(deliveryPlanDetailPO, deliveryPlanPO,
                        finalMaterialRiskLevelVOMapOfJoinKey,
                        finalProductBoxRelationVOMapOfProductCode, finalBoxInfoVOMapOfId));
        // 批量更新配送计划详情
        deliveryPlanDetailDao.updateBatch(newList);
        // 刷新库存推移数据
        refreshInventoryShift(deliveryPlanPO, newList);
        // 设置配送计划的发布状态为未发布
        deliveryPlanPO.setPublishStatus(PublishStatusEnum.UNPUBLISH.getCode());
        // 填充更新时间等基础信息
        BasePOUtils.updateFiller(deliveryPlanPO, new Date());
        // 更新配送计划
        deliveryPlanDao.update(deliveryPlanPO);
    }

    private void refreshInventoryShift(DeliveryPlanPO deliveryPlanPO, List<DeliveryPlanDetailPO> updateList) {
        List<DfpSwitchRelationBetweenProductVO> betweenProductList = dfpSwitchRelationBetweenProductService
                .selectByParams(ImmutableMap.of(
                        "enabled", YesOrNoEnum.YES.getCode(),
                        "oldOrNewProduct", deliveryPlanPO.getProductCode(),
                        "oemCode", deliveryPlanPO.getOemCode()));
        String productCode = deliveryPlanPO.getProductCode();
        DeliveryPlanPO switchDeliveryPlanPO = null;
        List<DeliveryPlanDetailPO> switchList = new ArrayList<>();
        //如果找到的是oldCode则为false，newCode则为true
        boolean switchNewCode = false;
        if (CollectionUtils.isNotEmpty(betweenProductList)) {
            // 如果存在工程变更关系，则获取组合产品代码
            DfpSwitchRelationBetweenProductVO dfpSwitchRelationBetweenProductVO = betweenProductList.get(0);
            if (StringUtils.isNotEmpty(dfpSwitchRelationBetweenProductVO.getOldProductCode()) && dfpSwitchRelationBetweenProductVO.getOldProductCode().equals(productCode)) {
                switchDeliveryPlanPO = deliveryPlanDao.selectByParams(ImmutableMap.of(
                        "versionId", deliveryPlanPO.getVersionId(),
                        "productCode", dfpSwitchRelationBetweenProductVO.getNewProductCode(),
                        "oemCode", deliveryPlanPO.getOemCode())).get(0);
                switchNewCode = true;
            } else if (StringUtils.isNotEmpty(dfpSwitchRelationBetweenProductVO.getNewProductCode())) {
                switchDeliveryPlanPO = deliveryPlanDao.selectByParams(ImmutableMap.of(
                        "versionId", deliveryPlanPO.getVersionId(),
                        "productCode", dfpSwitchRelationBetweenProductVO.getOldProductCode(),
                        "oemCode", deliveryPlanPO.getOemCode())).get(0);
            }
            if (switchDeliveryPlanPO != null) {
                switchList = deliveryPlanDetailDao.selectByDeliveryPlanIds(Collections.singletonList(switchDeliveryPlanPO.getId()));
            }
            productCode = dfpSwitchRelationBetweenProductVO.getOldProductCode() + "&" + dfpSwitchRelationBetweenProductVO.getNewProductCode();

        }

        // 删除推移表的数据,根据版本ID+本厂编码删除数据
        List<InventoryShiftPO> inventoryShiftPOS = inventoryShiftDao.selectByParams(ImmutableMap.of("versionId",
                deliveryPlanPO.getVersionId(), "productCode", productCode));
        Map<String, InventoryShiftPO> inventoryShiftPOMap =
                inventoryShiftPOS.stream().collect(Collectors.toMap(t -> DateUtils.dateToString(t.getPlannedDate()),
                        Function.identity(), (t1, t2) -> t2));

        List<DeliveryPlanDetailPO> newList =
                deliveryPlanDetailDao.selectByDeliveryPlanIds(Collections.singletonList(updateList.get(0).getDeliveryPlanDataId()));

        DeliveryPlanVersionVO deliveryPlanVersionVO =
                deliveryPlanVersionService.selectByPrimaryKey(deliveryPlanPO.getVersionId());
        String deliveryPlanVersionCode = deliveryPlanVersionVO.getVersionCode();

        // 主机厂数据
        List<String> oemCodes = Lists.newArrayList(deliveryPlanPO.getOemCode());

        // 获取主机厂的运输时间
        List<OemTransportTimeVO> oemTransportTimeVOList = oemTransportTimeService.selectByParams(ImmutableMap.of(
                "oemCodes", oemCodes));
        // 根据主机厂分组
        Map<String, List<OemTransportTimeVO>> oemTransportTimeVOGroup = new HashMap<>();
        if (CollectionUtils.isNotEmpty(oemTransportTimeVOList)) {
            oemTransportTimeVOGroup =
                    oemTransportTimeVOList.stream().collect(Collectors.groupingBy(OemTransportTimeVO::getOemCode));
        }

        // 查询主机厂库存点数据
        List<OemStockPointMapVO> oemStockPointMapVOS = oemStockPointMapService.selectOemStockPointByOemCodes(oemCodes);
        List<String> oemStockPointCodes =
                oemStockPointMapVOS.stream().map(OemStockPointMapVO::getStockPointCode).distinct().collect(Collectors.toList());
        Map<String, List<OemStockPointMapVO>> oemStockPointMapVOGroup =
                oemStockPointMapVOS.stream().collect(Collectors.groupingBy(OemStockPointMapVO::getOemCode));

        List<NewStockPointVO> newStockPointVOS = newMdsFeign.selectNewStockPointVOByParams(SystemHolder.getScenario(), ImmutableMap.of(
                "stockPointCodes", oemStockPointCodes));
        // 过滤出非"销售组织"的数据
        List<String> newStockPointCodeList = newStockPointVOS.stream()
                .filter(item -> !"SALE_ORGANIZATION".equals(item.getStockPointType()))
                .map(NewStockPointVO::getStockPointCode)
                .collect(Collectors.toList());

        // 主机厂期初库存
        Map<String, Integer> oemInventoryMap = new HashMap<>();
        Map<String, Integer> oemTransitInventoryMap = new HashMap<>();
        List<OemInventorySubmissionVO> oemInventorySubmissionVOS = oemInventorySubmissionService.selectAll();
        Date lastDay = DateUtils.stringToDate(deliveryPlanVersionCode.substring(deliveryPlanVersionCode.length() - 13,
                deliveryPlanVersionCode.length() - 5), "yyyyMMdd");
        oemInventorySubmissionVOS = oemInventorySubmissionVOS.stream()
                .filter(t -> null != t.getSubmissionDate()).collect(Collectors.toList());
        oemInventorySubmissionVOS.sort(Comparator.comparing(OemInventorySubmissionVO::getSubmissionDate));
        oemInventorySubmissionVOS =
                oemInventorySubmissionVOS.stream().filter(t -> !lastDay.before(t.getSubmissionDate())).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(oemInventorySubmissionVOS)) {
            oemInventoryMap =
                    oemInventorySubmissionVOS.stream().filter(item -> item.getOemInventoryQuantity() != null).collect(Collectors
                            .toMap(t -> t.getOemCode() + "&&" + t.getProductCode(),
                                    t -> t.getOemInventoryQuantity().intValue(), (t1, t2) -> t2));
            oemTransitInventoryMap = oemInventorySubmissionVOS.stream().filter(item -> item.getStockInventoryQuantity() != null).collect(Collectors
                    .toMap(t -> t.getOemCode() + "&&" + t.getProductCode(),
                            t -> t.getStockInventoryQuantity().intValue(), (t1, t2) -> t2));
        }
        // 本厂期初库存,key之前为productCode&&stockPointCode现在是oemCode&&productCode
        Map<String, Integer> productStockPointInventoryMap =
                deliveryPlanDomainService.getRealTimeInventory(deliveryPlanVersionCode,
                        oemTransitInventoryMap, Lists.newArrayList(deliveryPlanPO.getProductCode()));

        // 安全库存配置,key为productCode&&stockPointCode
        Map<String, SafetyStockLevelVO> safetyStockLevelVOMap = safetyStockLevelService.selectWithOutSaleOrg().stream()
                .collect(Collectors.toMap(SafetyStockLevelVO::getProductCode, Function.identity(), (t1, t2) -> t1));

        List<InventoryShiftPO> inventoryShiftPOListUpdate = new ArrayList<>();

        // 获取主机厂关联的库存点
        List<OemStockPointMapVO> oemStockPointMapVOList = oemStockPointMapVOGroup.get(deliveryPlanPO.getOemCode());
        if (CollectionUtils.isEmpty(oemStockPointMapVOList)) {
            log.warn("主机厂:{} 没有关联的库存点", deliveryPlanPO.getOemCode());
            return;
        }
        // List<OemStockPointMapVO> stockPointMapVOS = oemStockPointMapVOList.stream()
        //         .filter(item -> newStockPointCodeList.contains(item.getStockPointCode())).collect(Collectors.toList());
        //
        // if (CollectionUtils.isEmpty(stockPointMapVOS)) {
        //     log.warn("主机厂:{} 没有关联的库存点", deliveryPlanPO.getOemCode());
        //     return;
        // }
        //String stockPointCode = stockPointMapVOS.get(0).getStockPointCode();

        String key = deliveryPlanPO.getOemCode() + "&&" + deliveryPlanPO.getProductCode();

        SafetyStockLevelVO safetyStockLevelVO = safetyStockLevelVOMap.get(deliveryPlanPO.getProductCode());
        if (ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode().equals(deliveryPlanPO.getDemandCategory()) &&
                StringUtils.equals(OemBusinessTypeEnum.MTS.getCode(), deliveryPlanPO.getSupplyType()) && safetyStockLevelVO == null) {
            throw new BusinessException(deliveryPlanPO.getProductCode() + "没有安全库存配置");
        }

        // 获取主机厂运输时间
        int oemTransportSupplyDays = 0;
        List<OemTransportTimeVO> oemTransportTimeVOS = oemTransportTimeVOGroup.get(deliveryPlanPO.getOemCode());
        // 获取优先级最高的数据
        if (CollectionUtils.isNotEmpty(oemTransportTimeVOS)) {
            if (StringUtils.isNotBlank(deliveryPlanPO.getTransportationRouteId())) {
                Map<String, OemTransportTimeVO> oemTransportTimeVOMap =
                        oemTransportTimeVOS.stream().collect(Collectors.toMap(OemTransportTimeVO::getId,
                                Function.identity(), (t1, t2) -> t1));
                OemTransportTimeVO oemTransportTimeVO =
                        oemTransportTimeVOMap.get(deliveryPlanPO.getTransportationRouteId());
                oemTransportSupplyDays = calculateTransportDays(null == oemTransportTimeVO
                        ? 0 : (oemTransportTimeVO.getTransportationTime() == null
                        ? 0 : oemTransportTimeVO.getTransportationTime().doubleValue()));
            } else {
                OemTransportTimeVO oemTransportTimeVO =
                        oemTransportTimeVOS.stream().min(Comparator.comparing(OemTransportTimeVO::getPriority)).orElse(new OemTransportTimeVO());
                // 计算主机厂运输时间
                oemTransportSupplyDays = calculateTransportDays(null == oemTransportTimeVO.getTransportationTime() ?
                        0 : oemTransportTimeVO.getTransportationTime().doubleValue());
            }
        }
        // 日期初始化
        List<Date> dateList = DeliveryPlanDateUtils.getDeliveryPlanDateList(deliveryPlanVersionCode);
        calculateDemandDateList(oemTransportSupplyDays, dateList);
        // 主机厂装车日历
        // Map<String, List<ResourceCalendarVO>> resourceCalendarGroupOemCodeMap =
        //         deliveryPlanDomainService.getFuture30DaysCalendar(oemCodes, dateList);
        List<InventoryShiftPO> inventoryShiftPOListUpdateTemp = new ArrayList<>();
        if (OemBusinessTypeEnum.MTO.getCode().equals(deliveryPlanPO.getSupplyType()) || ProductionDemandTypeEnum.PROJECT_DEMAND.getCode().equals(deliveryPlanPO.getDemandCategory())) {
            generateManualMtoData(deliveryPlanPO, dateList, oemTransportSupplyDays,
                    inventoryShiftPOListUpdateTemp, newList, inventoryShiftPOMap);
        } else if (OemBusinessTypeEnum.MTS.getCode().equals(deliveryPlanPO.getSupplyType())) {
            // 期初库存
            Integer openingInventory = productStockPointInventoryMap.get(key) == null ? 0 :
                    productStockPointInventoryMap.get(key);
            // 主机厂期初库存
            Integer oemOpeningInventory =
                    oemInventoryMap.get(deliveryPlanPO.getOemCode() + "&&" + deliveryPlanPO.getProductCode()) == null
                            ? 0 : oemInventoryMap.get(deliveryPlanPO.getOemCode() + "&&" + deliveryPlanPO.getProductCode());

            generateManualMtsData(deliveryPlanPO, dateList, oemTransportSupplyDays,
                    openingInventory, oemOpeningInventory, inventoryShiftPOListUpdateTemp, newList,
                    inventoryShiftPOMap, switchList, switchNewCode);
        }

        if (CollectionUtils.isNotEmpty(inventoryShiftPOListUpdateTemp)) {
            inventoryShiftPOListUpdate.addAll(inventoryShiftPOListUpdateTemp);
        }

        if (CollectionUtils.isNotEmpty(inventoryShiftPOListUpdate)) {
            BasePOUtils.updateBatchFiller(inventoryShiftPOListUpdate);
            inventoryShiftDao.updateBatchSelective(inventoryShiftPOListUpdate);
        }
    }

    /**
     * @param deliveryPlanPO                  发货计划
     * @param dateList                        日期列表
     * @param supplyDays                      主机厂运输天数
     * @param openingInventory                实时库存
     * @param inventoryShiftPOListUpdate      推移表数据(修改的数据)
     * @param deliveryPlanDetailPOList        发货计划明细
     * @param inventoryShiftPOMap             推移表数据，key:日期,value:推移表对象
     */
    private void generateManualMtsData(DeliveryPlanPO deliveryPlanPO,
                                       List<Date> dateList,
                                       int supplyDays,
                                       Integer openingInventory,
                                       Integer oemOpeningInventory,
                                       List<InventoryShiftPO> inventoryShiftPOListUpdate,
                                       List<DeliveryPlanDetailPO> deliveryPlanDetailPOList,
                                       Map<String, InventoryShiftPO> inventoryShiftPOMap,
                                       List<DeliveryPlanDetailPO> switchDetailPOList, boolean switchNewCode) {
        // List<ResourceCalendarVO> resourceCalendarVOList =
        //         resourceCalendarGroupOemCodeMap.get(deliveryPlanPO.getOemCode());
        // if (CollectionUtils.isEmpty(resourceCalendarVOList)) {
        //     throw new BusinessException(deliveryPlanPO.getOemCode() + "未来30天没有装车日历");
        // }

        Map<String, Integer> demandOfDay = new HashMap<>();
        inventoryShiftPOMap.forEach((k, v) -> demandOfDay
                .put(k, null == v.getCustomerDemand() ? 0 : v.getCustomerDemand()));

        // List<Long> resourceCalendarTime = resourceCalendarVOList.stream().map(ResourceCalendarVO::getWorkDay)
        //         .map(Date::getTime).distinct().collect(Collectors.toList());

        Map<String, DeliveryPlanDetailPO> deliveryPlanDetailMap = deliveryPlanDetailPOList.stream()
                .collect(Collectors.toMap(t -> DateUtils.dateToString(t.getDemandTime()),
                        Function.identity(), (t1, t2) -> t2));

        Map<String, DeliveryPlanDetailPO> switchDeliveryPlanDetailMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(switchDetailPOList)) {
            switchDeliveryPlanDetailMap = switchDetailPOList.stream()
                    .collect(Collectors.toMap(t -> DateUtils.dateToString(t.getDemandTime()),
                            Function.identity(), (t1, t2) -> t2));
        }

        List<Date> endingInventoryDateList = initEndingInventoryDateList(dateList);
        Integer endingInventory = openingInventory;
        Integer oemEndingInventory = oemOpeningInventory;
        for (int i = 0; i < dateList.size(); i++) {
            Date date = dateList.get(i);
            InventoryShiftPO inventoryShiftPO = inventoryShiftPOMap.get(DateUtils.dateToString(date));
            if (!inventoryShiftPOMap.containsKey(DateUtils.dateToString(date))) {
                continue;
            }

            // 获取客户需求
            Integer demandQuantity = inventoryShiftPO.getCustomerDemand();
            // if (!resourceCalendarTime.contains(date.getTime())) {
            //     continue;
            // }
            // 期初库存 = 前一天的期末库存
            Integer openingInventoryTemp = endingInventory;
            // 主机厂期初库存
            Integer oemOpeningInventoryTemp = oemEndingInventory;

            // 发货计划的开始时间
            Date deliveryPlanStartDate = DateUtils.moveDay(date, -supplyDays);
            DeliveryPlanDetailPO deliveryPlanDetailPO =
                    deliveryPlanDetailMap.get(DateUtils.dateToString(deliveryPlanStartDate));
            DeliveryPlanDetailPO switchDeliveryPlanDetailPO =
                    switchDeliveryPlanDetailMap.get(DateUtils.dateToString(deliveryPlanStartDate));

            Integer inRoad = deliveryPlanDetailMap.get(DateUtils.dateToString(deliveryPlanStartDate)) == null ? 0 :
                    deliveryPlanDetailMap.get(DateUtils.dateToString(deliveryPlanStartDate)).getDemandQuantity();
            Integer switchRoad = switchDeliveryPlanDetailMap.get(DateUtils.dateToString(deliveryPlanStartDate)) == null ? 0 :
                    switchDeliveryPlanDetailMap.get(DateUtils.dateToString(deliveryPlanStartDate)).getDemandQuantity();

            // 先用主机厂期初库存满足需求
            oemEndingInventory = oemOpeningInventoryTemp - demandQuantity;
            // 未满足需求数量
            int unfulfilledQuantity = demandQuantity - oemOpeningInventoryTemp;
            oemEndingInventory = oemEndingInventory <= 0 ? 0 : oemEndingInventory;

            // 计算期末库存：期初库存+在途数据-客户需求
            endingInventory = openingInventoryTemp + inRoad + switchRoad - unfulfilledQuantity;

            // 当天期末库存天数

            BigDecimal endingInventoryDays = getManualEndingInventoryDays(endingInventoryDateList, endingInventory, i, demandOfDay);

            // 期望库存减去实际的期末库存就是发货计划量
            if (null != deliveryPlanDetailPO && null != switchDeliveryPlanDetailPO) {
                Integer deliveryPlanQuantity = (deliveryPlanDetailPO.getDemandQuantity() == null ? 0 :
                        deliveryPlanDetailPO.getDemandQuantity())
                        + (switchDeliveryPlanDetailPO.getDemandQuantity() == null ? 0 :
                        switchDeliveryPlanDetailPO.getDemandQuantity());
                inventoryShiftPO.setArrivalPlan(deliveryPlanQuantity);
                if (switchNewCode) {
                    inventoryShiftPO.setOldArrivalPlan(deliveryPlanDetailPO.getDemandQuantity());
                    inventoryShiftPO.setNewArrivalPlan(switchDeliveryPlanDetailPO.getDemandQuantity());
                    inventoryShiftPO.setOldDeliveryPlan(deliveryPlanDetailPO.getDemandQuantity());
                    inventoryShiftPO.setNewDeliveryPlan(switchDeliveryPlanDetailPO.getDemandQuantity());
                } else {
                    inventoryShiftPO.setOldArrivalPlan(switchDeliveryPlanDetailPO.getDemandQuantity());
                    inventoryShiftPO.setNewArrivalPlan(deliveryPlanDetailPO.getDemandQuantity());
                    inventoryShiftPO.setOldDeliveryPlan(switchDeliveryPlanDetailPO.getDemandQuantity());
                    inventoryShiftPO.setNewDeliveryPlan(deliveryPlanDetailPO.getDemandQuantity());
                }
            } else if (null != deliveryPlanDetailPO) {
                inventoryShiftPO.setArrivalPlan(deliveryPlanDetailPO.getDemandQuantity());
                if (switchNewCode) {
                    inventoryShiftPO.setOldArrivalPlan(deliveryPlanDetailPO.getDemandQuantity());
                    inventoryShiftPO.setOldDeliveryPlan(deliveryPlanDetailPO.getDemandQuantity());
                } else {
                    inventoryShiftPO.setNewArrivalPlan(deliveryPlanDetailPO.getDemandQuantity());
                    inventoryShiftPO.setNewDeliveryPlan(deliveryPlanDetailPO.getDemandQuantity());
                }
            } else if (null != switchDeliveryPlanDetailPO) {
                inventoryShiftPO.setArrivalPlan(switchDeliveryPlanDetailPO.getDemandQuantity());
                if (switchNewCode) {
                    inventoryShiftPO.setNewArrivalPlan(switchDeliveryPlanDetailPO.getDemandQuantity());
                    inventoryShiftPO.setNewDeliveryPlan(switchDeliveryPlanDetailPO.getDemandQuantity());
                } else {
                    inventoryShiftPO.setOldArrivalPlan(switchDeliveryPlanDetailPO.getDemandQuantity());
                    inventoryShiftPO.setOldDeliveryPlan(switchDeliveryPlanDetailPO.getDemandQuantity());
                }
            }

            // 期初库存
            inventoryShiftPO.setOpeningInventory(openingInventoryTemp);
            // 期末库存
            inventoryShiftPO.setEndingInventory(endingInventory >= 0 ? endingInventory : 0);
            // 期末库存天数
            inventoryShiftPO.setEndingInventoryDays(endingInventoryDays);
            // 累计库存缺口
            inventoryShiftPO.setAccumulatedInventoryGap(endingInventory >= 0 ? 0 : -endingInventory);
            inventoryShiftPOListUpdate.add(inventoryShiftPO);
            endingInventory = inventoryShiftPO.getEndingInventory();
        }
    }

    private void generateManualMtoData(DeliveryPlanPO deliveryPlanPO,
                                       List<Date> dateList,
                                       int supplyDays,
                                       List<InventoryShiftPO> inventoryShiftPOListUpdate,
                                       List<DeliveryPlanDetailPO> deliveryPlanDetailPOList,
                                       Map<String, InventoryShiftPO> inventoryShiftPOMap) {
        // List<ResourceCalendarVO> resourceCalendarVOList =
        //         resourceCalendarGroupOemCodeMap.get(deliveryPlanPO.getOemCode());
        // if (CollectionUtils.isEmpty(resourceCalendarVOList)) {
        //     throw new BusinessException(deliveryPlanPO.getOemCode() + "未来30天没有装车日历");
        // }

        // Map<String, Integer> demandOfDay = new HashMap<>();
        // inventoryShiftPOMap.forEach((k, v) -> demandOfDay
        //         .put(k, null == v.getCustomerDemand() ? 0 : v.getCustomerDemand()));

        // List<Long> resourceCalendarTime = resourceCalendarVOList.stream().map(ResourceCalendarVO::getWorkDay)
        //         .map(Date::getTime).distinct().collect(Collectors.toList());

        Map<String, DeliveryPlanDetailPO> deliveryPlanDetailMap = deliveryPlanDetailPOList.stream()
                .collect(Collectors.toMap(t -> DateUtils.dateToString(t.getDemandTime()),
                        Function.identity(), (t1, t2) -> t2));


        for (Date date : dateList) {
            InventoryShiftPO inventoryShiftPO = inventoryShiftPOMap.get(DateUtils.dateToString(date));
            if (inventoryShiftPO == null) {
                continue;
            }
            // 获取客户需求
            Integer demandQuantity = inventoryShiftPO.getCustomerDemand();
            // if (!resourceCalendarTime.contains(date.getTime())) {
            //     continue;
            // }

            // 发货计划的开始时间
            Date deliveryPlanStartDate = DateUtils.moveDay(date, -supplyDays);
            DeliveryPlanDetailPO deliveryPlanDetailPO =
                    deliveryPlanDetailMap.get(DateUtils.dateToString(deliveryPlanStartDate));

            if (null != deliveryPlanDetailPO) {
                inventoryShiftPO.setArrivalPlan(deliveryPlanDetailPO.getDemandQuantity() == null ?
                        0 : deliveryPlanDetailPO.getDemandQuantity());
            }

            inventoryShiftPOListUpdate.add(inventoryShiftPO);

        }
    }

    public void addSwitchProductCode(List<String> oemCodeScope, List<String> productScope,
                                     List<DfpSwitchRelationBetweenProductVO> switchRelationBetweenProductVOS) {
        if (CollectionUtils.isEmpty(switchRelationBetweenProductVOS)) {
            return;
        }
        checkSwitchProductCodeScope(switchRelationBetweenProductVOS);
        for (DfpSwitchRelationBetweenProductVO srbp : switchRelationBetweenProductVOS) {
            if (oemCodeScope.contains(srbp.getOemCode())
                    && (productScope.contains(srbp.getOldProductCode()) || productScope.contains(srbp.getNewProductCode()))) {
                if (!productScope.contains(srbp.getOldProductCode())) {
                    productScope.add(srbp.getOldProductCode());
                }
                if (!productScope.contains(srbp.getNewProductCode())) {
                    productScope.add(srbp.getNewProductCode());
                }
            }
        }
    }

    public void checkSwitchProductCodeScope(List<DfpSwitchRelationBetweenProductVO> switchRelationBetweenProductVOS) {
        Map<String, List<DfpSwitchRelationBetweenProductVO>> map = switchRelationBetweenProductVOS.stream().collect(Collectors.groupingBy(DfpSwitchRelationBetweenProductVO::getOemCode));
        for (List<DfpSwitchRelationBetweenProductVO> list : map.values()) {
            List<String> oldProductList =
                    list.stream().map(DfpSwitchRelationBetweenProductVO::getOldProductCode).collect(Collectors.toList());
            List<String> newProductList =
                    list.stream().map(DfpSwitchRelationBetweenProductVO::getNewProductCode).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(oldProductList) || CollectionUtils.isEmpty(newProductList)) {
                return;
            }
            for (String oldProduct : oldProductList) {
                for (String newProduct : newProductList) {
                    if (StringUtils.isEmpty(oldProduct) || StringUtils.isEmpty(newProduct)) {
                        continue;
                    }
                    if (oldProduct.equals(newProduct)) {
                        throw new BusinessException("请检查产品编码: " + oldProduct + "的新旧物料关系");
                    }
                }
            }
        }

    }

    public void addSwitchCombinationProductCode(List<String> oemCodeScope, List<String> productScope,
                                                List<DfpSwitchRelationBetweenProductVO> switchRelationBetweenProductVOS,
                                                List<String> combinationProductCodes) {
        for (DfpSwitchRelationBetweenProductVO srbp : switchRelationBetweenProductVOS) {
            String combinationProductCode = srbp.getOldProductCode() + "&" + srbp.getNewProductCode();

            // 如果组合产品代码已经存在，则跳过
            if (combinationProductCodes.contains(combinationProductCode)) {
                continue;
            }

            boolean isProductInScope = productScope.contains(srbp.getOldProductCode()) || productScope.contains(srbp.getNewProductCode());

            if (CollectionUtils.isEmpty(oemCodeScope)) {
                if (isProductInScope) {
                    combinationProductCodes.add(combinationProductCode);
                }
            } else {
                if (oemCodeScope.contains(srbp.getOemCode()) && isProductInScope) {
                    combinationProductCodes.add(combinationProductCode);
                }
            }
        }
    }

    @Override
    public BaseResponse<Void> doDeliveryPlanCalc(DeliveryPlanCalculateDTO deliveryPlanCalculateDTO, boolean saved) {
        //警告信息
        List<String> noRouteOemList = new ArrayList<>();
        List<String> noSafetyStockLeve = new ArrayList<>();
        List<String> productBoxRelationWarningList = new ArrayList<>();
        List<String> savedDeliveryPlanDetailWarningList = new ArrayList<>();

        String deliveryPlanVersionCode = deliveryPlanCalculateDTO.getVersionCode();
        List<DeliveryPlanVersionVO> deliveryPlanVersionList = deliveryPlanVersionService
                .selectByParams(ImmutableMap.of("versionCode", deliveryPlanCalculateDTO.getVersionCode()));
        DeliveryPlanVersionVO deliveryPlanVersion = deliveryPlanVersionList.get(0);
        // 查询当前用户负责的物料数据
        List<NewProductStockPointVO> newProductStockPointVOS =
                this.newMdsFeign.selectProductStockPointByParams(SystemHolder.getScenario(),
                        ImmutableMap.of("productTypes", Lists.newArrayList(ProductTypeEnum.FG.getCode(),
                                        ProductTypeEnum.MPBL.getCode(), ProductTypeEnum.SA.getCode()),
                            			"orderPlanner", SystemHolder.getUserId()));
        if (CollectionUtils.isEmpty(newProductStockPointVOS)) {
            throw new BusinessException("当前用户没有配置物料数据权限");
        }
        // 主机厂数据
        // 本次发货计算的主机厂范围，本厂编码范围
        List<String> oemCodeScope = new ArrayList<>();
        List<String> productScope = newProductStockPointVOS.stream()
                .map(NewProductStockPointVO::getProductCode).collect(Collectors.toList());

        List<String> combinationProductScope = new ArrayList<>();
        //查询新旧产品工程变更关系
        List<DfpSwitchRelationBetweenProductVO> switchRelationBetweenProductVOS = getSwitchRelationBetweenProductVOList();
        if (CollectionUtils.isNotEmpty(deliveryPlanCalculateDTO.getDeliveryPlanList())) {
            oemCodeScope = deliveryPlanCalculateDTO.getDeliveryPlanList().stream().map(DeliveryPlanDTO::getOemCode)
                    .collect(Collectors.toList());
            productScope = deliveryPlanCalculateDTO.getDeliveryPlanList().stream().map(DeliveryPlanDTO::getProductCode)
                    .collect(Collectors.toList());
            //将存在新旧产品工程变更关系的productCode添加到productScope
            addSwitchProductCode(oemCodeScope, productScope, switchRelationBetweenProductVOS);
        }
        addSwitchCombinationProductCode(oemCodeScope, productScope, switchRelationBetweenProductVOS, combinationProductScope);
        //删除前将用户保存的发货计划明细保留下来
        List<DeliveryPlanDetailVO> savedDeliveryPlanDetailList = new ArrayList<>();
        // 重复计算时先删除本次版本的历史计算结果数据
        savedDeliveryPlanDetailList = deleteCalculateResult(deliveryPlanCalculateDTO, oemCodeScope, productScope,
                combinationProductScope, savedDeliveryPlanDetailList);

        Date lastDay = DateUtils.stringToDate(deliveryPlanVersionCode.substring(deliveryPlanVersionCode.length() - 13,
                deliveryPlanVersionCode.length() - 5), "yyyyMMdd");
        // 初始化数据
        DeliveryPlanCalcSpace deliveryPlanCalcSpace = initializeSpace(newProductStockPointVOS, deliveryPlanVersionCode
                , productScope, lastDay, switchRelationBetweenProductVOS, saved, deliveryPlanCalculateDTO, savedDeliveryPlanDetailList);
        // 最终需求（合并日需求和一致性预测需求数据）
        List<String> months = deliveryPlanDomainService.getMonths(lastDay);// 四个月year-month格式
        Map<String, List<CleanDemandDataDetailVO>> cleanDemandDataDetailVOSMap = deliveryPlanDomainService.
                mergeCleanDemandAndForecastDemandData(deliveryPlanVersion, oemCodeScope, productScope,
                        deliveryPlanCalcSpace.getVehicleModelCodeList(), months, lastDay, false);
        List<CleanDemandDataDetailVO> cleanDemandDataDetailVOS = getCleanDemandDetailVOs(cleanDemandDataDetailVOSMap);

        oemCodeScope =
                cleanDemandDataDetailVOS.stream().map(CleanDemandDataDetailVO::getOemCode).distinct().collect(Collectors.toList());
        List<String> demandProductCodeList =
                cleanDemandDataDetailVOS.stream().map(CleanDemandDataDetailVO::getProductCode).distinct().collect(Collectors.toList());
        initDataByProductScope(deliveryPlanCalcSpace, oemCodeScope, demandProductCodeList, productBoxRelationWarningList, 
        		deliveryPlanVersionCode, cleanDemandDataDetailVOS);

        // 主机厂装车日历未来30天装车日历
        // 日期初始化--todo:装车日历在需求中做了判断处理，在库存推移中应该不需要判断了
        List<Date> dateList = DeliveryPlanDateUtils.getDeliveryPlanDateList(deliveryPlanVersionCode);
        Map<String, List<ResourceCalendarVO>> resourceCalendarGroupOemCodeMap =
                deliveryPlanDomainService.getFuture30DaysCalendar(oemCodeScope, dateList);

        List<DeliveryPlanPO> deliveryPlanPOListCreate = new ArrayList<>();
        List<DeliveryPlanDetailPO> deliveryPlanDetailPOListCreate = new ArrayList<>();
        List<InventoryShiftPO> inventoryShiftPOListCreate = new ArrayList<>();
        //工程变更物料新旧物料集合
        List<String> oldCodeList = deliveryPlanCalcSpace.getOldCodeList();
        List<String> newCodeList = deliveryPlanCalcSpace.getNewCodeList();
        //已经计算过的工程变更物料
        List<String> calculatedProduct = new ArrayList<>();
        for (String demandCategory : cleanDemandDataDetailVOSMap.keySet()) {
            List<CleanDemandDataDetailVO> cleanDemandDataDetailByCategory =
                    cleanDemandDataDetailVOSMap.get(demandCategory);
            Map<String, List<CleanDemandDataDetailVO>> cleanDemandDataDetailByOemCodeAndProductCode =
                    cleanDemandDataDetailByCategory
                            .stream().collect(Collectors.groupingBy(t -> t.getOemCode() + "&&" + t.getProductCode()));
            for (String oemCodeProductCode : cleanDemandDataDetailByOemCodeAndProductCode.keySet()) {
                List<Date> initDateList = DeliveryPlanDateUtils.getDeliveryPlanDateList(deliveryPlanVersionCode);
                List<CleanDemandDataDetailVO> cleanDemandDataDetailList = cleanDemandDataDetailByOemCodeAndProductCode.get(oemCodeProductCode);
                //在新旧产品工程变更关系中，已经计算过两个产品，计算过的产品跳过
                if (calculatedProduct.contains(oemCodeProductCode)) {
                    continue;
                }
                String oemCode = oemCodeProductCode.split("&&")[0];
                String productCode = oemCodeProductCode.split("&&")[1];
                // 获取主机厂运输时间
                int oemTransportSupplyDays = 0;
                String oemTransportTimeVOId = "";
                List<OemTransportTimeVO> oemTransportTimeVOS = deliveryPlanCalcSpace.getOemTransportTimeVOList(oemCode);
                // 获取优先级最小的数据
                if (CollectionUtils.isNotEmpty(oemTransportTimeVOS)) {
                    OemTransportTimeVO oemTransportTimeVO =
                            oemTransportTimeVOS.stream().max(Comparator.comparing(OemTransportTimeVO::getPriority)).orElse(new OemTransportTimeVO());
                    // 计算主机厂运输时间
                    //transportationTime = oemTransportTimeVO.getTransportationTime();
                    oemTransportSupplyDays = calculateTransportDays(null == oemTransportTimeVO.getTransportationTime() ?
                            0 : oemTransportTimeVO.getTransportationTime().doubleValue());
                    oemTransportTimeVOId = oemTransportTimeVO.getId();
                }

                // 分组统计求和每天的需求数量
                Map<String, Integer> demandSumGroupByDay = cleanDemandDataDetailList.stream()
                        .filter(t -> t.getDemandQuantity() != null)
                        .collect(Collectors.groupingBy(t -> DateUtils.dateToString(t.getDemandTime()),
                                Collectors.summingInt(t -> t.getDemandQuantity().intValue())));

                if (CollectionUtils.isEmpty(oemTransportTimeVOS)) {
                    noRouteOemList.add(String.format(oemCode));
                    log.info("该主机厂:{}没有运输路径", oemCode);
                    continue;
                }
                DeliveryPlanPO deliveryPlanPO = createDeliveryPlan(deliveryPlanVersion.getId(), productCode, oemCode,
                        oemTransportTimeVOId, deliveryPlanCalcSpace, demandCategory);

                // 补齐计算时间范围
                calculateDemandDateList(oemTransportSupplyDays, initDateList);

                String key = oemCode + "&&" + productCode;
                // 生成发货计划详情和库存推移表
                if (ProductionDemandTypeEnum.PROJECT_DEMAND.getCode().equals(demandCategory) ||
                        OemBusinessTypeEnum.MTO.getCode().equals(deliveryPlanPO.getSupplyType())
                        || OemBusinessTypeEnum.MTO_BOX.getCode().equals(deliveryPlanPO.getSupplyType())) {
                    generateMtoData(deliveryPlanPO, initDateList, oemTransportSupplyDays, demandSumGroupByDay,
                            resourceCalendarGroupOemCodeMap, deliveryPlanDetailPOListCreate,
                            inventoryShiftPOListCreate, deliveryPlanCalcSpace.getMaterialRiskLevelVOMapOfJoinKey(),
                            deliveryPlanCalcSpace.getProductBoxRelationVOMapOfProductCode(), deliveryPlanCalcSpace.getBoxInfoVOMapOfId(),
                            productBoxRelationWarningList, deliveryPlanCalcSpace.getSavedDeliveryPlanDetailMap(),
                            savedDeliveryPlanDetailWarningList,
                            deliveryPlanCalcSpace);
                    deliveryPlanPOListCreate.add(deliveryPlanPO);
                } else if (OemBusinessTypeEnum.MTS.getCode().equals(deliveryPlanPO.getSupplyType())) {
                    // 库存推移表新建数据集
                    List<InventoryShiftPO> inventoryShiftPOListCreateTemp = new ArrayList<>();
                    //新旧产品工程变更判断
                    if (oldCodeList.contains(key) || newCodeList.contains(key)) {
                        DfpSwitchRelationBetweenProductVO switchRelation = null;
                        DeliveryPlanPO oldDeliveryPlanPO = null, newDeliveryPlanPO = null;
                        List<DeliveryPlanDetailPO> oldDeliveryPlanDetailPOListCreateTemp = new ArrayList<>();
                        List<DeliveryPlanDetailPO> newDeliveryPlanDetailPOListCreateTemp = new ArrayList<>();
                        if (newCodeList.contains(key)) {
                            switchRelation = deliveryPlanCalcSpace.getNewSwitchRelationVO(key);
                            if (switchRelation != null) {
                                calculatedProduct.add(key);
                                calculatedProduct.add(switchRelation.getOemCode() + "&&" + switchRelation.getOldProductCode());
                                newDeliveryPlanPO = deliveryPlanPO;
                                newDeliveryPlanDetailPOListCreateTemp = addPreVersionData(newDeliveryPlanPO,
                                        deliveryPlanCalcSpace.getDeliveryPlanDetailByProductCodeAndOemCodeMap());

                                oldDeliveryPlanPO = createDeliveryPlan(deliveryPlanVersion.getId(), switchRelation.getOldProductCode(),
                                        oemCode, oemTransportTimeVOId, deliveryPlanCalcSpace, demandCategory);

                                oldDeliveryPlanDetailPOListCreateTemp = addPreVersionData(oldDeliveryPlanPO,
                                        deliveryPlanCalcSpace.getDeliveryPlanDetailByProductCodeAndOemCodeMap());
                            }
                        } else if (oldCodeList.contains(key)) {
                            switchRelation = deliveryPlanCalcSpace.getOldSwitchRelationVO(key);
                            if (switchRelation != null) {
                                calculatedProduct.add(key);
                                calculatedProduct.add(switchRelation.getOemCode() + "&&" + switchRelation.getNewProductCode());
                                oldDeliveryPlanPO = deliveryPlanPO;
                                oldDeliveryPlanDetailPOListCreateTemp = addPreVersionData(oldDeliveryPlanPO,
                                        deliveryPlanCalcSpace.getDeliveryPlanDetailByProductCodeAndOemCodeMap());

                                newDeliveryPlanPO = createDeliveryPlan(deliveryPlanVersion.getId(), switchRelation.getNewProductCode(),
                                        oemCode, oemTransportTimeVOId, deliveryPlanCalcSpace, demandCategory);

                                newDeliveryPlanDetailPOListCreateTemp = addPreVersionData(newDeliveryPlanPO,
                                        deliveryPlanCalcSpace.getDeliveryPlanDetailByProductCodeAndOemCodeMap());
                            }
                        }
                        if (switchRelation != null) {
                            String oldKey = switchRelation.getOemCode() + "&&" + switchRelation.getOldProductCode();
                            Map<String, Integer> oldDemandSumGroupByDay = new HashMap<>();
                            if (cleanDemandDataDetailByOemCodeAndProductCode.get(oldKey) != null) {
                                oldDemandSumGroupByDay = cleanDemandDataDetailByOemCodeAndProductCode.get(oldKey).stream()
                                        .filter(t -> t.getDemandQuantity() != null)
                                        .collect(Collectors.groupingBy(t -> DateUtils.dateToString(t.getDemandTime()),
                                                Collectors.summingInt(t -> t.getDemandQuantity().intValue())));
                            }
                            Map<String, Integer> newDemandSumGroupByDay = new HashMap<>();
                            String newKey = switchRelation.getOemCode() + "&&" + switchRelation.getNewProductCode();
                            if (cleanDemandDataDetailByOemCodeAndProductCode.get(newKey) != null) {
                                newDemandSumGroupByDay = cleanDemandDataDetailByOemCodeAndProductCode.get(newKey).stream()
                                        .filter(t -> t.getDemandQuantity() != null)
                                        .collect(Collectors.groupingBy(t -> DateUtils.dateToString(t.getDemandTime()),
                                                Collectors.summingInt(t -> t.getDemandQuantity().intValue())));
                            }
                            if (SwitchRelationModeEnum.SMOOTH_SWITCH.getCode().equals(switchRelation.getSwitchMode())) {
                                generateMtsSmoothSwitch(deliveryPlanCalcSpace.getSafetyStockLevelVOMap(), oldDeliveryPlanPO, newDeliveryPlanPO
                                        , noSafetyStockLeve,
                                        deliveryPlanCalcSpace.getProductStockPointInventoryMap(),
                                        deliveryPlanCalcSpace.getOemInventoryMap(),
                                        deliveryPlanCalcSpace.getProductStockPointInRoadMap(),
                                        initDateList, oemTransportSupplyDays, oldDemandSumGroupByDay,
                                        newDemandSumGroupByDay,
                                        resourceCalendarGroupOemCodeMap, oldDeliveryPlanDetailPOListCreateTemp,
                                        newDeliveryPlanDetailPOListCreateTemp,
                                        inventoryShiftPOListCreateTemp,
                                        deliveryPlanCalcSpace.getMaterialRiskLevelVOMapOfJoinKey(),
                                        deliveryPlanCalcSpace.getProductBoxRelationVOMapOfProductCode(),
                                        deliveryPlanCalcSpace.getDeliveryPlanLockConfigVO(deliveryPlanPO.getOemCode()),
                                        deliveryPlanCalcSpace.getDeliveryPlanReplenishConfigVO(deliveryPlanPO.getOemCode()),
                                        deliveryPlanCalcSpace.getBoxInfoVOMapOfId(), productBoxRelationWarningList,
                                        deliveryPlanCalcSpace.getReceivedOemInventorySubmissionMap(),
                                        deliveryPlanCalcSpace.getNewProductStockPointVO(newDeliveryPlanPO.getProductCode()));
                            } else if (SwitchRelationModeEnum.FORCED_SWITCH_QUANTITY.getCode().equals(switchRelation.getSwitchMode())) {
                                generateMtsSwitchQuantityRefactor(oldDeliveryPlanPO, newDeliveryPlanPO, noSafetyStockLeve,
                                        productBoxRelationWarningList,
                                        initDateList, oemTransportSupplyDays, oldDemandSumGroupByDay,
                                        newDemandSumGroupByDay, oldDeliveryPlanDetailPOListCreateTemp,
                                        newDeliveryPlanDetailPOListCreateTemp,
                                        inventoryShiftPOListCreateTemp,
                                        switchRelation, deliveryPlanCalcSpace);
                            } else if (SwitchRelationModeEnum.FORCED_SWITCH_TIME.getCode().equals(switchRelation.getSwitchMode())) {
                                generateMtsSwitchTimeRefactor(oldDeliveryPlanPO, newDeliveryPlanPO, noSafetyStockLeve,
                                        productBoxRelationWarningList,
                                        initDateList, oemTransportSupplyDays, oldDemandSumGroupByDay,
                                        newDemandSumGroupByDay,
                                        oldDeliveryPlanDetailPOListCreateTemp,
                                        newDeliveryPlanDetailPOListCreateTemp,
                                        inventoryShiftPOListCreateTemp,
                                        switchRelation, deliveryPlanCalcSpace);
                            }
                        }
                        if (CollectionUtils.isNotEmpty(oldDeliveryPlanDetailPOListCreateTemp)) {
                            deliveryPlanDetailPOListCreate.addAll(oldDeliveryPlanDetailPOListCreateTemp);
                        }
                        if (CollectionUtils.isNotEmpty(newDeliveryPlanDetailPOListCreateTemp)) {
                            deliveryPlanDetailPOListCreate.addAll(newDeliveryPlanDetailPOListCreateTemp);
                        }
                        deliveryPlanPOListCreate.add(oldDeliveryPlanPO);
                        deliveryPlanPOListCreate.add(newDeliveryPlanPO);
                    } else {
                        //最新已发布锁定期内发货计划数据--todo:锁定期内detail数据，by productCode查询，涉及新旧产品变更时
                        List<DeliveryPlanDetailPO> deliveryPlanDetailPOListCreateTemp = addPreVersionData(deliveryPlanPO,
                                deliveryPlanCalcSpace.getDeliveryPlanDetailByProductCodeAndOemCodeMap());
                        generateMtsNormalData(deliveryPlanCalcSpace.getSafetyStockLevelVOMap(), deliveryPlanPO,
                                deliveryPlanCalcSpace.getProductStockPointVOMap().get(productCode),
                                noSafetyStockLeve,
                                deliveryPlanCalcSpace.getProductStockPointInventoryMap(), key, deliveryPlanCalcSpace.getOemInventoryMap(),
                                deliveryPlanCalcSpace.getProductStockPointInRoadMap(),
                                initDateList, oemTransportSupplyDays, demandSumGroupByDay,
                                resourceCalendarGroupOemCodeMap, deliveryPlanDetailPOListCreateTemp,
                                inventoryShiftPOListCreateTemp,
                                deliveryPlanCalcSpace.getMaterialRiskLevelVOMapOfJoinKey(),
                                deliveryPlanCalcSpace.getProductBoxRelationVOMapOfProductCode(),
                                deliveryPlanCalcSpace.getDeliveryPlanLockConfigVOMap(),
                                deliveryPlanCalcSpace.getDeliveryPlanReplenishConfigVOMap(),
                                deliveryPlanCalcSpace.getBoxInfoVOMapOfId(), productBoxRelationWarningList,
                                deliveryPlanCalcSpace.getReceivedOemInventorySubmissionMap(),
                                deliveryPlanCalcSpace.getSavedDeliveryPlanDetailMap(),
                                savedDeliveryPlanDetailWarningList,
                                deliveryPlanCalcSpace);
                        if (CollectionUtils.isNotEmpty(deliveryPlanDetailPOListCreateTemp)) {
                            deliveryPlanDetailPOListCreate.addAll(deliveryPlanDetailPOListCreateTemp);
                        }
                        deliveryPlanPOListCreate.add(deliveryPlanPO);
                    }

                    if (CollectionUtils.isNotEmpty(inventoryShiftPOListCreateTemp)) {
                        inventoryShiftPOListCreate.addAll(inventoryShiftPOListCreateTemp);
                    }
                }
                //deliveryPlanPOListCreate.add(deliveryPlanPO);
            }
        }

        if (CollectionUtils.isNotEmpty(inventoryShiftPOListCreate)) {
            BasePOUtils.insertBatchFiller(inventoryShiftPOListCreate);
            inventoryShiftDao.insertBatchWithPrimaryKey(inventoryShiftPOListCreate);
        }
        if (CollectionUtils.isNotEmpty(deliveryPlanPOListCreate)) {
            BasePOUtils.insertBatchFiller(deliveryPlanPOListCreate);
            deliveryPlanDao.insertBatchWithPrimaryKey(deliveryPlanPOListCreate);
        }
        if (CollectionUtils.isNotEmpty(deliveryPlanDetailPOListCreate)) {
            BasePOUtils.insertBatchFiller(deliveryPlanDetailPOListCreate);
            deliveryPlanDetailDao.insertBatchWithPrimaryKey(deliveryPlanDetailPOListCreate);
        }
        //发货计划编制计算—放假逻辑处理
        doHolidayAdjustment(oldCodeList, newCodeList, dateList, deliveryPlanVersion.getId(), newProductStockPointVOS, 
        		deliveryPlanCalcSpace);
        
        //MTS没有新旧物料关系的重新计算期末库存天数,标准安全库存量
        doCalcMtsEndingInventoryDays(oldCodeList, newCodeList, dateList, deliveryPlanVersion.getId(), newProductStockPointVOS, 
        		deliveryPlanCalcSpace);

        List<String> warningMessageList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(noRouteOemList)) {
        	noRouteOemList = noRouteOemList.stream().distinct().collect(Collectors.toList());
            warningMessageList.add(String.format("主机厂:%s没有运输路径", String.join(",", noRouteOemList)));
        }
        if (CollectionUtils.isNotEmpty(noSafetyStockLeve)) {
        	noSafetyStockLeve = noSafetyStockLeve.stream().distinct().collect(Collectors.toList());
            warningMessageList.add(String.format("本厂编码:%s没有安全库存配置", String.join(",", noSafetyStockLeve)));
        }
        if (CollectionUtils.isNotEmpty(productBoxRelationWarningList)) {
        	productBoxRelationWarningList = productBoxRelationWarningList.stream().distinct().collect(Collectors.toList());
            warningMessageList.add(String.format("本厂编码:%s未维护产品与成品箱关系优先级", String.join(",",
                    productBoxRelationWarningList)));
        }
        if (CollectionUtils.isNotEmpty(savedDeliveryPlanDetailWarningList)) {
        	savedDeliveryPlanDetailWarningList = savedDeliveryPlanDetailWarningList.stream().distinct().collect(Collectors.toList());
            warningMessageList.add(String.format("%s保存后的发货计划存在期末库存为负", String.join(",",
                    savedDeliveryPlanDetailWarningList)));
        }
        if (CollectionUtils.isNotEmpty(warningMessageList)) {
            return BaseResponse.success(String.join("<br/>", warningMessageList));
        }
        return BaseResponse.success();
    }

    /**
     * 发货计划编制计算—放假逻辑
     * @param dateList
     * @param versionId
     */
	private void doMtoBoxDeliveryQty(List<Date> dateList, String versionId) {
		//1.默认查询供应类型为MTO-BOX的数据
        List<DeliveryPlanVO> mtoBoxsDeliveryPlanList = deliveryPlanDao.selectVOByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(),
    			"versionId", versionId,
    			"supplyType" , OemBusinessTypeEnum.MTO_BOX.getCode()));
        if(CollectionUtils.isEmpty(mtoBoxsDeliveryPlanList)) {
        	return;
        }
        Map<String,DeliveryPlanVO> mtoBoxsDeliveryPlanMap = mtoBoxsDeliveryPlanList.stream()
        		.collect(Collectors.toMap(DeliveryPlanVO::getId,e->e,(v1, v2) -> v1));
        List<String> deliveryPlanIds = new ArrayList<>(mtoBoxsDeliveryPlanMap.keySet());

        //2.获取对应的发货计划详情（30天）
        List<DeliveryPlanDetailPO> deliveryPlanDetails = deliveryPlanDetailDao.selectByDeliveryPlanIds(deliveryPlanIds);
        Map<String, List<DeliveryPlanDetailPO>> deliveryPlanDetailMap = deliveryPlanDetails.stream()
        		.filter( e-> e.getDemandTime().compareTo(dateList.get(0)) >= 0
        		&& e.getDemandTime().compareTo(dateList.get(dateList.size() -1 )) <= 0)
        		.collect(Collectors.groupingBy(DeliveryPlanDetailPO::getDeliveryPlanDataId));

        //3.获取对应的库存推移数据
        List<String> oemProductList = mtoBoxsDeliveryPlanList.stream()
        		.map(e -> e.getOemCode() + "&" + e.getProductCode()).collect(Collectors.toList());
        List<InventoryShiftVO> inventoryShifts = inventoryShiftDao.selectVOByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(),
    			"versionId" , versionId));
        //过滤出对应发货计划的数据，并按照主机厂，产品编码，时间分组
        Map<String,InventoryShiftVO> inventoryShiftMap = inventoryShifts.stream()
        		.filter( e-> oemProductList.contains(e.getOemCode() + "&" + e.getProductCode()))
        		.collect(Collectors.toMap(e -> e.getOemCode() + "&" + e.getProductCode()
    			+ "&" + DateUtils.dateToString(e.getPlannedDate()), e->e,(v1, v2) -> v1));

        //4.获取发货计划偏移时间数据-主机厂的运输时间
        List<OemTransportTimeVO> oemTransportTimeVOList = oemTransportTimeService.selectByParams(ImmutableMap.of(
                "oemCodes", mtoBoxsDeliveryPlanList.stream().map(DeliveryPlanVO::getOemCode).distinct().collect(Collectors.toList())));
		//根据主机厂分组
        Map<String, List<OemTransportTimeVO>> oemTransportTimeVOGroup = oemTransportTimeVOList.stream()
        		.collect(Collectors.groupingBy(OemTransportTimeVO::getOemCode));

        //5.获取产品成箱关系数据
        List<String> productCodeList = mtoBoxsDeliveryPlanList.stream().map(DeliveryPlanVO::getProductCode).distinct().collect(Collectors.toList());
        List<ProductBoxRelationVO> productBoxRelationVOList =
                newMdsFeign.selectProductBoxRelationVOByProductCodeList(SystemHolder.getScenario(), productCodeList);
        //过滤无效数据,只要维护优先级的数据,且维护了标准装载量的数据
        productBoxRelationVOList = productBoxRelationVOList.stream()
                .filter(e -> YesOrNoEnum.YES.getCode().equals(e.getEnabled()) && e.getStandardLoad() != null
                && e.getPriority() != null)
                .collect(Collectors.toList());
        Map<String, List<ProductBoxRelationVO>> productBoxRelationMap = productBoxRelationVOList.stream()
        		.collect(Collectors.groupingBy(ProductBoxRelationVO::getProductCode));
        Map<String, ProductBoxRelationVO> productBoxRelationVOMapOfProductCode = new HashMap<>();
		for (Entry<String, List<ProductBoxRelationVO>> productBoxRelationEntry : productBoxRelationMap.entrySet()) {
            List<ProductBoxRelationVO> boxRelationList = productBoxRelationEntry.getValue();
            boxRelationList.sort(Comparator.comparing(ProductBoxRelationVO::getPriority));
            productBoxRelationVOMapOfProductCode.put(productBoxRelationEntry.getKey(), boxRelationList.get(0));
        }

		//6.处理发货数量
        List<DeliveryPlanDetailPO> batchUpdateDetail = new ArrayList<>();
        List<InventoryShiftVO> batchUpdateShift = new ArrayList<>();
        for (Entry<String, List<DeliveryPlanDetailPO>> entrty : deliveryPlanDetailMap.entrySet()) {
        	DeliveryPlanVO deliveryPlanVO = mtoBoxsDeliveryPlanMap.get(entrty.getKey());
        	String productCode = deliveryPlanVO.getProductCode();
        	ProductBoxRelationVO productBoxRelationVO = productBoxRelationVOMapOfProductCode.get(productCode);
        	if(productBoxRelationVO == null) {
        		//如果产品成箱关系数据未维护好，则过滤
        		continue;
        	}
        	//成箱片数
        	BigDecimal standardLoad = new BigDecimal(productBoxRelationVO.getStandardLoad());
        	//获取发货提前期
        	String oemCode = deliveryPlanVO.getOemCode();
        	int oemTransportSuppiyDays = getOemTransportSuppiyDays(oemTransportTimeVOGroup, deliveryPlanVO);
        	//按照发货时间排序
        	List<DeliveryPlanDetailPO> detailList = entrty.getValue();
        	detailList.sort(Comparator.comparing(DeliveryPlanDetailPO::getDemandTime));
        	for (DeliveryPlanDetailPO detail : detailList) {
        		Date demandTime = detail.getDemandTime();
        		BigDecimal demandQuantity = new BigDecimal(detail.getDemandQuantity());
        		//计算整箱的数量
        		BigDecimal boxQuantity = demandQuantity.divide(standardLoad, 0, BigDecimal.ROUND_UP);
        		BigDecimal newDemanDQuantity = demandQuantity.divide(standardLoad, 0, BigDecimal.ROUND_UP).multiply(standardLoad);
        		if(demandQuantity.compareTo(newDemanDQuantity) != 0) {
        			//更新发货数量，库存推移表到货数量
        			detail.setDemandQuantity(newDemanDQuantity.intValue());
        			detail.setBoxQuantity(boxQuantity.intValue());
        			detail.setBoxDesc(detail.getBoxQuantity() + "箱");
        			batchUpdateDetail.add(detail);
        			Date arrivalDay = DateUtils.moveDay(demandTime, oemTransportSuppiyDays);
        			InventoryShiftVO inventoryShift = inventoryShiftMap.get(oemCode + "&" + productCode
        			+ "&" + DateUtils.dateToString(arrivalDay));
        			if(inventoryShift != null) {
        				inventoryShift.setArrivalPlan(detail.getDemandQuantity());
        				batchUpdateShift.add(inventoryShift);
        			}
        		}
			}
		}

        //跟新发货对接单详情及库存推移
        if(CollectionUtils.isNotEmpty(batchUpdateDetail)) {
        	deliveryPlanDetailDao.updateBatchSelective(batchUpdateDetail);
        }
        if(CollectionUtils.isNotEmpty(batchUpdateShift)) {
        	List<InventoryShiftPO> vo2Pos = InventoryShiftConvertor.INSTANCE.vo2Pos(batchUpdateShift);
        	inventoryShiftDao.updateBatchSelective(vo2Pos);
        }
	}

	/**
     * 发货计划编制计算—放假逻辑
     * @param oldCodeList
     * @param newCodeList
     * @param versionId 
     * @param newProductStockPointVOS
	 * @param deliveryPlanCalcSpace 
     */
	private void doHolidayAdjustment(List<String> oldCodeList, List<String> newCodeList, List<Date> dateList,
			String versionId, List<NewProductStockPointVO> newProductStockPointVOS, DeliveryPlanCalcSpace deliveryPlanCalcSpace) {
		//主机厂放假天数
        Map<String, List<String>> oemHolidayMap = deliveryPlanCalcSpace.getOemHolidayMap();
		if(oemHolidayMap.isEmpty()){
        	return;
        }
		//1.默认查询供应类型为MTS的数据
        List<DeliveryPlanVO> mtsDeliveryPlanList = deliveryPlanDao.selectVOByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(), 
    			"versionId", versionId, 
    			"demandCategory", ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode(), 
    			"supplyType" , OemBusinessTypeEnum.MTS.getCode()));
        //只处理当前用户权限下的发货计划（物料权限）
        List<String> userProductCodes = newProductStockPointVOS.stream().map(NewProductStockPointVO::getProductCode)
        		.distinct().collect(Collectors.toList());
        //2.过滤不存在新旧物料替代关系的数据，且是用户权限下的物料
        mtsDeliveryPlanList = mtsDeliveryPlanList.stream().filter( e -> !oldCodeList.contains(e.getOemCode() + "&&" + e.getProductCode()) 
        		&& !newCodeList.contains(e.getOemCode() + "&&" + e.getProductCode())
        		&& userProductCodes.contains(e.getProductCode()))
        		.collect(Collectors.toList());
        if(CollectionUtils.isEmpty(mtsDeliveryPlanList)) {
        	return;
        }
        //把存在放假逻辑的数据过滤出来，查询出对应的发货计划，发货计划详情，库存推移数据
        mtsDeliveryPlanList = mtsDeliveryPlanList.stream().filter( e -> oemHolidayMap.containsKey(e.getOemCode())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(mtsDeliveryPlanList)) {
        	return;
        }
        
        // 获取主机厂的运输时间
        List<OemTransportTimeVO> oemTransportTimeVOList = oemTransportTimeService.selectByParams(ImmutableMap.of(
                "oemCodes", mtsDeliveryPlanList.stream().map(DeliveryPlanVO::getOemCode).distinct().collect(Collectors.toList())));
				// 根据主机厂分组
        Map<String, List<OemTransportTimeVO>> oemTransportTimeVOGroup = oemTransportTimeVOList.stream()
        		.collect(Collectors.groupingBy(OemTransportTimeVO::getOemCode));
        List<String> deliveryPlanIds = mtsDeliveryPlanList.stream().map(DeliveryPlanVO::getId).collect(Collectors.toList());
        List<DeliveryPlanDetailPO> deliveryPlanDetails = deliveryPlanDetailDao.selectByDeliveryPlanIds(deliveryPlanIds);
        Map<String, List<DeliveryPlanDetailPO>> deliveryPlanDetailMap = deliveryPlanDetails.stream()
        		.collect(Collectors.groupingBy(DeliveryPlanDetailPO::getDeliveryPlanDataId));
        List<InventoryShiftVO> inventoryShifts = inventoryShiftDao.selectVOByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(), 
    			"versionId" , mtsDeliveryPlanList.get(0).getVersionId()));
        //计划日期过滤数据，并按照主机厂，产品编码分组
        Map<String, List<InventoryShiftVO>> inventoryShiftMap = inventoryShifts.stream().filter( e-> e.getPlannedDate().compareTo(dateList.get(0)) >= 0
        		&& e.getPlannedDate().compareTo(dateList.get(dateList.size() -1 )) <= 0)
			.collect(Collectors.groupingBy(e -> e.getOemCode() + "&" + e.getProductCode()));
        //处理需要修改的发货计划详情和库存推移数据
        List<InventoryShiftVO> batchUpdateShift = new ArrayList<>();
        List<DeliveryPlanDetailPO> batchUpdateDetail = new ArrayList<>();
//        //处理放假时间
//        for (DeliveryPlanVO deliveryPlanVO : mtsDeliveryPlanList) {
//			//处理每个发货计划的发货计划详情，库存推移数据
//        	List<DeliveryPlanDetailPO> detailList = deliveryPlanDetailMap.get(deliveryPlanVO.getId());
//        	if(CollectionUtils.isEmpty(detailList)){
//        		continue;
//        	}
//        	List<InventoryShiftVO> shiftList = inventoryShiftMap.get(deliveryPlanVO.getOemCode() + "&" + deliveryPlanVO.getProductCode());
//        	shiftList.sort(Comparator.comparing(InventoryShiftVO::getPlannedDate).reversed());
//        	List<String> oemHolidayList = oemHolidayMap.get(deliveryPlanVO.getOemCode());
//            for (int i = 0; i < shiftList.size(); i++) {
//            	InventoryShiftVO inventoryShiftVO = shiftList.get(i);
//            	//没有装车日历，没有客户需求，有到货数量
//            	String plannedDateStr = DateUtils.dateToString(inventoryShiftVO.getPlannedDate());
//            	Integer customerDemand = inventoryShiftVO.getCustomerDemand();
//            	if(oemHolidayList.contains(plannedDateStr) && customerDemand > 0) {
//            		oemHolidayList.remove(plannedDateStr);
//            	}
//			}
//		}
        
        for (DeliveryPlanVO deliveryPlanVO : mtsDeliveryPlanList) {
			//处理每个发货计划的发货计划详情，库存推移数据
        	List<DeliveryPlanDetailPO> detailList = deliveryPlanDetailMap.get(deliveryPlanVO.getId());
        	if(CollectionUtils.isEmpty(detailList)){
        		continue;
        	}
        	
        	detailList.sort(Comparator.comparing(DeliveryPlanDetailPO::getDemandTime));
        	Map<String, DeliveryPlanDetailPO> detailMap = detailList.stream()
        			.collect(Collectors.toMap( e-> DateUtils.dateToString(e.getDemandTime()),e->e,(v1, v2) -> v1));
        	
        	List<InventoryShiftVO> shiftList = inventoryShiftMap.get(deliveryPlanVO.getOemCode() + "&" + deliveryPlanVO.getProductCode());
        	shiftList.sort(Comparator.comparing(InventoryShiftVO::getPlannedDate).reversed());
        	List<String> oemHolidayList = oemHolidayMap.get(deliveryPlanVO.getOemCode());
        	// 获取主机厂运输时间
            int oemTransportSuppiyDays = getOemTransportSuppiyDays(oemTransportTimeVOGroup, deliveryPlanVO);
            Integer holidayArrivalPlan = 0;
            //当前发货单详情需要修改的数据
            Map<String, DeliveryPlanDetailPO> batchUpdateDetailMap = new HashMap<>();
            Map<String, InventoryShiftVO> batchUpdateShiftMap = new HashMap<>();
            Integer openingInventory = null;
            Date savedSinPlannedDate = null;
            for (int i = 0; i < shiftList.size(); i++) {
            	InventoryShiftVO inventoryShiftVO = shiftList.get(i);
            	//没有装车日历，没有客户需求，有到货数量
            	String plannedDateStr = DateUtils.dateToString(inventoryShiftVO.getPlannedDate());
            	Integer customerDemand = inventoryShiftVO.getCustomerDemand();
            	Integer deliveryPlan = inventoryShiftVO.getDeliveryPlan() == null ? 0 : inventoryShiftVO.getDeliveryPlan();
            	Date plannedDate = inventoryShiftVO.getPlannedDate();
            	//获取发货时间
            	Date deliveryDay = DateUtils.moveDay(plannedDate, - oemTransportSuppiyDays);
            	
            	//获取发货日期的发货明细
            	DeliveryPlanDetailPO deliveryDayPlan = detailMap.get(DateUtils.dateToString(deliveryDay));
            	Boolean holidayFlag = true;
            	if(deliveryDay.compareTo(DateUtils.getDayFirstTime(new Date())) >= 0 && deliveryDayPlan != null 
            			&& YesOrNoEnum.YES.getCode().equals(deliveryDayPlan.getSavedSign())) {
            		//当发货明细的的保存标识为YES，那么就走不放假逻辑
            		holidayFlag = false;
            		//维护有保存标识对应的库存推移数据的时间
            		if(savedSinPlannedDate == null) {
            			savedSinPlannedDate = plannedDate;
            		}
            	}
            	if(oemHolidayList.contains(plannedDateStr) && customerDemand == 0 && deliveryPlan > 0
            			&& holidayFlag) {
//            			) {
            		//放假逻辑,期末库存 = 期初库存（厂外）- 客户需求 + 到货计划（arrpl+在途）
            		if(i < (shiftList.size() -1)) {
            			//不是第一天
                		inventoryShiftVO.setEndingInventory(inventoryShiftVO.getEndingInventory()- deliveryPlan);
                		inventoryShiftVO.setDeliveryPlan(0);
                		holidayArrivalPlan += deliveryPlan;
                		batchUpdateShiftMap.put(DateUtils.dateToString(plannedDate), inventoryShiftVO);
            		} else {
            			//第一天
            			inventoryShiftVO.setEndingInventory(inventoryShiftVO.getEndingInventory() + holidayArrivalPlan);
                		inventoryShiftVO.setDeliveryPlan(deliveryPlan + holidayArrivalPlan);
                		batchUpdateShiftMap.put(DateUtils.dateToString(plannedDate), inventoryShiftVO);
            		}
            		//当天放假，这天的期初库存（厂外）和期末库存等于前一天（不放假）的 期初库存（厂外）
            		if(openingInventory != null && plannedDate.compareTo(DateUtils.getDayFirstTime(new Date())) > 0) {
            			inventoryShiftVO.setOpeningInventory(openingInventory);
            			inventoryShiftVO.setEndingInventory(openingInventory);
            		}
            		//处理对应的发货详情，扣减对应的到货数量
            		if(deliveryDay.compareTo(dateList.get(0)) <= 0) {
            			//如果这个时间早于第一天的时间，那么放在第一天时间发货
            			DeliveryPlanDetailPO deliveryPlanDetailPO = batchUpdateDetailMap.get(DateUtils.dateToString(dateList.get(0)));
            			if(deliveryPlanDetailPO == null) {
            				deliveryPlanDetailPO = detailMap.get(DateUtils.dateToString(dateList.get(0)));
            			}
            			if(deliveryPlan > 0 && oemTransportSuppiyDays > 0) {
            				deliveryPlanDetailPO.setUrgentSign(YesOrNoEnum.YES.getCode());
            				deliveryPlanDetailPO.setDemandQuantity(deliveryPlanDetailPO.getDemandQuantity() + deliveryPlan);
            			}
                		setBoxNumAndDesc(deliveryPlanCalcSpace, inventoryShiftVO, deliveryPlanDetailPO);
                		batchUpdateDetailMap.put(DateUtils.dateToString(dateList.get(0)), deliveryPlanDetailPO);
            		}else {
            			//如果这个时间晚于第一天的时间，原发货时间的发货数量扣除
            			DeliveryPlanDetailPO deliveryPlanDetailPO = batchUpdateDetailMap.get(DateUtils.dateToString(deliveryDay));
            			if(deliveryPlanDetailPO == null) {
            				deliveryPlanDetailPO = detailMap.get(DateUtils.dateToString(deliveryDay));
            			}
                		deliveryPlanDetailPO.setDemandQuantity(deliveryPlanDetailPO.getDemandQuantity() - deliveryPlan);
                		setBoxNumAndDesc(deliveryPlanCalcSpace, inventoryShiftVO, deliveryPlanDetailPO);
                		batchUpdateDetailMap.put(DateUtils.dateToString(deliveryDay), deliveryPlanDetailPO);
            		}
            	}else {
            		openingInventory = inventoryShiftVO.getOpeningInventory();
            		if(!holidayFlag) {
            			holidayArrivalPlan = 0;
            		}
            		//不放假，且需要调整的数量>0
            		if(holidayArrivalPlan > 0) {
            			//不是放假逻辑，到货数量 > 0
            			Integer endingInventory = inventoryShiftVO.getEndingInventory() == null ? 0 : inventoryShiftVO.getEndingInventory();
                		inventoryShiftVO.setEndingInventory(endingInventory + holidayArrivalPlan);
                		inventoryShiftVO.setDeliveryPlan(deliveryPlan + holidayArrivalPlan);
                		batchUpdateShiftMap.put(DateUtils.dateToString(plannedDate), inventoryShiftVO);
                		DeliveryPlanDetailPO deliveryPlanDetailPO = null;
                		if(deliveryDay.compareTo(dateList.get(0)) <= 0) {
                			deliveryPlanDetailPO = batchUpdateDetailMap.get(DateUtils.dateToString(dateList.get(0)));
                			if(deliveryPlanDetailPO == null) {
                				deliveryPlanDetailPO = detailMap.get(DateUtils.dateToString(dateList.get(0)));
                			}
                			deliveryPlanDetailPO.setUrgentSign(YesOrNoEnum.YES.getCode());
                    		deliveryPlanDetailPO.setDemandQuantity(deliveryPlanDetailPO.getDemandQuantity() + holidayArrivalPlan);
                    		setBoxNumAndDesc(deliveryPlanCalcSpace, inventoryShiftVO, deliveryPlanDetailPO);
                    		batchUpdateDetailMap.put(DateUtils.dateToString(dateList.get(0)), deliveryPlanDetailPO);
                		}else {
                			//如果这个时间晚于第一天的时间，原发货时间的发货数量扣除
                			deliveryPlanDetailPO = batchUpdateDetailMap.get(DateUtils.dateToString(deliveryDay));
                			if(deliveryPlanDetailPO == null) {
                				deliveryPlanDetailPO = detailMap.get(DateUtils.dateToString(deliveryDay));
                			}
                    		deliveryPlanDetailPO.setDemandQuantity(deliveryPlanDetailPO.getDemandQuantity() + holidayArrivalPlan);
                    		setBoxNumAndDesc(deliveryPlanCalcSpace, inventoryShiftVO, deliveryPlanDetailPO);
                    		batchUpdateDetailMap.put(DateUtils.dateToString(deliveryDay), deliveryPlanDetailPO);
                		}
                		holidayArrivalPlan = 0;
            		}
            	}
			}
            
            //处理库存推移数据（对应发货计划有保存标识的那天数据）
            if(savedSinPlannedDate != null) {
            	shiftList.sort(Comparator.comparing(InventoryShiftVO::getPlannedDate));
            	Integer endingInventory = 0;
            	for (InventoryShiftVO inventoryShiftVO : shiftList) {
            		Date plannedDate = inventoryShiftVO.getPlannedDate();
            		String plannedDateStr = DateUtils.dateToString(plannedDate);
            		if(batchUpdateShiftMap.containsKey(plannedDateStr)) {
            			inventoryShiftVO = batchUpdateShiftMap.get(plannedDateStr);
            		}
            		if(plannedDate.compareTo(savedSinPlannedDate) > 0) {
            			Integer diffQty = inventoryShiftVO.getOpeningInventory() - endingInventory;
            			inventoryShiftVO.setOpeningInventory(endingInventory);
            			inventoryShiftVO.setEndingInventory(inventoryShiftVO.getEndingInventory() - diffQty);
            			batchUpdateShiftMap.put(DateUtils.dateToString(plannedDate), inventoryShiftVO);
            		}
            		if(plannedDate.compareTo(savedSinPlannedDate) >= 0) {
            			endingInventory = inventoryShiftVO.getEndingInventory();
            		}
				}
            }
            if(!batchUpdateDetailMap.isEmpty()) {
            	batchUpdateDetail.addAll(new ArrayList<>(batchUpdateDetailMap.values()));
            }
            if(!batchUpdateShiftMap.isEmpty()) {
            	batchUpdateShift.addAll(new ArrayList<>(batchUpdateShiftMap.values()));
            }
		}
        if(CollectionUtils.isNotEmpty(batchUpdateDetail)) {
        	deliveryPlanDetailDao.updateBatchSelective(batchUpdateDetail);
        }
        if(CollectionUtils.isNotEmpty(batchUpdateShift)) {
        	List<InventoryShiftPO> list = InventoryShiftConvertor.INSTANCE.vo2Pos(batchUpdateShift);
        	inventoryShiftDao.updateBatchSelective(list);
        }
	}
	
	
	/**
	 * MTS没有新旧物料关系的重新计算期末库存天数
	 * @param oldCodeList
	 * @param newCodeList
	 * @param dateList
	 * @param versionId
	 * @param newProductStockPointVOS
	 * @param deliveryPlanCalcSpace
	 */
	private void doCalcMtsEndingInventoryDays(List<String> oldCodeList, List<String> newCodeList, List<Date> dateList,
			String versionId, List<NewProductStockPointVO> newProductStockPointVOS, DeliveryPlanCalcSpace deliveryPlanCalcSpace) {
		//1.默认查询供应类型为MTS的数据
        List<DeliveryPlanVO> mtsDeliveryPlanList = deliveryPlanDao.selectVOByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(), 
    			"versionId", versionId, 
    			"demandCategory", ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode(), 
    			"supplyType" , OemBusinessTypeEnum.MTS.getCode()));
        //只处理当前用户权限下的发货计划（物料权限）
        List<String> userProductCodes = newProductStockPointVOS.stream().map(NewProductStockPointVO::getProductCode)
        		.distinct().collect(Collectors.toList());
        //2.过滤不存在新旧物料替代关系的数据，且是用户权限下的物料
        mtsDeliveryPlanList = mtsDeliveryPlanList.stream().filter( e -> !oldCodeList.contains(e.getOemCode() + "&&" + e.getProductCode()) 
        		&& !newCodeList.contains(e.getOemCode() + "&&" + e.getProductCode())
        		&& userProductCodes.contains(e.getProductCode()))
        		.collect(Collectors.toList());
        if(CollectionUtils.isEmpty(mtsDeliveryPlanList)) {
        	return;
        }
    	//处理期末库存天数数据
    	List<String> shiftOemCodes = mtsDeliveryPlanList.stream().map(DeliveryPlanVO::getOemCode)
    			.distinct().collect(Collectors.toList());
    	List<String> shiftProductCodes = mtsDeliveryPlanList.stream().map(DeliveryPlanVO::getProductCode)
    			.distinct().collect(Collectors.toList());
    	List<String> oemProductCodes = mtsDeliveryPlanList.stream().map(e -> e.getOemCode() + "&&" + e.getProductCode())
    			.distinct().collect(Collectors.toList());
    	List<InventoryShiftPO> changeDayShifts = inventoryShiftDao.selectByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(), 
    			"oemCodes", shiftOemCodes, 
    			"productCodes", shiftProductCodes, 
    			"versionId" , mtsDeliveryPlanList.get(0).getVersionId()));
    	//按照主机厂，产品编码分组处理
    	Map<String, List<InventoryShiftPO>> changeDayShiftMap = changeDayShifts.stream()
    			.filter( e-> e.getPlannedDate().compareTo(dateList.get(0)) >= 0
				&& e.getEndingInventoryDays() != null
        		&& oemProductCodes.contains(e.getOemCode() + "&&" + e.getProductCode()))
        		.collect(Collectors.groupingBy(e -> e.getOemCode() + "&" + e.getProductCode()));
    	List<InventoryShiftPO> updateChangeDayList = new ArrayList<>();
    	for (List<InventoryShiftPO> changeDayList : changeDayShiftMap.values()) {
    		changeDayList.sort(Comparator.comparing(InventoryShiftPO::getPlannedDate));
    		List<Date> plannedDates = changeDayList.stream().map(InventoryShiftPO::getPlannedDate).collect(Collectors.toList());    		
    		//按天
    		Map<String, Integer> demandSumGroupByDay = changeDayList.stream()
    				.collect(Collectors.toMap(e-> DateUtils.dateToString(e.getPlannedDate()), InventoryShiftPO::getCustomerDemand,(v1, v2) -> v1));
    		//获取主机厂对应的放假时间
            List<String> oemHolidayList = deliveryPlanCalcSpace.getOemHolidayMap()
            		.getOrDefault(changeDayList.get(0).getOemCode(), new ArrayList<>());
    		for (int i = 0; i < changeDayList.size(); i++) {
    			InventoryShiftPO inventoryShiftPO = changeDayList.get(i);
        		BigDecimal endingInventoryDays = getEndingInventoryDays(plannedDates, demandSumGroupByDay, 
        				inventoryShiftPO.getEndingInventory(), i, new ArrayList<>());
        		inventoryShiftPO.setEndingInventoryDays(endingInventoryDays);
        		//重新计算标准安全库存量
//        		BigDecimal standardSafetyInventoryDays = inventoryShiftPO.getStandardSafetyInventoryDays();
//        		if(standardSafetyInventoryDays != null) {
//        			BigDecimal standardSafetyInventoryLevel = getStandardSafetyInventoryLevel(plannedDates, demandSumGroupByDay, i, 
//            				oemHolidayList, inventoryShiftPO.getStandardSafetyInventoryDays().intValue());
//            		inventoryShiftPO.setStandardSafetyInventoryLevel(standardSafetyInventoryLevel.intValue());
//        		}
    			updateChangeDayList.add(inventoryShiftPO);
			}
		}
    	if(CollectionUtils.isNotEmpty(updateChangeDayList)) {
    		inventoryShiftDao.updateBatchSelective(updateChangeDayList);
    	}
	}
	

	private static BigDecimal getStandardSafetyInventoryLevel(List<Date> dateList,
	                                                 Map<String, Integer> demandSumGroupByDay,
	                                                 int i, List<String> oemHolidayList,
	                                                 Integer standardSafetyInventoryDays) {
	    BigDecimal standardSafetyInventoryLevel = BigDecimal.ZERO;
	    for (int j = i + 1; j < dateList.size(); j++) {
	    	if(standardSafetyInventoryDays <= 0) {
	    		break;
	    	}
	        Date dateAfterOne = dateList.get(j);
	        String dateAfterOneStr = DateUtils.dateToString(dateAfterOne);
	        //如果这一天放假，那么这天直接跳过，不计算期末库存天数
	        if(oemHolidayList.contains(dateAfterOneStr)) {
	        	continue;
	        }
	        int customerDemandAfterOne = demandSumGroupByDay.get(DateUtils.dateToString(dateAfterOne)) == null
	                ? 0 : demandSumGroupByDay.get(DateUtils.dateToString(dateAfterOne));
	        standardSafetyInventoryLevel = standardSafetyInventoryLevel.add(BigDecimal.valueOf(customerDemandAfterOne));
	        standardSafetyInventoryDays --;
	    }
	    return standardSafetyInventoryLevel;
	}
	

	/**
	 * 维护箱数，成箱描述
	 * @param deliveryPlanCalcSpace
	 * @param inventoryShiftVO
	 * @param deliveryPlanDetailPO
	 */
	private void setBoxNumAndDesc(DeliveryPlanCalcSpace deliveryPlanCalcSpace, InventoryShiftVO inventoryShiftVO,
			DeliveryPlanDetailPO deliveryPlanDetailPO) {
		Map<String, BoxInfoVO> boxInfoVOMapOfId = deliveryPlanCalcSpace.getBoxInfoVOMapOfId();
		Map<String, List<ProductBoxRelationVO>> productBoxRelationVOMapOfProductCode = deliveryPlanCalcSpace.getProductBoxRelationVOMapOfProductCode();
		ProductBoxRelationVO productBoxRelationVO = new ProductBoxRelationVO();
		List<ProductBoxRelationVO> productBoxRelationVOList =
		        productBoxRelationVOMapOfProductCode.get(inventoryShiftVO.getProductCode());
		if (CollectionUtils.isNotEmpty(productBoxRelationVOList)) {
		    productBoxRelationVO = productBoxRelationVOList.get(0);
		}
		Integer standardLoad = productBoxRelationVO.getStandardLoad();
		if(standardLoad == null) {
			deliveryPlanDetailPO.setBoxQuantity(0);
			deliveryPlanDetailPO.setBoxDesc("未维护产品成箱片数");
			return;
		}
		BoxInfoVO boxInfoVO = boxInfoVOMapOfId.get(productBoxRelationVO.getBoxId());
		setBoxDesc(deliveryPlanDetailPO, boxInfoVO, deliveryPlanDetailPO.getDemandQuantity(), productBoxRelationVO);
		BigDecimal boxNum = BigDecimal.valueOf(deliveryPlanDetailPO.getDemandQuantity())
				.divide(BigDecimal.valueOf(productBoxRelationVO.getStandardLoad()), 0 ,BigDecimal.ROUND_UP);
		deliveryPlanDetailPO.setBoxQuantity(boxNum.intValue());
	}


	/**
	 * 获取主机厂运输天数
	 * @param oemTransportTimeVOGroup
	 * @param deliveryPlanVO
	 * @return
	 */
	private int getOemTransportSuppiyDays(Map<String, List<OemTransportTimeVO>> oemTransportTimeVOGroup,
			DeliveryPlanVO deliveryPlanVO) {
		int oemTransportSupplyDays = 0;
		List<OemTransportTimeVO> oemTransportTimeVOS = oemTransportTimeVOGroup.get(deliveryPlanVO.getOemCode());
		// 获取优先级最高的数据
		if (CollectionUtils.isNotEmpty(oemTransportTimeVOS)) {
		    if (StringUtils.isNotBlank(deliveryPlanVO.getTransportationRouteId())) {
		        Map<String, OemTransportTimeVO> oemTransportTimeVOMap =
		                oemTransportTimeVOS.stream().collect(Collectors.toMap(OemTransportTimeVO::getId,
		                        Function.identity(), (t1, t2) -> t1));
		        OemTransportTimeVO oemTransportTimeVO =
		                oemTransportTimeVOMap.get(deliveryPlanVO.getTransportationRouteId());
		        oemTransportSupplyDays = calculateTransportDays(null == oemTransportTimeVO
		                ? 0 : (oemTransportTimeVO.getTransportationTime() == null
		                ? 0 : oemTransportTimeVO.getTransportationTime().doubleValue()));
		    } else {
		        OemTransportTimeVO oemTransportTimeVO =
		                oemTransportTimeVOS.stream().min(Comparator.comparing(OemTransportTimeVO::getPriority)).orElse(new OemTransportTimeVO());
		        // 计算主机厂运输时间
		        oemTransportSupplyDays = calculateTransportDays(null == oemTransportTimeVO.getTransportationTime() ?
		                0 : oemTransportTimeVO.getTransportationTime().doubleValue());
		    }
		}
		return oemTransportSupplyDays;
	}

    private DeliveryPlanPO createDeliveryPlan(String versionId, String productCode, String oemCode,
                                              String oemTransportTimeVOId, DeliveryPlanCalcSpace deliveryPlanCalcSpace,
                                              String demandCategory) {
        DeliveryPlanPO deliveryPlanPO = new DeliveryPlanPO();
        deliveryPlanPO.setId(UUIDUtil.getUUID());
        deliveryPlanPO.setVersionId(versionId);
        deliveryPlanPO.setProductCode(productCode);
        deliveryPlanPO.setOemCode(oemCode);
        deliveryPlanPO.setPublishStatus(PublishStatusEnum.UNPUBLISH.getCode());
        deliveryPlanPO.setSupplyType(deliveryPlanCalcSpace.getOemVO(oemCode).getBusinessType());
        deliveryPlanPO.setTradeType(deliveryPlanCalcSpace.getOemVO(oemCode).getMarketType());
        deliveryPlanPO.setTransportationRouteId(oemTransportTimeVOId);
        deliveryPlanPO.setDemandCategory(demandCategory);
        return deliveryPlanPO;
    }

    private DeliveryPlanCalcSpace initializeSpace(List<NewProductStockPointVO> newProductStockPointVOS,
                                                  String deliveryPlanVersionCode, List<String> productScope,
                                                  Date lastDay, List<DfpSwitchRelationBetweenProductVO> switchRelationBetweenProductVOS,
                                                  boolean saved, DeliveryPlanCalculateDTO deliveryPlanCalcDTO, List<DeliveryPlanDetailVO> savedDeliveryPlanDetailList) {
        DeliveryPlanCalcSpace deliveryPlanCalcSpace = new DeliveryPlanCalcSpace();
        deliveryPlanCalcSpace.initNewProductStockPointVOList(newProductStockPointVOS);
        //主机厂数据
        List<OemVO> oemVOList = oemService.selectAll();
        deliveryPlanCalcSpace.initOemVOList(oemVOList);
        //主机厂运输时间数据
        List<OemTransportTimeVO> oemTransportTimeVOList = oemTransportTimeService.selectByParams(ImmutableMap.of(
                "oemCodes", deliveryPlanCalcSpace.getOemCodeList()));
        deliveryPlanCalcSpace.initOemTransportTimeVOGroup(oemTransportTimeVOList);
        // 发货计划锁定配置
        List<DeliveryPlanLockConfigVO> deliveryPlanLockConfigVOS = deliveryPlanLockConfigService.selectAll();
        deliveryPlanCalcSpace.initDeliveryPlanLockConfigVOMap(deliveryPlanLockConfigVOS);
        //发货计划补货策略配置
        List<DeliveryPlanReplenishConfigVO> deliveryPlanReplenishConfigVOS =
                deliveryPlanReplenishConfigService.selectAll();
        deliveryPlanCalcSpace.initDeliveryPlanReplenishConfigVOMap(deliveryPlanReplenishConfigVOS);
        // 主机厂库存提报数据（主机厂期初库存+部分在途+部分期初库存）
        List<OemInventorySubmissionVO> oemInventorySubmissionVOS = oemInventorySubmissionService.selectAll();
        deliveryPlanCalcSpace.initOemInventoryMap(oemInventorySubmissionVOS, lastDay);
        // 期初库存
        deliveryPlanCalcSpace.setProductStockPointInventoryMap(deliveryPlanDomainService.getRealTimeInventory(deliveryPlanVersionCode,
                deliveryPlanCalcSpace.getOemTransitInventoryMap(), productScope));
        // 安全库存配置
        deliveryPlanCalcSpace.setSafetyStockLevelVOMap(safetyStockLevelService.selectWithOutSaleOrg().stream().collect(Collectors
                .toMap(SafetyStockLevelVO::getProductCode, Function.identity(), (t1, t2) -> t1)));
        //MTS主机厂最新发货版本发货计划数据(已锁定)
        deliveryPlanCalcSpace.initPublishedDeliveryDetailMap(getLatestPublishedDeliveryPlanInLockDate(deliveryPlanLockConfigVOS, deliveryPlanVersionCode));
        //工程变更新旧物料关系
        deliveryPlanCalcSpace.initSwitchRelationMap(switchRelationBetweenProductVOS);
        //已保存的发货计划明细
        deliveryPlanCalcSpace.setSavedDeliveryPlanDetailMap(getSavedDeliveryPlanDetailMap(saved, deliveryPlanCalcDTO, savedDeliveryPlanDetailList));
        //处理产品对应的是否整箱
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(SystemHolder.getScenario(),
                "SALE_ORGANIZATION",
                "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        if(StringUtils.isNotEmpty(rangeData)) {
        	List<String> stockPoints = Arrays.asList(rangeData.split(","));
        	Map<String, String> productFullBoxMap = newProductStockPointVOS.stream().filter(e -> stockPoints.contains(e.getStockPointCode())
        			&& StringUtils.isNotEmpty(e.getFullBoxFlag()))
        		.collect(Collectors.toMap(NewProductStockPointVO::getProductCode,NewProductStockPointVO::getFullBoxFlag,(v1, v2) -> v1));
        	deliveryPlanCalcSpace.setProductFullBoxMap(productFullBoxMap);
        }
        return deliveryPlanCalcSpace;
    }

    private Map<String, List<DeliveryPlanDetailPO>> getSavedDeliveryPlanDetailMap(boolean saved,
                                                                                  DeliveryPlanCalculateDTO deliveryPlanCalcDTO, List<DeliveryPlanDetailVO> savedDeliveryPlanDetailList) {
        Map<String, List<DeliveryPlanDetailPO>> deliveryPlanDetailPOMap = new HashMap<>();
        Map<String, List<DeliveryPlanDetailVO>> savedDeliveryPlanDetailMap =
                savedDeliveryPlanDetailList.stream().collect(Collectors.groupingBy(item -> item.getOemCode() + "&&" + item.getProductCode()));
        if (saved) {
            List<DeliveryPlanDTO> deliveryPlanDTOS = deliveryPlanCalcDTO.getDeliveryPlanList();
            deliveryPlanDTOS.forEach(item -> {
                List<DeliveryPlanDetailPO> deliveryPlanDetailPOList =
                        DeliveryPlanDetailConvertor.INSTANCE.dto2Pos(item.getDetailList());
                //将小于保存时间之前并且本次没有修改的数据找出来
                Date savedTime =
                        deliveryPlanDetailPOList.stream().filter(x -> x.getDemandTime() != null).max(Comparator.comparing(DeliveryPlanDetailPO::getDemandTime)).isPresent() ?
                                deliveryPlanDetailPOList.stream().filter(x -> x.getDemandTime() != null).max(Comparator.comparing(DeliveryPlanDetailPO::getDemandTime)).get().getDemandTime() : null;
                if (savedTime != null) {
                    List<DeliveryPlanDetailVO> oldList =
                            savedDeliveryPlanDetailMap.get(item.getOemCode() + "&&" + item.getProductCode());
                    if (CollectionUtils.isNotEmpty(oldList)) {
                        oldList = oldList.stream().filter(x -> {
                            boolean flag = x.getDemandTime().compareTo(savedTime) <= 0;
                            if (!flag) {
                                return false;
                            }
                            for (DeliveryPlanDetailPO po : deliveryPlanDetailPOList) {
                                if (po.getId().equals(x.getId())) {
                                    return false;
                                }
                            }
                            return true;
                        }).collect(Collectors.toList());
                    }
                    if (CollectionUtils.isNotEmpty(oldList)) {
                        List<DeliveryPlanDetailPO> oldPOList = new ArrayList<>();
                        oldList.forEach(z -> {
                            z.setSavedSign(YesOrNoEnum.YES.getCode());
                            oldPOList.add(DeliveryPlanDetailConvertor.INSTANCE.vo2Po(z));
                        });
                        deliveryPlanDetailPOList.addAll(oldPOList);

                    }
                }
                deliveryPlanDetailPOMap.put(item.getOemCode() + "&&" + item.getProductCode(), deliveryPlanDetailPOList);
            });

        } else {
            savedDeliveryPlanDetailMap.forEach((key, value) -> {
                Date savedTime =
                        value.stream().filter(x -> YesOrNoEnum.YES.getCode().equals(x.getSavedSign()) && x.getDemandTime() != null).max(Comparator.comparing(DeliveryPlanDetailVO::getDemandTime)).isPresent() ?
                                value.stream().filter(x -> YesOrNoEnum.YES.getCode().equals(x.getSavedSign()) && x.getDemandTime() != null).max(Comparator.comparing(DeliveryPlanDetailVO::getDemandTime)).get().getDemandTime() : null;
                if (savedTime == null) {
                    return;
                }
                List<DeliveryPlanDetailPO> deliveryPlanDetailPOList = new ArrayList<>();
                value.forEach(item -> {
                    if (!item.getDemandTime().after(savedTime)) {
                        deliveryPlanDetailPOList.add(DeliveryPlanDetailConvertor.INSTANCE.vo2Po(item));
                    }
                });
                deliveryPlanDetailPOMap.put(key, deliveryPlanDetailPOList);
            });
        }
        return deliveryPlanDetailPOMap;
    }

    private void initDataByProductScope(DeliveryPlanCalcSpace deliveryPlanCalcSpace,
                                        List<String> oemCodeScope, List<String> productScope,
                                        List<String> productBoxRelationWarningList, String deliveryPlanVersionCode,
                                        List<CleanDemandDataDetailVO> cleanDemandDataDetailVOS) {
        //产品主机厂映射
        Map<String, List<String>> productOemMap = getProductOemMap(deliveryPlanCalcSpace.getNewProductStockPointVOS(),
                oemCodeScope);
        //在途数据
        deliveryPlanCalcSpace.setProductStockPointInRoadMap(getProductStockPointInRoad(productScope,
                deliveryPlanCalcSpace.getOemVOList(), productOemMap, oemCodeScope));

        //维护发货清单号对应的中转库入库数量
        List<String> listNumList = new ArrayList<>();
        Map<String, List<WarehouseReleaseToWarehouseVO>> productStockPointInRoadMap = deliveryPlanCalcSpace.getProductStockPointInRoadMap();
        for (List<WarehouseReleaseToWarehouseVO> warehouseList : productStockPointInRoadMap.values()) {
            for (WarehouseReleaseToWarehouseVO warehouse : warehouseList) {
                String listNum = warehouse.getListNum();
                if (StringUtils.isNotEmpty(listNum) && !listNumList.contains(listNum)) {
                    listNumList.add(listNum);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(listNumList)) {
            List<OemInventorySubmissionVO> oemInventorySubmissionList = oemInventorySubmissionService.selectByParams(ImmutableMap.of(
                    "enabled", YesOrNoEnum.YES.getCode(),
                    "shippingListNumbers", listNumList));
            Map<String, BigDecimal> shippingListNumberMap = oemInventorySubmissionList.stream()
                    .filter(e -> StringUtils.isNotEmpty(e.getShippingListNumber()) && !Objects.isNull(e.getTransitEnterQuantity()))
                    .collect(Collectors.toMap(OemInventorySubmissionVO::getShippingListNumber, OemInventorySubmissionVO::getTransitEnterQuantity,
                            (value1, value2) -> value1));
            deliveryPlanCalcSpace.setShippingListNumberMap(shippingListNumberMap);
        }

        //零件风险等级和箱体信息
        if (CollectionUtils.isNotEmpty(productScope)) {
            List<PartRiskLevelVO> partRiskLevelVOS = partRiskLevelService.selectByProductCodeList(productScope);
            // 根据主机厂+零件号分组
            deliveryPlanCalcSpace.setMaterialRiskLevelVOMapOfJoinKey(partRiskLevelVOS.stream().collect(Collectors.toMap(item -> String.join(
                    "&", item.getOemCode(), item.getProductCode()), Function.identity(), (t1, t2) -> t2)));

            // 查询零件与成品箱关系
            List<ProductBoxRelationVO> productBoxRelationVOList =
                    newMdsFeign.selectProductBoxRelationVOByProductCodeList(SystemHolder.getScenario(),
                            productScope);
            //过滤无效数据,且维护了标准装载量的数据
            productBoxRelationVOList = productBoxRelationVOList.stream()
                    .filter(e -> YesOrNoEnum.YES.getCode().equals(e.getEnabled()) && e.getStandardLoad() != null)
                    .collect(Collectors.toList());
            //按照产品编码分组
            Map<String, List<ProductBoxRelationVO>> productBoxRelationMap = productBoxRelationVOList.stream().collect(Collectors.groupingBy(ProductBoxRelationVO::getProductCode));
            Map<String, List<ProductBoxRelationVO>> productBoxRelationVOMapOfProductCode = new HashMap<>();
            for (Entry<String, List<ProductBoxRelationVO>> productBoxRelationEntry : productBoxRelationMap.entrySet()) {
                List<ProductBoxRelationVO> boxRelationList = productBoxRelationEntry.getValue();
                //过滤有优先级的数据
                List<ProductBoxRelationVO> priorityBoxRelationList = boxRelationList.stream()
                        .filter(e -> e.getPriority() != null).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(priorityBoxRelationList)) {
                    priorityBoxRelationList.sort(Comparator.comparing(ProductBoxRelationVO::getPriority));
                    productBoxRelationVOMapOfProductCode.put(productBoxRelationEntry.getKey(), priorityBoxRelationList);
                } else {
                    productBoxRelationVOMapOfProductCode.put(productBoxRelationEntry.getKey(), boxRelationList);
                }
                //未维护优先级的进行告警处理
                if (!productBoxRelationWarningList.contains(productBoxRelationEntry.getKey())
                        && boxRelationList.size() != priorityBoxRelationList.size()) {
                    productBoxRelationWarningList.add(productBoxRelationEntry.getKey());
                }

            }
            deliveryPlanCalcSpace.setProductBoxRelationVOMapOfProductCode(productBoxRelationVOMapOfProductCode);
            // 查询箱体信息
            if (CollectionUtils.isNotEmpty(productBoxRelationVOList)) {
                List<String> boxIdList =
                        productBoxRelationVOList.stream().map(ProductBoxRelationVO::getBoxId).distinct().collect(Collectors.toList());
                List<BoxInfoVO> boxInfoVOList = newMdsFeign.selectBoxInfoVOByBoxIdList(SystemHolder.getScenario(),
                        boxIdList);
                deliveryPlanCalcSpace.setBoxInfoVOMapOfId(boxInfoVOList.stream().collect(Collectors.toMap(BoxInfoVO::getId,
                        Function.identity(), (t1, t2) -> t2)));
            }
        }
        
        //维护主机厂对应的放假时间
        Map<String, List<String>> oemHolidayMap = getOemHolidayMap(oemCodeScope, deliveryPlanVersionCode);
        deliveryPlanCalcSpace.setOemHolidayMap(oemHolidayMap);
        //根据需求数量移除对应的放假时间
        Map<String, List<CleanDemandDataDetailVO>> cleanDemandDataMap = cleanDemandDataDetailVOS.stream()
        		.filter(e -> StringUtils.isNotEmpty(e.getOemCode()) 
        				&& e.getDemandQuantity() != null && e.getDemandQuantity().compareTo(BigDecimal.ZERO) > 0)
        		.collect(Collectors.groupingBy(CleanDemandDataDetailVO::getOemCode));
        for (Entry<String, List<CleanDemandDataDetailVO>> cleanDemandDataEntry : cleanDemandDataMap.entrySet()){
        	List<String> oemHolidayList = oemHolidayMap.get(cleanDemandDataEntry.getKey());
        	if(CollectionUtils.isEmpty(oemHolidayList)) {
        		continue;
        	}
        	for (CleanDemandDataDetailVO cleanDemandDataDetailVO : cleanDemandDataEntry.getValue()) {
        		String demandTimeStr = DateUtils.dateToString(cleanDemandDataDetailVO.getDemandTime());
				if(oemHolidayList.contains(demandTimeStr)) {
					oemHolidayList.remove(demandTimeStr);
				}
			}
		}
        
    }

    /**
     * 获取主机厂放假时间Map
     * @param oemCodeScope
     * @param deliveryPlanVersionCode
     * @return
     */
	private Map<String, List<String>> getOemHolidayMap(List<String> oemCodeScope, String deliveryPlanVersionCode) {
		BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(SystemHolder.getScenario(),
                "SALE_ORGANIZATION",
                "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        List<OemStockPointMapVO> oemStockPointMapList = oemStockPointMapService.selectExcludeStockPointCode(rangeData);
        
        List<OemVO> oemList = oemService.selectAll();
    	List<String> mtsOemCodeList = oemList.stream().filter( e -> OemBusinessTypeEnum.MTS.getCode().equals(e.getBusinessType()))
        		.map(OemVO::getOemCode)
        		.collect(Collectors.toList());
        //校验是否维护对应的 主机厂库存点关联关系
        List<String> oemStockPointOemCodes = oemStockPointMapList.stream().map(OemStockPointMapVO::getOemCode)
        		.distinct().collect(Collectors.toList());
        List<String> differenceOemCodes = oemCodeScope.stream()
                .filter(item -> !oemStockPointOemCodes.contains(item) && mtsOemCodeList.contains(item))
                .collect(Collectors.toList());
        //只过滤出MTS的主机厂
        if(CollectionUtils.isNotEmpty(differenceOemCodes)) {
        	throw new BusinessException("主机厂" + differenceOemCodes.toString() + "未维护主机厂库存点关联关系"); 
        }
        
        //获取主机厂对应的库存点，然后通过库存点获取所有的主机厂
        List<String> mtsStockPointCodeList = oemStockPointMapList.stream()
        		.filter(e -> oemCodeScope.contains(e.getOemCode())).map(OemStockPointMapVO::getStockPointCode)
        		.distinct().collect(Collectors.toList());
        List<String> allOemCode = oemStockPointMapList.stream()
        		.filter(e -> mtsStockPointCodeList.contains(e.getStockPointCode())).map(OemStockPointMapVO::getOemCode)
        		.distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(allOemCode)) {
        	return new HashMap<>();
        }
        //获取这些主机厂的装车日历(90天)
        List<Date> dateList = DeliveryPlanDateUtils.getThreeMonthDateList(deliveryPlanVersionCode);
        Map<String, List<ResourceCalendarVO>> resourceCalendarGroupOemCodeMap = dfpResourceCalendarService
        		.getResourceByOem(allOemCode, dateList, true);
        //处理主机厂放假数据,key:主机厂，value: 库存点
        Map<String, String> oemStockPointMap = oemStockPointMapList.stream()
        		.collect(Collectors.toMap(OemStockPointMapVO::getOemCode,OemStockPointMapVO::getStockPointCode,(v1, v2) -> v1));
        Map<String, List<String>> stockPointOmeMap = oemStockPointMapList.stream()
                .collect(Collectors.groupingBy(
                		OemStockPointMapVO::getStockPointCode,
                    Collectors.mapping(OemStockPointMapVO::getOemCode, Collectors.toList())
                ));
        //确定当前主机厂那几天放假
        Map<String, List<String>> oemHolidayMap = new HashMap<>();
        for (String mtsOemCode : oemCodeScope) {
        	List<String> allWorkDayStrList = dateList.stream()
    				.map( e-> DateUtils.dateToString(e)).distinct().collect(Collectors.toList());
        	String stockPointCode = oemStockPointMap.get(mtsOemCode);
        	if(!mtsOemCodeList.contains(mtsOemCode)) {
        		continue;
        	}
        	if(StringUtils.isEmpty(stockPointCode)) {
        		throw new BusinessException(mtsOemCode + "未维护主机厂库存点关联关系"); 
        	}
        	List<String> sameStockPointOmeCodes = stockPointOmeMap.get(stockPointCode);
        	for (String sameOmeCode : sameStockPointOmeCodes) {
        		List<ResourceCalendarVO> resourceCalendars = resourceCalendarGroupOemCodeMap.get(sameOmeCode);
                if (CollectionUtils.isEmpty(resourceCalendars)) {
                	continue;
                }
        		List<String> workDayStrList = resourceCalendars.stream()
        				.map( e-> DateUtils.dateToString(e.getWorkDay())).distinct().collect(Collectors.toList());
        		allWorkDayStrList.removeAll(workDayStrList);
			}
        	if(CollectionUtils.isNotEmpty(allWorkDayStrList)){
        		oemHolidayMap.put(mtsOemCode, allWorkDayStrList);
        	}
		}
		return oemHolidayMap;
	}

    //产品主机厂映射
    private Map<String, List<String>> getProductOemMap(List<NewProductStockPointVO> newProductStockPointVOS,
                                                       List<String> oemCodeScope) {

        Map<String, List<String>> result = new HashMap<>();
        if (CollectionUtils.isEmpty(oemCodeScope)) {
            return result;
        }
        if (CollectionUtils.isEmpty(newProductStockPointVOS)) {
            return result;
        }
        //产线车型映射
        Map<String, String> productVehicleMap =
                newProductStockPointVOS.stream().filter(item -> StringUtils.isNotEmpty(item.getVehicleModelCode())).collect(Collectors.toMap(NewProductStockPointVO::getProductCode,
                        NewProductStockPointVO::getVehicleModelCode, (t1, t2) -> t1));
        //车型主机厂映射
        List<OemProductLineMapPO> oemProductLineMapVOList = oemProductLineMapDao.selectByOemCodeList(oemCodeScope);
        Map<String, List<OemProductLineMapPO>> oemProductLineMapVOMap = oemProductLineMapVOList.stream()
                .collect(Collectors.groupingBy(OemProductLineMapPO::getVehicleModelCode));

        productVehicleMap.forEach((productCode, vehicleModelCode) -> {
            if (oemProductLineMapVOMap.containsKey(vehicleModelCode)) {
                List<String> oemCodeList = oemProductLineMapVOMap.get(vehicleModelCode).stream()
                        .map(OemProductLineMapPO::getOemCode).collect(Collectors.toList());
                result.put(productCode, oemCodeList);
            }
        });

        return result;
    }

    private List<CleanDemandDataDetailVO> getCleanDemandDetailVOs(Map<String, List<CleanDemandDataDetailVO>> cleanDemandDataDetailVOSMap) {
        return cleanDemandDataDetailVOSMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
    }

    public void generateMtsNormalData(Map<String, SafetyStockLevelVO> safetyStockLevelVOMap,
                                      DeliveryPlanPO deliveryPlanPO, NewProductStockPointVO newProductStockPointVO,
                                      List<String> noSafetyStockLeve,
                                      Map<String, Integer> productStockPointInventoryMap,
                                      String key, Map<String, Integer> oemInventoryMap,
                                      Map<String, List<WarehouseReleaseToWarehouseVO>> productStockPointInRoadMap,
                                      List<Date> dateList, int oemTransportSupplyDays,
                                      Map<String, Integer> demandSumGroupByDay,
                                      Map<String, List<ResourceCalendarVO>> resourceCalendarGroupOemCodeMap,
                                      List<DeliveryPlanDetailPO> deliveryPlanDetailPOListCreateTemp,
                                      List<InventoryShiftPO> inventoryShiftPOListCreateTemp,
                                      Map<String, PartRiskLevelVO> finalMaterialRiskLevelVOMapOfJoinKey,
                                      Map<String, List<ProductBoxRelationVO>> finalProductBoxRelationVOMapOfProductCode,
                                      Map<String, DeliveryPlanLockConfigVO> deliveryPlanLockConfigVOMap,
                                      Map<String, DeliveryPlanReplenishConfigVO> deliveryPlanReplenishConfigVOMap,
                                      Map<String, BoxInfoVO> finalBoxInfoVOMapOfId,
                                      List<String> productBoxRelationWarningList,
                                      Map<String, List<OemInventorySubmissionVO>> receivedOemInventorySubmissionMap,
                                      Map<String, List<DeliveryPlanDetailPO>> savedDeliveryPlanDetailMap,
                                      List<String> savedDeliveryPlanDetailWarningList, DeliveryPlanCalcSpace deliveryPlanCalcSpace
    ) {
        SafetyStockLevelVO safetyStockLevelVO = safetyStockLevelVOMap.get(deliveryPlanPO.getProductCode());
        if (safetyStockLevelVO == null) {
            noSafetyStockLeve.add(deliveryPlanPO.getProductCode());
            return;
        }
        // 期初库存
        Integer openingInventory = productStockPointInventoryMap.get(key) == null ? 0 :
                productStockPointInventoryMap.get(key);
        // 主机厂期初库存
        Integer oemOpeningInventory =
                oemInventoryMap.get(deliveryPlanPO.getOemCode() + "&&" + deliveryPlanPO.getProductCode()) == null
                        ? 0 :
                        oemInventoryMap.get(deliveryPlanPO.getOemCode() + "&&" + deliveryPlanPO.getProductCode());
        // 在途数据
        List<WarehouseReleaseToWarehouseVO> warehouseReleaseRecordVOS = productStockPointInRoadMap.get(deliveryPlanPO.getOemCode() + "&&" + deliveryPlanPO.getProductCode());
        List<OemInventorySubmissionVO> receivedOemInventorySubmissionVOS =
                receivedOemInventorySubmissionMap.get(deliveryPlanPO.getOemCode() + "&&" + deliveryPlanPO.getProductCode());
        List<DeliveryPlanDetailPO> savedDeliveryPlanDetailPOList =
                savedDeliveryPlanDetailMap.get(deliveryPlanPO.getOemCode() + "&&" + deliveryPlanPO.getProductCode());
        generateMtsData(deliveryPlanPO, dateList, oemTransportSupplyDays, demandSumGroupByDay,
                resourceCalendarGroupOemCodeMap, openingInventory, oemOpeningInventory,
                warehouseReleaseRecordVOS, safetyStockLevelVO, newProductStockPointVO,
                deliveryPlanLockConfigVOMap.get(deliveryPlanPO.getOemCode()),
                deliveryPlanReplenishConfigVOMap.get(deliveryPlanPO.getOemCode()),
                deliveryPlanDetailPOListCreateTemp, inventoryShiftPOListCreateTemp,
                finalMaterialRiskLevelVOMapOfJoinKey,
                finalProductBoxRelationVOMapOfProductCode, finalBoxInfoVOMapOfId,
                productBoxRelationWarningList, receivedOemInventorySubmissionVOS, savedDeliveryPlanDetailPOList,
                savedDeliveryPlanDetailWarningList, deliveryPlanCalcSpace);

    }


    public List<DfpSwitchRelationBetweenProductVO> getSwitchRelationBetweenProductVOList() {
        return dfpSwitchRelationBetweenProductService.selectAll().stream().filter(item -> (
                YesOrNoEnum.YES.getCode().equals(item.getEnabled()))
        ).collect(Collectors.toList());
    }

    public void generateMtsSwitchQuantityRefactor(DeliveryPlanPO oldDeliveryPlanPO, DeliveryPlanPO newDeliveryPlanPO,
                                                  List<String> noSafetyStockLeve, List<String> productBoxRelationWarningList,
                                                  List<Date> dateList, int oemTransportSupplyDays,
                                                  Map<String, Integer> oldDemandSumGroupByDay, Map<String, Integer> newDemandSumGroupByDay,
                                                  List<DeliveryPlanDetailPO> oldDeliveryPlanDetailPOListCreateTemp,
                                                  List<DeliveryPlanDetailPO> newDeliveryPlanDetailPOListCreateTemp,
                                                  List<InventoryShiftPO> inventoryShiftPOListCreateTemp,
                                                  DfpSwitchRelationBetweenProductVO switchRelation, DeliveryPlanCalcSpace deliveryPlanCalcSpace) {
        //新旧产品及切换时间

        String oldProductCode = switchRelation.getOldProductCode();
        String newProductCode = switchRelation.getNewProductCode();
        String oldKey = oldDeliveryPlanPO.getOemCode() + "&&" + oldProductCode;
        String newKey = oldDeliveryPlanPO.getOemCode() + "&&" + newProductCode;

        //新旧物料安全库存水位
        SafetyStockLevelVO oldSafeStockLevelVO = deliveryPlanCalcSpace.getSafetyStockLevelVO(oldProductCode);
        SafetyStockLevelVO newSafeStockLevelVO = deliveryPlanCalcSpace.getSafetyStockLevelVO(newProductCode);
        if (oldSafeStockLevelVO == null) {
            noSafetyStockLeve.add(oldProductCode);
            return;
        }
        if (newSafeStockLevelVO == null) {
            noSafetyStockLeve.add(newProductCode);
            return;
        }

        // 期初库存
        Integer oldOpeningInventory = deliveryPlanCalcSpace.getProductStockPointInventory(oldKey);
        Integer newOpeningInventory = deliveryPlanCalcSpace.getProductStockPointInventory(newKey);
        // 主机厂期初库存
        int oldOemOpeningInventory = deliveryPlanCalcSpace.getOemInventory(oldKey);
        int newOemOpeningInventory = deliveryPlanCalcSpace.getOemInventory(newKey);
        //创建一个临时发货计划锁定配置
        DeliveryPlanLockConfigVO deliveryPlanLockConfigVO = deliveryPlanCalcSpace.getDeliveryPlanLockConfigVO(oldDeliveryPlanPO.getOemCode());
        DeliveryPlanLockConfigVO newDeliveryPlanLockConfigVO = getConfig(deliveryPlanLockConfigVO, newSafeStockLevelVO
                , oldSafeStockLevelVO);

        if (deliveryPlanLockConfigVO == null) {
            deliveryPlanLockConfigVO = new DeliveryPlanLockConfigVO();
            deliveryPlanLockConfigVO.setStockDaysMinLimit(oldSafeStockLevelVO.getMinStockDay());
            deliveryPlanLockConfigVO.setStockDaysMaxLimit(oldSafeStockLevelVO.getStandardStockDay());
        } else {
            if ((StringUtils.equals(YesOrNoEnum.NO.getCode(), deliveryPlanLockConfigVO.getUseStockDaysMin()) && null == deliveryPlanLockConfigVO.getStockDaysMinLimit()) ||
                    (StringUtils.equals(YesOrNoEnum.NO.getCode(), deliveryPlanLockConfigVO.getUseStockDaysStandard()) && null == deliveryPlanLockConfigVO.getStockDaysMaxLimit())) {
                throw new BusinessException(String.format("主机厂【%s】最小、标准库存天数为空，请在计划锁定配置页面维护",
                        oldDeliveryPlanPO.getOemCode()));
            }

            if (YesOrNoEnum.YES.getCode().equals(deliveryPlanLockConfigVO.getUseStockDaysMin())) {
                deliveryPlanLockConfigVO.setStockDaysMinLimit(oldSafeStockLevelVO.getMinStockDay());
            } else {
                //两个else的作用是为了当不用safetyStockLevel参数时，保持newConfig和oldConfig配置一致
                newDeliveryPlanLockConfigVO.setStockDaysMinLimit(deliveryPlanLockConfigVO.getStockDaysMinLimit());
            }
            if (YesOrNoEnum.YES.getCode().equals(deliveryPlanLockConfigVO.getUseStockDaysStandard())) {
                deliveryPlanLockConfigVO.setStockDaysMaxLimit(oldSafeStockLevelVO.getStandardStockDay());
            } else {
                //两个else的作用是为了当不用safetyStockLevel参数时，保持newConfig和oldConfig配置一致
                newDeliveryPlanLockConfigVO.setStockDaysMaxLimit(deliveryPlanLockConfigVO.getStockDaysMaxLimit());
            }
        }

        if (null == deliveryPlanLockConfigVO.getStockDaysMaxLimit() || null == deliveryPlanLockConfigVO.getStockDaysMinLimit()) {
            throw new BusinessException(String.format("主机厂【%s】,产品编码【%s】最小、标准库存天数为空，请在安全库存管理页面维护",
                    oldDeliveryPlanPO.getOemCode(), oldDeliveryPlanPO.getProductCode()));
        }

        Map<String, DeliveryPlanDetailPO> oldDeliveryPlanDetailMap = oldDeliveryPlanDetailPOListCreateTemp.stream()
                .collect(Collectors.toMap(t -> DateUtils.dateToString(t.getDemandTime()),
                        Function.identity(), (t1, t2) -> t2));
        Map<String, DeliveryPlanDetailPO> newDeliveryPlanDetailMap = newDeliveryPlanDetailPOListCreateTemp.stream()
                .collect(Collectors.toMap(t -> DateUtils.dateToString(t.getDemandTime()),
                        Function.identity(), (t1, t2) -> t2));

        Map<String, Integer> demandSumGroupByDay = getDemandSummery(oldDemandSumGroupByDay, newDemandSumGroupByDay);
        
        //获取主机厂对应的放假时间（新旧物料放假逻辑暂不处理）
        List<String> oemHolidayList = new ArrayList<>();

        LinkedList<BigDecimal> endInventoryDaysList = new LinkedList<>();
        List<Date> endingInventoryDateList = initEndingInventoryDateList(dateList);
        Integer oldEndingInventory = oldOpeningInventory;
        Integer oldOemEndingInventory = oldOemOpeningInventory;
        Integer newEndingInventory = newOpeningInventory;
        Integer newOemEndingInventory = newOemOpeningInventory;
        boolean switchStart = false;
        boolean untilSwitchTime = false;
        boolean switchSign = false;
        Date switchTime = switchRelation.getSwitchTime();
        int deliveryQuantity = switchRelation.getSwitchQuantity();
        //switchQuantity只计算了发货数量，在抵扣需求的过程中，记录旧物料抵扣了多少需求数量
        int demandQuantityCount = deliveryQuantity;
        int oldBeforeQuantity = 0, newBeforeQuantity = 0;
        for (int i = 0; i < dateList.size(); i++) {
            Date date = dateList.get(i);
            // 发货计划的开始时间
            Date deliveryPlanStartDate = DateUtils.moveDay(date, -oemTransportSupplyDays);

            //从这个时间点开始累计发货数量直到等于切换数量
            if (!untilSwitchTime && !date.before(switchTime)) {
                untilSwitchTime = true;
                if (oldEndingInventory + oldOemEndingInventory >= deliveryQuantity) {
                    switchStart = true;
                    deliveryQuantity = 0;
                } else {
                    deliveryQuantity -= (oldEndingInventory + oldOemEndingInventory);
                }
            }
            InventoryShiftCalcVO inventoryShiftCalcVO = null;
            if (!switchStart) {
                inventoryShiftCalcVO = createInventoryShiftCalcVO(oldSafeStockLevelVO, oldDeliveryPlanPO, date,
                        deliveryPlanStartDate);
            } else {
                inventoryShiftCalcVO = createInventoryShiftCalcVO(newSafeStockLevelVO, newDeliveryPlanPO, date,
                        deliveryPlanStartDate);
            }
            inventoryShiftCalcVO.setProductCode(oldProductCode + "&" + newProductCode);
            //期初库存
            inventoryShiftCalcVO.setOldOpeningInventory(oldEndingInventory);
            inventoryShiftCalcVO.setNewOpeningInventory(newEndingInventory);
            inventoryShiftCalcVO.setOldOemOpeningInventory(oldOemEndingInventory);
            inventoryShiftCalcVO.setNewOemOpeningInventory(newOemEndingInventory);
            //需求数量
            Integer demandQuantity = demandSumGroupByDay.get(DateUtils.dateToString(date)) == null ?
                    0 : demandSumGroupByDay.get(DateUtils.dateToString(date));
            Integer oldDemandQuantity = oldDemandSumGroupByDay.get(DateUtils.dateToString(date)) == null ?
                    0 : oldDemandSumGroupByDay.get(DateUtils.dateToString(date));
            Integer newDemandQuantity = newDemandSumGroupByDay.get(DateUtils.dateToString(date)) == null ?
                    0 : newDemandSumGroupByDay.get(DateUtils.dateToString(date));
            inventoryShiftCalcVO.setCustomerDemand(demandQuantity);
            inventoryShiftCalcVO.setOldDemand(oldDemandQuantity);
            inventoryShiftCalcVO.setNewDemand(newDemandQuantity);

            //在途数据
            Integer oldReceiveQuantity = deliveryPlanCalcSpace.getReceiveQuantity(date, oemTransportSupplyDays,
                    oldKey);
            Integer newReceiveQuantity = deliveryPlanCalcSpace.getReceiveQuantity(date, oemTransportSupplyDays,
                    newKey);
            inventoryShiftCalcVO.setReceive(oldReceiveQuantity + newReceiveQuantity);
            inventoryShiftCalcVO.setOldReceive(oldReceiveQuantity);
            inventoryShiftCalcVO.setNewReceive(newReceiveQuantity);
            //锁定计划量
            DeliveryPlanDetailPO oldDeliveryPlanDetailPO =
                    oldDeliveryPlanDetailMap.get(DateUtils.dateToString(deliveryPlanStartDate));
            DeliveryPlanDetailPO newDeliveryPlanDetailPO =
                    newDeliveryPlanDetailMap.get(DateUtils.dateToString(deliveryPlanStartDate));
            Integer inRoad = oldDeliveryPlanDetailPO == null ? 0 : oldDeliveryPlanDetailPO.getDemandQuantity();
            Integer inRoadNew = newDeliveryPlanDetailPO == null ? 0 : newDeliveryPlanDetailPO.getDemandQuantity();
            inventoryShiftCalcVO.setInRoad(inRoad);
            inventoryShiftCalcVO.setInRoadNew(inRoadNew);

            if (oldDeliveryPlanDetailPO == null) {
                oldDeliveryPlanDetailPO = new DeliveryPlanDetailPO();
                if (!deliveryPlanStartDate.before(dateList.get(0))) {
                    oldDeliveryPlanDetailPOListCreateTemp.add(oldDeliveryPlanDetailPO);
                }
                oldDeliveryPlanDetailPO.setDeliveryPlanDataId(oldDeliveryPlanPO.getId());
                oldDeliveryPlanDetailPO.setDemandTime(deliveryPlanStartDate);
            }
            if (newDeliveryPlanDetailPO == null) {
                newDeliveryPlanDetailPO = new DeliveryPlanDetailPO();
                if (!deliveryPlanStartDate.before(dateList.get(0))) {
                    newDeliveryPlanDetailPOListCreateTemp.add(newDeliveryPlanDetailPO);
                }
                newDeliveryPlanDetailPO.setDeliveryPlanDataId(newDeliveryPlanPO.getId());
                newDeliveryPlanDetailPO.setDemandTime(deliveryPlanStartDate);
            }
            //切换时旧物料满足的需求数量
            int oldFulfillDemand = 0;
            if (!switchStart) {
                BigDecimal endingInventoryDays = inventoryShiftCalcVO.calcOldEndingInventoryDay(endingInventoryDateList,
                        demandSumGroupByDay, i);
                if (endInventoryDaysList.isEmpty()) {
                    endInventoryDaysList = getExpectEndingInventoryDays(deliveryPlanLockConfigVO,
                            deliveryPlanCalcSpace.getDeliveryPlanReplenishConfigVO(oldDeliveryPlanPO.getOemCode()),
                            endingInventoryDays);
                    // if (deliveryPlanStartDate.before(dateList.get(0))) {
                    //     endInventoryDaysList.clear();
                    //     endInventoryDaysList.add(BigDecimal.ZERO);
                    // }
                }
                BigDecimal expectEndingInventoryDays = endInventoryDaysList.removeFirst();
                // 根据期望库存天数反算期望的期末库存
                int expectEndingInventory = getExpectEndingInventory(endingInventoryDateList, demandSumGroupByDay, i,
                        expectEndingInventoryDays);
                int replenishPlanQty = inventoryShiftCalcVO.getOldReplenishPlanQty(expectEndingInventory);
                if (untilSwitchTime && demandQuantityCount > 0) {
                    //如果此时已经开始切换，但是之前备库的旧物料还没发满切换数量，继续用这个数量满足需求
                    if (demandQuantityCount > inventoryShiftCalcVO.getCustomerDemand()) {
                        oldFulfillDemand = inventoryShiftCalcVO.getCustomerDemand();
                    } else {
                        oldFulfillDemand = demandQuantityCount;
                    }
                }
                replenishPlanQty = Math.max(replenishPlanQty, 0);

                //处于切换时间节点，特殊处理数据后直接continue下个循环
                if (untilSwitchTime && deliveryQuantity <= replenishPlanQty) {
                    switchStart = true;
                    if (!switchSign) {
                        switchSign = true;
                        inventoryShiftCalcVO.setSwitchSign(YesOrNoEnum.YES.getCode());
                    }
                    Integer replenishPlanQty3 = 0, replenishPlanQty4 = 0;

                    oldDeliveryPlanDetailPO.setDemandQuantity(deliveryQuantity);

                    replenishPlanQty3 = getNormalReplenishPlanQtyWithSwitchQuantity(dateList, i, demandSumGroupByDay,
                            oldDeliveryPlanDetailPO,
                            oldDeliveryPlanPO, deliveryPlanCalcSpace.getMaterialRiskLevelVOMapOfJoinKey(),
                            deliveryPlanCalcSpace.getProductBoxRelationVOMapOfProductCode(), deliveryPlanCalcSpace.getBoxInfoVOMapOfId(),
                            oldSafeStockLevelVO,
                            productBoxRelationWarningList, inventoryShiftCalcVO.getOldUnfulfilledEnding(),
                            deliveryPlanCalcSpace.getNewProductStockPointVO(oldProductCode), deliveryQuantity, oemHolidayList);
                    oldDeliveryPlanDetailPO.setDemandQuantity(replenishPlanQty3);

                    newDeliveryPlanDetailPO.setDemandQuantity(replenishPlanQty - replenishPlanQty3);
                    replenishPlanQty4 = getNormalReplenishPlanQty(dateList, i, demandSumGroupByDay,
                            newDeliveryPlanDetailPO,
                            newDeliveryPlanPO, deliveryPlanCalcSpace.getMaterialRiskLevelVOMapOfJoinKey(),
                            deliveryPlanCalcSpace.getProductBoxRelationVOMapOfProductCode(), deliveryPlanCalcSpace.getBoxInfoVOMapOfId(),
                            newSafeStockLevelVO,
                            productBoxRelationWarningList,
                            inventoryShiftCalcVO.getOldAndNewUnfulfilledEnding() + replenishPlanQty3,
                            deliveryPlanCalcSpace.getNewProductStockPointVO(oldProductCode), oemHolidayList);
                    newDeliveryPlanDetailPO.setDemandQuantity(replenishPlanQty4);
                    inventoryShiftCalcVO.setOldDeliveryPlan(replenishPlanQty3);
                    inventoryShiftCalcVO.setNewDeliveryPlan(replenishPlanQty4);

                } else {
                    // 发货计划量
                    oldDeliveryPlanDetailPO.setDemandQuantity(replenishPlanQty);
                    int replenishPlanQty2 = getNormalReplenishPlanQtyWithSwitchQuantity(dateList, i, demandSumGroupByDay,
                            oldDeliveryPlanDetailPO,
                            oldDeliveryPlanPO, deliveryPlanCalcSpace.getMaterialRiskLevelVOMapOfJoinKey(),
                            deliveryPlanCalcSpace.getProductBoxRelationVOMapOfProductCode(),
                            deliveryPlanCalcSpace.getBoxInfoVOMapOfId(), oldSafeStockLevelVO,
                            productBoxRelationWarningList, inventoryShiftCalcVO.getOldUnfulfilledEnding(),
                            deliveryPlanCalcSpace.getNewProductStockPointVO(oldProductCode), deliveryQuantity, oemHolidayList);
                    oldDeliveryPlanDetailPO.setDemandQuantity(replenishPlanQty2);
                    inventoryShiftCalcVO.setOldDeliveryPlan(replenishPlanQty2);
                    inventoryShiftCalcVO.setNewDeliveryPlan(0);

                }
                if (deliveryPlanStartDate.before(dateList.get(0))) {
                    oldBeforeQuantity = oldBeforeQuantity + inventoryShiftCalcVO.getOldDeliveryPlan();
                    newBeforeQuantity = newBeforeQuantity + inventoryShiftCalcVO.getNewDeliveryPlan();
                }
                if (deliveryPlanStartDate.equals(dateList.get(0))) {
                    if (oldBeforeQuantity > 0) {
                        oldDeliveryPlanDetailPO.setUrgentSign(YesOrNoEnum.YES.getCode());
                    }
                    if (newBeforeQuantity > 0) {
                        newDeliveryPlanDetailPO.setUrgentSign(YesOrNoEnum.YES.getCode());
                    }
                    oldDeliveryPlanDetailPO.setDemandQuantity(oldBeforeQuantity + inventoryShiftCalcVO.getOldDeliveryPlan());
                    newDeliveryPlanDetailPO.setDemandQuantity(newBeforeQuantity + inventoryShiftCalcVO.getNewDeliveryPlan());
                }

                // 计算期末库存和期末库存天数
                // 计算期末库存：期初库存+在途数据-客户需求
                int totalEndingInventory = 0;
                if (untilSwitchTime) {
                    totalEndingInventory = inventoryShiftCalcVO.calcOldEndingInventoryWithQuantity(oldFulfillDemand);
                } else {
                    totalEndingInventory = inventoryShiftCalcVO.calcOldEndingInventoryWithQuantity(inventoryShiftCalcVO.getCustomerDemand());
                }
                // if (openingInventoryTemp + oemOpeningInventoryTemp+finalOldEndingInventory >= customerDemand && demandQuantityCount > 0){
                //     //计算期末库存天数需要考虑
                //     totalEndingInventory = totalEndingInventory + oldFulfillDemand;
                // }
                // 当天期末库存天数
                endingInventoryDays = getEndingInventoryDays(endingInventoryDateList, demandSumGroupByDay, totalEndingInventory, i, oemHolidayList);
                inventoryShiftCalcVO.setOldEndingInventoryDays(endingInventoryDays);
                int newTotalEndingInventory = 0;
                if (untilSwitchTime) {
                    newTotalEndingInventory = inventoryShiftCalcVO.calcOldAndNewEndingInventory(oldFulfillDemand);
                } else {
                    newTotalEndingInventory =
                            inventoryShiftCalcVO.calcOldAndNewEndingInventory(inventoryShiftCalcVO.getCustomerDemand());
                }

                // 当天期末库存天数
                endingInventoryDays = getEndingInventoryDays(endingInventoryDateList, demandSumGroupByDay, newTotalEndingInventory, i, oemHolidayList);
                inventoryShiftCalcVO.setNewEndingInventoryDays(endingInventoryDays);
                //安全库存相关采用旧物料配置
                Integer minInventoryLevel = getInventoryLevel(i, oldSafeStockLevelVO.getMinStockDay(), dateList,
                        demandSumGroupByDay);
                inventoryShiftCalcVO.setEndingInventoryMinSafeDiff(inventoryShiftCalcVO.getOldEndingInventory() - minInventoryLevel);
                inventoryShiftCalcVO.setStandardSafetyInventoryLevel(getInventoryLevel(i,
                        oldSafeStockLevelVO.getStandardStockDay(), dateList, demandSumGroupByDay));
                //显示字段用旧物料的数量
                inventoryShiftCalcVO.setOldAsDisplay();
                //当前旧物料已经抵扣需求的数量
                demandQuantityCount = demandQuantityCount - oldFulfillDemand;
                if (demandQuantityCount <= 0) {
                    //发货计划不能超过切换数量，期初库存满足需求也不能超过切换数量
                    switchStart = true;
                    if (!switchSign) {
                        switchSign = true;
                        inventoryShiftCalcVO.setSwitchSign(YesOrNoEnum.YES.getCode());
                    }
                }
            } else {
                if (untilSwitchTime && demandQuantityCount > 0) {
                    //如果此时已经开始切换，但是之前备库的旧物料还没发满切换数量，继续用这个数量满足需求
                    if (demandQuantityCount > inventoryShiftCalcVO.getCustomerDemand()) {
                        oldFulfillDemand = inventoryShiftCalcVO.getCustomerDemand();
                    } else {
                        oldFulfillDemand = demandQuantityCount;
                    }

                }
                BigDecimal endingInventoryDays =
                        inventoryShiftCalcVO.calcNewEndingInventoryDayWithQuantity(endingInventoryDateList,
                                demandSumGroupByDay, i, oldFulfillDemand);
                if (endInventoryDaysList.isEmpty()) {
                    endInventoryDaysList = getExpectEndingInventoryDays(newDeliveryPlanLockConfigVO,
                            deliveryPlanCalcSpace.getDeliveryPlanReplenishConfigVO(newDeliveryPlanPO.getOemCode()),
                            endingInventoryDays);
                    // if (deliveryPlanStartDate.before(dateList.get(0))) {
                    //     endInventoryDaysList.clear();
                    //     endInventoryDaysList.add(BigDecimal.ZERO);
                    // }
                }
                BigDecimal expectEndingInventoryDays = endInventoryDaysList.removeFirst();
                // 根据期望库存天数反算期望的期末库存
                int expectEndingInventory = getExpectEndingInventory(endingInventoryDateList, demandSumGroupByDay, i,
                        expectEndingInventoryDays);
                int replenishPlanQty = inventoryShiftCalcVO.getNewReplenishPlanQty(expectEndingInventory);

                //如果此时满足需求的数量还不到切换数量，用旧物料库存满足这部分需求，剩下的是新物料发货计划量
                replenishPlanQty = replenishPlanQty - oldFulfillDemand;

                replenishPlanQty = Math.max(replenishPlanQty, 0);
                newDeliveryPlanDetailPO.setDemandQuantity(replenishPlanQty);
                //切换后计算
                int replenishPlanQty2 = getNormalReplenishPlanQty(dateList, i, demandSumGroupByDay, newDeliveryPlanDetailPO,
                        newDeliveryPlanPO, deliveryPlanCalcSpace.getMaterialRiskLevelVOMapOfJoinKey(),
                        deliveryPlanCalcSpace.getProductBoxRelationVOMapOfProductCode(), deliveryPlanCalcSpace.getBoxInfoVOMapOfId(), newSafeStockLevelVO,
                        productBoxRelationWarningList, inventoryShiftCalcVO.getNewUnfulfilledEnding(),
                        deliveryPlanCalcSpace.getNewProductStockPointVO(newDeliveryPlanPO.getProductCode()), oemHolidayList);
                newDeliveryPlanDetailPO.setDemandQuantity(replenishPlanQty2);
                inventoryShiftCalcVO.setNewDeliveryPlan(replenishPlanQty2);

                inventoryShiftCalcVO.setOldDeliveryPlan(0);
                inventoryShiftCalcVO.setOldEndingWithQuantity(oldFulfillDemand);
                inventoryShiftCalcVO.setOldEndingInventoryDays(BigDecimal.ZERO);

                if (deliveryPlanStartDate.before(dateList.get(0))) {
                    oldBeforeQuantity = oldBeforeQuantity + inventoryShiftCalcVO.getOldDeliveryPlan();
                    newBeforeQuantity = newBeforeQuantity + inventoryShiftCalcVO.getNewDeliveryPlan();
                }
                if (deliveryPlanStartDate.equals(dateList.get(0))) {
                    if (oldBeforeQuantity > 0) {
                        oldDeliveryPlanDetailPO.setUrgentSign(YesOrNoEnum.YES.getCode());
                    }
                    if (newBeforeQuantity > 0) {
                        newDeliveryPlanDetailPO.setUrgentSign(YesOrNoEnum.YES.getCode());
                    }
                    oldDeliveryPlanDetailPO.setDemandQuantity(oldBeforeQuantity + inventoryShiftCalcVO.getOldDeliveryPlan());
                    newDeliveryPlanDetailPO.setDemandQuantity(newBeforeQuantity + inventoryShiftCalcVO.getNewDeliveryPlan());
                }

                int newTotalEndingInventory = inventoryShiftCalcVO.calcNewEndingInventory();
                // 当天期末库存天数
                endingInventoryDays = getEndingInventoryDays(endingInventoryDateList, demandSumGroupByDay,
                        newTotalEndingInventory + oldFulfillDemand, i, oemHolidayList);
                inventoryShiftCalcVO.setNewEndingInventoryDays(endingInventoryDays);
                inventoryShiftCalcVO.setNewAsDisplay();

                //安全库存相关采用旧物料配置
                Integer minInventoryLevel = getInventoryLevel(i, newSafeStockLevelVO.getMinStockDay(), dateList,
                        demandSumGroupByDay);
                inventoryShiftCalcVO.setEndingInventoryMinSafeDiff(inventoryShiftCalcVO.getNewEndingInventory() - minInventoryLevel);
                inventoryShiftCalcVO.setStandardSafetyInventoryLevel(getInventoryLevel(i,
                        newSafeStockLevelVO.getStandardStockDay(), dateList, demandSumGroupByDay));

                if (demandQuantityCount > 0) {
                    demandQuantityCount = Math.max(demandQuantityCount - oldFulfillDemand, 0);
                }
            }
            InventoryShiftPO inventoryShiftPO = inventoryShiftCalcVO.createInventoryShiftPO();
            inventoryShiftPOListCreateTemp.add(inventoryShiftPO);
            oldEndingInventory = inventoryShiftCalcVO.getOldEndingInventory();
            oldOemEndingInventory = inventoryShiftCalcVO.getOldOemEndingInventory();
            newEndingInventory = inventoryShiftCalcVO.getNewEndingInventory();
            newOemEndingInventory = inventoryShiftCalcVO.getNewOemEndingInventory();
        }
    }

    private InventoryShiftCalcVO createInventoryShiftCalcVO(SafetyStockLevelVO safetyStockLevelVO,
                                                            DeliveryPlanPO deliveryPlanPO, Date date, Date deliveryPlanStartDate) {
        InventoryShiftCalcVO inventoryShiftCalcVO = new InventoryShiftCalcVO();
        inventoryShiftCalcVO.setVersionId(deliveryPlanPO.getVersionId());
        inventoryShiftCalcVO.setOemCode(deliveryPlanPO.getOemCode());
        inventoryShiftCalcVO.setProductCode(deliveryPlanPO.getProductCode());
        inventoryShiftCalcVO.setPlannedDate(date);
        inventoryShiftCalcVO.setDeliveryDate(deliveryPlanStartDate);
        inventoryShiftCalcVO.setBusinessType(deliveryPlanPO.getSupplyType());
        inventoryShiftCalcVO.setMarketType(deliveryPlanPO.getTradeType());

        inventoryShiftCalcVO.setMinimumSafetyInventoryDays(safetyStockLevelVO.getMinStockDay());
        inventoryShiftCalcVO.setStandardSafetyInventoryDays(safetyStockLevelVO.getStandardStockDay());
        return inventoryShiftCalcVO;
    }

    public void generateMtsSwitchTimeRefactor(DeliveryPlanPO oldDeliveryPlanPO,
                                              DeliveryPlanPO newDeliveryPlanPO,
                                              List<String> noSafetyStockLeve, List<String> productBoxRelationWarningList,
                                              List<Date> dateList, int oemTransportSupplyDays,
                                              Map<String, Integer> oldDemandSumGroupByDay,
                                              Map<String, Integer> newDemandSumGroupByDay,
                                              List<DeliveryPlanDetailPO> oldDeliveryPlanDetailPOListCreateTemp,
                                              List<DeliveryPlanDetailPO> newDeliveryPlanDetailPOListCreateTemp,
                                              List<InventoryShiftPO> inventoryShiftPOListCreateTemp,
                                              DfpSwitchRelationBetweenProductVO switchRelation, DeliveryPlanCalcSpace deliveryPlanCalcSpace
    ) {
        //新旧产品及切换时间

        String oldProductCode = switchRelation.getOldProductCode();
        String newProductCode = switchRelation.getNewProductCode();
        String oldKey = oldDeliveryPlanPO.getOemCode() + "&&" + oldProductCode;
        String newKey = oldDeliveryPlanPO.getOemCode() + "&&" + newProductCode;

        //新旧物料安全库存水位
        SafetyStockLevelVO oldSafeStockLevelVO = deliveryPlanCalcSpace.getSafetyStockLevelVO(oldProductCode);
        SafetyStockLevelVO newSafeStockLevelVO = deliveryPlanCalcSpace.getSafetyStockLevelVO(newProductCode);
        if (oldSafeStockLevelVO == null) {
            noSafetyStockLeve.add(oldProductCode);
            return;
        }
        if (newSafeStockLevelVO == null) {
            noSafetyStockLeve.add(newProductCode);
            return;
        }

        // 期初库存
        Integer oldOpeningInventory = deliveryPlanCalcSpace.getProductStockPointInventory(oldKey);
        Integer newOpeningInventory = deliveryPlanCalcSpace.getProductStockPointInventory(newKey);
        // 主机厂期初库存
        int oldOemOpeningInventory = deliveryPlanCalcSpace.getOemInventory(oldKey);
        int newOemOpeningInventory = deliveryPlanCalcSpace.getOemInventory(newKey);
        //创建一个临时发货计划锁定配置
        DeliveryPlanLockConfigVO deliveryPlanLockConfigVO = deliveryPlanCalcSpace.getDeliveryPlanLockConfigVO(oldDeliveryPlanPO.getOemCode());
        DeliveryPlanLockConfigVO newDeliveryPlanLockConfigVO = getConfig(deliveryPlanLockConfigVO, newSafeStockLevelVO
                , oldSafeStockLevelVO);

        if (deliveryPlanLockConfigVO == null) {
            deliveryPlanLockConfigVO = new DeliveryPlanLockConfigVO();
            deliveryPlanLockConfigVO.setStockDaysMinLimit(oldSafeStockLevelVO.getMinStockDay());
            deliveryPlanLockConfigVO.setStockDaysMaxLimit(oldSafeStockLevelVO.getStandardStockDay());
        } else {
            if ((StringUtils.equals(YesOrNoEnum.NO.getCode(), deliveryPlanLockConfigVO.getUseStockDaysMin()) && null == deliveryPlanLockConfigVO.getStockDaysMinLimit()) ||
                    (StringUtils.equals(YesOrNoEnum.NO.getCode(), deliveryPlanLockConfigVO.getUseStockDaysStandard()) && null == deliveryPlanLockConfigVO.getStockDaysMaxLimit())) {
                throw new BusinessException(String.format("主机厂【%s】最小、标准库存天数为空，请在计划锁定配置页面维护",
                        oldDeliveryPlanPO.getOemCode()));
            }

            if (YesOrNoEnum.YES.getCode().equals(deliveryPlanLockConfigVO.getUseStockDaysMin())) {
                deliveryPlanLockConfigVO.setStockDaysMinLimit(oldSafeStockLevelVO.getMinStockDay());
            } else {
                //两个else的作用是为了当不用safetyStockLevel参数时，保持newConfig和oldConfig配置一致
                newDeliveryPlanLockConfigVO.setStockDaysMinLimit(deliveryPlanLockConfigVO.getStockDaysMinLimit());
            }
            if (YesOrNoEnum.YES.getCode().equals(deliveryPlanLockConfigVO.getUseStockDaysStandard())) {
                deliveryPlanLockConfigVO.setStockDaysMaxLimit(oldSafeStockLevelVO.getStandardStockDay());
            } else {
                //两个else的作用是为了当不用safetyStockLevel参数时，保持newConfig和oldConfig配置一致
                newDeliveryPlanLockConfigVO.setStockDaysMaxLimit(deliveryPlanLockConfigVO.getStockDaysMaxLimit());
            }
        }

        if (null == deliveryPlanLockConfigVO.getStockDaysMaxLimit() || null == deliveryPlanLockConfigVO.getStockDaysMinLimit()) {
            throw new BusinessException(String.format("主机厂【%s】,产品编码【%s】最小、标准库存天数为空，请在安全库存管理页面维护",
                    oldDeliveryPlanPO.getOemCode(), oldDeliveryPlanPO.getProductCode()));
        }

        Map<String, DeliveryPlanDetailPO> oldDeliveryPlanDetailMap = oldDeliveryPlanDetailPOListCreateTemp.stream()
                .collect(Collectors.toMap(t -> DateUtils.dateToString(t.getDemandTime()),
                        Function.identity(), (t1, t2) -> t2));
        Map<String, DeliveryPlanDetailPO> newDeliveryPlanDetailMap = newDeliveryPlanDetailPOListCreateTemp.stream()
                .collect(Collectors.toMap(t -> DateUtils.dateToString(t.getDemandTime()),
                        Function.identity(), (t1, t2) -> t2));

        Map<String, Integer> demandSumGroupByDay = getDemandSummery(oldDemandSumGroupByDay, newDemandSumGroupByDay);
        
        //获取主机厂对应的放假时间（新旧物料放假逻辑暂不处理）
        List<String> oemHolidayList = new ArrayList<>();

        LinkedList<BigDecimal> endInventoryDaysList = new LinkedList<>();
        List<Date> endingInventoryDateList = initEndingInventoryDateList(dateList);
        Integer oldEndingInventory = oldOpeningInventory;
        Integer oldOemEndingInventory = oldOemOpeningInventory;
        Integer newEndingInventory = newOpeningInventory;
        Integer newOemEndingInventory = newOemOpeningInventory;
        boolean switchStart = false;
        boolean switchSign = false;
        Date switchTime = switchRelation.getSwitchTime();
        int oldBeforeQuantity = 0, newBeforeQuantity = 0;
        for (int i = 0; i < dateList.size(); i++) {
            Date date = dateList.get(i);
            // 发货计划的开始时间
            Date deliveryPlanStartDate = DateUtils.moveDay(date, -oemTransportSupplyDays);
            if (!switchStart && !switchTime.after(date)) {
                switchStart = true;
            }
            InventoryShiftCalcVO inventoryShiftCalcVO = null;
            if (!switchStart) {
                inventoryShiftCalcVO = createInventoryShiftCalcVO(oldSafeStockLevelVO, oldDeliveryPlanPO, date,
                        deliveryPlanStartDate);
            } else {
                inventoryShiftCalcVO = createInventoryShiftCalcVO(newSafeStockLevelVO, newDeliveryPlanPO, date,
                        deliveryPlanStartDate);
            }
            inventoryShiftCalcVO.setProductCode(oldProductCode + "&" + newProductCode);
            //期初库存
            inventoryShiftCalcVO.setOldOpeningInventory(oldEndingInventory);
            inventoryShiftCalcVO.setNewOpeningInventory(newEndingInventory);
            inventoryShiftCalcVO.setOldOemOpeningInventory(oldOemEndingInventory);
            inventoryShiftCalcVO.setNewOemOpeningInventory(newOemEndingInventory);
            //需求数量
            Integer demandQuantity = demandSumGroupByDay.get(DateUtils.dateToString(date)) == null ?
                    0 : demandSumGroupByDay.get(DateUtils.dateToString(date));
            Integer oldDemandQuantity = oldDemandSumGroupByDay.get(DateUtils.dateToString(date)) == null ?
                    0 : oldDemandSumGroupByDay.get(DateUtils.dateToString(date));
            Integer newDemandQuantity = newDemandSumGroupByDay.get(DateUtils.dateToString(date)) == null ?
                    0 : newDemandSumGroupByDay.get(DateUtils.dateToString(date));
            inventoryShiftCalcVO.setCustomerDemand(demandQuantity);
            inventoryShiftCalcVO.setOldDemand(oldDemandQuantity);
            inventoryShiftCalcVO.setNewDemand(newDemandQuantity);

            //在途数据
            Integer oldReceiveQuantity = deliveryPlanCalcSpace.getReceiveQuantity(date, oemTransportSupplyDays,
                    oldKey);
            Integer newReceiveQuantity = deliveryPlanCalcSpace.getReceiveQuantity(date, oemTransportSupplyDays,
                    newKey);
            inventoryShiftCalcVO.setReceive(oldReceiveQuantity + newReceiveQuantity);
            inventoryShiftCalcVO.setOldReceive(oldReceiveQuantity);
            inventoryShiftCalcVO.setNewReceive(newReceiveQuantity);
            //锁定计划量
            DeliveryPlanDetailPO oldDeliveryPlanDetailPO =
                    oldDeliveryPlanDetailMap.get(DateUtils.dateToString(deliveryPlanStartDate));
            DeliveryPlanDetailPO newDeliveryPlanDetailPO =
                    newDeliveryPlanDetailMap.get(DateUtils.dateToString(deliveryPlanStartDate));
            Integer inRoad = oldDeliveryPlanDetailPO == null ? 0 : oldDeliveryPlanDetailPO.getDemandQuantity();
            Integer inRoadNew = newDeliveryPlanDetailPO == null ? 0 : newDeliveryPlanDetailPO.getDemandQuantity();
            inventoryShiftCalcVO.setInRoad(inRoad);
            inventoryShiftCalcVO.setInRoadNew(inRoadNew);
            if (!switchStart) {
                BigDecimal endingInventoryDays = inventoryShiftCalcVO.calcOldEndingInventoryDay(endingInventoryDateList,
                        demandSumGroupByDay, i);
                if (endInventoryDaysList.isEmpty()) {
                    endInventoryDaysList = getExpectEndingInventoryDays(deliveryPlanLockConfigVO,
                            deliveryPlanCalcSpace.getDeliveryPlanReplenishConfigVO(oldDeliveryPlanPO.getOemCode()),
                            endingInventoryDays);
                }
                BigDecimal expectEndingInventoryDays = endInventoryDaysList.removeFirst();
                BigDecimal newExpectEndingInventoryDays = BigDecimal.ZERO;
                //用来做向上取整计算
                BigDecimal moveDays = expectEndingInventoryDays;
                //如果期望库存天数在切换时间之后，切换时间后旧物料不在发货
                if (!DateUtils.moveDay(date, moveDays.setScale(0, RoundingMode.CEILING).intValue()).before(switchTime)) {
                    newExpectEndingInventoryDays = expectEndingInventoryDays;
                    expectEndingInventoryDays = getDateDiff(date, switchTime).subtract(BigDecimal.ONE);
                }
                // 根据期望库存天数反算期望的期末库存
                int expectEndingInventory = getExpectEndingInventory(endingInventoryDateList, demandSumGroupByDay, i,
                        expectEndingInventoryDays);

                //当库存天数大于切换时间时，切换时间前备库用旧物料，切换时间后用新物料
                int replenishPlanQty = 0;
                int newReplenishPlanQty = 0;
                if (newExpectEndingInventoryDays.compareTo(BigDecimal.ZERO) > 0) {
                    int newExpectEndingInventory = getExpectEndingInventory(endingInventoryDateList, demandSumGroupByDay
                            , i, newExpectEndingInventoryDays);

                    // 切换前备库新物料需要考虑新物料库存
                    replenishPlanQty = inventoryShiftCalcVO.getOldReplenishPlanQty(expectEndingInventory);
                    //expectEndingInventory + customerDemand - (openingInventoryTemp +
                    // oemOpeningInventoryTemp + newOpeningInventory + newOemOpeningInventory) - oldReceiveQuantity - newReceiveQuantity;
                    newReplenishPlanQty = inventoryShiftCalcVO.getOldAndNewReplenishPlanQty(newExpectEndingInventory);
                } else {
                    replenishPlanQty = inventoryShiftCalcVO.getOldReplenishPlanQty(expectEndingInventory);
                    //expectEndingInventory + customerDemand - (openingInventoryTemp +
                    // oemOpeningInventoryTemp) - oldReceiveQuantity - newReceiveQuantity;
                }
                replenishPlanQty = Math.max(replenishPlanQty, 0);
                newReplenishPlanQty = Math.max(newReplenishPlanQty, 0);

                if (oldDeliveryPlanDetailPO == null) {
                    oldDeliveryPlanDetailPO = new DeliveryPlanDetailPO();
                    if (!deliveryPlanStartDate.before(dateList.get(0))) {
                        oldDeliveryPlanDetailPOListCreateTemp.add(oldDeliveryPlanDetailPO);
                    }
                    oldDeliveryPlanDetailPO.setDeliveryPlanDataId(oldDeliveryPlanPO.getId());
                    oldDeliveryPlanDetailPO.setDemandTime(deliveryPlanStartDate);
                }
                oldDeliveryPlanDetailPO.setDemandQuantity(replenishPlanQty);
                if (newDeliveryPlanDetailPO == null) {
                    newDeliveryPlanDetailPO = new DeliveryPlanDetailPO();
                    if (!deliveryPlanStartDate.before(dateList.get(0))) {
                        newDeliveryPlanDetailPOListCreateTemp.add(newDeliveryPlanDetailPO);
                    }
                    newDeliveryPlanDetailPO.setDeliveryPlanDataId(newDeliveryPlanPO.getId());
                    newDeliveryPlanDetailPO.setDemandTime(deliveryPlanStartDate);
                }
                newDeliveryPlanDetailPO.setDemandQuantity(newReplenishPlanQty);
                //整箱发货量
                int replenishPlanQty2 = 0;
                int replenishPlanQty3 = 0;

                if (newReplenishPlanQty == 0) {
                    //发货时间不能超过切换时间
                    BigDecimal diffDay = getDateDiff(date, switchTime).subtract(BigDecimal.ONE);
                    //旧物料发货
                    replenishPlanQty2 = getNormalReplenishPlanQtyWithTime(dateList, i, demandSumGroupByDay,
                            oldDeliveryPlanDetailPO,
                            oldDeliveryPlanPO, deliveryPlanCalcSpace.getMaterialRiskLevelVOMapOfJoinKey(),
                            deliveryPlanCalcSpace.getProductBoxRelationVOMapOfProductCode(),
                            deliveryPlanCalcSpace.getBoxInfoVOMapOfId(), oldSafeStockLevelVO,
                            productBoxRelationWarningList, inventoryShiftCalcVO.getOldUnfulfilledEnding(),
                            deliveryPlanCalcSpace.getNewProductStockPointVO(oldDeliveryPlanPO.getProductCode()), diffDay, oemHolidayList);
                    oldDeliveryPlanDetailPO.setDemandQuantity(replenishPlanQty2);
                    inventoryShiftCalcVO.setOldDeliveryPlan(replenishPlanQty2);
                    inventoryShiftCalcVO.setNewDeliveryPlan(0);
                } else {
                    //超出期末库存天数，旧物料发货，新物料备库
                    replenishPlanQty2 = replenishPlanQty;
                    replenishPlanQty3 = getNormalReplenishPlanQty(dateList, i, demandSumGroupByDay, newDeliveryPlanDetailPO,
                            newDeliveryPlanPO, deliveryPlanCalcSpace.getMaterialRiskLevelVOMapOfJoinKey(),
                            deliveryPlanCalcSpace.getProductBoxRelationVOMapOfProductCode(),
                            deliveryPlanCalcSpace.getBoxInfoVOMapOfId(), newSafeStockLevelVO,
                            productBoxRelationWarningList, inventoryShiftCalcVO.getOldAndNewUnfulfilledEnding(),
                            deliveryPlanCalcSpace.getNewProductStockPointVO(newDeliveryPlanPO.getProductCode()), oemHolidayList);
                    oldDeliveryPlanDetailPO.setDemandQuantity(replenishPlanQty2);
                    newDeliveryPlanDetailPO.setDemandQuantity(replenishPlanQty3);
                    inventoryShiftCalcVO.setOldDeliveryPlan(replenishPlanQty2);
                    inventoryShiftCalcVO.setNewDeliveryPlan(replenishPlanQty3);
                }
                if (deliveryPlanStartDate.before(dateList.get(0))) {
                    oldBeforeQuantity = oldBeforeQuantity + replenishPlanQty2;
                    newBeforeQuantity = newBeforeQuantity + replenishPlanQty3;
                }
                if (deliveryPlanStartDate.equals(dateList.get(0))) {
                    if (oldBeforeQuantity > 0) {
                        oldDeliveryPlanDetailPO.setUrgentSign(YesOrNoEnum.YES.getCode());
                    }
                    if (newBeforeQuantity > 0) {
                        newDeliveryPlanDetailPO.setUrgentSign(YesOrNoEnum.YES.getCode());
                    }
                    oldDeliveryPlanDetailPO.setDemandQuantity(oldBeforeQuantity + replenishPlanQty2);
                    newDeliveryPlanDetailPO.setDemandQuantity(newBeforeQuantity + replenishPlanQty3);
                }
                // 计算期末库存和期末库存天数
                // 计算期末库存：期初库存+在途数据-客户需求
                int totalEndingInventory = inventoryShiftCalcVO.calcOldEndingInventory();
                // if (openingInventoryTemp + oemOpeningInventoryTemp+finalOldEndingInventory >= customerDemand && demandQuantityCount > 0){
                //     //计算期末库存天数需要考虑
                //     totalEndingInventory = totalEndingInventory + oldFulfillDemand;
                // }
                // 当天期末库存天数
                endingInventoryDays = getEndingInventoryDays(endingInventoryDateList, demandSumGroupByDay, totalEndingInventory, i, oemHolidayList);
                inventoryShiftCalcVO.setOldEndingInventoryDays(endingInventoryDays);
                int newTotalEndingInventory =
                        inventoryShiftCalcVO.calcOldAndNewEndingInventory(inventoryShiftCalcVO.getCustomerDemand());
                // 当天期末库存天数
                endingInventoryDays = getEndingInventoryDays(endingInventoryDateList, demandSumGroupByDay, newTotalEndingInventory, i, oemHolidayList);
                inventoryShiftCalcVO.setNewEndingInventoryDays(endingInventoryDays);
                //安全库存相关采用旧物料配置
                Integer minInventoryLevel = getInventoryLevel(i, oldSafeStockLevelVO.getMinStockDay(), dateList,
                        demandSumGroupByDay);
                inventoryShiftCalcVO.setEndingInventoryMinSafeDiff(inventoryShiftCalcVO.getOldEndingInventory() - minInventoryLevel);
                inventoryShiftCalcVO.setStandardSafetyInventoryLevel(getInventoryLevel(i,
                        oldSafeStockLevelVO.getStandardStockDay(), dateList, demandSumGroupByDay));
                //显示字段用旧物料的数量
                inventoryShiftCalcVO.setOldAsDisplay();
            } else {
                if (!switchSign) {
                    switchSign = true;
                    inventoryShiftCalcVO.setSwitchSign(YesOrNoEnum.YES.getCode());
                }
                BigDecimal endingInventoryDays = inventoryShiftCalcVO.calcNewEndingInventoryDay(endingInventoryDateList,
                        demandSumGroupByDay, i);
                if (endInventoryDaysList.isEmpty()) {
                    endInventoryDaysList = getExpectEndingInventoryDays(newDeliveryPlanLockConfigVO,
                            deliveryPlanCalcSpace.getDeliveryPlanReplenishConfigVO(newDeliveryPlanPO.getOemCode()),
                            endingInventoryDays);
                    // if (deliveryPlanStartDate.before(dateList.get(0))) {
                    //     endInventoryDaysList.clear();
                    //     endInventoryDaysList.add(BigDecimal.ZERO);
                    // }
                }
                BigDecimal expectEndingInventoryDays = endInventoryDaysList.removeFirst();
                // 根据期望库存天数反算期望的期末库存
                int expectEndingInventory = getExpectEndingInventory(endingInventoryDateList, demandSumGroupByDay, i,
                        expectEndingInventoryDays);
                int replenishPlanQty = inventoryShiftCalcVO.getNewReplenishPlanQty(expectEndingInventory);

                if (newDeliveryPlanDetailPO == null) {
                    newDeliveryPlanDetailPO = new DeliveryPlanDetailPO();
                    if (!deliveryPlanStartDate.before(dateList.get(0))) {
                        newDeliveryPlanDetailPOListCreateTemp.add(newDeliveryPlanDetailPO);
                    }
                    newDeliveryPlanDetailPO.setDeliveryPlanDataId(newDeliveryPlanPO.getId());
                    newDeliveryPlanDetailPO.setDemandTime(deliveryPlanStartDate);
                }
                replenishPlanQty = Math.max(replenishPlanQty, 0);
                newDeliveryPlanDetailPO.setDemandQuantity(replenishPlanQty);
                //切换后计算
                int replenishPlanQty2 = getNormalReplenishPlanQty(dateList, i, demandSumGroupByDay, newDeliveryPlanDetailPO,
                        newDeliveryPlanPO, deliveryPlanCalcSpace.getMaterialRiskLevelVOMapOfJoinKey(),
                        deliveryPlanCalcSpace.getProductBoxRelationVOMapOfProductCode(),
                        deliveryPlanCalcSpace.getBoxInfoVOMapOfId(), newSafeStockLevelVO,
                        productBoxRelationWarningList, inventoryShiftCalcVO.getNewUnfulfilledEnding(),
                        deliveryPlanCalcSpace.getNewProductStockPointVO(newDeliveryPlanPO.getProductCode()), oemHolidayList);
                newDeliveryPlanDetailPO.setDemandQuantity(replenishPlanQty2);
                inventoryShiftCalcVO.setNewDeliveryPlan(replenishPlanQty2);
                //紧急发货
                if (deliveryPlanStartDate.before(dateList.get(0))) {
                    newBeforeQuantity = newBeforeQuantity + replenishPlanQty2;
                }
                if (deliveryPlanStartDate.equals(dateList.get(0))) {
                    if (newBeforeQuantity > 0) {
                        newDeliveryPlanDetailPO.setUrgentSign(YesOrNoEnum.YES.getCode());
                    }
                    if (oldBeforeQuantity > 0) {
                        if (oldDeliveryPlanDetailPO == null) {
                            oldDeliveryPlanDetailPO = new DeliveryPlanDetailPO();
                            if (!deliveryPlanStartDate.before(dateList.get(0))) {
                                oldDeliveryPlanDetailPOListCreateTemp.add(oldDeliveryPlanDetailPO);
                            }
                            oldDeliveryPlanDetailPO.setDeliveryPlanDataId(oldDeliveryPlanPO.getId());
                            oldDeliveryPlanDetailPO.setDemandTime(deliveryPlanStartDate);
                        }
                        oldDeliveryPlanDetailPO.setUrgentSign(YesOrNoEnum.YES.getCode());
                        oldDeliveryPlanDetailPO.setDemandQuantity(oldBeforeQuantity);
                    }
                    newDeliveryPlanDetailPO.setDemandQuantity(newBeforeQuantity + replenishPlanQty2);
                }

                inventoryShiftCalcVO.setOldDeliveryPlan(0);
                inventoryShiftCalcVO.setOldArrivalPlan(inventoryShiftCalcVO.getOldReceive());
                inventoryShiftCalcVO.setOldEndingInventory(inventoryShiftCalcVO.getOldOpeningInventory());
                inventoryShiftCalcVO.setOldOemEndingInventory(inventoryShiftCalcVO.getOldOemOpeningInventory());
                inventoryShiftCalcVO.setOldEndingInventoryDays(BigDecimal.ZERO);

                int newTotalEndingInventory = inventoryShiftCalcVO.calcNewEndingInventory();
                // 当天期末库存天数
                endingInventoryDays = getEndingInventoryDays(endingInventoryDateList, demandSumGroupByDay,
                        newTotalEndingInventory, i, oemHolidayList);
                inventoryShiftCalcVO.setNewEndingInventoryDays(endingInventoryDays);
                inventoryShiftCalcVO.setNewAsDisplay();

                //安全库存相关采用旧物料配置
                Integer minInventoryLevel = getInventoryLevel(i, newSafeStockLevelVO.getMinStockDay(), dateList,
                        demandSumGroupByDay);
                inventoryShiftCalcVO.setEndingInventoryMinSafeDiff(inventoryShiftCalcVO.getNewEndingInventory() - minInventoryLevel);
                inventoryShiftCalcVO.setStandardSafetyInventoryLevel(getInventoryLevel(i,
                        newSafeStockLevelVO.getStandardStockDay(), dateList, demandSumGroupByDay));
            }
            InventoryShiftPO inventoryShiftPO = inventoryShiftCalcVO.createInventoryShiftPO();
            inventoryShiftPOListCreateTemp.add(inventoryShiftPO);
            oldEndingInventory = inventoryShiftCalcVO.getOldEndingInventory();
            oldOemEndingInventory = inventoryShiftCalcVO.getOldOemEndingInventory();
            newEndingInventory = inventoryShiftCalcVO.getNewEndingInventory();
            newOemEndingInventory = inventoryShiftCalcVO.getNewOemEndingInventory();
        }

    }

    public DeliveryPlanLockConfigVO getConfig(DeliveryPlanLockConfigVO config, SafetyStockLevelVO safetyStockLevelVO,
                                              SafetyStockLevelVO oldSafetyStockLevelVO) {
        DeliveryPlanLockConfigVO result = new DeliveryPlanLockConfigVO();
        if (safetyStockLevelVO != null) {
            result.setStockDaysMinLimit(safetyStockLevelVO.getMinStockDay());
            result.setStockDaysMaxLimit(safetyStockLevelVO.getStandardStockDay());
        } else if (config != null) {
            result.setStockDaysMinLimit(config.getStockDaysMinLimit());
            result.setStockDaysMaxLimit(config.getStockDaysMaxLimit());
        } else if (oldSafetyStockLevelVO != null) {
            result.setStockDaysMinLimit(oldSafetyStockLevelVO.getMinStockDay());
            result.setStockDaysMaxLimit(oldSafetyStockLevelVO.getStandardStockDay());
        }
        return result;
    }

    public BigDecimal getDateDiff(Date date1, Date date2) {
        LocalDate localDate1 = date1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localDate2 = date2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return new BigDecimal(ChronoUnit.DAYS.between(localDate1, localDate2));
    }

    public Map<String, Integer> getDemandSummery(Map<String, Integer> oldDemandSumGroupByDay,
                                                 Map<String, Integer> newDemandSumGroupByDay) {
        Map<String, Integer> demandSumGroupByDay = new HashMap<>();
        for (String key : oldDemandSumGroupByDay.keySet()) {
            demandSumGroupByDay.put(key, (oldDemandSumGroupByDay.get(key) == null ? 0 : oldDemandSumGroupByDay.get(key))
                    + (newDemandSumGroupByDay.get(key) == null ? 0 : newDemandSumGroupByDay.get(key)));
        }
        for (String key : newDemandSumGroupByDay.keySet()) {
            if (!demandSumGroupByDay.containsKey(key)) {
                demandSumGroupByDay.put(key, newDemandSumGroupByDay.get(key) == null ? 0 : newDemandSumGroupByDay.get(key));
            }
        }
        return demandSumGroupByDay;
    }

    public void generateMtsSmoothSwitch(Map<String, SafetyStockLevelVO> safetyStockLevelVOMap,
                                        DeliveryPlanPO oldDeliveryPlanPO, DeliveryPlanPO newDeliveryPlanPO,
                                        List<String> noSafetyStockLeve,
                                        Map<String, Integer> productStockPointInventoryMap,
                                        Map<String, Integer> oemInventoryMap,
                                        Map<String, List<WarehouseReleaseToWarehouseVO>> productStockPointInRoadMap,
                                        List<Date> dateList, int oemTransportSupplyDays,
                                        Map<String, Integer> oldDemandSumGroupByDay,
                                        Map<String, Integer> newDemandSumGroupByDay,
                                        Map<String, List<ResourceCalendarVO>> resourceCalendarGroupOemCodeMap,
                                        List<DeliveryPlanDetailPO> oldDeliveryPlanDetailPOListCreateTemp,
                                        List<DeliveryPlanDetailPO> newDeliveryPlanDetailPOListCreateTemp,
                                        List<InventoryShiftPO> inventoryShiftPOListCreateTemp,
                                        Map<String, PartRiskLevelVO> finalMaterialRiskLevelVOMapOfJoinKey,
                                        Map<String, List<ProductBoxRelationVO>> finalProductBoxRelationVOMapOfProductCode,
                                        DeliveryPlanLockConfigVO deliveryPlanLockConfigVO,
                                        DeliveryPlanReplenishConfigVO deliveryPlanReplenishConfigVO,
                                        Map<String, BoxInfoVO> finalBoxInfoVOMapOfId,
                                        List<String> productBoxRelationWarningList, Map<String, List<OemInventorySubmissionVO>> receivedOemInventorySubmissionMap,
                                        NewProductStockPointVO newProductStockPointVO) {
        //旧安全库存水位
        SafetyStockLevelVO oldSafetyStockLevelVO = safetyStockLevelVOMap.get(oldDeliveryPlanPO.getProductCode());
        if (oldSafetyStockLevelVO == null) {
            noSafetyStockLeve.add(oldDeliveryPlanPO.getProductCode());
            return;
        }
        //新安全库存水位
        SafetyStockLevelVO newSafetyStockLevelVO = safetyStockLevelVOMap.get(newDeliveryPlanPO.getProductCode());
        if (newSafetyStockLevelVO == null) {
            noSafetyStockLeve.add(newDeliveryPlanPO.getProductCode());
            return;
        }
        // 旧期初库
        String oldKey = oldDeliveryPlanPO.getOemCode() + "&&" + oldDeliveryPlanPO.getProductCode();
        Integer oldOpeningInventory = productStockPointInventoryMap.get(oldKey) == null ? 0 :
                productStockPointInventoryMap.get(oldKey);
        // 旧主机厂期初库存
        Integer oldOemOpeningInventory =
                oemInventoryMap.get(oldKey) == null
                        ? 0 :
                        oemInventoryMap.get(oldKey);
        // 新期初库
        String newKey = newDeliveryPlanPO.getOemCode() + "&&" + newDeliveryPlanPO.getProductCode();
        Integer newOpeningInventory = productStockPointInventoryMap.get(newKey) == null ? 0 :
                productStockPointInventoryMap.get(newKey);
        // 新主机厂期初库存
        Integer newOemOpeningInventory =
                oemInventoryMap.get(newKey) == null
                        ? 0 :
                        oemInventoryMap.get(newKey);
        // 旧在途数据
        List<WarehouseReleaseToWarehouseVO> oldWarehouseReleaseRecordVOS =
                productStockPointInRoadMap.get(oldKey);
        // 新在途数据
        List<WarehouseReleaseToWarehouseVO> newWarehouseReleaseRecordVOS = productStockPointInRoadMap.get(newKey);
        //维护在途数据和发货计划数据
        generateMtsSmoothSwitchData(oldDeliveryPlanPO, newDeliveryPlanPO,
                dateList, oemTransportSupplyDays,
                oldDemandSumGroupByDay, newDemandSumGroupByDay,
                resourceCalendarGroupOemCodeMap,
                oldOpeningInventory, newOpeningInventory,
                oldOemOpeningInventory, newOemOpeningInventory,
                oldWarehouseReleaseRecordVOS, newWarehouseReleaseRecordVOS,
                oldSafetyStockLevelVO, newSafetyStockLevelVO,
                deliveryPlanLockConfigVO, deliveryPlanReplenishConfigVO,
                oldDeliveryPlanDetailPOListCreateTemp, newDeliveryPlanDetailPOListCreateTemp,
                inventoryShiftPOListCreateTemp,
                finalMaterialRiskLevelVOMapOfJoinKey,
                finalProductBoxRelationVOMapOfProductCode, finalBoxInfoVOMapOfId,
                productBoxRelationWarningList, receivedOemInventorySubmissionMap, newProductStockPointVO);
    }

    /**
     *
     * @param oldDeliveryPlanPO    （旧）发货计划
     * @param newDeliveryPlanPO    （新）发货计划
     * @param dateList 库存推移表时间范围
     * @param supplyDays 主机厂可以运输到达的天数
     * @param oldDemandSumGroupByDay （旧）by物料by天的需求数量 --从合并后的日需求和一致性预测需求中数据得来
     * @param newDemandSumGroupByDay （新）by物料by天的需求数量 --从合并后的日需求和一致性预测需求中数据得来
     * @param resourceCalendarGroupOemCodeMap by主机厂装车日历
     * @param oldOpeningInventory （旧）物料期初库存
     * @param newOpeningInventory （新）物料期初库存
     * @param oldOemOpeningInventory （旧）主机厂期初库存
     * @param newOemOpeningInventory （新）主机厂期初库存
     * @param oldWarehouseReleaseRecordVOS （旧）在途数据
     * @param newWarehouseReleaseRecordVOS （新）在途数据
     * @param newSafetyStockLevelVO （新）安全库存配置
     * @param deliveryPlanLockConfigVO 发货计划锁定配置
     * @param deliveryPlanReplenishConfigVO 发货计划补货策略
     * @param oldDeliveryPlanDetailPOListCreateTemp （旧）发货计划明细新建数据集--本身已经包含上版锁定期内数据
     * @param newDeliveryPlanDetailPOListCreateTemp （新）发货计划明细新建数据集--本身已经包含上版锁定期内数据
     * @param inventoryShiftPOListCreateTemp 库存推移表新建数据集
     * @param materialRiskLevelVOMapOfJoinKey 零件风险等级
     * @param productBoxRelationVOMapOfProductCode 产品成箱关系
     * @param boxInfoVOMapOfId 成箱信息
     * @param productBoxRelationWarningList
     */
    private void generateMtsSmoothSwitchData(DeliveryPlanPO oldDeliveryPlanPO, DeliveryPlanPO newDeliveryPlanPO,
                                             List<Date> dateList, int supplyDays, Map<String, Integer> oldDemandSumGroupByDay,
                                             Map<String, Integer> newDemandSumGroupByDay,
                                             Map<String, List<ResourceCalendarVO>> resourceCalendarGroupOemCodeMap, Integer oldOpeningInventory,
                                             Integer newOpeningInventory, Integer oldOemOpeningInventory, Integer newOemOpeningInventory,
                                             List<WarehouseReleaseToWarehouseVO> oldWarehouseReleaseRecordVOS,
                                             List<WarehouseReleaseToWarehouseVO> newWarehouseReleaseRecordVOS, SafetyStockLevelVO oldSafetyStockLevelVOa,
                                             SafetyStockLevelVO newSafetyStockLevelVO, DeliveryPlanLockConfigVO deliveryPlanLockConfigVO,
                                             DeliveryPlanReplenishConfigVO deliveryPlanReplenishConfigVO,
                                             List<DeliveryPlanDetailPO> oldDeliveryPlanDetailPOListCreateTemp,
                                             List<DeliveryPlanDetailPO> newDeliveryPlanDetailPOListCreateTemp,
                                             List<InventoryShiftPO> inventoryShiftPOListCreateTemp,
                                             Map<String, PartRiskLevelVO> materialRiskLevelVOMapOfJoinKey,
                                             Map<String, List<ProductBoxRelationVO>> productBoxRelationVOMapOfProductCode,
                                             Map<String, BoxInfoVO> boxInfoVOMapOfId, List<String> productBoxRelationWarningList,
                                             Map<String, List<OemInventorySubmissionVO>> receivedOemInventorySubmissionMap, NewProductStockPointVO newProductStockPointVO) {
        List<ResourceCalendarVO> resourceCalendarVOList =
                resourceCalendarGroupOemCodeMap.get(oldDeliveryPlanPO.getOemCode());
        if (deliveryPlanLockConfigVO == null) {
            deliveryPlanLockConfigVO = new DeliveryPlanLockConfigVO();
            deliveryPlanLockConfigVO.setStockDaysMinLimit(newSafetyStockLevelVO.getMinStockDay());
            deliveryPlanLockConfigVO.setStockDaysMaxLimit(newSafetyStockLevelVO.getStandardStockDay());
        } else {
            if ((StringUtils.equals(YesOrNoEnum.NO.getCode(), deliveryPlanLockConfigVO.getUseStockDaysMin()) && null == deliveryPlanLockConfigVO.getStockDaysMinLimit()) ||
                    (StringUtils.equals(YesOrNoEnum.NO.getCode(), deliveryPlanLockConfigVO.getUseStockDaysStandard()) && null == deliveryPlanLockConfigVO.getStockDaysMaxLimit())) {
                throw new BusinessException(String.format("主机厂【%s】最小、标准库存天数为空，请在计划锁定配置页面维护",
                        newDeliveryPlanPO.getOemCode()));
            }

            if (YesOrNoEnum.YES.getCode().equals(deliveryPlanLockConfigVO.getUseStockDaysMin())) {
                deliveryPlanLockConfigVO.setStockDaysMinLimit(newSafetyStockLevelVO.getMinStockDay());
            }
            if (YesOrNoEnum.YES.getCode().equals(deliveryPlanLockConfigVO.getUseStockDaysStandard())) {
                deliveryPlanLockConfigVO.setStockDaysMaxLimit(newSafetyStockLevelVO.getStandardStockDay());
            }
        }

        if (null == deliveryPlanLockConfigVO.getStockDaysMaxLimit() || null == deliveryPlanLockConfigVO.getStockDaysMinLimit()) {
            throw new BusinessException(String.format("主机厂【%s】,产品编码【%s】最小、标准库存天数为空，请在安全库存管理页面维护",
                    newDeliveryPlanPO.getOemCode(), newDeliveryPlanPO.getProductCode()));
        }

        List<Long> resourceCalendarTime =
                resourceCalendarVOList.stream().map(ResourceCalendarVO::getWorkDay).map(Date::getTime).distinct().collect(Collectors.toList());
        //（旧）发货计划明细新建数据集--本身已经包含上版锁定期内数据
        Map<String, DeliveryPlanDetailPO> oldDeliveryPlanDetailMap = oldDeliveryPlanDetailPOListCreateTemp.stream()
                .collect(Collectors.toMap(t -> DateUtils.dateToString(t.getDemandTime()),
                        Function.identity(), (t1, t2) -> t2));
        //（新）发货计划明细新建数据集--本身已经包含上版锁定期内数据
        Map<String, DeliveryPlanDetailPO> newDeliveryPlanDetailMap = newDeliveryPlanDetailPOListCreateTemp.stream()
                .collect(Collectors.toMap(t -> DateUtils.dateToString(t.getDemandTime()),
                        Function.identity(), (t1, t2) -> t2));

        Map<String, Integer> demandMapSummary = getDemandSummery(oldDemandSumGroupByDay, newDemandSumGroupByDay);
        
        //获取主机厂对应的放假时间（新旧物料放假逻辑暂不处理）
        List<String> oemHolidayList = new ArrayList<>();
        
        LinkedList<BigDecimal> endInventoryDaysList = new LinkedList<>();
        //期末库存天数计算需要时间范围外数据，用一个新的list进行计算
        List<Date> endingInventoryDateList = initEndingInventoryDateList(dateList);
        Integer oldEndingInventory = oldOpeningInventory;
        Integer oldOemEndingInventory = oldOemOpeningInventory;
        Integer newEndingInventory = newOpeningInventory;
        Integer newOemEndingInventory = newOemOpeningInventory;
        boolean switchStart = false;
        boolean switchSign = false;
        int beforeQuantity = 0;
        for (int i = 0; i < dateList.size(); i++) {
            Date date = dateList.get(i);
            Integer oldDemandQuantity = oldDemandSumGroupByDay.get(DateUtils.dateToString(date));
            Integer newDemandQuantity = newDemandSumGroupByDay.get(DateUtils.dateToString(date));
            if (oldDemandQuantity == null && newDemandQuantity == null
                    && !resourceCalendarTime.contains(date.getTime())) {
                oldDemandQuantity = 0;
                newDemandQuantity = 0;
                //continue;
            }
            // (旧)仓库到货数据：arriveDate和date同一天
            //中转库数量
            List<OemInventorySubmissionVO> oldOemInventoryList =
                    receivedOemInventorySubmissionMap.get(oldDeliveryPlanPO.getOemCode() + "&&" + oldDeliveryPlanPO.getProductCode());
            List<OemInventorySubmissionVO> newOemInventoryList =
                    receivedOemInventorySubmissionMap.get(newDeliveryPlanPO.getOemCode() + "&&" + newDeliveryPlanPO.getProductCode());
            Integer oldReceiveQuantity = getReceiveQuantity(date, supplyDays, oldWarehouseReleaseRecordVOS, oldOemInventoryList, null);
            oldReceiveQuantity = oldReceiveQuantity == null ? 0 : oldReceiveQuantity;
            // (新)仓库到货数据：arriveDate和date同一天
            Integer newReceiveQuantity = getReceiveQuantity(date, supplyDays, newWarehouseReleaseRecordVOS, newOemInventoryList, null);
            newReceiveQuantity = newReceiveQuantity == null ? 0 : newReceiveQuantity;
            // （旧）客户需求
            Integer oldCustomerDemand = oldDemandQuantity == null ? 0 : oldDemandQuantity;
            // （新）客户需求
            Integer newCustomerDemand = newDemandQuantity == null ? 0 : newDemandQuantity;

            Integer customerDemand = oldCustomerDemand + newCustomerDemand;

            // （旧）期初库存
            Integer oldOpeningInventoryTemp = oldEndingInventory;
            // （旧）主机厂期初库存
            Integer oldOemOpeningInventoryTemp = oldOemEndingInventory;

            // （新）期初库存
            Integer newOpeningInventoryTemp = newEndingInventory;
            // （新）主机厂期初库存
            Integer newOemOpeningInventoryTemp = newOemEndingInventory;

            // 发货计划的开始时间
            Date deliveryPlanStartDate = DateUtils.moveDay(date, -supplyDays);
            boolean oldDeliveryPlanDetailPOFlag = true;
            boolean newDeliveryPlanDetailPOFlag = true;
            //            boolean computeBatchFlag = false;
            //(旧)发货明细数据，在途数量
            DeliveryPlanDetailPO oldDeliveryPlanDetailPO =
                    oldDeliveryPlanDetailMap.get(DateUtils.dateToString(deliveryPlanStartDate));
            Integer oldInRoad = oldDeliveryPlanDetailPO == null ? 0 : oldDeliveryPlanDetailPO.getDemandQuantity();
            //(新)发货明细数据，在途数量
            DeliveryPlanDetailPO newDeliveryPlanDetailPO =
                    newDeliveryPlanDetailMap.get(DateUtils.dateToString(deliveryPlanStartDate));
            Integer newInRoad = newDeliveryPlanDetailPO == null ? 0 : newDeliveryPlanDetailPO.getDemandQuantity();

            // 先用（旧）主机厂期初库存满足需求, 减（旧）客户需求，(新)客户需求
            oldOemEndingInventory = oldOemOpeningInventoryTemp - customerDemand;
            oldOemEndingInventory = Math.max(oldOemEndingInventory, 0);
            // 未满足需求数量
            int unfulfilledQuantity = customerDemand - oldOemOpeningInventoryTemp;
            // if(oldOemEndingInventory <= 0) {
            // 	unfulfilledQuantity = customerDemand - oldOemOpeningInventoryTemp - newOemOpeningInventoryTemp;
            // 	//再用（新）主机厂期初库存满足需求，
            //     newOemEndingInventory = newOemOpeningInventoryTemp + oldOemEndingInventory;
            // 	oldOemEndingInventory = 0;
            // 	newOemEndingInventory = newOemEndingInventory <= 0 ? 0 : newOemEndingInventory;
            // }

            unfulfilledQuantity = Math.max(unfulfilledQuantity, 0);
            if (oldEndingInventory <= 0) {
                switchStart = true;
            }
            // 计算期末库存：(旧)期初库存+(旧)在途数据-客户需求
            int endingInventory =
                    oldOpeningInventoryTemp + oldInRoad + newInRoad + oldReceiveQuantity + newReceiveQuantity - unfulfilledQuantity;
            if (endingInventory <= 0) {
                oldEndingInventory = 0;
                //再用：(新)期初库存+(新)在途数据-客户需求
                newOemEndingInventory = newOemOpeningInventoryTemp + endingInventory;
                if (newOemEndingInventory <= 0) {
                    newEndingInventory = newOpeningInventoryTemp + newOemEndingInventory;
                    newOemEndingInventory = 0;
                    newEndingInventory = Math.max(newEndingInventory, 0);
                }

            } else {
                oldEndingInventory = endingInventory;
            }

            // 主机厂期末库存和本厂期末库存总和(旧+新)
            Integer endingInventorySum = oldEndingInventory + oldOemEndingInventory + newEndingInventory + newOemEndingInventory;

            // 当天期末库存天数（新+旧）
            BigDecimal endingInventoryDays = getEndingInventoryDays(endingInventoryDateList,
                    demandMapSummary, endingInventorySum
                    , i, oemHolidayList);

            // 计算期望期末库存天数
            if (endInventoryDaysList.isEmpty()) {
                endInventoryDaysList = getExpectEndingInventoryDays(deliveryPlanLockConfigVO,
                        deliveryPlanReplenishConfigVO,
                        endingInventoryDays);
                // if (deliveryPlanStartDate.before(dateList.get(0))) {
                //     endInventoryDaysList.clear();
                //     endInventoryDaysList.add(BigDecimal.ZERO);
                // }
            }

            BigDecimal expectEndingInventoryDays = endInventoryDaysList.removeFirst();
            // 根据期望库存天数反算期望的期末库存（新）
            int expectEndingInventory = getExpectEndingInventory(dateList,
                    demandMapSummary, i,
                    expectEndingInventoryDays);
            // if (!deliveryPlanStartDate.before(dateList.get(0))) {
            //     // （旧）期望库存减去实际的期初库存就是发货计划量
            //     // int oldReplenishPlanQty =
            //     //         expectEndingInventory + oldCustomerDemand - (oldOpeningInventoryTemp + oldOemOpeningInventoryTemp);
            //     // oldReplenishPlanQty = Math.max(oldReplenishPlanQty, 0);
            //
            // }
            if (oldDeliveryPlanDetailPO == null) {
                oldDeliveryPlanDetailPO = new DeliveryPlanDetailPO();
                if (!deliveryPlanStartDate.before(dateList.get(0))) {
                    oldDeliveryPlanDetailPOListCreateTemp.add(oldDeliveryPlanDetailPO);
                }
            }
            oldDeliveryPlanDetailPOFlag = false;
            oldDeliveryPlanDetailPO.setDeliveryPlanDataId(oldDeliveryPlanPO.getId());
            // 发货计划量
            oldDeliveryPlanDetailPO.setDemandQuantity(oldInRoad);
            oldDeliveryPlanDetailPO.setDemandTime(deliveryPlanStartDate);

            // （新）期望库存减去实际的期初库存就是发货计划量
            // 旧物料库存不够意味着要用新物料的发货计划填充
            if (newDeliveryPlanDetailPO == null) {
                newDeliveryPlanDetailPO = new DeliveryPlanDetailPO();
                if (!deliveryPlanStartDate.before(dateList.get(0))) {
                    newDeliveryPlanDetailPOListCreateTemp.add(newDeliveryPlanDetailPO);
                }
            }
            newDeliveryPlanDetailPO.setDeliveryPlanDataId(newDeliveryPlanPO.getId());
            newDeliveryPlanDetailPO.setDemandTime(deliveryPlanStartDate);
            if (oldEndingInventory <= expectEndingInventory) {
                // expectEndingInventory = expectEndingInventory - oldEndingInventory;
                // oldEndingInventory = 0;
                int newReplenishPlanQty =
                        expectEndingInventory + customerDemand - (oldOpeningInventoryTemp + oldOemOpeningInventoryTemp +
                                newOpeningInventoryTemp + newOemOpeningInventoryTemp) - oldReceiveQuantity - newReceiveQuantity;
                newReplenishPlanQty = Math.max(newReplenishPlanQty, 0);

                // 发货计划量
                newDeliveryPlanDetailPO.setDemandQuantity(newReplenishPlanQty);

            }

            //            InventoryShiftPO inventoryShiftPO = getInventoryShiftPO(receiveQuantity, deliveryPlanPO, dateList,
            //                    demandSumGroupByDay,
            //                    safetyStockLevelVO, date, openingInventoryTemp, demandQuantity, deliveryPlanDetailPO,
            //                    deliveryPlanDetailPOFlag, endingInventory, deliveryPlanStartDate, expectEndingInventoryDays,
            //                    endingInventoryDays, expectEndingInventory, i, computeBatchFlag);

            InventoryShiftPO inventoryShiftPO = new InventoryShiftPO();
            inventoryShiftPO.setVersionId(oldDeliveryPlanPO.getVersionId());
            inventoryShiftPO.setProductCode(oldDeliveryPlanPO.getProductCode() + "&" + newDeliveryPlanPO.getProductCode());
            inventoryShiftPO.setOemCode(oldDeliveryPlanPO.getOemCode());
            inventoryShiftPO.setPlannedDate(date);
            inventoryShiftPO.setDeliveryDate(deliveryPlanStartDate);
            inventoryShiftPO.setBusinessType(oldDeliveryPlanPO.getSupplyType());
            inventoryShiftPO.setMarketType(oldDeliveryPlanPO.getTradeType());
            inventoryShiftPO.setOpeningInventory(oldOpeningInventoryTemp + newOpeningInventoryTemp);
            inventoryShiftPO.setOldOpeningInventory(oldOpeningInventoryTemp);
            inventoryShiftPO.setNewOpeningInventory(newOpeningInventoryTemp);
            inventoryShiftPO.setCustomerDemand(customerDemand);
            inventoryShiftPO.setOldDemand(oldCustomerDemand);
            inventoryShiftPO.setNewDemand(newCustomerDemand);
            inventoryShiftPO.setOldOemOpeningInventory(oldOemOpeningInventoryTemp);
            inventoryShiftPO.setNewOemOpeningInventory(newOemOpeningInventoryTemp);
            if (switchStart && !switchSign) {
                switchSign = true;
                inventoryShiftPO.setSwitchSign(YesOrNoEnum.YES.getDesc());
            }
            //            Integer arrivalPlan = 0;
            Integer accumulatedInventoryGap = 0;
            accumulatedInventoryGap = oldEndingInventory + newEndingInventory >= 0 ? 0 : -oldEndingInventory - newEndingInventory;
            inventoryShiftPO.setAccumulatedInventoryGap(accumulatedInventoryGap);
            //            inventoryShiftPO.setEndingInventory(endingInventory);

            //默认算新的 TODO
            inventoryShiftPO.setMinimumSafetyInventoryDays(newSafetyStockLevelVO.getMinStockDay());
            inventoryShiftPO.setStandardSafetyInventoryDays(newSafetyStockLevelVO.getStandardStockDay());
            inventoryShiftPO.setStandardSafetyInventoryLevel(getInventoryLevel(i,
                    newSafetyStockLevelVO.getStandardStockDay(), dateList, newDemandSumGroupByDay));
            inventoryShiftPOListCreateTemp.add(inventoryShiftPO);

            // (旧)整箱发货
            // int oldReplenishPlanQty2 = getReplenishPlanQty(endingInventoryDateList, i, oldDemandSumGroupByDay, oldDeliveryPlanDetailPO,
            // 		oldDeliveryPlanPO, materialRiskLevelVOMapOfJoinKey,
            //         productBoxRelationVOMapOfProductCode, boxInfoVOMapOfId, null,
            //         productBoxRelationWarningList);
            int oldTotalEndingInventory =
                    oldOpeningInventoryTemp + oldOemOpeningInventoryTemp
                            + oldReceiveQuantity - customerDemand;
            BigDecimal oldEndingInventoryDays = BigDecimal.ZERO;
            if (oldTotalEndingInventory > 0) {
                oldEndingInventoryDays = getEndingInventoryDays(endingInventoryDateList, demandMapSummary,
                        oldTotalEndingInventory, i, oemHolidayList);
            }
            inventoryShiftPO.setOldEndingInventoryDays(oldEndingInventoryDays);
            int unfulfilledEnding =
                    oldOpeningInventoryTemp + oldOemOpeningInventoryTemp + newOpeningInventoryTemp + newOemOpeningInventoryTemp - customerDemand + oldReceiveQuantity + newReceiveQuantity;
            // (新)整箱发货
            int newReplenishPlanQty2 = getNormalReplenishPlanQty(endingInventoryDateList, i, newDemandSumGroupByDay, newDeliveryPlanDetailPO,
                    newDeliveryPlanPO, materialRiskLevelVOMapOfJoinKey,
                    productBoxRelationVOMapOfProductCode, boxInfoVOMapOfId, newSafetyStockLevelVO,
                    productBoxRelationWarningList, unfulfilledEnding, newProductStockPointVO, oemHolidayList);
            // 计算期末库存和期末库存天数
            // 计算期末库存：期初库存+在途数据-客户需求
            int totalEndingInventory =
                    oldOpeningInventoryTemp + oldOemOpeningInventoryTemp + newOpeningInventoryTemp + newOemOpeningInventoryTemp
                            + newReplenishPlanQty2 + oldReceiveQuantity + newReceiveQuantity - customerDemand;
            totalEndingInventory = Math.max(totalEndingInventory, 0);
            // int newTotalEndingInventory =
            // 		newOpeningInventoryTemp + newOemOpeningInventoryTemp + newReplenishPlanQty2 + newReceiveQuantity - newCustomerDemand;
            // newTotalEndingInventory = Math.max(newTotalEndingInventory, 0);
            // 当天期末库存天数(是否默认计算新的)

            endingInventoryDays = getEndingInventoryDays(endingInventoryDateList, demandMapSummary, totalEndingInventory, i, oemHolidayList);
            // if (null != oldDeliveryPlanDetailPO) {
            // 	oldDeliveryPlanDetailPO.setDemandQuantity(oldReplenishPlanQty2);
            // }

            if (deliveryPlanStartDate.before(dateList.get(0))) {
                beforeQuantity = beforeQuantity + newReplenishPlanQty2;
            }
            if (deliveryPlanStartDate.equals(dateList.get(0))) {
                if (beforeQuantity > 0) {
                    newDeliveryPlanDetailPO.setUrgentSign(YesOrNoEnum.YES.getCode());
                }
                newDeliveryPlanDetailPO.setDemandQuantity(beforeQuantity + newReplenishPlanQty2);
            } else {
                newDeliveryPlanDetailPO.setDemandQuantity(newReplenishPlanQty2);
            }
            // oldEndingInventory = oldTotalEndingInventory;
            // log.info("发货计划计算,日期：{}, 整箱发货数量:{}, 期末库存数量:{}", DateUtils.dateToString(date), oldReplenishPlanQty2,
            // 		oldEndingInventory);
            endingInventory = totalEndingInventory - oldOemEndingInventory - newOemEndingInventory;
            newEndingInventory = endingInventory - oldEndingInventory;
            log.info("发货计划计算,日期：{}, 整箱发货数量:{}, 期末库存数量:{}", DateUtils.dateToString(date), newReplenishPlanQty2,
                    endingInventory);

            inventoryShiftPO.setArrivalPlan(oldReceiveQuantity + newReplenishPlanQty2 + newReceiveQuantity);
            inventoryShiftPO.setOldArrivalPlan(oldReceiveQuantity);
            inventoryShiftPO.setNewArrivalPlan(newReplenishPlanQty2 + newReceiveQuantity);
            inventoryShiftPO.setDeliveryPlan(newReplenishPlanQty2);
            inventoryShiftPO.setOldDeliveryPlan(0);
            inventoryShiftPO.setNewDeliveryPlan(newReplenishPlanQty2);
            inventoryShiftPO.setReceive(oldReceiveQuantity + newReceiveQuantity);
            inventoryShiftPO.setOldReceive(oldReceiveQuantity);
            inventoryShiftPO.setNewReceive(newReceiveQuantity);
            inventoryShiftPO.setEndingInventoryDays(endingInventoryDays);//新
            inventoryShiftPO.setNewEndingInventoryDays(endingInventoryDays);
            inventoryShiftPO.setEndingInventory(endingInventory);
            inventoryShiftPO.setOldEndingInventory(oldEndingInventory);
            inventoryShiftPO.setNewEndingInventory(newEndingInventory);

            Integer newMinInventoryLevel = getInventoryLevel(i, newSafetyStockLevelVO.getMinStockDay(), dateList,
                    newDemandSumGroupByDay);//新的
            inventoryShiftPO.setEndingInventoryMinSafeDiff(newEndingInventory - newMinInventoryLevel);//新的

            inventoryShiftPO.setOldOemEndingInventory(oldOemEndingInventory);
            inventoryShiftPO.setNewOemEndingInventory(newOemEndingInventory);
            inventoryShiftPO.setOemEndingInventory(oldOemEndingInventory + newOemEndingInventory);
            inventoryShiftPO.setOemOpeningInventory(oldOemOpeningInventoryTemp + newOemOpeningInventoryTemp);

            oldEndingInventory = inventoryShiftPO.getOldEndingInventory();
            //            oldOemEndingInventory = inventoryShiftPO.getOemEndingInventory();
            newEndingInventory = inventoryShiftPO.getNewEndingInventory();
            //            newOemEndingInventory = inventoryShiftPO.getOemEndingInventory();
        }
    }


    /**
     * 删除当前发货计划版本数据
     *
     * @param deliveryPlanCalculate
     * @param oemCodeScope
     * @param productScope
     */
    private List<DeliveryPlanDetailVO> deleteCalculateResult(DeliveryPlanCalculateDTO deliveryPlanCalculate,
                                                             List<String> oemCodeScope,
                                                             List<String> productScope, List<String> combinationProductScope,
                                                             List<DeliveryPlanDetailVO> savedDeliveryPlanDetailList) {
        List<DeliveryPlanPO> deliveryPlanList = deliveryPlanDao
                .selectByParams(ImmutableMap.of("versionId", deliveryPlanCalculate.getVersionId()));
        List<String> deleteDeliveryPlanIds = deliveryPlanList.stream()
                .filter(t -> productScope.contains(t.getProductCode()))
                .map(DeliveryPlanPO::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteDeliveryPlanIds)) {
            savedDeliveryPlanDetailList =
                    deliveryPlanDetailDao.selectSavedByDeliveryPlanIds(deleteDeliveryPlanIds);
            deliveryPlanDao.deleteBatch(deleteDeliveryPlanIds);
            deliveryPlanDetailDao.deleteBatchByPlanIds(deleteDeliveryPlanIds);
        }
        //inventoryShift的物料编码存在工程变更新旧物料组合在一起
        productScope.addAll(combinationProductScope);
        inventoryShiftDao.deleteByVersionIdOemProduct(deliveryPlanCalculate.getVersionId(), oemCodeScope, productScope);
        return savedDeliveryPlanDetailList;
    }

    /**
     * 上一版本主机厂锁定期的发货计划数据
     *
     * @param deliveryPlanPO
     * @param preVersionDeliveryPlanDetailMap
     * @return java.util.List<com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanDetailPO>
     */
    private List<DeliveryPlanDetailPO> addPreVersionData(DeliveryPlanPO deliveryPlanPO,
                                                         Map<String, List<DeliveryPlanDetailVO>> preVersionDeliveryPlanDetailMap) {
        List<DeliveryPlanDetailPO> resultList = new ArrayList<>();
        String key = deliveryPlanPO.getProductCode() + "&&" + deliveryPlanPO.getOemCode();
        List<DeliveryPlanDetailVO> deliveryPlanDetailVOList = preVersionDeliveryPlanDetailMap.get(key);
        if (CollectionUtils.isNotEmpty(deliveryPlanDetailVOList)) {
            deliveryPlanDetailVOList.forEach(deliveryPlanDetail -> {
                DeliveryPlanDetailPO deliveryPlanDetailPO =
                        DeliveryPlanDetailConvertor.INSTANCE.vo2Po(deliveryPlanDetail);
                deliveryPlanDetailPO.setDeliveryPlanDataId(deliveryPlanPO.getId());
                deliveryPlanDetailPO.setId(null);
                resultList.add(deliveryPlanDetailPO);
            });
        }
        return resultList;
    }

    public List<DeliveryPlanDetailVO> getLatestPublishedDeliveryPlanInLockDate(List<DeliveryPlanLockConfigVO> deliveryPlanLockConfigVOList, String deliveryPlanVersionCode) {
        List<DeliveryPlanDetailVO> deliveryPlanDetailList = new ArrayList<>();
        // 查询最新已发布发货计划版本号
        String versionCode = deliveryPlanDao.selectLatestPublishedVersionCode();
        if (StringUtils.isNotEmpty(versionCode)) {

            Date firstDate =
                    DateUtils.stringToDate(deliveryPlanVersionCode.substring(deliveryPlanVersionCode.length() - 13,
                            deliveryPlanVersionCode.length() - 5), "yyyyMMdd");
            // 锁定配置按锁定天数分组
            Map<Integer, List<DeliveryPlanLockConfigVO>> deliveryPlanLockConfigVOMap =
                    deliveryPlanLockConfigVOList.stream().filter(config -> config.getLockDays() > 0).collect(Collectors.groupingBy(DeliveryPlanLockConfigVO::getLockDays));
            // 查询发货计划
            List<String> oemCodes = deliveryPlanLockConfigVOMap.values().stream().flatMap(List::stream)
                    .map(DeliveryPlanLockConfigVO::getOemCode).collect(Collectors.toList());
            List<DeliveryPlanVO> deliveryPlanPOList = deliveryPlanDao.selectVOByParams(ImmutableMap.of("oemCodes",
                    oemCodes, "versionCode", versionCode));

            if (!deliveryPlanLockConfigVOMap.isEmpty()) {
                deliveryPlanLockConfigVOMap.forEach((days, configList) -> {
                    List<String> deliveryPlanIds = deliveryPlanPOList.stream().filter(t ->
                                    configList.stream().map(DeliveryPlanLockConfigVO::getOemCode).collect(Collectors.toList()).contains(t.getOemCode()))
                            .map(DeliveryPlanVO::getId).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(deliveryPlanIds)) {
                        List<Date> dateList = new ArrayList<>();
                        dateList.add(firstDate);
                        for (int i = 1; i < days; i++) {
                            dateList.add(DateUtils.moveDay(firstDate, i));
                        }
                        // 查询发货计划详情，条件：发货计划id列表、锁定日期列表
                        List<DeliveryPlanDetailVO> deliveryPlanDetailVOList =
                                deliveryPlanDetailDao.selectVOByParams(ImmutableMap.of("deliveryPlanDataIds",
                                        deliveryPlanIds, "demandTimeList", dateList));
                        if (CollectionUtils.isNotEmpty(deliveryPlanDetailVOList)) {
                            deliveryPlanDetailList.addAll(deliveryPlanDetailVOList);
                        }
                    }
                });
            }
        }
        return deliveryPlanDetailList;
    }

    /**
     * 生成MTO数据
     *
     * @param deliveryPlanPO
     * @param dateList
     * @param supplyDays
     * @param demandSumGroupByDay
     * @param resourceCalendarGroupOemCodeMap
     * @param deliveryPlanDetailPOListCreate
     * @param inventoryShiftPOListCreate
     * @param materialRiskLevelVOMapOfJoinKey
     * @param productBoxRelationVOMapOfProductCode
     * @param finalBoxInfoVOMapOfId
     * @param productBoxRelationWarningList
     * @param deliveryPlanCalcSpace 
     */
    private void generateMtoData(DeliveryPlanPO deliveryPlanPO,
                                 List<Date> dateList,
                                 int supplyDays,
                                 Map<String, Integer> demandSumGroupByDay,
                                 Map<String, List<ResourceCalendarVO>> resourceCalendarGroupOemCodeMap,
                                 List<DeliveryPlanDetailPO> deliveryPlanDetailPOListCreate,
                                 List<InventoryShiftPO> inventoryShiftPOListCreate,
                                 Map<String, PartRiskLevelVO> materialRiskLevelVOMapOfJoinKey,
                                 Map<String, List<ProductBoxRelationVO>> productBoxRelationVOMapOfProductCode,
                                 Map<String, BoxInfoVO> finalBoxInfoVOMapOfId,
                                 List<String> productBoxRelationWarningList,
                                 Map<String, List<DeliveryPlanDetailPO>> savedDeliveryPlanDetailListMap,
                                 List<String> savedDeliveryPlanDetailWarningList, DeliveryPlanCalcSpace deliveryPlanCalcSpace) {
        deliveryPlanPO.setInTransitTotal(0);
        List<ResourceCalendarVO> resourceCalendarVOList =
                resourceCalendarGroupOemCodeMap.get(deliveryPlanPO.getOemCode());
        if (CollectionUtils.isEmpty(resourceCalendarVOList)) {
            throw new BusinessException(deliveryPlanPO.getOemCode() + "未来30天没有装车日历");
        }
        List<Long> resourceCalendarTime =
                resourceCalendarVOList.stream().map(ResourceCalendarVO::getWorkDay).map(Date::getTime).distinct().collect(Collectors.toList());
        List<DeliveryPlanDetailPO> savedDeliveryPlanDetailPOList =
                savedDeliveryPlanDetailListMap.get(deliveryPlanPO.getOemCode() + "&&" + deliveryPlanPO.getProductCode());
        Map<String, DeliveryPlanDetailPO> savedDeliveryPlanDetailMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(savedDeliveryPlanDetailPOList)) {
            savedDeliveryPlanDetailPOList.sort(Comparator.comparing(DeliveryPlanDetailPO::getDemandTime));
            savedDeliveryPlanDetailMap = savedDeliveryPlanDetailPOList.stream()
                    .collect(Collectors.toMap(t -> DateUtils.dateToString(t.getDemandTime()),
                            Function.identity(), (t1, t2) -> t2));
        }
        //获取产品对应的成箱关系
        String fullBoxFlag = deliveryPlanCalcSpace.getProductFullBoxMap().get(deliveryPlanPO.getProductCode());
        
        //凑整箱补充的数量
        BigDecimal fullBoxAddQty = BigDecimal.ZERO;
        int beforeQuantity = 0;
        for (Date date : dateList) {
            Integer demandQuantity = demandSumGroupByDay.get(DateUtils.dateToString(date));
            if (demandQuantity == null && !resourceCalendarTime.contains(date.getTime())) {
                demandQuantity = 0;
            }
            Integer defaultDemandQuantity = demandQuantity == null ? 0 : demandQuantity;
            DeliveryPlanDetailPO deliveryPlanDetailPO = new DeliveryPlanDetailPO();
            deliveryPlanDetailPO.setDeliveryPlanDataId(deliveryPlanPO.getId());
            deliveryPlanDetailPO.setDemandQuantity(demandQuantity == null ? 0 : demandQuantity);
            Date deliveryPlanStartDate = DateUtils.moveDay(date, -supplyDays);
            deliveryPlanDetailPO.setDemandTime(deliveryPlanStartDate);
            DeliveryPlanDetailPO savedDeliveryPlanDetailPO =
                    savedDeliveryPlanDetailMap.get(DateUtils.dateToString(deliveryPlanDetailPO.getDemandTime()));

            BasePOUtils.insertFiller(deliveryPlanDetailPO);
            demandQuantity = demandQuantity == null ? 0 : demandQuantity;
            
            // 获取零件与箱体的关系
            ProductBoxRelationVO productBoxRelationVO = new ProductBoxRelationVO();
            List<ProductBoxRelationVO> productBoxRelationVOList =
                    productBoxRelationVOMapOfProductCode.get(deliveryPlanPO.getProductCode());
            if (CollectionUtils.isNotEmpty(productBoxRelationVOList)) {
                productBoxRelationVO = productBoxRelationVOList.get(0);
            }
            //根据产品编码，判断当前是否需要走整箱逻辑（MTO_BOX默认走整箱逻辑）
            Integer standardLoad = productBoxRelationVO.getStandardLoad();
            if(((StringUtils.isNotBlank(fullBoxFlag) && YesOrNoEnum.YES.getCode().equals(fullBoxFlag) && demandQuantity > 0) 
            		|| OemBusinessTypeEnum.MTO_BOX.getCode().equals(deliveryPlanPO.getSupplyType()))
            		&& standardLoad != null && standardLoad > 0 
            		&& ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode().equals(deliveryPlanPO.getDemandCategory())) {
            	BigDecimal sourceDemandQuantity = BigDecimal.valueOf(demandQuantity);
            	if(fullBoxAddQty.compareTo(sourceDemandQuantity) >= 0) {
            		//如果凑整箱剩余数量大于等于当前发货数量，那么当前发货数量为0
            		demandQuantity = 0;
            		fullBoxAddQty = fullBoxAddQty.subtract(sourceDemandQuantity);
            	}else {
            		//如果凑整箱剩余数量小当前发货数量，剩余数量需要凑整箱发货，并重新维护凑整箱的剩余数量
            		sourceDemandQuantity = sourceDemandQuantity.subtract(fullBoxAddQty);
            		BigDecimal boxNum = sourceDemandQuantity.divide(BigDecimal.valueOf(standardLoad), 0 ,BigDecimal.ROUND_UP);
                	demandQuantity = boxNum.multiply(BigDecimal.valueOf(standardLoad)).intValue();
                	fullBoxAddQty = BigDecimal.valueOf(demandQuantity).subtract(sourceDemandQuantity);
            	}
            }
            int deliveryQuantity = demandQuantity;
            if (deliveryPlanStartDate.before(dateList.get(0))) {
                beforeQuantity = beforeQuantity + demandQuantity;
            } else {
                deliveryPlanDetailPOListCreate.add(deliveryPlanDetailPO);
            }
            if (deliveryPlanStartDate.equals(dateList.get(0))) {
                if (beforeQuantity > 0) {
                    deliveryPlanDetailPO.setUrgentSign(YesOrNoEnum.YES.getCode());
                }
                deliveryPlanDetailPO.setDemandQuantity(beforeQuantity + demandQuantity);
                deliveryQuantity = deliveryPlanDetailPO.getDemandQuantity();
            } else {
                deliveryPlanDetailPO.setDemandQuantity(demandQuantity);
            }
            if (savedDeliveryPlanDetailPO != null) {
                deliveryPlanDetailPO.setDemandQuantity(savedDeliveryPlanDetailPO.getDemandQuantity());
                deliveryPlanDetailPO.setSavedSign(savedDeliveryPlanDetailPO.getSavedSign());
                if (YesOrNoEnum.YES.getCode().equals(savedDeliveryPlanDetailPO.getUrgentSign())) {
                    deliveryQuantity = deliveryPlanDetailPO.getDemandQuantity() - beforeQuantity;
                } else {
                    deliveryQuantity = deliveryPlanDetailPO.getDemandQuantity();
                }
            }
            InventoryShiftPO inventoryShiftPO = new InventoryShiftPO();
            inventoryShiftPO.setVersionId(deliveryPlanPO.getVersionId());
            inventoryShiftPO.setProductCode(deliveryPlanPO.getProductCode());
            inventoryShiftPO.setOemCode(deliveryPlanPO.getOemCode());
            inventoryShiftPO.setPlannedDate(date);
            inventoryShiftPO.setDeliveryDate(deliveryPlanDetailPO.getDemandTime());
            inventoryShiftPO.setBusinessType(deliveryPlanPO.getSupplyType());
            inventoryShiftPO.setMarketType(deliveryPlanPO.getTradeType());
            inventoryShiftPO.setCustomerDemand(defaultDemandQuantity);
            inventoryShiftPO.setArrivalPlan(deliveryQuantity);
            inventoryShiftPOListCreate.add(inventoryShiftPO);
            if(standardLoad != null && standardLoad > 0 && ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode().equals(deliveryPlanPO.getDemandCategory())) {
            	BigDecimal boxNum = BigDecimal.valueOf(deliveryPlanDetailPO.getDemandQuantity())
            			.divide(BigDecimal.valueOf(standardLoad), 0 ,BigDecimal.ROUND_UP);
            	deliveryPlanDetailPO.setBoxQuantity(boxNum.intValue());
            }
            if(ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode().equals(deliveryPlanPO.getDemandCategory())) {
            	BoxInfoVO boxInfoVO = finalBoxInfoVOMapOfId.get(productBoxRelationVO.getBoxId());
            	setBoxDesc(deliveryPlanDetailPO, boxInfoVO, deliveryPlanDetailPO.getDemandQuantity(), productBoxRelationVO);
                deliveryPlanPO.setPiecePerBox(productBoxRelationVO.getStandardLoad());
            }
        }
        BasePOUtils.insertBatchFiller(inventoryShiftPOListCreate);
    }

    /**
     * @param deliveryPlanPO  发货计划
     * @param dateList  库存推移表时间范围
     * @param supplyDays 主机厂可以运输到达的天数
     * @param demandSumGroupByDay by物料by天的需求数量 --从合并后的日需求和一致性预测需求中数据得来
     * @param resourceCalendarGroupOemCodeMap by主机厂装车日历
     * @param openingInventory  物料期初库存
     * @param oemOpeningInventory   主机厂期初库存
     * @param warehouseReleaseRecordVOList  在途数据
     * @param safetyStockLevelVO    安全库存配置
     * @param deliveryPlanLockConfigVO 发货计划锁定配置
     * @param deliveryPlanReplenishConfigVO 发货计划补货策略
     * @param deliveryPlanDetailPOListCreate 发货计划明细新建数据集--本身已经包含上版锁定期内数据
     * @param inventoryShiftPOListCreate 库存推移表新建数据集
     * @param materialRiskLevelVOMapOfJoinKey 零件风险等级
     * @param productBoxRelationVOMapOfProductCode 产品成箱关系
     * @param boxInfoVOMapOfId  成箱信息
     * @param deliveryPlanCalcSpace
     */
    private void generateMtsData(DeliveryPlanPO deliveryPlanPO,
                                 List<Date> dateList,
                                 Integer supplyDays,
                                 Map<String, Integer> demandSumGroupByDay,
                                 Map<String, List<ResourceCalendarVO>> resourceCalendarGroupOemCodeMap,
                                 Integer openingInventory,
                                 Integer oemOpeningInventory,
                                 List<WarehouseReleaseToWarehouseVO> warehouseReleaseRecordVOList,
                                 SafetyStockLevelVO safetyStockLevelVO, NewProductStockPointVO newProductStockPointVO,
                                 DeliveryPlanLockConfigVO deliveryPlanLockConfigVO,
                                 DeliveryPlanReplenishConfigVO deliveryPlanReplenishConfigVO,
                                 List<DeliveryPlanDetailPO> deliveryPlanDetailPOListCreate,
                                 List<InventoryShiftPO> inventoryShiftPOListCreate,
                                 Map<String, PartRiskLevelVO> materialRiskLevelVOMapOfJoinKey,
                                 Map<String, List<ProductBoxRelationVO>> productBoxRelationVOMapOfProductCode,
                                 Map<String, BoxInfoVO> boxInfoVOMapOfId, List<String> productBoxRelationWarningList,
                                 List<OemInventorySubmissionVO> receivedOemInventorySubmissionVOS,
                                 List<DeliveryPlanDetailPO> savedDeliveryPlanDetailPOList,
                                 List<String> savedDeliveryPlanDetailWarningList, DeliveryPlanCalcSpace deliveryPlanCalcSpace) {
        List<ResourceCalendarVO> resourceCalendarVOList =
                resourceCalendarGroupOemCodeMap.get(deliveryPlanPO.getOemCode());
        if (CollectionUtils.isEmpty(resourceCalendarVOList)) {
            throw new BusinessException(deliveryPlanPO.getOemCode() + "未来30天没有装车日历");
        }
        if (deliveryPlanLockConfigVO == null) {
            deliveryPlanLockConfigVO = new DeliveryPlanLockConfigVO();
            deliveryPlanLockConfigVO.setStockDaysMinLimit(safetyStockLevelVO.getMinStockDay());
            deliveryPlanLockConfigVO.setStockDaysMaxLimit(safetyStockLevelVO.getStandardStockDay());
        } else {
            if ((StringUtils.equals(YesOrNoEnum.NO.getCode(), deliveryPlanLockConfigVO.getUseStockDaysMin()) && null == deliveryPlanLockConfigVO.getStockDaysMinLimit()) ||
                    (StringUtils.equals(YesOrNoEnum.NO.getCode(), deliveryPlanLockConfigVO.getUseStockDaysStandard()) && null == deliveryPlanLockConfigVO.getStockDaysMaxLimit())) {
                throw new BusinessException(String.format("主机厂【%s】最小、标准库存天数为空，请在计划锁定配置页面维护",
                        deliveryPlanPO.getOemCode()));
            }

            if (YesOrNoEnum.YES.getCode().equals(deliveryPlanLockConfigVO.getUseStockDaysMin())) {
                deliveryPlanLockConfigVO.setStockDaysMinLimit(safetyStockLevelVO.getMinStockDay());
            }
            if (YesOrNoEnum.YES.getCode().equals(deliveryPlanLockConfigVO.getUseStockDaysStandard())) {
                deliveryPlanLockConfigVO.setStockDaysMaxLimit(safetyStockLevelVO.getStandardStockDay());
            }
        }

        if (null == deliveryPlanLockConfigVO.getStockDaysMaxLimit() || null == deliveryPlanLockConfigVO.getStockDaysMinLimit()) {
            throw new BusinessException(String.format("主机厂【%s】,产品编码【%s】最小、标准库存天数为空，请在安全库存管理页面维护",
                    deliveryPlanPO.getOemCode(), deliveryPlanPO.getProductCode()));
        }

        List<Long> resourceCalendarTime =
                resourceCalendarVOList.stream().map(ResourceCalendarVO::getWorkDay).map(Date::getTime).distinct().collect(Collectors.toList());

        Map<String, DeliveryPlanDetailPO> deliveryPlanDetailMap = deliveryPlanDetailPOListCreate.stream()
                .collect(Collectors.toMap(t -> DateUtils.dateToString(t.getDemandTime()),
                        Function.identity(), (t1, t2) -> t2));
        Map<String, DeliveryPlanDetailPO> savedDeliveryPlanDetailMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(savedDeliveryPlanDetailPOList)) {
            savedDeliveryPlanDetailPOList.sort(Comparator.comparing(DeliveryPlanDetailPO::getDemandTime));
            savedDeliveryPlanDetailMap = savedDeliveryPlanDetailPOList.stream()
                    .collect(Collectors.toMap(t -> DateUtils.dateToString(t.getDemandTime()),
                            Function.identity(), (t1, t2) -> t2));
        }
        //期望期末库存天数集合
        LinkedList<BigDecimal> endInventoryDaysList = new LinkedList<>();
        //期末库存天数计算需要时间范围外数据，用一个新的list进行计算
        List<Date> endingInventoryDateList = initEndingInventoryDateList(dateList);
        Integer endingInventory = openingInventory;
        Integer oemEndingInventory = oemOpeningInventory;
        int beforeQuantity = 0;
        
        //获取主机厂对应的放假时间
        List<String> oemHolidayList = deliveryPlanCalcSpace.getOemHolidayMap()
        		.getOrDefault(deliveryPlanPO.getOemCode(), new ArrayList<>());
        //维护单箱片数
        ProductBoxRelationVO productBoxRelationVO = new ProductBoxRelationVO();
        List<ProductBoxRelationVO> productBoxRelationVOList =
                productBoxRelationVOMapOfProductCode.get(deliveryPlanPO.getProductCode());
        if (CollectionUtils.isNotEmpty(productBoxRelationVOList)) {
            productBoxRelationVO = productBoxRelationVOList.get(0);
        }
        Integer standardLoad = productBoxRelationVO.getStandardLoad();
        if(deliveryPlanPO.getPiecePerBox() == null) {
        	deliveryPlanPO.setPiecePerBox(standardLoad);
        }
        
        //获取产品对应的成箱关系
        String fullBoxFlag = deliveryPlanCalcSpace.getProductFullBoxMap().get(deliveryPlanPO.getProductCode());
        BigDecimal dayBeforeEndingInventoryDays = null;
        for (int i = 0; i < dateList.size(); i++) {
            Date date = dateList.get(i);
            Integer demandQuantity = demandSumGroupByDay.get(DateUtils.dateToString(date));
            if (demandQuantity == null && !resourceCalendarTime.contains(date.getTime())) {
                demandQuantity = 0;
                //continue;
            }
            // 期初库存
            Integer openingInventoryTemp = endingInventory;
            // 主机厂期初库存
            Integer oemOpeningInventoryTemp = oemEndingInventory;
            // 客户需求
            Integer customerDemand = demandQuantity == null ? 0 : demandQuantity;
            // 仓库到货数据：arriveDate和date同一天
            Integer receiveQuantity = getReceiveQuantity(date, supplyDays, warehouseReleaseRecordVOList
                    , receivedOemInventorySubmissionVOS, deliveryPlanCalcSpace.getShippingListNumberMap());
            receiveQuantity = receiveQuantity == null ? 0 : receiveQuantity;
            // 发货计划的开始时间
            Date deliveryPlanStartDate = DateUtils.moveDay(date, -supplyDays);
            boolean deliveryPlanDetailPOFlag = true;
            boolean computeBatchFlag = false;
            DeliveryPlanDetailPO deliveryPlanDetailPO =
                    deliveryPlanDetailMap.get(DateUtils.dateToString(deliveryPlanStartDate));

            Integer inRoad = deliveryPlanDetailPO == null ? 0 : deliveryPlanDetailPO.getDemandQuantity();

            // 先用主机厂期初库存满足需求
            oemEndingInventory = oemOpeningInventoryTemp - customerDemand;
            // 未满足需求数量
            int unfulfilledQuantity = customerDemand - oemOpeningInventoryTemp;
            oemEndingInventory = oemEndingInventory <= 0 ? 0 : oemEndingInventory;
            unfulfilledQuantity = Math.max(unfulfilledQuantity, 0);

            // 计算期末库存：期初库存+在途数据-客户需求
            endingInventory = openingInventoryTemp + inRoad + receiveQuantity - unfulfilledQuantity;
            endingInventory = endingInventory <= 0 ? 0 : endingInventory;
            // 主机厂期末库存和本厂期末库存总和
            Integer endingInventorySum = endingInventory + oemEndingInventory;
            // 当天期末库存天数
            BigDecimal endingInventoryDays = getEndingInventoryDays(endingInventoryDateList, demandSumGroupByDay, endingInventorySum
                    , i, oemHolidayList);

            DeliveryPlanDetailPO savedDeliveryPlanDetailPO =
                    savedDeliveryPlanDetailMap.get(DateUtils.dateToString(deliveryPlanStartDate));
            //库存推移按照用户需求进行计算，不重新计算发货
            if (savedDeliveryPlanDetailPO != null) {
                endingInventory =
                        savedDeliveryPlanDetailPO.getDemandQuantity() + openingInventoryTemp + receiveQuantity - unfulfilledQuantity;
                endingInventorySum = endingInventory + oemEndingInventory;
                endingInventoryDays = getEndingInventoryDays(endingInventoryDateList, demandSumGroupByDay, endingInventorySum
                        , i, oemHolidayList);
                if (endingInventory < 0) {
                    savedDeliveryPlanDetailWarningList.add("产品编码:" + deliveryPlanPO.getProductCode() + " 日期:" + DateUtils.dateToString(date));
                }
                InventoryShiftPO inventoryShiftPO = createInventoryShiftPO(receiveQuantity, deliveryPlanPO, dateList,
                        demandSumGroupByDay,
                        safetyStockLevelVO, date, openingInventoryTemp, oemOpeningInventoryTemp, customerDemand,
                        endingInventory, oemEndingInventory, savedDeliveryPlanDetailPO.getDemandQuantity(),
                        endingInventoryDays,
                        deliveryPlanStartDate, i);
                setBoxDesc(savedDeliveryPlanDetailPO, boxInfoVOMapOfId.get(productBoxRelationVO.getBoxId()),
                        savedDeliveryPlanDetailPO.getDemandQuantity(), productBoxRelationVO);
                inventoryShiftPO.setAccumulatedInventoryGap(endingInventory);
                if (deliveryPlanDetailPO == null) {
                    savedDeliveryPlanDetailPO.setId(null);
                    savedDeliveryPlanDetailPO.setDeliveryPlanDataId(deliveryPlanPO.getId());
                    deliveryPlanDetailPOListCreate.add(savedDeliveryPlanDetailPO);
                } else {
                    deliveryPlanDetailPO.setDeliveryPlanDataId(deliveryPlanPO.getId());
                    deliveryPlanDetailPO.setSavedSign(savedDeliveryPlanDetailPO.getSavedSign());
                    deliveryPlanDetailPO.setDemandQuantity(savedDeliveryPlanDetailPO.getDemandQuantity());
                }
                if (YesOrNoEnum.YES.getCode().equals(savedDeliveryPlanDetailPO.getUrgentSign())) {
                    inventoryShiftPO.setDeliveryPlan(inventoryShiftPO.getDeliveryPlan() - beforeQuantity);
                    inventoryShiftPO.setArrivalPlan(inventoryShiftPO.getDeliveryPlan() + inventoryShiftPO.getReceive());
                    inventoryShiftPO.setEndingInventory(inventoryShiftPO.getEndingInventory() - beforeQuantity);
                    endingInventory = inventoryShiftPO.getEndingInventory();
                }
                inventoryShiftPOListCreate.add(inventoryShiftPO);
                continue;
            }
            // 计算期望期末库存天数
            if (endInventoryDaysList.isEmpty()) {
                endInventoryDaysList = getExpectEndingInventoryDays(deliveryPlanLockConfigVO,
                        deliveryPlanReplenishConfigVO,
                        endingInventoryDays);
            }
            BigDecimal expectEndingInventoryDays = endInventoryDaysList.removeFirst();
            // 根据期望库存天数反算期望的期末库存
            int expectEndingInventory = getExpectEndingInventory(dateList, demandSumGroupByDay, i,
                    expectEndingInventoryDays);
            log.info("MTS, 主机厂:{}, 产品编码:{}, 当前date:{}, deliveryPlanStartDate:{}, 是否新增deliveryPlanDetailPO:{},期望库存天数:{},期望库存数量:{}",
                    deliveryPlanPO.getOemCode(), deliveryPlanPO.getProductCode(), DateUtils.dateToString(date),
                    DateUtils.dateToString(deliveryPlanStartDate), !deliveryPlanStartDate.before(dateList.get(0)),
                    expectEndingInventoryDays, expectEndingInventory);

            // 期望库存减去实际的期初库存就是发货计划量
            int replenishPlanQty =
                    expectEndingInventory + customerDemand - (openingInventoryTemp + oemOpeningInventoryTemp) - receiveQuantity;
            replenishPlanQty = Math.max(replenishPlanQty, 0);
            if (deliveryPlanDetailPO == null) {
                deliveryPlanDetailPO = new DeliveryPlanDetailPO();
                if (!deliveryPlanStartDate.before(dateList.get(0))) {
                    deliveryPlanDetailPOListCreate.add(deliveryPlanDetailPO);
                }
            }
            deliveryPlanDetailPOFlag = false;
            deliveryPlanDetailPO.setDeliveryPlanDataId(deliveryPlanPO.getId());
            // 发货计划量
            deliveryPlanDetailPO.setDemandQuantity(replenishPlanQty);
            deliveryPlanDetailPO.setDemandTime(deliveryPlanStartDate);
            InventoryShiftPO inventoryShiftPO = getInventoryShiftPO(receiveQuantity, deliveryPlanPO, dateList,
                    demandSumGroupByDay,
                    safetyStockLevelVO, date, openingInventoryTemp, demandQuantity, deliveryPlanDetailPO,
                    deliveryPlanDetailPOFlag, endingInventory, deliveryPlanStartDate, expectEndingInventoryDays,
                    endingInventoryDays, expectEndingInventory, i, computeBatchFlag);
            inventoryShiftPOListCreate.add(inventoryShiftPO);
            
            int replenishPlanQty2 = deliveryPlanDetailPO.getDemandQuantity();
            Boolean loosePieceFlag = false;
            if(StringUtils.isNotBlank(fullBoxFlag) && YesOrNoEnum.YES.getCode().equals(fullBoxFlag) 
            		&& standardLoad != null && standardLoad > 0) {
            	BigDecimal boxNum = BigDecimal.valueOf(replenishPlanQty2).divide(BigDecimal.valueOf(standardLoad), 0 ,BigDecimal.ROUND_UP);
            	replenishPlanQty2 = boxNum.multiply(BigDecimal.valueOf(standardLoad)).intValue();
            } else if(StringUtils.isNotBlank(fullBoxFlag) && YesOrNoEnum.NO.getCode().equals(fullBoxFlag)) {
            	//散片逻辑
            	replenishPlanQty2 = deliveryPlanDetailPO.getDemandQuantity();
            	loosePieceFlag = true;
            } else {
            	// 原整箱发货逻辑
                int unfulfilledEnding = openingInventoryTemp + oemOpeningInventoryTemp - customerDemand + receiveQuantity;
                replenishPlanQty2 = getNormalReplenishPlanQty(endingInventoryDateList, i, demandSumGroupByDay, deliveryPlanDetailPO,
                        deliveryPlanPO, materialRiskLevelVOMapOfJoinKey,
                        productBoxRelationVOMapOfProductCode, boxInfoVOMapOfId, safetyStockLevelVO,
                        productBoxRelationWarningList, unfulfilledEnding, newProductStockPointVO, oemHolidayList);
                //获取零件风险等级，处理原逻辑散片
                String joinKey = String.join("&", deliveryPlanPO.getOemCode(), deliveryPlanPO.getProductCode());
                PartRiskLevelVO partRiskLevelVO = materialRiskLevelVOMapOfJoinKey.get(joinKey);
                if(partRiskLevelVO != null && StringUtils.equals("高", partRiskLevelVO.getMaterialRiskLevel())) {
                	loosePieceFlag = true;
                }
            }
            
            // 计算期末库存和期末库存天数
            // 计算期末库存：期初库存+在途数据-客户需求
            int totalEndingInventory =
                    openingInventoryTemp + oemOpeningInventoryTemp + replenishPlanQty2 + receiveQuantity - customerDemand;
            totalEndingInventory = Math.max(totalEndingInventory, 0);
            // 当天期末库存天数
            endingInventoryDays = getEndingInventoryDays(endingInventoryDateList, demandSumGroupByDay, totalEndingInventory, 
            		i, oemHolidayList);
            
            //库存天数，发货数量重新计算处理（库存天数补货逻辑）
            BigDecimal standardSafetyInventoryDays = inventoryShiftPO.getStandardSafetyInventoryDays();
			BigDecimal minimumSafetyInventoryDays = inventoryShiftPO.getMinimumSafetyInventoryDays();
			if(standardSafetyInventoryDays != null && minimumSafetyInventoryDays != null) {
				//发货计划补货策略
				BigDecimal normalStrategy = BigDecimal.valueOf(0.5);
	            if(deliveryPlanReplenishConfigVO != null) {
	            	normalStrategy = deliveryPlanReplenishConfigVO.getNormalStrategy();
	            }
	            BigDecimal subStandardSafetyInventoryDays = standardSafetyInventoryDays.subtract(normalStrategy);
	            if(date.after(new Date()) && endingInventoryDays.compareTo(standardSafetyInventoryDays) < 0) {
	            	//添加补库时间，如果算出的期末库存天数小于等于补库时间，则跳出循环
	            	BigDecimal addEndingInventoryDays = endingInventoryDays.add(normalStrategy);
	            	if (endingInventoryDays.compareTo(minimumSafetyInventoryDays) <= 0) {
	    		        if (dayBeforeEndingInventoryDays == null || dayBeforeEndingInventoryDays.compareTo(subStandardSafetyInventoryDays) < 0) {
	    		        	addEndingInventoryDays = (dayBeforeEndingInventoryDays == null ? minimumSafetyInventoryDays : dayBeforeEndingInventoryDays).add(normalStrategy);
	    		        } else {
	    		        	addEndingInventoryDays = standardSafetyInventoryDays;
	    		        }
	    		    } else if (endingInventoryDays.compareTo(subStandardSafetyInventoryDays) <= 0) {
	    		        if (dayBeforeEndingInventoryDays == null || dayBeforeEndingInventoryDays.compareTo(subStandardSafetyInventoryDays) < 0) {
	    		        	addEndingInventoryDays = (dayBeforeEndingInventoryDays == null ? endingInventoryDays : dayBeforeEndingInventoryDays).add(normalStrategy);
	    		        } else { 
	    		        	addEndingInventoryDays = standardSafetyInventoryDays;
	    		        }
	    		    } else if (endingInventoryDays.compareTo(standardSafetyInventoryDays) < 0) {
	    		    	addEndingInventoryDays = standardSafetyInventoryDays;
	    		    }
	            	if(addEndingInventoryDays.compareTo(endingInventoryDays) < 0) {
	            		addEndingInventoryDays = endingInventoryDays;
	            	}
	            	Integer needaddDemandQuantity = getNeedaddDemandQuantity(endingInventoryDateList, demandSumGroupByDay, 
	            			addEndingInventoryDays, totalEndingInventory, i, oemHolidayList);
	            	if(needaddDemandQuantity < 0) {
	            		needaddDemandQuantity = 0;
	            	}
	            	if(standardLoad != null && standardLoad > 0 && !loosePieceFlag){
                		//成箱补货逻辑
	            		BigDecimal multiply = BigDecimal.valueOf(needaddDemandQuantity)
	            				.divide(BigDecimal.valueOf(standardLoad), 0, BigDecimal.ROUND_UP).multiply(BigDecimal.valueOf(standardLoad));
	            		replenishPlanQty2 = replenishPlanQty2 + multiply.intValue();
	            		totalEndingInventory = totalEndingInventory + multiply.intValue();
                	}else {
                		//散片补货逻辑
                		replenishPlanQty2 = replenishPlanQty2 + needaddDemandQuantity;
                		totalEndingInventory = totalEndingInventory + needaddDemandQuantity;
                	}
                	// 当天期末库存天数
	            	endingInventoryDays = getEndingInventoryDays(endingInventoryDateList, demandSumGroupByDay, totalEndingInventory, i, oemHolidayList);
	            }
			}
            
            if (deliveryPlanStartDate.before(dateList.get(0))) {
                beforeQuantity = beforeQuantity + replenishPlanQty2;
            }
            if (deliveryPlanStartDate.equals(dateList.get(0))) {
                if (beforeQuantity > 0) {
                    deliveryPlanDetailPO.setUrgentSign(YesOrNoEnum.YES.getCode());
                }
                deliveryPlanDetailPO.setDemandQuantity(beforeQuantity + replenishPlanQty2);
            } else {
                deliveryPlanDetailPO.setDemandQuantity(replenishPlanQty2);
            }
            //重新维护箱数，成箱描述
            if(standardLoad != null && standardLoad > 0) {
            	BigDecimal boxNum = BigDecimal.valueOf(deliveryPlanDetailPO.getDemandQuantity()).divide(BigDecimal.valueOf(standardLoad), 0 ,BigDecimal.ROUND_UP);
            	deliveryPlanDetailPO.setBoxQuantity(boxNum.intValue());
            }
            BoxInfoVO boxInfoVO = boxInfoVOMapOfId.get(productBoxRelationVO.getBoxId());
            setBoxDesc(deliveryPlanDetailPO, boxInfoVO, deliveryPlanDetailPO.getDemandQuantity(), productBoxRelationVO);
            endingInventory = totalEndingInventory - oemEndingInventory;
            log.info("发货计划计算,日期：{}, 整箱发货数量:{}, 期末库存数量:{}", DateUtils.dateToString(date), replenishPlanQty2,
                    endingInventory);
            inventoryShiftPO.setDeliveryDate(deliveryPlanStartDate);
            inventoryShiftPO.setArrivalPlan(replenishPlanQty2 + receiveQuantity);
            inventoryShiftPO.setDeliveryPlan(replenishPlanQty2);
            inventoryShiftPO.setReceive(receiveQuantity);
            inventoryShiftPO.setEndingInventoryDays(endingInventoryDays);
            inventoryShiftPO.setEndingInventory(endingInventory);
            Integer minInventoryLevel = getInventoryLevel(i, safetyStockLevelVO.getMinStockDay(), dateList,
                    demandSumGroupByDay);
            inventoryShiftPO.setEndingInventoryMinSafeDiff(endingInventory - minInventoryLevel);
            inventoryShiftPO.setOemEndingInventory(oemEndingInventory);
            inventoryShiftPO.setOemOpeningInventory(oemOpeningInventoryTemp);
            endingInventory = inventoryShiftPO.getEndingInventory();
            oemEndingInventory = inventoryShiftPO.getOemEndingInventory();
            //处理前一天的期末库存天数（如果历史天有大于等于标准库存天数的数据，则默认都补到标准库存天数）
            if(Objects.equals(DateUtils.dateToString(inventoryShiftPO.getPlannedDate()), DateUtils.dateToString(new Date()))
            		|| inventoryShiftPO.getPlannedDate().after(new Date())) {
            	if(standardSafetyInventoryDays != null && inventoryShiftPO.getEndingInventoryDays().compareTo(standardSafetyInventoryDays) >= 0 ) {
    				dayBeforeEndingInventoryDays = standardSafetyInventoryDays;
    			}else {
    				dayBeforeEndingInventoryDays = inventoryShiftPO.getEndingInventoryDays();
    			}
            }
        }
    }

    /**
     * 处理期末库存天数
     * 1.如果第一天【期末库存天数】大于等于【标准安全库存天数】，则每天在客户需求扣减完成后，维持每天【期末库存天数 = 标准安全库存天数】
     * 2.如果第一天的【期末库存天数】 小于等于【标准安全库存天数】 - 【补库策略】，则每天固定补 【补库策略】，直至补至【标准安全库存天数】
     * 3.如果第一天的 【期末库存天数】 大于【标准安全库存天数 - 补库策略】，则补至【标准安全库存天数】
     * 
     * 假设【最小安全库存天数】为1，【标准安全库存天数】为5，【补库策略】为0.5，如果第一天【期初库存】-【客户需求】之后的【期末库存天数】为
     * 3.7，  3.7 < 5-0.5，符合条件2，则第二天的期末库存天数为4.2，4.2 < 5-0.5，符合条件2，第三天的期末库存天数为4.7，符合条件3，此时第四天的期末
     * 库存天数为5，符合条件1，然后每天【期末库存天数 = 标准安全库存天数】
     * 
     * @param nextDayEndingInventoryDays
     * @param standardSafetyInventoryDays
     * @param subStandardSafetyInventoryDays
     * @param date
     * @param inventoryShiftPO
     * @param normalStrategy 
     * @return
     */
	private BigDecimal getNewEndingInventoryDays(BigDecimal nextDayEndingInventoryDays,
			BigDecimal standardSafetyInventoryDays, BigDecimal subStandardSafetyInventoryDays, Date date,
			InventoryShiftPO inventoryShiftPO, BigDecimal normalStrategy) {
		if(nextDayEndingInventoryDays == null && Objects.equals(DateUtils.dateToString(date), DateUtils.dateToString(new Date()))) {
			//计算次日的期末库存天数
			BigDecimal currentEndingInventoryDays = inventoryShiftPO.getEndingInventoryDays();
			if(currentEndingInventoryDays.compareTo(standardSafetyInventoryDays) >= 0 
					|| currentEndingInventoryDays.compareTo(subStandardSafetyInventoryDays) > 0){
				nextDayEndingInventoryDays = standardSafetyInventoryDays;
			}else if (currentEndingInventoryDays.compareTo(subStandardSafetyInventoryDays) <= 0) {
				nextDayEndingInventoryDays = currentEndingInventoryDays.add(normalStrategy);
			}
		}else if(nextDayEndingInventoryDays != null && date.after(new Date())) {
			inventoryShiftPO.setEndingInventoryDays(nextDayEndingInventoryDays);
			if(nextDayEndingInventoryDays.compareTo(standardSafetyInventoryDays) >= 0 
					|| nextDayEndingInventoryDays.compareTo(subStandardSafetyInventoryDays) > 0){
				nextDayEndingInventoryDays = standardSafetyInventoryDays;
			}else if (nextDayEndingInventoryDays.compareTo(subStandardSafetyInventoryDays) <= 0) {
				nextDayEndingInventoryDays = nextDayEndingInventoryDays.add(normalStrategy);
			}
		}
		return nextDayEndingInventoryDays;
	}

    private Integer getReceiveQuantity(Date date,
                                       Integer supplyDays,
                                       List<WarehouseReleaseToWarehouseVO> warehouseReleaseRecordList,
                                       List<OemInventorySubmissionVO> receiveOemInventorySubmissionVOList,
                                       Map<String, BigDecimal> shippingListNumberMap) {
        int receiveQuantity = 0;
        if (CollectionUtils.isNotEmpty(warehouseReleaseRecordList)) {
            for (WarehouseReleaseToWarehouseVO warehouseReleaseRecordVO : warehouseReleaseRecordList) {
                if (warehouseReleaseRecordVO.getActualCompletionTime() != null) {
                    continue;
                }
                // 如果预计到达时间有值，实际到达时间没值
                if (warehouseReleaseRecordVO.getEstimatedCompletionTime() != null
                        && DateUtils.getDayFirstTime(warehouseReleaseRecordVO.getEstimatedCompletionTime()).compareTo(date) == 0) {
                    receiveQuantity += warehouseReleaseRecordVO.getSumQty().intValue();
                }

                // 预计到达时间没有值
                if (warehouseReleaseRecordVO.getEstimatedCompletionTime() == null
                        && DateUtils.moveDay(DateUtils.getDayFirstTime(warehouseReleaseRecordVO.getCreationDate()),
                        supplyDays).compareTo(date) == 0) {
                    receiveQuantity += warehouseReleaseRecordVO.getSumQty().intValue();

                    if (shippingListNumberMap != null && shippingListNumberMap.containsKey(warehouseReleaseRecordVO.getListNum())) {
                        receiveQuantity = receiveQuantity
                                - shippingListNumberMap.get(warehouseReleaseRecordVO.getListNum()).intValue();
                        if (receiveQuantity < 0) {
                            receiveQuantity = 0;
                        }
                    }
                }

            }
        }

        if (CollectionUtils.isEmpty(receiveOemInventorySubmissionVOList)) {
            return receiveQuantity;
        }
        for (OemInventorySubmissionVO oemInventorySubmissionVO : receiveOemInventorySubmissionVOList) {
            if (oemInventorySubmissionVO.getSubmissionDate() != null && oemInventorySubmissionVO.getTransitWaitQuantity() != null && DateUtils.getDayFirstTime(oemInventorySubmissionVO.getSubmissionDate()).compareTo(date) == 0) {
                receiveQuantity += oemInventorySubmissionVO.getTransitWaitQuantity().intValue();
            }
        }
        return receiveQuantity;
    }

    private void setReplenishPlanQty4Save(
            DeliveryPlanDetailPO deliveryPlanDetailPO,
            DeliveryPlanPO deliveryPlanPO,
            Map<String, PartRiskLevelVO> materialRiskLevelVOMapOfJoinKey,
            Map<String, ProductBoxRelationVO> productBoxRelationVOMapOfProductCode,
            Map<String, BoxInfoVO> boxInfoVOMapOfId) {
        if (null == deliveryPlanDetailPO) {
            return;
        }
        // 获取发货计划量
        Integer demandQuantity = deliveryPlanDetailPO.getDemandQuantity();
        // 获取零件风险等级
        String joinKey = String.join("&", deliveryPlanPO.getOemCode(), deliveryPlanPO.getProductCode());
        PartRiskLevelVO partRiskLevelVO = materialRiskLevelVOMapOfJoinKey.get(joinKey);


        // 获取零件与箱体的关系
        ProductBoxRelationVO productBoxRelationVO = productBoxRelationVOMapOfProductCode
                .getOrDefault(deliveryPlanPO.getProductCode(), new ProductBoxRelationVO());
        // 获取箱体信息
        BoxInfoVO boxInfoVO = boxInfoVOMapOfId.getOrDefault(productBoxRelationVO.getBoxId(), new BoxInfoVO());
        Integer piecePerBox = productBoxRelationVO.getStandardLoad();
        Integer boxPerRow = boxInfoVO.getBoxPerRow();
        // 维护单箱片数
        deliveryPlanPO.setPiecePerBox(piecePerBox);
        if (null == partRiskLevelVO || StringUtils.equals("高", partRiskLevelVO.getMaterialRiskLevel())) {
            return;
        }
        int unit = 0;
        if ("低".equals(partRiskLevelVO.getMaterialRiskLevel())) {
            // 单排箱数 * 单箱片数
            unit = piecePerBox == null ? 1 : piecePerBox * (boxPerRow == null ? 1 : boxPerRow);
        }

        // 中风险只看 单箱片数
        if ("中".equals(partRiskLevelVO.getMaterialRiskLevel())) {
            unit = piecePerBox == null ? 1 : piecePerBox;
        }


        // 发货数量 / unit
        int boxCountTemp = unit == 0 ? 0 : (demandQuantity / unit);
        int remainder = unit == 0 ? 0 : (demandQuantity % unit);
        if (remainder == 0) {
            // 为整数则不处理
            deliveryPlanDetailPO.setBoxQuantity(boxCountTemp);
            return;
        }
        // 余数凑成整箱，向上取整
        int boxCount = boxCountTemp + 1;
        deliveryPlanDetailPO.setBoxQuantity(boxCount);
    }

    private void setReplenishPlanQty4Mto(
            DeliveryPlanDetailPO deliveryPlanDetailPO,
            DeliveryPlanPO deliveryPlanPO,
            Map<String, PartRiskLevelVO> materialRiskLevelVOMapOfJoinKey,
            Map<String, List<ProductBoxRelationVO>> productBoxRelationVOMapOfProductCode,
            Map<String, BoxInfoVO> boxInfoVOMapOfId, List<String> productBoxRelationWarningList) {
        if (null == deliveryPlanDetailPO) {
            return;
        }
        // 获取发货计划量
        Integer demandQuantity = deliveryPlanDetailPO.getDemandQuantity();
        // 获取零件风险等级
        String joinKey = String.join("&", deliveryPlanPO.getOemCode(), deliveryPlanPO.getProductCode());
        PartRiskLevelVO partRiskLevelVO = materialRiskLevelVOMapOfJoinKey.get(joinKey);

        // 获取零件与箱体的关系
        ProductBoxRelationVO productBoxRelationVO = new ProductBoxRelationVO();
        List<ProductBoxRelationVO> productBoxRelationVOList =
                productBoxRelationVOMapOfProductCode.get(deliveryPlanPO.getProductCode());

        if (CollectionUtils.isNotEmpty(productBoxRelationVOList)) {
            List<ProductBoxRelationVO> filterProductBoxRelationVOList =
                    productBoxRelationVOList.stream().filter(e -> e.getPriority() != null).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterProductBoxRelationVOList)) {
                productBoxRelationVO = productBoxRelationVOList.get(0);
                if (!productBoxRelationWarningList.contains(deliveryPlanPO.getProductCode()) && productBoxRelationVOList.size() > 1) {
                    productBoxRelationWarningList.add(deliveryPlanPO.getProductCode());
                }
            } else {
                Optional<ProductBoxRelationVO> maxOptional =
                        filterProductBoxRelationVOList.stream().max(Comparator.comparing(ProductBoxRelationVO::getPriority));
                if (maxOptional.isPresent()) {
                    productBoxRelationVO = maxOptional.get();
                }
            }
        }

        // 获取箱体信息
        BoxInfoVO boxInfoVO = boxInfoVOMapOfId.getOrDefault(productBoxRelationVO.getBoxId(), new BoxInfoVO());
        // 维护单箱片数
        Integer piecePerBox = productBoxRelationVO.getStandardLoad();
        Integer boxPerRow = boxInfoVO.getBoxPerRow();
        deliveryPlanPO.setPiecePerBox(piecePerBox);
        if (null == partRiskLevelVO || StringUtils.equals("高",
                partRiskLevelVO.getMaterialRiskLevel())) {
            return;
        }
        int unit = 0;
        if ("低".equals(partRiskLevelVO.getMaterialRiskLevel())) {
            // 单排箱数 * 单箱片数
            unit = piecePerBox == null ? 1 : piecePerBox * (boxPerRow == null ? 1 : boxPerRow);
        }

        // 中风险只看 单箱片数
        if ("中".equals(partRiskLevelVO.getMaterialRiskLevel())) {
            unit = piecePerBox == null ? 1 : piecePerBox;
        }

        // 发货数量 / unit
        int boxCountTemp = unit == 0 ? 0 : (demandQuantity / unit);
        int remainder = unit == 0 ? 0 : (demandQuantity % unit);
        if (remainder == 0) {
            // 为整数则不处理
            deliveryPlanDetailPO.setBoxQuantity(boxCountTemp);
            return;
        }
        // 余数凑成整箱，向上取整
        int boxCount = boxCountTemp + 1;
        deliveryPlanDetailPO.setDemandQuantity(unit * boxCount);
        deliveryPlanDetailPO.setBoxQuantity(boxCount);
    }

    private Integer getNormalReplenishPlanQty(List<Date> dateList, int dateIndex,
                                              Map<String, Integer> demandSumGroupByDay,
                                              DeliveryPlanDetailPO deliveryPlanDetailPO,
                                              DeliveryPlanPO deliveryPlanPO,
                                              Map<String, PartRiskLevelVO> materialRiskLevelVOMapOfJoinKey,
                                              Map<String, List<ProductBoxRelationVO>> productBoxRelationVOMapOfProductCode,
                                              Map<String, BoxInfoVO> boxInfoVOMapOfId,
                                              SafetyStockLevelVO safetyStockLevelVO,
                                              List<String> productBoxRelationWarningList, int endingInventory,
                                              NewProductStockPointVO newProductStockPointVO, List<String> oemHolidayList) {
        if (null == deliveryPlanDetailPO) {
            return 0;
        }
        // 获取发货计划量
        Integer demandQuantity = deliveryPlanDetailPO.getDemandQuantity();
        if (null == demandQuantity || demandQuantity == 0) {
            return 0;
        }
        // 获取零件风险等级
        String joinKey = String.join("&", deliveryPlanPO.getOemCode(), deliveryPlanPO.getProductCode());
        PartRiskLevelVO partRiskLevelVO = materialRiskLevelVOMapOfJoinKey.get(joinKey);

        // 获取零件与箱体的关系
        ProductBoxRelationVO productBoxRelationVO = new ProductBoxRelationVO();
        List<ProductBoxRelationVO> productBoxRelationVOList =
                productBoxRelationVOMapOfProductCode.get(deliveryPlanPO.getProductCode());
        if (CollectionUtils.isNotEmpty(productBoxRelationVOList)) {
            productBoxRelationVO = productBoxRelationVOList.get(0);
        }
        BoxInfoVO boxInfoVO = boxInfoVOMapOfId.get(productBoxRelationVO.getBoxId());
        if (partRiskLevelVO != null && StringUtils.equals("高", partRiskLevelVO.getMaterialRiskLevel())) {
            if (productBoxRelationVO.getStandardLoad() != null) {
                Integer perBox = productBoxRelationVO.getStandardLoad();
                deliveryPlanPO.setPiecePerBox(perBox);
            }
            setBoxDesc(deliveryPlanDetailPO, boxInfoVO, demandQuantity, productBoxRelationVO);
            return demandQuantity;
        }
        //int endingInventory = 0;
        // 获取箱体信息

        if (productBoxRelationVO.getStandardLoad() == null || productBoxRelationVO.getStandardLoad() == 0) {
            setBoxDesc(deliveryPlanDetailPO, boxInfoVO, demandQuantity, productBoxRelationVO);
            return demandQuantity;
        }
        Integer piecePerBox = productBoxRelationVO.getStandardLoad();
        // 维护单箱片数
        deliveryPlanPO.setPiecePerBox(piecePerBox);

        int unit = piecePerBox;

        // 发货数量 / unit
        int boxCountTemp = (demandQuantity / unit);
        if (boxCountTemp == 0 && newProductStockPointVO != null && newProductStockPointVO.getProductEop() != null) {
            Date date = dateList.get(dateIndex);
            if (date.compareTo(newProductStockPointVO.getProductEop()) >= 0) {
                setBoxDesc(deliveryPlanDetailPO, boxInfoVO, demandQuantity, productBoxRelationVO);
                return demandQuantity;
            }
        }
        int remainder = (demandQuantity % unit);
        int boxCount = boxCountTemp;
        if (remainder > 0) {
            // 余数凑成整箱，向上取整
            boxCount = boxCountTemp + 1;
        }

        int replenishPlanQty = unit * boxCount;

        // 进行向上/向下取整， 整数不需要取整
        // 如向上取整后当天推演期末库存天数＞标准安全库存天数，则进行向下取整，
        // 如向下取整后当天推演期末库存天数＜最小安全库存天数，则进行向上取整，如出现反复，则向上取整；

        // 推演期末库存天数  用"到货计划量"去往后推演
        BigDecimal endingInventoryDays = BigDecimal.ZERO;
        if (MapUtils.isNotEmpty(demandSumGroupByDay)) {
            endingInventoryDays = getEndingInventoryDays(dateList, demandSumGroupByDay, replenishPlanQty + endingInventory, dateIndex, oemHolidayList);
        }

        BigDecimal standardStockDay = BigDecimal.ZERO;
        BigDecimal minStockDay = BigDecimal.ZERO;
        if (null != safetyStockLevelVO) {
            // 标准安全库存天数
            standardStockDay = safetyStockLevelVO.getStandardStockDay();
            // 最小安全库存天数
            minStockDay = safetyStockLevelVO.getMinStockDay();
        }

        if (endingInventoryDays.doubleValue() > standardStockDay.doubleValue()) {
            // 向下取整
            boxCount = boxCountTemp;
            if (boxCount == 0) {
                boxCount = 1;
                replenishPlanQty = unit * boxCount;
                deliveryPlanDetailPO.setBoxQuantity(boxCount);
                setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
                return replenishPlanQty;
            }
            replenishPlanQty = unit * boxCount;
            // 向下取整的发货计划量推演期末库存
            endingInventoryDays = getEndingInventoryDays(dateList, demandSumGroupByDay, replenishPlanQty + endingInventory,
                    dateIndex, oemHolidayList);
            // 向下取整的发货计划量期末库存天数小于最小安全库存天数，则向上取整
            if (endingInventoryDays.doubleValue() <= minStockDay.doubleValue()) {
                //int ceilValue = boxCountTemp + 1;
                boxCount = boxCountTemp + 1;
                deliveryPlanDetailPO.setBoxQuantity(boxCount);
                replenishPlanQty = unit * boxCount;
                setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
                return replenishPlanQty;
            }
        } else if (boxCountTemp == 0) {
            deliveryPlanDetailPO.setBoxQuantity(boxCount);
            setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
            return replenishPlanQty;
        }

        if (boxInfoVO == null ||
                boxInfoVO.getPerStackQuantity() == null || boxInfoVO.getPerStackQuantity() == 0 || boxCount == 0) {
            deliveryPlanDetailPO.setBoxQuantity(boxCount);
            setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
            return replenishPlanQty;
        }

        int stackRemainder = boxCount % boxInfoVO.getPerStackQuantity();
        unit = piecePerBox * boxInfoVO.getPerStackQuantity();
        int stackCountTemp = (boxCount / boxInfoVO.getPerStackQuantity());
        int stackCount = stackCountTemp;
        if (stackRemainder > 0) {
            stackCount = stackCountTemp + 1;
        }

        replenishPlanQty = unit * stackCount;
        endingInventoryDays = getEndingInventoryDays(dateList, demandSumGroupByDay, replenishPlanQty + endingInventory,
                dateIndex, oemHolidayList);
        //取整垛之后的比较
        if (endingInventoryDays.doubleValue() > standardStockDay.doubleValue()) {
            stackCount = stackCountTemp;
            if (stackCount == 0) {
                deliveryPlanDetailPO.setBoxQuantity(boxCount);
                setBoxDesc(deliveryPlanDetailPO, boxInfoVO, piecePerBox * boxCount, productBoxRelationVO);
                return piecePerBox * boxCount;
            }
            replenishPlanQty = unit * stackCount;
            endingInventoryDays = getEndingInventoryDays(dateList, demandSumGroupByDay, replenishPlanQty + endingInventory,
                    dateIndex, oemHolidayList);
            //垛数向下取整小于最小安全库存天数，则返回整箱
            if (endingInventoryDays.doubleValue() < minStockDay.doubleValue()) {
                replenishPlanQty = piecePerBox * boxCount;
                deliveryPlanDetailPO.setBoxQuantity(boxCount);
                setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
                return replenishPlanQty;
            }

        }
        if (boxInfoVO.getBoxPerRow() == null || boxInfoVO.getBoxPerRow() == 0) {
            deliveryPlanDetailPO.setBoxQuantity(boxInfoVO.getPerStackQuantity() * stackCount);
            setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
            return replenishPlanQty;
        }
        int rowRemainder = boxCount % boxInfoVO.getBoxPerRow();
        int rowCountTemp = (boxCount / boxInfoVO.getBoxPerRow());
        int rowCount = rowCountTemp;
        if (rowRemainder > 0) {
            rowCount = rowCountTemp + 1;
        }

        unit = piecePerBox * boxInfoVO.getBoxPerRow();
        replenishPlanQty = unit * rowCount;
        endingInventoryDays = getEndingInventoryDays(dateList, demandSumGroupByDay, replenishPlanQty + endingInventory,
                dateIndex, oemHolidayList);
        //整排整垛之后的比较
        if (endingInventoryDays.doubleValue() > standardStockDay.doubleValue()) {
            rowCount = rowCountTemp;
            if (rowCount == 0) {
                replenishPlanQty = piecePerBox * boxInfoVO.getPerStackQuantity() * stackCount;
                deliveryPlanDetailPO.setBoxQuantity(boxInfoVO.getPerStackQuantity() * stackCount);
                setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
                return replenishPlanQty;
            }
            replenishPlanQty = unit * rowCount;
            endingInventoryDays = getEndingInventoryDays(dateList, demandSumGroupByDay, replenishPlanQty + endingInventory,
                    dateIndex, oemHolidayList);
            if (endingInventoryDays.doubleValue() < minStockDay.doubleValue()) {
                replenishPlanQty = piecePerBox * boxInfoVO.getPerStackQuantity() * stackCount;
                deliveryPlanDetailPO.setBoxQuantity(boxInfoVO.getPerStackQuantity() * stackCount);
                setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
                return replenishPlanQty;
            }
        }

        // if (endingInventoryDays.doubleValue() <= minStockDay.doubleValue()) {
        //     // 向上取整
        //     int ceilValue = boxCountTemp + 1;
        //     deliveryPlanDetailPO.setBoxQuantity(ceilValue);
        //     return ceilValue * unit;
        // }
        boxCount = rowCount * boxInfoVO.getBoxPerRow();
        deliveryPlanDetailPO.setBoxQuantity(boxCount);
        setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
        return replenishPlanQty;
    }

    private ProductBoxRelationVO getProductBoxRelationVO(String productCode, List<String> productBoxRelationWarningList,
                                                         Map<String, List<ProductBoxRelationVO>> productBoxRelationVOMapOfProductCode) {
        // 获取零件与箱体的关系
        ProductBoxRelationVO productBoxRelationVO = new ProductBoxRelationVO();
        List<ProductBoxRelationVO> productBoxRelationVOList =
                productBoxRelationVOMapOfProductCode.get(productCode);
        if (CollectionUtils.isNotEmpty(productBoxRelationVOList)) {
            productBoxRelationVO = productBoxRelationVOList.get(0);
        }
        return productBoxRelationVO;
    }

    private Integer getNormalReplenishPlanQtyWithTime(List<Date> dateList, int dateIndex,
                                                      Map<String, Integer> demandSumGroupByDay,
                                                      DeliveryPlanDetailPO deliveryPlanDetailPO,
                                                      DeliveryPlanPO deliveryPlanPO,
                                                      Map<String, PartRiskLevelVO> materialRiskLevelVOMapOfJoinKey,
                                                      Map<String, List<ProductBoxRelationVO>> productBoxRelationVOMapOfProductCode,
                                                      Map<String, BoxInfoVO> boxInfoVOMapOfId,
                                                      SafetyStockLevelVO safetyStockLevelVO,
                                                      List<String> productBoxRelationWarningList, int endingInventory,
                                                      NewProductStockPointVO newProductStockPointVO, BigDecimal diffDay,
                                                      List<String> oemHolidayList) {
        if (null == deliveryPlanDetailPO) {
            return 0;
        }
        // 获取发货计划量
        Integer demandQuantity = deliveryPlanDetailPO.getDemandQuantity();
        if (null == demandQuantity || demandQuantity == 0) {
            return 0;
        }
        // 获取零件风险等级
        String joinKey = String.join("&", deliveryPlanPO.getOemCode(), deliveryPlanPO.getProductCode());
        PartRiskLevelVO partRiskLevelVO = materialRiskLevelVOMapOfJoinKey.get(joinKey);

        // 获取零件与箱体的关系
        ProductBoxRelationVO productBoxRelationVO = new ProductBoxRelationVO();
        List<ProductBoxRelationVO> productBoxRelationVOList =
                productBoxRelationVOMapOfProductCode.get(deliveryPlanPO.getProductCode());
        if (CollectionUtils.isNotEmpty(productBoxRelationVOList)) {
            productBoxRelationVO = productBoxRelationVOList.get(0);
        }
        BoxInfoVO boxInfoVO = boxInfoVOMapOfId.get(productBoxRelationVO.getBoxId());
        if (partRiskLevelVO != null && StringUtils.equals("高", partRiskLevelVO.getMaterialRiskLevel())) {
            if (productBoxRelationVO.getStandardLoad() != null) {
                Integer perBox = productBoxRelationVO.getStandardLoad();
                deliveryPlanPO.setPiecePerBox(perBox);
            }
            setBoxDesc(deliveryPlanDetailPO, boxInfoVO, demandQuantity, productBoxRelationVO);
            return demandQuantity;
        }
        //int endingInventory = 0;
        // 获取箱体信息

        if (productBoxRelationVO.getStandardLoad() == null || productBoxRelationVO.getStandardLoad() == 0) {
            setBoxDesc(deliveryPlanDetailPO, boxInfoVO, demandQuantity, productBoxRelationVO);
            return demandQuantity;
        }
        Integer piecePerBox = productBoxRelationVO.getStandardLoad();
        // 维护单箱片数
        deliveryPlanPO.setPiecePerBox(piecePerBox);

        int unit = piecePerBox;

        // 发货数量 / unit
        int boxCountTemp = (demandQuantity / unit);
        if (boxCountTemp == 0 && newProductStockPointVO != null && newProductStockPointVO.getProductEop() != null) {
            Date date = dateList.get(dateIndex);
            if (date.compareTo(newProductStockPointVO.getProductEop()) >= 0) {
                setBoxDesc(deliveryPlanDetailPO, boxInfoVO, demandQuantity, productBoxRelationVO);
                return demandQuantity;
            }
        }
        int remainder = (demandQuantity % unit);
        int boxCount = boxCountTemp;
        if (remainder > 0) {
            // 余数凑成整箱，向上取整
            boxCount = boxCountTemp + 1;
        }

        int replenishPlanQty = unit * boxCount;

        // 进行向上/向下取整， 整数不需要取整
        // 如向上取整后当天推演期末库存天数＞标准安全库存天数，则进行向下取整，
        // 如向下取整后当天推演期末库存天数＜最小安全库存天数，则进行向上取整，如出现反复，则向上取整；

        // 推演期末库存天数  用"到货计划量"去往后推演
        BigDecimal endingInventoryDays = BigDecimal.ZERO;
        if (MapUtils.isNotEmpty(demandSumGroupByDay)) {
            endingInventoryDays = getEndingInventoryDays(dateList, demandSumGroupByDay, replenishPlanQty + endingInventory, dateIndex, oemHolidayList);
        }

        BigDecimal standardStockDay = BigDecimal.ZERO;
        BigDecimal minStockDay = BigDecimal.ZERO;
        if (null != safetyStockLevelVO) {
            // 标准安全库存天数
            standardStockDay = safetyStockLevelVO.getStandardStockDay();
            // 最小安全库存天数
            minStockDay = safetyStockLevelVO.getMinStockDay();
        }
        if (endingInventoryDays.compareTo(diffDay) > 0) {
            replenishPlanQty = demandQuantity;
            deliveryPlanDetailPO.setBoxQuantity(replenishPlanQty / piecePerBox);
            setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
            return replenishPlanQty;
        }
        if (endingInventoryDays.doubleValue() > standardStockDay.doubleValue()) {
            // 向下取整
            boxCount = boxCountTemp;
            if (boxCount == 0) {
                boxCount = 1;
                replenishPlanQty = unit * boxCount;
                deliveryPlanDetailPO.setBoxQuantity(boxCount);
                setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
                return replenishPlanQty;
            }
            replenishPlanQty = unit * boxCount;
            // 向下取整的发货计划量推演期末库存
            endingInventoryDays = getEndingInventoryDays(dateList, demandSumGroupByDay, replenishPlanQty + endingInventory,
                    dateIndex, oemHolidayList);
            // 向下取整的发货计划量期末库存天数小于最小安全库存天数，则向上取整
            if (endingInventoryDays.doubleValue() <= minStockDay.doubleValue()) {
                //int ceilValue = boxCountTemp + 1;
                boxCount = boxCountTemp + 1;
                deliveryPlanDetailPO.setBoxQuantity(boxCount);
                replenishPlanQty = unit * boxCount;
                setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
                return replenishPlanQty;
            }
        } else if (boxCountTemp == 0) {
            deliveryPlanDetailPO.setBoxQuantity(boxCount);
            setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
            return replenishPlanQty;
        }

        if (boxInfoVO == null ||
                boxInfoVO.getPerStackQuantity() == null || boxInfoVO.getPerStackQuantity() == 0 || boxCount == 0) {
            deliveryPlanDetailPO.setBoxQuantity(boxCount);
            setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
            return replenishPlanQty;
        }

        int stackRemainder = boxCount % boxInfoVO.getPerStackQuantity();
        unit = piecePerBox * boxInfoVO.getPerStackQuantity();
        int stackCountTemp = (boxCount / boxInfoVO.getPerStackQuantity());
        int stackCount = stackCountTemp;
        if (stackRemainder > 0) {
            stackCount = stackCountTemp + 1;
        }

        replenishPlanQty = unit * stackCount;
        endingInventoryDays = getEndingInventoryDays(dateList, demandSumGroupByDay, replenishPlanQty + endingInventory,
                dateIndex, oemHolidayList);
        if (endingInventoryDays.compareTo(diffDay) > 0) {
            replenishPlanQty = demandQuantity;
            deliveryPlanDetailPO.setBoxQuantity(replenishPlanQty / piecePerBox);
            setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
            return replenishPlanQty;
        }
        //取整垛之后的比较
        if (endingInventoryDays.doubleValue() > standardStockDay.doubleValue()) {
            stackCount = stackCountTemp;
            if (stackCount == 0) {
                deliveryPlanDetailPO.setBoxQuantity(boxCount);
                setBoxDesc(deliveryPlanDetailPO, boxInfoVO, piecePerBox * boxCount, productBoxRelationVO);
                return piecePerBox * boxCount;
            }
            replenishPlanQty = unit * stackCount;
            endingInventoryDays = getEndingInventoryDays(dateList, demandSumGroupByDay, replenishPlanQty + endingInventory,
                    dateIndex, oemHolidayList);
            //垛数向下取整小于最小安全库存天数，则返回整箱
            if (endingInventoryDays.doubleValue() < minStockDay.doubleValue()) {
                replenishPlanQty = piecePerBox * boxCount;
                deliveryPlanDetailPO.setBoxQuantity(boxCount);
                setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
                return replenishPlanQty;
            }

        }
        if (boxInfoVO.getBoxPerRow() == null || boxInfoVO.getBoxPerRow() == 0) {
            deliveryPlanDetailPO.setBoxQuantity(boxInfoVO.getPerStackQuantity() * stackCount);
            setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
            return replenishPlanQty;
        }
        int rowRemainder = boxCount % boxInfoVO.getBoxPerRow();
        int rowCountTemp = (boxCount / boxInfoVO.getBoxPerRow());
        int rowCount = rowCountTemp;
        if (rowRemainder > 0) {
            rowCount = rowCountTemp + 1;
        }

        unit = piecePerBox * boxInfoVO.getBoxPerRow();
        replenishPlanQty = unit * rowCount;
        endingInventoryDays = getEndingInventoryDays(dateList, demandSumGroupByDay, replenishPlanQty + endingInventory,
                dateIndex, oemHolidayList);
        if (endingInventoryDays.compareTo(diffDay) > 0) {
            replenishPlanQty = demandQuantity;
            deliveryPlanDetailPO.setBoxQuantity(replenishPlanQty / piecePerBox);
            setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
            return replenishPlanQty;
        }
        //整排整垛之后的比较
        if (endingInventoryDays.doubleValue() > standardStockDay.doubleValue()) {
            rowCount = rowCountTemp;
            if (rowCount == 0) {
                replenishPlanQty = piecePerBox * boxInfoVO.getPerStackQuantity() * stackCount;
                deliveryPlanDetailPO.setBoxQuantity(boxInfoVO.getPerStackQuantity() * stackCount);
                setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
                return replenishPlanQty;
            }
            replenishPlanQty = unit * rowCount;
            endingInventoryDays = getEndingInventoryDays(dateList, demandSumGroupByDay, replenishPlanQty + endingInventory,
                    dateIndex, oemHolidayList);
            if (endingInventoryDays.doubleValue() < minStockDay.doubleValue()) {
                replenishPlanQty = piecePerBox * boxInfoVO.getPerStackQuantity() * stackCount;
                deliveryPlanDetailPO.setBoxQuantity(boxInfoVO.getPerStackQuantity() * stackCount);
                setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
                return replenishPlanQty;
            }
        }

        // if (endingInventoryDays.doubleValue() <= minStockDay.doubleValue()) {
        //     // 向上取整
        //     int ceilValue = boxCountTemp + 1;
        //     deliveryPlanDetailPO.setBoxQuantity(ceilValue);
        //     return ceilValue * unit;
        // }
        boxCount = rowCount * boxInfoVO.getBoxPerRow();
        deliveryPlanDetailPO.setBoxQuantity(boxCount);
        setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
        return replenishPlanQty;
    }

    private Integer getNormalReplenishPlanQtyWithSwitchQuantity(List<Date> dateList, int dateIndex,
                                                                Map<String, Integer> demandSumGroupByDay,
                                                                DeliveryPlanDetailPO deliveryPlanDetailPO,
                                                                DeliveryPlanPO deliveryPlanPO,
                                                                Map<String, PartRiskLevelVO> materialRiskLevelVOMapOfJoinKey,
                                                                Map<String, List<ProductBoxRelationVO>> productBoxRelationVOMapOfProductCode,
                                                                Map<String, BoxInfoVO> boxInfoVOMapOfId,
                                                                SafetyStockLevelVO safetyStockLevelVO,
                                                                List<String> productBoxRelationWarningList, int endingInventory,
                                                                NewProductStockPointVO newProductStockPointVO, int switchQuantity,
                                                                List<String> oemHolidayList) {
        if (null == deliveryPlanDetailPO) {
            return 0;
        }
        // 获取发货计划量
        Integer demandQuantity = deliveryPlanDetailPO.getDemandQuantity();
        if (null == demandQuantity || demandQuantity == 0) {
            return 0;
        }
        // 获取零件风险等级
        String joinKey = String.join("&", deliveryPlanPO.getOemCode(), deliveryPlanPO.getProductCode());
        PartRiskLevelVO partRiskLevelVO = materialRiskLevelVOMapOfJoinKey.get(joinKey);

        // 获取零件与箱体的关系
        ProductBoxRelationVO productBoxRelationVO = new ProductBoxRelationVO();
        List<ProductBoxRelationVO> productBoxRelationVOList =
                productBoxRelationVOMapOfProductCode.get(deliveryPlanPO.getProductCode());
        if (CollectionUtils.isNotEmpty(productBoxRelationVOList)) {
            productBoxRelationVO = productBoxRelationVOList.get(0);
        }
        BoxInfoVO boxInfoVO = boxInfoVOMapOfId.get(productBoxRelationVO.getBoxId());
        if (partRiskLevelVO != null && StringUtils.equals("高", partRiskLevelVO.getMaterialRiskLevel())) {
            if (productBoxRelationVO.getStandardLoad() != null) {
                Integer perBox = productBoxRelationVO.getStandardLoad();
                deliveryPlanPO.setPiecePerBox(perBox);
            }
            setBoxDesc(deliveryPlanDetailPO, boxInfoVO, demandQuantity, productBoxRelationVO);
            return demandQuantity;
        }
        //int endingInventory = 0;
        // 获取箱体信息

        if (productBoxRelationVO.getStandardLoad() == null || productBoxRelationVO.getStandardLoad() == 0) {
            setBoxDesc(deliveryPlanDetailPO, boxInfoVO, demandQuantity, productBoxRelationVO);
            return demandQuantity;
        }
        Integer piecePerBox = productBoxRelationVO.getStandardLoad();
        // 维护单箱片数
        deliveryPlanPO.setPiecePerBox(piecePerBox);

        int unit = piecePerBox;
        /*if ("低".equals(partRiskLevelVO.getMaterialRiskLevel())) {
            // 单排箱数 * 单箱片数
            unit = piecePerBox == null ? 1 : piecePerBox * (boxInfoVO.getBoxPerRow() == null
                    ? 1 : boxInfoVO.getBoxPerRow());
        }

        // 中风险只看 单箱片数
        if ("中".equals(partRiskLevelVO.getMaterialRiskLevel())) {
            unit = piecePerBox == null ? 1 : piecePerBox;
        }*/

        // 发货数量 / unit
        int boxCountTemp = (demandQuantity / unit);
        if (boxCountTemp == 0 && newProductStockPointVO != null && newProductStockPointVO.getProductEop() != null) {
            Date date = dateList.get(dateIndex);
            if (date.compareTo(newProductStockPointVO.getProductEop()) >= 0) {
                setBoxDesc(deliveryPlanDetailPO, boxInfoVO, demandQuantity, productBoxRelationVO);
                return demandQuantity;
            }
        }
        int remainder = (demandQuantity % unit);
        int boxCount = boxCountTemp;
        if (remainder > 0) {
            // 余数凑成整箱，向上取整
            boxCount = boxCountTemp + 1;
        }

        int replenishPlanQty = unit * boxCount;
        if (replenishPlanQty >= switchQuantity) {
            replenishPlanQty = switchQuantity;
            deliveryPlanDetailPO.setBoxQuantity(replenishPlanQty / piecePerBox);
            setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
            return replenishPlanQty;
        }

        // 进行向上/向下取整， 整数不需要取整
        // 如向上取整后当天推演期末库存天数＞标准安全库存天数，则进行向下取整，
        // 如向下取整后当天推演期末库存天数＜最小安全库存天数，则进行向上取整，如出现反复，则向上取整；

        // 推演期末库存天数  用"到货计划量"去往后推演
        BigDecimal endingInventoryDays = BigDecimal.ZERO;
        if (MapUtils.isNotEmpty(demandSumGroupByDay)) {
            endingInventoryDays = getEndingInventoryDays(dateList, demandSumGroupByDay, replenishPlanQty + endingInventory, dateIndex, oemHolidayList);
        }

        BigDecimal standardStockDay = BigDecimal.ZERO;
        BigDecimal minStockDay = BigDecimal.ZERO;
        if (null != safetyStockLevelVO) {
            // 标准安全库存天数
            standardStockDay = safetyStockLevelVO.getStandardStockDay();
            // 最小安全库存天数
            minStockDay = safetyStockLevelVO.getMinStockDay();
        }

        if (endingInventoryDays.doubleValue() > standardStockDay.doubleValue()) {
            // 向下取整
            boxCount = boxCountTemp;
            if (boxCount == 0) {
                boxCount = 1;
                replenishPlanQty = unit * boxCount;
                deliveryPlanDetailPO.setBoxQuantity(boxCount);
                setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
                return replenishPlanQty;
            }
            replenishPlanQty = unit * boxCount;
            // 向下取整的发货计划量推演期末库存
            endingInventoryDays = getEndingInventoryDays(dateList, demandSumGroupByDay, replenishPlanQty + endingInventory,
                    dateIndex, oemHolidayList);
            // 向下取整的发货计划量期末库存天数小于最小安全库存天数，则向上取整
            if (endingInventoryDays.doubleValue() <= minStockDay.doubleValue()) {
                //int ceilValue = boxCountTemp + 1;
                boxCount = boxCountTemp + 1;
                deliveryPlanDetailPO.setBoxQuantity(boxCount);
                replenishPlanQty = unit * boxCount;
                setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
                return replenishPlanQty;
            }
        } else if (boxCountTemp == 0) {
            deliveryPlanDetailPO.setBoxQuantity(boxCount);
            setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
            return replenishPlanQty;
        }

        if (boxInfoVO == null ||
                boxInfoVO.getPerStackQuantity() == null || boxInfoVO.getPerStackQuantity() == 0 || boxCount == 0) {
            deliveryPlanDetailPO.setBoxQuantity(boxCount);
            setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
            return replenishPlanQty;
        }

        int stackRemainder = boxCount % boxInfoVO.getPerStackQuantity();
        unit = piecePerBox * boxInfoVO.getPerStackQuantity();
        int stackCountTemp = (boxCount / boxInfoVO.getPerStackQuantity());
        int stackCount = stackCountTemp;
        if (stackRemainder > 0) {
            stackCount = stackCountTemp + 1;
        }

        replenishPlanQty = unit * stackCount;
        if (replenishPlanQty >= switchQuantity) {
            replenishPlanQty = switchQuantity;
            deliveryPlanDetailPO.setBoxQuantity(replenishPlanQty / piecePerBox);
            setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
            return replenishPlanQty;
        }
        endingInventoryDays = getEndingInventoryDays(dateList, demandSumGroupByDay, replenishPlanQty + endingInventory,
                dateIndex, oemHolidayList);
        //取整垛之后的比较
        if (endingInventoryDays.doubleValue() > standardStockDay.doubleValue()) {
            stackCount = stackCountTemp;
            if (stackCount == 0) {
                deliveryPlanDetailPO.setBoxQuantity(boxCount);
                setBoxDesc(deliveryPlanDetailPO, boxInfoVO, piecePerBox * boxCount, productBoxRelationVO);
                return piecePerBox * boxCount;
            }
            replenishPlanQty = unit * stackCount;
            endingInventoryDays = getEndingInventoryDays(dateList, demandSumGroupByDay, replenishPlanQty + endingInventory,
                    dateIndex, oemHolidayList);
            //垛数向下取整小于最小安全库存天数，则返回整箱
            if (endingInventoryDays.doubleValue() < minStockDay.doubleValue()) {
                replenishPlanQty = piecePerBox * boxCount;
                deliveryPlanDetailPO.setBoxQuantity(boxCount);
                setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
                return replenishPlanQty;
            }

        }
        if (boxInfoVO.getBoxPerRow() == null || boxInfoVO.getBoxPerRow() == 0) {
            deliveryPlanDetailPO.setBoxQuantity(boxInfoVO.getPerStackQuantity() * stackCount);
            setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
            return replenishPlanQty;
        }
        int rowRemainder = boxCount % boxInfoVO.getBoxPerRow();
        int rowCountTemp = (boxCount / boxInfoVO.getBoxPerRow());
        int rowCount = rowCountTemp;
        if (rowRemainder > 0) {
            rowCount = rowCountTemp + 1;
        }

        unit = piecePerBox * boxInfoVO.getBoxPerRow();
        replenishPlanQty = unit * rowCount;
        if (replenishPlanQty >= switchQuantity) {
            replenishPlanQty = switchQuantity;
            deliveryPlanDetailPO.setBoxQuantity(replenishPlanQty / piecePerBox);
            setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
            return replenishPlanQty;
        }
        endingInventoryDays = getEndingInventoryDays(dateList, demandSumGroupByDay, replenishPlanQty + endingInventory,
                dateIndex, oemHolidayList);
        //整排整垛之后的比较
        if (endingInventoryDays.doubleValue() > standardStockDay.doubleValue()) {
            rowCount = rowCountTemp;
            if (rowCount == 0) {
                replenishPlanQty = piecePerBox * boxInfoVO.getPerStackQuantity() * stackCount;
                deliveryPlanDetailPO.setBoxQuantity(boxInfoVO.getPerStackQuantity() * stackCount);
                setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
                return replenishPlanQty;
            }
            replenishPlanQty = unit * rowCount;
            endingInventoryDays = getEndingInventoryDays(dateList, demandSumGroupByDay, replenishPlanQty + endingInventory,
                    dateIndex, oemHolidayList);
            if (endingInventoryDays.doubleValue() < minStockDay.doubleValue()) {
                replenishPlanQty = piecePerBox * boxInfoVO.getPerStackQuantity() * stackCount;
                deliveryPlanDetailPO.setBoxQuantity(boxInfoVO.getPerStackQuantity() * stackCount);
                setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
                return replenishPlanQty;
            }
        }

        // if (endingInventoryDays.doubleValue() <= minStockDay.doubleValue()) {
        //     // 向上取整
        //     int ceilValue = boxCountTemp + 1;
        //     deliveryPlanDetailPO.setBoxQuantity(ceilValue);
        //     return ceilValue * unit;
        // }
        boxCount = rowCount * boxInfoVO.getBoxPerRow();
        deliveryPlanDetailPO.setBoxQuantity(boxCount);
        setBoxDesc(deliveryPlanDetailPO, boxInfoVO, replenishPlanQty, productBoxRelationVO);
        return replenishPlanQty;
    }


    private void setBoxDesc(DeliveryPlanDetailPO deliveryPlanDetailPO, BoxInfoVO boxInfoVO, Integer demandQuantity,
                            ProductBoxRelationVO productBoxRelationVO) {
        if (productBoxRelationVO == null || productBoxRelationVO.getStandardLoad() == null || productBoxRelationVO.getStandardLoad() == 0 || demandQuantity == null || demandQuantity == 0) {
            return;
        }
        StringBuilder boxDesc = new StringBuilder();

        int boxRemainder = demandQuantity % productBoxRelationVO.getStandardLoad();
        int boxCount = demandQuantity / productBoxRelationVO.getStandardLoad();
        double roundedResult = boxCount;
        if (boxRemainder > 0) {
            BigDecimal result = BigDecimal.valueOf(demandQuantity).divide(BigDecimal.valueOf(productBoxRelationVO.getStandardLoad()), 1,
                    RoundingMode.HALF_UP);
            roundedResult = result.doubleValue();
            ;
        }
        if (boxInfoVO == null || boxInfoVO.getBoxPerRow() == null || boxInfoVO.getBoxPerRow() == 0) {
            if (boxInfoVO == null || boxInfoVO.getPerStackQuantity() == null || boxInfoVO.getPerStackQuantity() == 0) {
                if (boxCount > 0) {
                    boxDesc.append(boxCount).append("箱");
                }
                if (boxRemainder > 0) {
                    boxDesc.append(boxRemainder).append("片");
                }
                deliveryPlanDetailPO.setBoxDesc(roundedResult + "箱(" + boxDesc.append(")"));
                return;
            } else {
                int stackCount = boxCount / boxInfoVO.getPerStackQuantity();
                int stackRemainder = boxCount % boxInfoVO.getPerStackQuantity();
                boxDesc.append(stackCount).append("垛");
                if (stackRemainder > 0) {
                    boxDesc.append(stackRemainder).append("箱");
                }
                if (boxRemainder > 0) {
                    boxDesc.append(boxRemainder).append("片");
                }
                deliveryPlanDetailPO.setBoxDesc(roundedResult + "箱(" + boxDesc.append(")"));
                return;
            }

        }
        int rowRemainder = boxCount % boxInfoVO.getBoxPerRow();
        int rowCount = boxCount / boxInfoVO.getBoxPerRow();
        if (rowCount > 0) {
            boxDesc.append(rowCount).append("排");
        }
        if (rowRemainder > 0) {
            if (boxInfoVO.getPerStackQuantity() == null || boxInfoVO.getPerStackQuantity() == 0) {
                boxDesc.append(rowRemainder).append("箱");

            } else {
                int stackCount = rowRemainder / boxInfoVO.getPerStackQuantity();
                int stackRemainder = rowRemainder % boxInfoVO.getPerStackQuantity();
                if (stackCount > 0) {
                    boxDesc.append(stackCount).append("垛");
                }
                if (stackRemainder > 0) {
                    boxDesc.append(stackRemainder).append("箱");
                }

            }
        }
        if (boxRemainder > 0) {
            boxDesc.append(boxRemainder).append("片");
        }

        deliveryPlanDetailPO.setBoxDesc(roundedResult + "箱(" + boxDesc.append(")"));
    }

    private InventoryShiftPO getInventoryShiftPO(Integer receiveQuantity,
                                                 DeliveryPlanPO deliveryPlanPO,
                                                 List<Date> dateList,
                                                 Map<String, Integer> demandSumGroupByDay,
                                                 SafetyStockLevelVO safetyStockLevelVO,
                                                 Date date,
                                                 Integer beginInventory,
                                                 Integer demandQuantity,
                                                 DeliveryPlanDetailPO deliveryPlanDetailPO,
                                                 boolean deliveryPlanDetailPOFlag,
                                                 Integer endingInventory,
                                                 Date deliveryPlanStartDate,
                                                 BigDecimal expectEndingInventoryDays,
                                                 BigDecimal endingInventoryDays,
                                                 int expectEndingInventory,
                                                 int i,
                                                 boolean computeBatchFlag) {
        InventoryShiftPO inventoryShiftPO = new InventoryShiftPO();
        inventoryShiftPO.setVersionId(deliveryPlanPO.getVersionId());
        inventoryShiftPO.setProductCode(deliveryPlanPO.getProductCode());
        inventoryShiftPO.setOemCode(deliveryPlanPO.getOemCode());
        inventoryShiftPO.setPlannedDate(date);
        inventoryShiftPO.setDeliveryDate(deliveryPlanStartDate);
        inventoryShiftPO.setBusinessType(deliveryPlanPO.getSupplyType());
        inventoryShiftPO.setMarketType(deliveryPlanPO.getTradeType());
        inventoryShiftPO.setOpeningInventory(beginInventory);
        inventoryShiftPO.setCustomerDemand(demandQuantity == null ? 0 : demandQuantity);
        if (deliveryPlanDetailPO == null) {
            inventoryShiftPO.setArrivalPlan(receiveQuantity);
            inventoryShiftPO.setAccumulatedInventoryGap(endingInventory >= 0 ? 0 : -endingInventory);
            inventoryShiftPO.setEndingInventory(endingInventory < 0 ? 0 : endingInventory);
            if (!deliveryPlanStartDate.before(dateList.get(0))) {
                if (computeBatchFlag) {
                    inventoryShiftPO.setEndingInventoryDays(endingInventoryDays);
                } else {
                    inventoryShiftPO.setEndingInventoryDays(expectEndingInventoryDays);
                }
            } else {
                inventoryShiftPO.setEndingInventoryDays(endingInventoryDays);
            }

        } else {
            inventoryShiftPO.setArrivalPlan(deliveryPlanDetailPO.getDemandQuantity() + receiveQuantity);
            if (deliveryPlanDetailPOFlag) {
                inventoryShiftPO.setAccumulatedInventoryGap(endingInventory >= 0 ? 0 : -endingInventory);
                inventoryShiftPO.setEndingInventory(endingInventory < 0 ? 0 : endingInventory);
                inventoryShiftPO.setEndingInventoryDays(endingInventoryDays);
            } else {
                inventoryShiftPO.setAccumulatedInventoryGap(0);
                if (computeBatchFlag) {
                    inventoryShiftPO.setEndingInventory(endingInventory < 0 ? 0 : endingInventory);
                    inventoryShiftPO.setEndingInventoryDays(endingInventoryDays);
                } else {
                    inventoryShiftPO.setEndingInventory(expectEndingInventory);
                    inventoryShiftPO.setEndingInventoryDays(expectEndingInventoryDays);
                }

            }

        }
        inventoryShiftPO.setMinimumSafetyInventoryDays(safetyStockLevelVO.getMinStockDay());
        inventoryShiftPO.setStandardSafetyInventoryDays(safetyStockLevelVO.getStandardStockDay());
        inventoryShiftPO.setStandardSafetyInventoryLevel(getInventoryLevel(i,
                safetyStockLevelVO.getStandardStockDay(), dateList, demandSumGroupByDay));
        return inventoryShiftPO;
    }

    private InventoryShiftPO createInventoryShiftPO(Integer receiveQuantity,
                                                    DeliveryPlanPO deliveryPlanPO,
                                                    List<Date> dateList,
                                                    Map<String, Integer> demandSumGroupByDay,
                                                    SafetyStockLevelVO safetyStockLevelVO,
                                                    Date date,
                                                    Integer beginInventory, Integer oemOpeningInventory,
                                                    Integer demandQuantity, Integer endingInventory,
                                                    Integer oemEndingInventory, Integer deliveryQuantity,
                                                    BigDecimal endingInventoryDays,
                                                    Date deliveryPlanStartDate,
                                                    int i) {
        InventoryShiftPO inventoryShiftPO = new InventoryShiftPO();
        inventoryShiftPO.setVersionId(deliveryPlanPO.getVersionId());
        inventoryShiftPO.setProductCode(deliveryPlanPO.getProductCode());
        inventoryShiftPO.setOemCode(deliveryPlanPO.getOemCode());
        inventoryShiftPO.setPlannedDate(date);
        inventoryShiftPO.setDeliveryDate(deliveryPlanStartDate);
        inventoryShiftPO.setBusinessType(deliveryPlanPO.getSupplyType());
        inventoryShiftPO.setMarketType(deliveryPlanPO.getTradeType());
        inventoryShiftPO.setOpeningInventory(beginInventory);
        inventoryShiftPO.setEndingInventory(endingInventory);
        inventoryShiftPO.setOemOpeningInventory(oemOpeningInventory);
        inventoryShiftPO.setOemEndingInventory(oemEndingInventory);
        inventoryShiftPO.setReceive(receiveQuantity);
        inventoryShiftPO.setDeliveryPlan(deliveryQuantity);
        inventoryShiftPO.setArrivalPlan(deliveryQuantity + receiveQuantity);
        inventoryShiftPO.setEndingInventoryDays(endingInventoryDays);
        inventoryShiftPO.setCustomerDemand(demandQuantity == null ? 0 : demandQuantity);

        inventoryShiftPO.setMinimumSafetyInventoryDays(safetyStockLevelVO.getMinStockDay());
        inventoryShiftPO.setStandardSafetyInventoryDays(safetyStockLevelVO.getStandardStockDay());
        inventoryShiftPO.setStandardSafetyInventoryLevel(getInventoryLevel(i,
                safetyStockLevelVO.getStandardStockDay(), dateList, demandSumGroupByDay));
        return inventoryShiftPO;
    }

    /**
     * @param index
     * @param standardStockDay
     * @param dateList
     * @param demandQtyOfDay
     * @return java.lang.Integer
     */
    private Integer getInventoryLevel(int index,
                                      BigDecimal standardStockDay,
                                      List<Date> dateList,
                                      Map<String, Integer> demandQtyOfDay) {
        BigDecimal standardStockDayInt = (standardStockDay == null) ? BigDecimal.ZERO : standardStockDay;
        Date currentDate = dateList.get(index);
        int result = 0;
        if (standardStockDayInt.compareTo(BigDecimal.ZERO) == 0) {
            return result;
        }
        List<BigDecimal> bigDecimals = splitBigDecimal(standardStockDayInt);
        for (int i = 1; i <= bigDecimals.size(); i++) {
            Date dateAfter = DateUtils.moveDay(currentDate, i);
            int demandQty = demandQtyOfDay.get(DateUtils.dateToString(dateAfter)) == null ?
                    0 : demandQtyOfDay.get(DateUtils.dateToString(dateAfter));
            result += bigDecimals.get(i - 1).multiply(BigDecimal.valueOf(demandQty))
                    .setScale(0, RoundingMode.CEILING).intValue();
        }
        return result;
    }

    private List<BigDecimal> splitBigDecimal(BigDecimal bigDecimal) {
        BigDecimal unit = BigDecimal.ONE;
        List<BigDecimal> parts = new ArrayList<>();
        BigDecimal remainder = bigDecimal;
        while (remainder.compareTo(unit) >= 0) {
            parts.add(unit);
            remainder = remainder.subtract(unit);
        }
        if (remainder.compareTo(BigDecimal.ZERO) > 0) {
            parts.add(remainder);
        }
        return parts;
    }

    /**
     * 物料在途数
     *
     * @param demandProductCodeList
     * @return java.util.Map<java.lang.String, java.util.List < com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordVO>>
     */
    private Map<String, List<WarehouseReleaseToWarehouseVO>> getProductStockPointInRoad(List<String> demandProductCodeList
            , List<OemVO> oemVOList, Map<String, List<String>> productOemMap, List<String> oemCodeScope) {
        Map<String, List<WarehouseReleaseToWarehouseVO>> result = new HashMap<>();
        if (CollectionUtils.isEmpty(oemVOList)) {
            return result;
        }
        Map<String, List<String>> shipmentLocatorCodesMap = new HashMap<>();
        oemVOList.forEach(t -> {
            if (StringUtils.isEmpty(t.getTargetStockLocation()) || !oemCodeScope.contains(t.getOemCode())) {
                return;
            }
            String[] targetStockLocations = t.getTargetStockLocation().split(",");
            List<String> shipmentLocatorCodes = Arrays.stream(targetStockLocations)
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(shipmentLocatorCodes)) {
                shipmentLocatorCodes.forEach(shipmentLocatorCode -> {
                    List<String> list = shipmentLocatorCodesMap.computeIfAbsent(shipmentLocatorCode,
                            key -> new ArrayList<>());
                    if (!list.contains(t.getOemCode())) {
                        list.add(t.getOemCode());
                    }
                });
            }
        });

        List<WarehouseReleaseToWarehouseVO> warehouseReleaseRecordList =
                warehouseReleaseToWarehouseService.getInRoad(demandProductCodeList,
                        new ArrayList<>(shipmentLocatorCodesMap.keySet()));
        warehouseReleaseRecordList = warehouseReleaseRecordList.stream().filter(t ->
                shipmentLocatorCodesMap.containsKey(t.getShipmentLocatorCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(warehouseReleaseRecordList)) {
            for (WarehouseReleaseToWarehouseVO t : warehouseReleaseRecordList) {
                List<String> shipmentLocatorCodeOemCodes = shipmentLocatorCodesMap.get(t.getShipmentLocatorCode());
                List<String> productOemCodes = productOemMap.get(t.getShipmentLocatorCode());
                String oemCode = null;
                if (CollectionUtils.isNotEmpty(shipmentLocatorCodeOemCodes) && CollectionUtils.isNotEmpty(productOemCodes)) {
                    for (String shipmentLocatorCodeOemCode : shipmentLocatorCodeOemCodes) {
                        for (String productOemCode : productOemCodes) {
                            if (shipmentLocatorCodeOemCode.equals(productOemCode)) {
                                oemCode = shipmentLocatorCodeOemCode;
                                break;
                            }
                        }
                    }
                }
                if (StringUtils.isEmpty(oemCode) && CollectionUtils.isNotEmpty(shipmentLocatorCodeOemCodes)) {
                    oemCode = shipmentLocatorCodeOemCodes.get(0);
                } else if (StringUtils.isEmpty(oemCode) && CollectionUtils.isNotEmpty(productOemCodes)) {
                    oemCode = productOemCodes.get(0);
                }
                List<WarehouseReleaseToWarehouseVO> list = result.computeIfAbsent(oemCode + "&&" + t.getItemCode(),
                        key -> new ArrayList<>());
                list.add(t);
            }
        }
        return result;
    }

    /**
     * 查询主机厂运输路径
     *
     * @param deliveryPlanCalculateDTO
     * @return java.util.Map<java.lang.String, com.yhl.scp.dfp.transport.vo.TransportRoutingVO>
     */
    @SuppressWarnings("unused")
    private Map<String, TransportRoutingVO> getOemTransportRouting(DeliveryPlanCalculateDTO deliveryPlanCalculateDTO) {

        if (deliveryPlanCalculateDTO != null
                && CollectionUtils.isNotEmpty(deliveryPlanCalculateDTO.getDeliveryPlanList())) {
            List<String> appointRoutingIds = deliveryPlanCalculateDTO.getDeliveryPlanList().stream()
                    .map(DeliveryPlanDTO::getTransportationRouteId).collect(Collectors.toList());
            List<TransportRoutingVO> transportRoutingVOList = transportRoutingService.selectVOByParams(ImmutableMap.of(
                    "ids", appointRoutingIds));
            return transportRoutingVOList.stream().collect(Collectors.toMap(TransportRoutingVO::getOemCode,
                    Function.identity(), (t1, t2) -> t1));
        }
        List<TransportRoutingVO> transportRoutingVOList = transportRoutingService.getOemTransportRouting();
        Map<String, Optional<TransportRoutingVO>> groupedByPriority = transportRoutingVOList.stream()
                .collect(Collectors.groupingBy(
                        TransportRoutingVO::getOemCode,
                        Collectors.minBy(Comparator.comparing(TransportRoutingVO::getPriority,
                                Comparator.nullsLast(Comparator.naturalOrder())))
                ));
        // 将 Optional 转换为实际的对象
        return groupedByPriority.entrySet().stream()
                .filter(entry -> entry.getValue().isPresent())
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().get(),
                        (t1, t2) -> t2));
    }

    @Override
    public void doUsePreviousPlan(String oemCodes, String versionCode, String prevVersionCode) {
        String[] oemCodeArr = oemCodes.split(",");
        List<String> oemCodeList = new ArrayList<>(Arrays.asList(oemCodeArr));

        // 根据版本号查询发货计划版本
        List<DeliveryPlanVersionVO> deliveryPlanVersionVOList =
                deliveryPlanVersionService.selectByParams(ImmutableMap.of("versionCode", versionCode));
        if (StringUtils.equals(deliveryPlanVersionVOList.get(0).getVersionStatus(),
                VersionStatusEnum.PUBLISHED.getCode())) {
            throw new BusinessException("该版本已发布，不能使用上版计划");
        }
        // 日期初始化
        List<Date> dateList = DeliveryPlanDateUtils.getDeliveryPlanDateList(versionCode);
        // 上个版本的发货计划
        List<DeliveryPlanVO> prevDeliveryPlanPOList = deliveryPlanDao.selectVOByParams(ImmutableMap.of("versionCode",
                prevVersionCode));
        // 当前版本的发货计划
        List<DeliveryPlanVO> currentDeliveryPlanPOList = deliveryPlanDao.selectVOByParams(ImmutableMap.of(
                "versionCode", versionCode));
        List<String> deliveryPlanDeleteIds = currentDeliveryPlanPOList.stream().filter(t -> {
            if (CollectionUtils.isNotEmpty(oemCodeList)) {
                return oemCodeList.contains(t.getOemCode());
            }
            return true;
        }).map(DeliveryPlanVO::getId).collect(Collectors.toList());

        // 获取主机厂的运输时间
        List<OemTransportTimeVO> oemTransportTimeVOList = oemTransportTimeService.selectByParams(ImmutableMap.of(
                "oemCodes", oemCodeList));
        // 根据主机厂分组
        Map<String, List<OemTransportTimeVO>> oemTransportTimeVOGroup = new HashMap<>();
        if (CollectionUtils.isNotEmpty(oemTransportTimeVOList)) {
            oemTransportTimeVOGroup =
                    oemTransportTimeVOList.stream().collect(Collectors.groupingBy(OemTransportTimeVO::getOemCode));
        }


        // 过滤出所选主机厂的发货计划
        prevDeliveryPlanPOList =
                prevDeliveryPlanPOList.stream().filter(deliveryPlanPO -> oemCodeList.contains(deliveryPlanPO.getOemCode())).collect(Collectors.toList());
        // 发货计划根据主机厂分组
        Map<String, List<DeliveryPlanVO>> prevDeliveryPlanPOMapOfOemCode =
                prevDeliveryPlanPOList.stream().collect(Collectors.groupingBy(DeliveryPlanVO::getOemCode));
        List<String> prevDeliveryPlanIdAllList =
                prevDeliveryPlanPOList.stream().map(DeliveryPlanVO::getId).collect(Collectors.toList());
        // 查询发货计划明细
        List<DeliveryPlanDetailPO> prevDeliveryPlanDetailPOSAll =
                deliveryPlanDetailDao.selectByDeliveryPlanIds(prevDeliveryPlanIdAllList);
        // 发货计划明细按照发货计划分组
        Map<String, List<DeliveryPlanDetailPO>> prevDeliveryPlanDetailPoMapOfDataId =
                prevDeliveryPlanDetailPOSAll.stream().collect(Collectors.groupingBy(DeliveryPlanDetailPO::getDeliveryPlanDataId));
        // 获取库存推移表的数据
        List<InventoryShiftVO> inventoryShiftVOS = inventoryShiftDao.selectVOByParams(ImmutableMap.of("versionCode",
                versionCode));
        if (CollectionUtils.isEmpty(inventoryShiftVOS)) {
            throw new BusinessException("未查询到库存推移数据，请先执行发货计划计算");
        }
        Map<String, List<InventoryShiftVO>> inventoryShiftGroupByOemAndProduct =
                inventoryShiftVOS.stream().collect(Collectors.groupingBy(
                        t -> String.join("&&", t.getOemCode(), t.getProductCode())));
        List<InventoryShiftPO> inventoryShiftPOS = InventoryShiftConvertor.INSTANCE.vo2Pos(inventoryShiftVOS);

        // 根据主机厂进行分组
        Map<String, List<InventoryShiftPO>> inventoryShiftPoMapOfOemCode =
                inventoryShiftPOS.stream().collect(Collectors.groupingBy(InventoryShiftPO::getOemCode));

        // 主机厂装车日历
        // 安全库存配置,key为productCode&&stockPointCode
        Map<String, SafetyStockLevelVO> safetyStockLevelVOMap =
                safetyStockLevelService.selectWithOutSaleOrg().stream().collect(Collectors
                        .toMap(SafetyStockLevelVO::getProductCode, Function.identity(),
                                (t1, t2) -> t2));

        List<DeliveryPlanPO> createDeliveryPlanPoList = new ArrayList<>();
        List<DeliveryPlanDetailPO> createDeliveryPlanDetailPoList = new ArrayList<>();
        List<InventoryShiftPO> updateInventoryShiftPoList = new ArrayList<>();

        for (String oemCode : oemCodeList) {
            // 获取上一个版本主机厂对应的发货计划
            List<DeliveryPlanVO> prevDeliveryPlanPOS = prevDeliveryPlanPOMapOfOemCode.get(oemCode);
            if (CollectionUtils.isEmpty(prevDeliveryPlanPOS)) {
                continue;
            }

            // 获取主机厂运输时间
            int oemTransportSupplyDays = 0;
            List<OemTransportTimeVO> oemTransportTimeVOS = oemTransportTimeVOGroup.get(oemCode);
            // 获取优先级最小的数据
            if (CollectionUtils.isNotEmpty(oemTransportTimeVOS)) {
                OemTransportTimeVO oemTransportTimeVO =
                        oemTransportTimeVOS.stream().max(Comparator.comparing(OemTransportTimeVO::getPriority)).orElse(new OemTransportTimeVO());
                // 计算主机厂运输时间
                oemTransportSupplyDays = calculateTransportDays(null == oemTransportTimeVO.getTransportationTime() ?
                        0 : oemTransportTimeVO.getTransportationTime().doubleValue());
            }

            // 获取运输路径
            calculateDemandDateList(oemTransportSupplyDays, dateList);
            List<InventoryShiftPO> currentInventoryShiftPOS = inventoryShiftPoMapOfOemCode.get(oemCode);
            // 按照本厂编码分组
            Map<String, List<InventoryShiftPO>> currentInventoryShiftPOMapOfProductCode =
                    currentInventoryShiftPOS.stream().collect(Collectors.groupingBy(InventoryShiftPO::getProductCode));
            // 按照本厂编码进行分组
            Map<String, List<DeliveryPlanVO>> prevDeliveryPlanPoMapOfProductCode =
                    prevDeliveryPlanPOS.stream().collect(Collectors.groupingBy(DeliveryPlanVO::getProductCode));
            for (Map.Entry<String, List<DeliveryPlanVO>> entry : prevDeliveryPlanPoMapOfProductCode.entrySet()) {
                List<InventoryShiftPO> currentInventoryShiftPOList =
                        currentInventoryShiftPOMapOfProductCode.get(entry.getKey());
                if (CollectionUtils.isEmpty(currentInventoryShiftPOList)) {
                    continue;
                }
                // 根据日期进行分组
                Map<String, InventoryShiftPO> currentInventoryShiftPOMapOfDate =
                        currentInventoryShiftPOList.stream().collect(Collectors
                                .toMap(t -> DateUtils.dateToString(t.getPlannedDate()), Function.identity(),
                                        (t1, t2) -> t2));
                DeliveryPlanVO prevDeliveryPlanPO = entry.getValue().get(0);
                // 获取发货计划明细
                List<DeliveryPlanDetailPO> prevDeliveryPlanDetailPOS =
                        prevDeliveryPlanDetailPoMapOfDataId.get(prevDeliveryPlanPO.getId());
                // 根据日期来分组
                Map<String, DeliveryPlanDetailPO> prevDeliveryPlanDetailOfDate =
                        prevDeliveryPlanDetailPOS.stream().collect(Collectors
                                .toMap(t -> DateUtils.dateToString(t.getDemandTime()), Function.identity(),
                                        (t1, t2) -> t2));

                String key = entry.getKey();
                // 期初库存
                String oemProduct = String.join("&&", prevDeliveryPlanPO.getOemCode(),
                        prevDeliveryPlanPO.getProductCode());
                List<InventoryShiftVO> inventoryShiftVOS1 = inventoryShiftGroupByOemAndProduct.get(oemProduct);
                inventoryShiftVOS1.sort(Comparator.comparing(InventoryShiftVO::getPlannedDate));
                Integer openingInventory = inventoryShiftVOS1.get(0).getOpeningInventory();
                Integer oemOpeningInventory = inventoryShiftVOS1.get(0).getOemOpeningInventory();

                SafetyStockLevelVO safetyStockLevelVO = safetyStockLevelVOMap.get(key);
                if (StringUtils.equals(OemBusinessTypeEnum.MTS.getCode(), prevDeliveryPlanPO.getSupplyType()) && safetyStockLevelVO == null) {
                    throw new BusinessException(key + "没有安全库存配置");
                }
                usePreviousPlanMtsData(dateList,
                        oemTransportSupplyDays,
                        openingInventory,
                        oemOpeningInventory,
                        updateInventoryShiftPoList,
                        prevDeliveryPlanDetailOfDate,
                        currentInventoryShiftPOMapOfDate,
                        safetyStockLevelVO);
            }

            // 新增上版本的发货计划
            prevDeliveryPlanPOS.forEach(deliveryPlanVO -> {
                List<DeliveryPlanDetailPO> list = prevDeliveryPlanDetailPoMapOfDataId.get(deliveryPlanVO.getId());
                DeliveryPlanPO deliveryPlanPO = DeliveryPlanConvertor.INSTANCE.vo2Po(deliveryPlanVO);
                deliveryPlanPO.setVersionId(deliveryPlanVersionVOList.get(0).getId());
                deliveryPlanPO.setId(UUIDUtil.getUUID());
                createDeliveryPlanPoList.add(deliveryPlanPO);

                // 新增发货计划明细数据
                list.forEach(deliveryPlanDetailPO -> {
                    deliveryPlanDetailPO.setId(UUIDUtil.getUUID());
                    deliveryPlanDetailPO.setDeliveryPlanDataId(deliveryPlanPO.getId());
                    deliveryPlanDetailPO.setCreator(null);
                    deliveryPlanDetailPO.setCreateTime(null);
                    createDeliveryPlanDetailPoList.add(deliveryPlanDetailPO);
                });
            });
        }
        // 删除当前版本发货计划和发货计划详情数据
        deliveryPlanDao.deleteBatch(deliveryPlanDeleteIds);
        deliveryPlanDetailDao.deleteBatchByPlanIds(deliveryPlanDeleteIds);
        // 新增发货计划数据
        if (CollectionUtils.isNotEmpty(createDeliveryPlanPoList)) {
            deliveryPlanDao.insertBatchWithPrimaryKey(createDeliveryPlanPoList);
        }
        // 新增发货计划明细数据
        if (CollectionUtils.isNotEmpty(createDeliveryPlanDetailPoList)) {
            deliveryPlanDetailDao.insertBatchWithPrimaryKey(createDeliveryPlanDetailPoList);
        }
        // 更新推移表数据
        if (CollectionUtils.isNotEmpty(updateInventoryShiftPoList)) {
            inventoryShiftDao.updateBatchSelective(updateInventoryShiftPoList);
        }
    }

    /**
     * @param dateList
     * @param supplyDays
     * @param openingInventory
     * @param oemOpeningInventory
     * @param updateInventoryShiftPoList
     * @param deliveryPlanDetailMap
     * @param inventoryShiftPOMap
     * @param safetyStockLevelVO
     */
    private void usePreviousPlanMtsData(List<Date> dateList,
                                        Integer supplyDays,
                                        Integer openingInventory,
                                        Integer oemOpeningInventory,
                                        List<InventoryShiftPO> updateInventoryShiftPoList,
                                        Map<String, DeliveryPlanDetailPO> deliveryPlanDetailMap,
                                        Map<String, InventoryShiftPO> inventoryShiftPOMap,
                                        SafetyStockLevelVO safetyStockLevelVO) {
        Map<String, Integer> demandQtyOfDay = new HashMap<>();
        inventoryShiftPOMap.forEach((key, value) -> {
            if (null != value.getCustomerDemand()) {
                demandQtyOfDay.put(key, value.getCustomerDemand());
            } else {
                demandQtyOfDay.put(key, 0);
            }
        });

        Integer endingInventory = openingInventory;
        Integer oemEndingInventory = oemOpeningInventory;
        for (int i = 0; i < dateList.size(); i++) {
            Date date = dateList.get(i);
            // 获取当前版本的推移表数据
            InventoryShiftPO inventoryShiftPO = inventoryShiftPOMap.get(DateUtils.dateToString(date));
            if (null == inventoryShiftPO) {
                continue;
            }
            // 客户需求量
            Integer customerDemand = inventoryShiftPO.getCustomerDemand();
            // 期初库存 = 前一天的期末库存
            Integer openingInventoryTemp = endingInventory;
            // 主机厂期初库存
            Integer oemOpeningInventoryTemp = oemEndingInventory;
            // 上版本的发货计划的开始时间
            Date deliveryPlanStartDate = DateUtils.moveDay(date, -supplyDays);
            DeliveryPlanDetailPO prevDeliveryPlanDetailPO =
                    deliveryPlanDetailMap.get(DateUtils.dateToString(deliveryPlanStartDate));
            int inRoad = deliveryPlanDetailMap.get(DateUtils.dateToString(deliveryPlanStartDate)) == null
                    ? 0 : deliveryPlanDetailMap.get(DateUtils.dateToString(deliveryPlanStartDate)).getDemandQuantity();

            // 先用主机厂期初库存满足需求
            oemEndingInventory = (oemOpeningInventoryTemp != null && customerDemand != null) ?
                    oemOpeningInventoryTemp - customerDemand : null;

            // 未满足需求数量
            Integer unfulfilledQuantity = (customerDemand != null && oemOpeningInventoryTemp != null) ?
                    customerDemand - oemOpeningInventoryTemp : null;

            oemEndingInventory = (oemEndingInventory != null && oemEndingInventory <= 0) ? Integer.valueOf(0) :
                    oemEndingInventory;

            unfulfilledQuantity = (unfulfilledQuantity != null && unfulfilledQuantity <= 0) ? Integer.valueOf(0) :
                    unfulfilledQuantity;

            // 计算期末库存：期初库存+在途数据-客户需求
            endingInventory = (openingInventoryTemp != null ? openingInventoryTemp : 0) +
                    inRoad - (unfulfilledQuantity != null ? unfulfilledQuantity : 0);

            // 主机厂期末库存和本厂期末库存总和

            Integer endingInventorySum = endingInventory +
                    (oemEndingInventory != null ? oemEndingInventory : 0);

            // 当天期末库存天数
            BigDecimal endingInventoryDays = getManualEndingInventoryDays(dateList, endingInventorySum, i,
                    demandQtyOfDay);
            // 期望库存减去实际的期末库存就是发货计划量
            if (null != prevDeliveryPlanDetailPO) {
                inventoryShiftPO.setArrivalPlan(prevDeliveryPlanDetailPO.getDemandQuantity());
            }
            // 期初库存
            inventoryShiftPO.setOpeningInventory(openingInventoryTemp);
            inventoryShiftPO.setOemOpeningInventory(oemOpeningInventoryTemp);
            // 期末库存
            inventoryShiftPO.setEndingInventory(Math.max(endingInventory, 0));
            inventoryShiftPO.setOemEndingInventory(oemEndingInventory);
            // 期末库存天数
            inventoryShiftPO.setEndingInventoryDays(endingInventoryDays);
            // 累计库存缺口
            inventoryShiftPO.setAccumulatedInventoryGap(endingInventory >= 0 ? 0 : -endingInventory);

            BigDecimal minStockDay = null == safetyStockLevelVO
                    ? BigDecimal.ZERO : (null == safetyStockLevelVO.getMinStockDay()
                    ? BigDecimal.ZERO : safetyStockLevelVO.getMinStockDay());
            Integer minInventoryLevel = getInventoryLevel(i, minStockDay, dateList, demandQtyOfDay);
            inventoryShiftPO.setEndingInventoryMinSafeDiff(endingInventory - minInventoryLevel);
            updateInventoryShiftPoList.add(inventoryShiftPO);
        }
    }

    @SneakyThrows
    @Override
    public void exportData(HttpServletResponse response, String versionCode) {
        ServletOutputStream out = response.getOutputStream();
        EasyExcelUtil.initResponse(response, "发货计划");
        List<List<String>> headers = Lists.newArrayList();
        headers.add(Collections.singletonList("版本号"));
        headers.add(Collections.singletonList("业务员"));
        headers.add(Collections.singletonList("主机厂代码"));
        headers.add(Collections.singletonList("主机厂名称"));
        headers.add(Collections.singletonList("客户零件号"));
        headers.add(Collections.singletonList("本厂编码"));
        headers.add(Collections.singletonList("车型"));
        headers.add(Collections.singletonList("收货地址"));
        headers.add(Collections.singletonList("销售类型"));
        Date now = new Date();
        List<String> dateList = get30Day(now);
        dateList.forEach(item -> headers.add(Collections.singletonList(item)));
        String beginDate = dateList.get(0);
        String endDate = dateList.get(dateList.size() - 1);

        List<DeliveryPlanExportVO> deliveryPlans = deliveryPlanDao.selectExportData(versionCode, beginDate, endDate);
        if (CollectionUtils.isEmpty(deliveryPlans)) {
            log.info("发货计划无数据");
            EasyExcel.write(out)
                    .sheet("发货计划")
                    .head(headers)
                    .registerWriteHandler(new CustomColumnWidthHandler())
                    .doWrite(Collections.emptyList());
            return;
        }
        Map<String, String> userId2UserNameMap = ipsNewFeign.userList().stream().collect(Collectors
                .toMap(User::getId, User::getCnName, (t1, t2) -> t2));
        List<List<Object>> dataList = new ArrayList<>();
        for (DeliveryPlanExportVO deliveryPlan : deliveryPlans) {
            List<Object> data = Lists.newArrayList(deliveryPlan.getVersionCode(),
                    userId2UserNameMap.getOrDefault(deliveryPlan.getOrderPlanner(), ""),
                    deliveryPlan.getOemCode(), deliveryPlan.getOemName(), deliveryPlan.getPartName(),
                    deliveryPlan.getProductCode(), deliveryPlan.getVehicleModelCode(), deliveryPlan.getReceiveAddress(),
                    deliveryPlan.getSalesType(),
                    deliveryPlan.getDay0(), deliveryPlan.getDay1(), deliveryPlan.getDay2(),
                    deliveryPlan.getDay3(), deliveryPlan.getDay4(), deliveryPlan.getDay5(),
                    deliveryPlan.getDay6(), deliveryPlan.getDay7(), deliveryPlan.getDay8(),
                    deliveryPlan.getDay9(), deliveryPlan.getDay10(), deliveryPlan.getDay11(),
                    deliveryPlan.getDay12(), deliveryPlan.getDay13(), deliveryPlan.getDay14(),
                    deliveryPlan.getDay15(), deliveryPlan.getDay16(), deliveryPlan.getDay17(),
                    deliveryPlan.getDay18(), deliveryPlan.getDay19(), deliveryPlan.getDay20(),
                    deliveryPlan.getDay21(), deliveryPlan.getDay22(), deliveryPlan.getDay23(),
                    deliveryPlan.getDay24(), deliveryPlan.getDay25(), deliveryPlan.getDay26(),
                    deliveryPlan.getDay27(), deliveryPlan.getDay28(), deliveryPlan.getDay29());
            dataList.add(data);
        }
        EasyExcel.write(out)
                .sheet("发货计划")
                .head(headers)
                .registerWriteHandler(new CustomColumnWidthHandler())
                .doWrite(dataList);
    }

    @Override
    public PageInfo<InventoryAndDeliveryDataVO> selectInventoryAndDelivery(InventoryAndDeliveryDTO inventoryAndDeliveryDTO) {
        if (StringUtils.isEmpty(inventoryAndDeliveryDTO.getStockPointCode())) {
            throw new BusinessException("请选择生产组织");
        }
        // 库存点
        String stockPointCode = inventoryAndDeliveryDTO.getStockPointCode();
        String operation = "";
        if ("S1".equals(stockPointCode)) {
            operation = "20";
        } else if ("S2".equals(stockPointCode)) {
            operation = "40";
        }

        // 最终结果
        List<InventoryAndDeliveryDataVO> allResult = new ArrayList<>();

        // 获取mds场景
        String mdsScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode()).getData();

        // 查询计划期间
        PlanningHorizonVO planningHorizon = mdsFeign.getPlanningHorizon();
        // 开始时间 = 历史展望开始时间
        Date startDate = planningHorizon.getHistoryRetrospectStartTime();
        // 转换 Date 为 LocalDate
        LocalDate startLocalDate = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        // 增加 7 天
        LocalDate endLocalDate = startLocalDate.plusDays(7);
        // 转换回 Date
        Date endDate = Date.from(endLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant());

        // 根据计划期间获取发货计划
        List<DeliveryPlanVO2> deliveryPlanList = this.selectVO2ByPlanPeriod("yyyyMMdd", DateUtils.dateToString(startDate), DateUtils.dateToString(endDate));
        // 收集发货计划的本厂编码
        List<String> deliveryPlanProductCodeList = deliveryPlanList.stream().map(DeliveryPlanVO2::getProductCode).distinct().collect(Collectors.toList());

        // 查询发货计划总览查询
        MasterPlanReq masterPlanReq02 = new MasterPlanReq();
        masterPlanReq02.setProductCodes(deliveryPlanProductCodeList);
        masterPlanReq02.setDeliverStartTime(startDate);
        masterPlanReq02.setDeliverEndTime(endDate);
        List<DeliveryPlanGeneralViewVO> deliveryPlanGeneralViewVOList = mpsFeign.getDeliveryPlanGeneralViewVO(masterPlanReq02);

        // 查询库存点数据，用于过滤非本厂库存
        List<NewStockPointVO> newStockPointVOList = newMdsFeign.selectStockPointByParams(mdsScenario, ImmutableMap.of("stockPointType", StockPointTypeEnum.BC.getCode()));
        List<String> bcStockPointList =
                newStockPointVOList.stream().map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
        // 查询实时库存
        List<InventoryBatchDetailVO> inventoryDataDTOList = inventoryBatchDetailService.selectByParams(new HashMap<>());
        // 半成品库存
        Map<String, List<InventoryBatchDetailVO>> semiFinishedGoodsInventoryMap =
                inventoryDataDTOList.stream()
                        .filter(t -> {
                            String productCode = t.getProductCode();
                            String[] parts = productCode.split("-", 2);
                            return (parts.length == 2) && bcStockPointList.contains(t.getStockPointCode());
                        })
                        .collect(Collectors.groupingBy(data -> data.getProductCode() + "&" + data.getStockPointCode()));

        // 成品库存
        Map<String, List<InventoryBatchDetailVO>> finishInventoryMap =
                inventoryDataDTOList.stream()
                        .filter(t -> {
                            String productCode = t.getProductCode();
                            String[] parts = productCode.split("-", 2);
                            return (parts.length < 2 || parts[1].isEmpty()) && bcStockPointList.contains(t.getStockPointCode());
                        })
                        .collect(Collectors.groupingBy(data -> data.getProductCode() + "&" + data.getStockPointCode()));

        // 查询主生产计划-工单
        MasterPlanReq masterPlanReq = new MasterPlanReq();
        masterPlanReq.setPlanOperation(operation);
        masterPlanReq.setProductCodes(deliveryPlanProductCodeList);
        List<MasterPlanWorkOrderBodyVO> masterPlanWorkOrderBodyVOList = mpsFeign.getMasterPlan(masterPlanReq);

        // 如无生产计划，则依据“产品资源生产关系”中，该产品，工序为主工序【S1为20，S2为40，工厂的主工序】，“生效时间”为T日所在月份，且优先级最高的资源为主设备
        if (CollectionUtils.isEmpty(masterPlanWorkOrderBodyVOList)) {
            List<ProductCandidateResourceTimeVO> productCandidateResourceTimeVOList;
            // 根据库存点区分并查询产品资源生产关系

            productCandidateResourceTimeVOList = newMdsFeign.selectProductCandidateResourceTimeByOperationCodeAndEffectiveTime(mdsScenario, operation, DateUtils.dateToString(startDate, "yyyy-MM"));

            // 转换生产计划实体
            masterPlanWorkOrderBodyVOList = productCandidateResourceTimeVOList.stream()
                    .map(productCandidateResourceTimeVO -> {
                        MasterPlanWorkOrderBodyVO masterPlanWorkOrderBodyVO = new MasterPlanWorkOrderBodyVO();
                        masterPlanWorkOrderBodyVO.setResourceCode(productCandidateResourceTimeVO.getResourceCode());
                        masterPlanWorkOrderBodyVO.setResourceName(productCandidateResourceTimeVO.getResourceName());
                        masterPlanWorkOrderBodyVO.setProductCode(productCandidateResourceTimeVO.getProductCode());
                        return masterPlanWorkOrderBodyVO;
                    })
                    .collect(Collectors.toList());
        }

        // 如“产品资源生产关系”中“生效时间”带月份的未查询到，则取"物品候选资源"，根据产品编码+工序（主工序【S1为20，S2为40，工厂的主工序】）+优先级最高的产线编码（主资源物理资源）
        if (CollectionUtils.isEmpty(masterPlanWorkOrderBodyVOList)) {
            List<ProductCandidateResourceVO> productCandidateResourceVOList;
            Map<String, Object> params = new HashMap<>();
            // 根据库存点区分并查询产品资源生产关系
            params.put("standardStepCode", operation);
            productCandidateResourceVOList = newMdsFeign.selectProductCandidateResourceVOByParams(params);

            if (CollectionUtils.isEmpty(productCandidateResourceVOList)) {
                return new PageInfo<>();
            }

            // 分组筛选出优先级最高的
            Map<Integer, List<ProductCandidateResourceVO>> groupedMap =
                    productCandidateResourceVOList.stream().collect(Collectors.groupingBy(ProductCandidateResourceVO::getPriority));
            Optional<Integer> maxPriority = groupedMap.keySet().stream()
                    .max(Comparator.naturalOrder());
            // 获取最大 priority 对应的 ProductCandidateResourceVO 列表
            productCandidateResourceVOList = maxPriority.map(groupedMap::get).orElse(Collections.emptyList());

            // 转换生产计划实体
            masterPlanWorkOrderBodyVOList = productCandidateResourceVOList.stream()
                    .map(productCandidateResourceTimeVO -> {
                        MasterPlanWorkOrderBodyVO masterPlanWorkOrderBodyVO = new MasterPlanWorkOrderBodyVO();
                        masterPlanWorkOrderBodyVO.setResourceCode(productCandidateResourceTimeVO.getPhysicalResourceCode());
                        masterPlanWorkOrderBodyVO.setResourceName(productCandidateResourceTimeVO.getPhysicalResourceName());
                        masterPlanWorkOrderBodyVO.setProductCode(productCandidateResourceTimeVO.getProductCode());
                        return masterPlanWorkOrderBodyVO;
                    })
                    .collect(Collectors.toList());
        }

        masterPlanWorkOrderBodyVOList = masterPlanWorkOrderBodyVOList.stream().filter(data -> StringUtils.isNotEmpty(data.getResourceCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(inventoryAndDeliveryDTO.getResourceCodeList())) {
            // 通过查询参数过滤主生产计划
            masterPlanWorkOrderBodyVOList = masterPlanWorkOrderBodyVOList.stream().filter(data -> inventoryAndDeliveryDTO.getResourceCodeList().contains(data.getResourceCode())).collect(Collectors.toList());
        }

        // 主生产计划根据主设备（资源名称）分组
        for (Map.Entry<String, List<MasterPlanWorkOrderBodyVO>> entity :
                masterPlanWorkOrderBodyVOList.stream().collect(Collectors.groupingBy(MasterPlanWorkOrderBodyVO::getResourceName)).entrySet()) {
            InventoryAndDeliveryDataVO inventoryAndDeliveryDataVO = new InventoryAndDeliveryDataVO();
            inventoryAndDeliveryDataVO.setMaster(entity.getKey());
            if (stockPointCode.equals("S1")) {
                // 计算S1库存
                List<InventoryAndDeliveryDataVO.Inventory> inventoryList = learnInventoryDataS1(stockPointCode, semiFinishedGoodsInventoryMap, finishInventoryMap, entity);
                inventoryAndDeliveryDataVO.setInventoryList(inventoryList);
            } else if (stockPointCode.equals("S2")) {
                // 计算S2库存
                List<InventoryAndDeliveryDataVO.Inventory> inventoryList = learnInventoryDataS2(stockPointCode, semiFinishedGoodsInventoryMap, finishInventoryMap, entity);
                inventoryAndDeliveryDataVO.setInventoryList(inventoryList);
            }
            // 计算发货
            List<InventoryAndDeliveryDataVO.Delivery> deliveryList = learnDeliveryData(stockPointCode, entity, startDate, endDate, deliveryPlanGeneralViewVOList);
            // 库存总和
            int inventorySum = inventoryAndDeliveryDataVO.getInventoryList()
                    .stream().filter(data -> data.getInventoryName().equals("合计"))
                    .mapToInt(InventoryAndDeliveryDataVO.Inventory::getCurrentQuantity).sum();
            // 日发货量总和
            Integer deliverySum = deliveryList.stream().mapToInt(InventoryAndDeliveryDataVO.Delivery::getCurrentQuantity).sum();
            InventoryAndDeliveryDataVO.Delivery totalDelivery = new InventoryAndDeliveryDataVO.Delivery();
            totalDelivery.setDeliveryName("一周发货总量");
            totalDelivery.setCurrentQuantity(deliverySum);
            deliveryList.add(totalDelivery);

            InventoryAndDeliveryDataVO.Delivery averageDelivery = new InventoryAndDeliveryDataVO.Delivery();
            averageDelivery.setDeliveryName("平均日发货量");
            if (0 == deliverySum) {
                averageDelivery.setCurrentQuantity(0);
            } else {
                averageDelivery.setCurrentQuantity(deliverySum / 8);
            }
            deliveryList.add(averageDelivery);

            InventoryAndDeliveryDataVO.Delivery turnoverDaysDelivery = new InventoryAndDeliveryDataVO.Delivery();
            turnoverDaysDelivery.setDeliveryName("周转天数");
            if (0 == inventorySum || 0 == averageDelivery.getCurrentQuantity()) {
                turnoverDaysDelivery.setCurrentQuantity(0);
            } else {
                turnoverDaysDelivery.setCurrentQuantity(inventorySum / averageDelivery.getCurrentQuantity());
            }
            deliveryList.add(turnoverDaysDelivery);

            inventoryAndDeliveryDataVO.setDeliveryList(deliveryList);
            allResult.add(inventoryAndDeliveryDataVO);
        }

        // 分页处理
        int pageNum = inventoryAndDeliveryDTO.getPageNum();
        int pageSize = inventoryAndDeliveryDTO.getPageSize();
        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, allResult.size());
        if (startIndex >= allResult.size()) {
            return new PageInfo<>();
        }

        List<InventoryAndDeliveryDataVO> pagedResult = allResult.subList(startIndex, endIndex);
        // 计算当前页的库存列合计
        Map<String, List<InventoryAndDeliveryDataVO.Inventory>> columnInventorySums = new HashMap<>();
        for (InventoryAndDeliveryDataVO dataVO : pagedResult) {
            List<InventoryAndDeliveryDataVO.Inventory> inventoryList = dataVO.getInventoryList();
            for (InventoryAndDeliveryDataVO.Inventory inventory : inventoryList) {
                columnInventorySums.computeIfAbsent(inventory.getInventoryName(), k -> new ArrayList<>()).add(inventory);
            }
        }
        List<InventoryAndDeliveryDataVO.Inventory> totalInventoryList = new ArrayList<>();
        for (Map.Entry<String, List<InventoryAndDeliveryDataVO.Inventory>> entry : columnInventorySums.entrySet()) {
            String inventoryName = entry.getKey();
            List<InventoryAndDeliveryDataVO.Inventory> inventories = entry.getValue();
            int totalQuantity = inventories.stream().mapToInt(InventoryAndDeliveryDataVO.Inventory::getCurrentQuantity).sum();
            InventoryAndDeliveryDataVO.Inventory totalInventory = new InventoryAndDeliveryDataVO.Inventory();
            totalInventory.setInventoryName(inventoryName);
            totalInventory.setCurrentQuantity(totalQuantity);
            totalInventoryList.add(totalInventory);
        }

        // 计算当前页的发货列合计
        Map<String, Integer> columnDeliverySums = new HashMap<>();
        for (InventoryAndDeliveryDataVO dataVO : pagedResult) {
            List<InventoryAndDeliveryDataVO.Delivery> deliveryList = dataVO.getDeliveryList();
            for (InventoryAndDeliveryDataVO.Delivery delivery : deliveryList) {
                columnDeliverySums.put(delivery.getDeliveryName(), columnDeliverySums.getOrDefault(delivery.getDeliveryName(), 0) + delivery.getCurrentQuantity());
            }
        }
        List<InventoryAndDeliveryDataVO.Delivery> totalDeliveryList = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : columnDeliverySums.entrySet()) {
            String deliveryName = entry.getKey();
            int totalQuantity = entry.getValue();
            InventoryAndDeliveryDataVO.Delivery totalDelivery = new InventoryAndDeliveryDataVO.Delivery();
            totalDelivery.setDeliveryName(deliveryName);
            totalDelivery.setCurrentQuantity(totalQuantity);
            totalDeliveryList.add(totalDelivery);
        }

        // 添加列合计行
        InventoryAndDeliveryDataVO columnTotalVO = new InventoryAndDeliveryDataVO();
        columnTotalVO.setMaster("合计");

        // 自定义比较器
        Comparator<InventoryAndDeliveryDataVO.Inventory> inventoryComparator = Comparator
                .comparing((InventoryAndDeliveryDataVO.Inventory d) -> "合计".equals(d.getInventoryName()) ? 1 : 0)
                .thenComparing(InventoryAndDeliveryDataVO.Inventory::getInventoryName);
        totalInventoryList.sort(inventoryComparator);
        columnTotalVO.setInventoryList(totalInventoryList);

        // 自定义比较器
        Comparator<InventoryAndDeliveryDataVO.Delivery> deliveryComparator = Comparator
                .<InventoryAndDeliveryDataVO.Delivery, Integer>comparing(d -> isDate(d.getDeliveryName()) ? 0 : 1)
                .thenComparing(d -> {
                    if (isDate(d.getDeliveryName())) {
                        return LocalDate.parse(d.getDeliveryName(), ISO_LOCAL_DATE);
                    }
                    return LocalDate.MAX;
                });
        totalDeliveryList.sort(deliveryComparator);
        columnTotalVO.setDeliveryList(totalDeliveryList);
        pagedResult.add(columnTotalVO);

        PageInfo<InventoryAndDeliveryDataVO> pageInfo = new PageInfo<>(pagedResult);
        pageInfo.setTotal(allResult.size());
        return pageInfo;
    }

    /**
     * 计算并组装生产组织S1的库存数据
     *
     * @param stockPointCode                库存点
     * @param semiFinishedGoodsInventoryMap 半成品库存
     * @param finishInventoryMap            成品库存
     * @param entity                        主生产计划
     * @return 库存数据
     */
    private List<InventoryAndDeliveryDataVO.Inventory> learnInventoryDataS1(String stockPointCode,
                                                                            Map<String, List<InventoryBatchDetailVO>> semiFinishedGoodsInventoryMap,
                                                                            Map<String, List<InventoryBatchDetailVO>> finishInventoryMap,
                                                                            Map.Entry<String, List<MasterPlanWorkOrderBodyVO>> entity) {
        List<InventoryAndDeliveryDataVO.Inventory> inventoryList = new ArrayList<>();

        // 收集主生产计划物料数据
        List<String> masterPlanWorkOrderProductCodeList = entity.getValue().stream().map(MasterPlanWorkOrderBodyVO::getProductCode).distinct().collect(Collectors.toList());

        // 收集主生产计划-30物料数据
        List<String> masterPlanWorkOrderProductCode30List = entity.getValue().stream().map(data -> data.getProductCode() + "-30").distinct().collect(Collectors.toList());

        // 收集主生产计划-20物料数据
        List<String> masterPlanWorkOrderProductCode20List = entity.getValue().stream().map(data -> data.getProductCode() + "-20").distinct().collect(Collectors.toList());

        // 收集主生产计划-10物料数据
        List<String> masterPlanWorkOrderProductCode10List = entity.getValue().stream().map(data -> data.getProductCode() + "-10").distinct().collect(Collectors.toList());

        // 成品库（库存点为SJ的成品库存）
        int finishedInventory = 0;
        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(defaultScenario.getData(),
                "SALE_ORGANIZATION",
                "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        for (String productCode : masterPlanWorkOrderProductCodeList) {
            List<InventoryBatchDetailVO> inventoryBatchDetailVOS = finishInventoryMap.get(productCode + "&" + rangeData);
            if (CollectionUtils.isNotEmpty(inventoryBatchDetailVOS)) {
                finishedInventory += inventoryBatchDetailVOS.stream()
                        .filter(data -> StringUtils.isNotEmpty(data.getCurrentQuantity()))
                        .mapToInt(inventoryBatchDetailVO -> (int) Float.parseFloat(inventoryBatchDetailVO.getCurrentQuantity())).sum();
            }
        }
        InventoryAndDeliveryDataVO.Inventory finishedInventoryObj = new InventoryAndDeliveryDataVO.Inventory();
        finishedInventoryObj.setInventoryName("成品库");
        finishedInventoryObj.setCurrentQuantity(finishedInventory);
        inventoryList.add(finishedInventoryObj);

        // 包装（库存点为S1，物料编码为成品编码的库存）
        int packageInventory = 0;
        for (String productCode : masterPlanWorkOrderProductCodeList) {
            List<InventoryBatchDetailVO> inventoryBatchDetailVOS = finishInventoryMap.get(productCode + "&" + stockPointCode);
            if (CollectionUtils.isNotEmpty(inventoryBatchDetailVOS)) {
                packageInventory += inventoryBatchDetailVOS.stream()
                        .filter(data -> StringUtils.isNotEmpty(data.getCurrentQuantity()))
                        .mapToInt(inventoryBatchDetailVO -> (int) Float.parseFloat(inventoryBatchDetailVO.getCurrentQuantity())).sum();
            }
        }
        InventoryAndDeliveryDataVO.Inventory packageInventoryObj = new InventoryAndDeliveryDataVO.Inventory();
        packageInventoryObj.setInventoryName("包装");
        packageInventoryObj.setCurrentQuantity(packageInventory);
        inventoryList.add(packageInventoryObj);

        // 包装前库存（库存点为S1，合片后的半品库存（即物料编码为产品编码-30））
        int beforePackageInventory = 0;
        for (String productCode : masterPlanWorkOrderProductCode30List) {
            List<InventoryBatchDetailVO> inventoryBatchDetailVOS = semiFinishedGoodsInventoryMap.get(productCode + "&" + stockPointCode);
            if (CollectionUtils.isNotEmpty(inventoryBatchDetailVOS)) {
                beforePackageInventory += inventoryBatchDetailVOS.stream()
                        .filter(data -> StringUtils.isNotEmpty(data.getCurrentQuantity()))
                        .mapToInt(inventoryBatchDetailVO -> (int) Float.parseFloat(inventoryBatchDetailVO.getCurrentQuantity())).sum();
            }
        }
        InventoryAndDeliveryDataVO.Inventory beforePackageInventoryObj = new InventoryAndDeliveryDataVO.Inventory();
        beforePackageInventoryObj.setInventoryName("包装前库存");
        beforePackageInventoryObj.setCurrentQuantity(beforePackageInventory);
        inventoryList.add(beforePackageInventoryObj);

        // 成型后（库存点为S1，成型后的半品库存（即物料编码为产品编码-20））
        int afterMoldingInventory = 0;
        for (String productCode : masterPlanWorkOrderProductCode20List) {
            List<InventoryBatchDetailVO> inventoryBatchDetailVOS = semiFinishedGoodsInventoryMap.get(productCode + "&" + stockPointCode);
            if (CollectionUtils.isNotEmpty(inventoryBatchDetailVOS)) {
                afterMoldingInventory += inventoryBatchDetailVOS.stream()
                        .filter(data -> StringUtils.isNotEmpty(data.getCurrentQuantity()))
                        .mapToInt(inventoryBatchDetailVO -> (int) Float.parseFloat(inventoryBatchDetailVO.getCurrentQuantity())).sum();
            }
        }
        InventoryAndDeliveryDataVO.Inventory afterMoldingInventoryObj = new InventoryAndDeliveryDataVO.Inventory();
        afterMoldingInventoryObj.setInventoryName("成型后");
        afterMoldingInventoryObj.setCurrentQuantity(afterMoldingInventory);
        inventoryList.add(afterMoldingInventoryObj);

        // 预处理（库存点为S1，预处理后的半品库存（即物料编码为产品编码-10））
        int pretreatmentInventory = 0;
        for (String productCode : masterPlanWorkOrderProductCode10List) {
            List<InventoryBatchDetailVO> inventoryBatchDetailVOS = semiFinishedGoodsInventoryMap.get(productCode + "&" + stockPointCode);
            if (CollectionUtils.isNotEmpty(inventoryBatchDetailVOS)) {
                pretreatmentInventory += inventoryBatchDetailVOS.stream()
                        .filter(data -> StringUtils.isNotEmpty(data.getCurrentQuantity()))
                        .mapToInt(inventoryBatchDetailVO -> (int) Float.parseFloat(inventoryBatchDetailVO.getCurrentQuantity())).sum();
            }
        }
        InventoryAndDeliveryDataVO.Inventory pretreatmentInventoryObj = new InventoryAndDeliveryDataVO.Inventory();
        pretreatmentInventoryObj.setInventoryName("预处理");
        pretreatmentInventoryObj.setCurrentQuantity(pretreatmentInventory);
        inventoryList.add(pretreatmentInventoryObj);

        // 合计
        InventoryAndDeliveryDataVO.Inventory totalInventoryObj = new InventoryAndDeliveryDataVO.Inventory();
        totalInventoryObj.setInventoryName("合计");
        totalInventoryObj.setCurrentQuantity(inventoryList.stream().mapToInt(InventoryAndDeliveryDataVO.Inventory::getCurrentQuantity).sum());
        inventoryList.add(totalInventoryObj);

        return inventoryList;
    }

    /**
     * 计算并组装生产组织S2的库存数据
     *
     * @param stockPointCode                库存点
     * @param semiFinishedGoodsInventoryMap 半成品库存
     * @param finishInventoryMap            成品库存
     * @param entity                        主生产计划
     * @return 库存数据
     */
    private List<InventoryAndDeliveryDataVO.Inventory> learnInventoryDataS2(String stockPointCode,
                                                                            Map<String, List<InventoryBatchDetailVO>> semiFinishedGoodsInventoryMap,
                                                                            Map<String, List<InventoryBatchDetailVO>> finishInventoryMap,
                                                                            Map.Entry<String, List<MasterPlanWorkOrderBodyVO>> entity) {
        List<InventoryAndDeliveryDataVO.Inventory> inventoryList = new ArrayList<>();

        // 收集主生产计划物料数据
        List<String> masterPlanWorkOrderProductCodeList = entity.getValue().stream().map(MasterPlanWorkOrderBodyVO::getProductCode).distinct().collect(Collectors.toList());

        // 收集主生产计划-45物料数据
        List<String> masterPlanWorkOrderProductCode45List = entity.getValue().stream().map(data -> data.getProductCode() + "-45").distinct().collect(Collectors.toList());

        // 收集主生产计划-40物料数据
        List<String> masterPlanWorkOrderProductCode40List = entity.getValue().stream().map(data -> data.getProductCode() + "-40").distinct().collect(Collectors.toList());

        // 收集主生产计划-20物料数据
        List<String> masterPlanWorkOrderProductCode20List = entity.getValue().stream().map(data -> data.getProductCode() + "-20").distinct().collect(Collectors.toList());
        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(defaultScenario.getData(),
                "SALE_ORGANIZATION",
                "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        // 成品库（库存点为SJ的成品库存）
        int finishedInventory = 0;
        for (String productCode : masterPlanWorkOrderProductCodeList) {
            List<InventoryBatchDetailVO> inventoryBatchDetailVOS = finishInventoryMap.get(productCode + "&" + rangeData);
            if (CollectionUtils.isNotEmpty(inventoryBatchDetailVOS)) {
                finishedInventory += inventoryBatchDetailVOS.stream()
                        .filter(data -> StringUtils.isNotEmpty(data.getCurrentQuantity()))
                        .mapToInt(inventoryBatchDetailVO -> (int) Float.parseFloat(inventoryBatchDetailVO.getCurrentQuantity())).sum();
            }
        }
        InventoryAndDeliveryDataVO.Inventory finishedInventoryObj = new InventoryAndDeliveryDataVO.Inventory();
        finishedInventoryObj.setInventoryName("成品库");
        finishedInventoryObj.setCurrentQuantity(finishedInventory);
        inventoryList.add(finishedInventoryObj);

        // 包装（库存点为S2，物料编码为成品编码的库存）
        int packageInventory = 0;
        for (String productCode : masterPlanWorkOrderProductCodeList) {
            List<InventoryBatchDetailVO> inventoryBatchDetailVOS = finishInventoryMap.get(productCode + "&" + stockPointCode);
            if (CollectionUtils.isNotEmpty(inventoryBatchDetailVOS)) {
                packageInventory += inventoryBatchDetailVOS.stream()
                        .filter(data -> StringUtils.isNotEmpty(data.getCurrentQuantity()))
                        .mapToInt(inventoryBatchDetailVO -> (int) Float.parseFloat(inventoryBatchDetailVO.getCurrentQuantity())).sum();
            }
        }
        InventoryAndDeliveryDataVO.Inventory packageInventoryObj = new InventoryAndDeliveryDataVO.Inventory();
        packageInventoryObj.setInventoryName("包装");
        packageInventoryObj.setCurrentQuantity(packageInventory);
        inventoryList.add(packageInventoryObj);

        // 包装前库存（库存点为S2，合片后的半品库存（即物料编码为产品编码-45））
        int beforePackageInventory = 0;
        for (String productCode : masterPlanWorkOrderProductCode45List) {
            List<InventoryBatchDetailVO> inventoryBatchDetailVOS = semiFinishedGoodsInventoryMap.get(productCode + "&" + stockPointCode);
            if (CollectionUtils.isNotEmpty(inventoryBatchDetailVOS)) {
                beforePackageInventory += inventoryBatchDetailVOS.stream()
                        .filter(data -> StringUtils.isNotEmpty(data.getCurrentQuantity()))
                        .mapToInt(inventoryBatchDetailVO -> (int) Float.parseFloat(inventoryBatchDetailVO.getCurrentQuantity())).sum();
            }
        }
        InventoryAndDeliveryDataVO.Inventory beforePackageInventoryObj = new InventoryAndDeliveryDataVO.Inventory();
        beforePackageInventoryObj.setInventoryName("包装前库存");
        beforePackageInventoryObj.setCurrentQuantity(beforePackageInventory);
        inventoryList.add(beforePackageInventoryObj);

        // 钢化后（库存点为S2，成型后的半品库存（即物料编码为产品编码-40））
        int afterTemperingInventory = 0;
        for (String productCode : masterPlanWorkOrderProductCode40List) {
            List<InventoryBatchDetailVO> inventoryBatchDetailVOS = semiFinishedGoodsInventoryMap.get(productCode + "&" + stockPointCode);
            if (CollectionUtils.isNotEmpty(inventoryBatchDetailVOS)) {
                afterTemperingInventory += inventoryBatchDetailVOS.stream()
                        .filter(data -> StringUtils.isNotEmpty(data.getCurrentQuantity()))
                        .mapToInt(inventoryBatchDetailVO -> (int) Float.parseFloat(inventoryBatchDetailVO.getCurrentQuantity())).sum();
            }
        }
        InventoryAndDeliveryDataVO.Inventory afterTemperingInventoryObj = new InventoryAndDeliveryDataVO.Inventory();
        afterTemperingInventoryObj.setInventoryName("钢化后");
        afterTemperingInventoryObj.setCurrentQuantity(afterTemperingInventory);
        inventoryList.add(afterTemperingInventoryObj);

        // 预处理（库存点为S2，预处理后的半品库存（即物料编码为产品编码-20））
        int pretreatmentInventory = 0;
        for (String productCode : masterPlanWorkOrderProductCode20List) {
            List<InventoryBatchDetailVO> inventoryBatchDetailVOS = semiFinishedGoodsInventoryMap.get(productCode + "&" + stockPointCode);
            if (CollectionUtils.isNotEmpty(inventoryBatchDetailVOS)) {
                pretreatmentInventory += inventoryBatchDetailVOS.stream()
                        .filter(data -> StringUtils.isNotEmpty(data.getCurrentQuantity()))
                        .mapToInt(inventoryBatchDetailVO -> Integer.parseInt(inventoryBatchDetailVO.getCurrentQuantity())).sum();
            }
        }
        InventoryAndDeliveryDataVO.Inventory pretreatmentInventoryObj = new InventoryAndDeliveryDataVO.Inventory();
        pretreatmentInventoryObj.setInventoryName("预处理");
        pretreatmentInventoryObj.setCurrentQuantity(pretreatmentInventory);
        inventoryList.add(pretreatmentInventoryObj);

        // 合计
        InventoryAndDeliveryDataVO.Inventory totalInventoryObj = new InventoryAndDeliveryDataVO.Inventory();
        totalInventoryObj.setInventoryName("合计");
        totalInventoryObj.setCurrentQuantity(inventoryList.stream()
                .mapToInt(InventoryAndDeliveryDataVO.Inventory::getCurrentQuantity)
                .sum());
        inventoryList.add(totalInventoryObj);

        return inventoryList;
    }

    /**
     * 计算并组装日发货计划的库存数据
     *
     * @param stockPointCode       库存点
     * @param entity               生产计划
     * @param startDate            开始日期
     * @param endDate              结束日期
     * @param deliveryPlanGeneralViewVOList     发货计划
     * @return 库存数据
     */
    private List<InventoryAndDeliveryDataVO.Delivery> learnDeliveryData(String stockPointCode, Map.Entry<String, List<MasterPlanWorkOrderBodyVO>> entity,
                                                                        Date startDate, Date endDate,
                                                                        List<DeliveryPlanGeneralViewVO> deliveryPlanGeneralViewVOList) {

        List<InventoryAndDeliveryDataVO.Delivery> deliveryList = new ArrayList<>();
        // 收集生产计划物料编码
        List<String> masterPlanProductCode = entity.getValue().stream().map(MasterPlanWorkOrderBodyVO::getProductCode).distinct().collect(Collectors.toList());

        Map<String, Integer> deliveryQuantityMap = deliveryPlanGeneralViewVOList.stream()
                .filter(data -> masterPlanProductCode.contains(data.getProductCode()))
                .flatMap(data -> data.getDetailList().stream())
                .collect(Collectors.groupingBy(
                        DeliveryPlanGeneralViewDetailVO::getDemandTimeStr,
                        HashMap::new,
                        Collectors.summingInt(DeliveryPlanGeneralViewDetailVO::getDemandQuantity)
                ));

        // 获取时间范围
        List<Date> datesInRange = getDatesInRange(startDate, endDate);
        for (Date date : datesInRange) {
            Integer demandQuantityInventory = deliveryQuantityMap.getOrDefault(DateUtils.dateToString(date), 0);

            InventoryAndDeliveryDataVO.Delivery delivery = new InventoryAndDeliveryDataVO.Delivery();
            delivery.setDeliveryName(DateUtils.dateToString(date));
            delivery.setCurrentQuantity(demandQuantityInventory);
            deliveryList.add(delivery);
        }
        return deliveryList;
    }

    public static List<String> get30Day(Date now) {
        List<String> dateList = Lists.newArrayList(DateUtils.dateToString(now));
        for (int i = 1; i < 30; i++) {
            String date = DateUtils.dateToString(DateUtils.moveCalendar(now, Calendar.DAY_OF_YEAR, i));
            dateList.add(date);
        }
        return dateList;
    }


    public static List<Date> getDatesInRange(Date startDate, Date endDate) {
        List<Date> dateList = new ArrayList<>();

        // 使用 Calendar 来处理日期增减
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);

        // 遍历日期范围，将每一天的日期加入列表
        while (!calendar.getTime().after(endDate)) {
            dateList.add(calendar.getTime());
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }

        return dateList;
    }

    private boolean isDate(String str) {
        try {
            LocalDate.parse(str, ISO_LOCAL_DATE);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public DeliveryPlanPublishCheckVO doPublishCheck(DeliveryPlanDTO deliveryPlanDTO) {
        String versionCode = deliveryPlanDTO.getVersionCode();
        List<String> ids = deliveryPlanDTO.getIds();
        List<DeliveryPlanPO> deliveryPlanPOS = deliveryPlanDao.selectByPrimaryKeys(ids);
        DeliveryPlanPublishCheckVO returnVO = new DeliveryPlanPublishCheckVO();
        //1.校验偏差率
        checkDeliveryPlanDeviationRatio(deliveryPlanPOS, returnVO);
        //2.校验是否存在相同产品编码的发货计划未下发
        List<String> productCodes = deliveryPlanPOS.stream().map(DeliveryPlanPO::getProductCode)
                .distinct().collect(Collectors.toList());
        List<String> unkeys = deliveryPlanPOS.stream().map(e -> e.getOemCode().split("_")[0] + "&" + e.getProductCode())
                .distinct().collect(Collectors.toList());
        String queryParamStr = " version_code = '" + versionCode + "'";
        List<DeliveryPlanVO> deliveryPlanList = this.selectByPage(
                new Pagination(1, 99999), null, queryParamStr);
        deliveryPlanList = deliveryPlanList.stream().filter(e -> productCodes.contains(e.getProductCode())
        		&& !ids.contains(e.getId())
                && unkeys.contains(e.getOemCode().split("_")[0] + "&" + e.getProductCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deliveryPlanList)) {
            deliveryPlanList.get(0).setDateList(DeliveryPlanDateUtils.getDeliveryPlanDateList(deliveryPlanList.get(0).getVersionCode()));
        }
        returnVO.setDeliveryPlanList(deliveryPlanList);
        return returnVO;
    }

    /**
     * 校验偏差率
     * @param deliveryPlanPOS
     * @param returnVO
     */
	private void checkDeliveryPlanDeviationRatio(List<DeliveryPlanPO> deliveryPlanPOS,
			DeliveryPlanPublishCheckVO returnVO) {
		List<DeliveryPlanPO> outPutDeliveryPlanList = deliveryPlanPOS.stream()
        		.filter(e-> ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode().equals(e.getDemandCategory())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(outPutDeliveryPlanList)) {
        	List<String> deliveryPlanIds = deliveryPlanPOS.stream().map(DeliveryPlanPO::getId).collect(Collectors.toList());
        	//获取对应的详情,统计待发数量
        	List<DeliveryPlanDetailPO> detailList = deliveryPlanDetailDao.selectByParams(ImmutableMap.of(
        			"enabled", YesOrNoEnum.YES.getCode(),
        			"deliveryPlanDataIds" , deliveryPlanIds,
        			"demandTimeStart" , DateUtils.getDayFirstTime(new Date()),
        			"demandTimeEnd" , DateUtils.getMonthLastDay(new Date())));
        	 Map<String, Integer> deliveryPlanDetailMap = detailList.stream().collect(Collectors.groupingBy(
                    		 DeliveryPlanDetailPO::getDeliveryPlanDataId, Collectors.summingInt(DeliveryPlanDetailPO::getDemandQuantity)));
        	 Map<String,String> deliveryPlanMap = outPutDeliveryPlanList.stream()
        			 .collect(Collectors.toMap(e -> String.join("&", e.getOemCode(), e.getProductCode()),
        					 DeliveryPlanPO::getId,(v1, v2) -> v1));
        	 List<String> productCodes = outPutDeliveryPlanList.stream().map(DeliveryPlanPO::getProductCode)
                    .distinct().collect(Collectors.toList());
        	 List<String> oemCodes = outPutDeliveryPlanList.stream().map(DeliveryPlanPO::getOemCode)
                    .distinct().collect(Collectors.toList());
        	 //统计已发数量（仓库发货数量+中转库发货数量）
        	 SwitchRelationVO switchRelation = dfpSwitchRelationBetweenProductService.getSwitchRelation(oemCodes,
                     productCodes);
             List<String> allProductCodes = switchRelation.getAllProductCodes();
             Map<String, String> newOldMap = switchRelation.getNewOldMap();
             Map<String, String> oldNewMap = switchRelation.getOldNewMap();
             Map<String, String> toNewOldMap = switchRelation.getNewOldMap();
             Map<String, String> toOldNewMap = switchRelation.getOldNewMap();
             Map<String, Object> releaseQueryMap = new HashMap<>();
             releaseQueryMap.put("oemCodes", oemCodes);
             releaseQueryMap.put("productCodes", allProductCodes);
             releaseQueryMap.put("beginDate", DateUtils.dateToString(DateUtils.getMonthFirstDay(new Date())));
             releaseQueryMap.put("endDate", DateUtils.dateToString(DateUtils.moveCalendar(new Date(), Calendar.DAY_OF_YEAR, 1),
                     DateUtils.COMMON_DATE_STR3));
             List<WarehouseReleaseRecordMonthVO> warehouseReleaseRecords = warehouseReleaseRecordService.selectMonthVOByParams(releaseQueryMap);
             Map<String, BigDecimal> qtyMap = warehouseReleaseRecords.stream().collect(Collectors.groupingBy(item ->
                     item.getOemCode() + Constants.DELIMITER + item.getItemCode(), Collectors
                     .reducing(BigDecimal.ZERO, WarehouseReleaseRecordMonthVO::getSumQty, BigDecimal::add)));
             for (WarehouseReleaseRecordMonthVO item : warehouseReleaseRecords) {
            	 BigDecimal sumQty = item.getSumQty();
                 String oemCode = item.getOemCode();
                 String itemCode = item.getItemCode();
                 if (newOldMap.containsKey(itemCode)) {
                     String xItemCode = newOldMap.get(itemCode);
                     String xKey = String.join(Constants.DELIMITER, oemCode, xItemCode);
                     sumQty = sumQty.add(qtyMap.getOrDefault(xKey, BigDecimal.ZERO));
                 }
                 if (oldNewMap.containsKey(itemCode)) {
                     String yItemCode = oldNewMap.get(itemCode);
                     String yKey = String.join(Constants.DELIMITER, oemCode, yItemCode);
                     sumQty = sumQty.add(qtyMap.getOrDefault(yKey, BigDecimal.ZERO));
                 }
                 item.setSumQty(sumQty);
             }
             Map<String, BigDecimal> releaseQtyMap = warehouseReleaseRecords.stream().collect(Collectors
                     .groupingBy(item -> String.join(Constants.DELIMITER, item.getOemCode(),
                             item.getItemCode()), Collectors.reducing(BigDecimal.ZERO,
                             WarehouseReleaseRecordMonthVO::getSumQty, BigDecimal::add)));
             //获取仓库发货至中转库的发货数据
             List<WarehouseReleaseToWarehouseMonthVO> warehouseReleaseToWarehouses = warehouseReleaseToWarehouseService
            		 .selectMonthVOByParams(releaseQueryMap);
             Map<String, BigDecimal> toQtyMap = warehouseReleaseToWarehouses.stream()
            		 .collect(Collectors.groupingBy(item ->item.getOemCode() + Constants.DELIMITER + item.getItemCode(), Collectors
    				 .reducing(BigDecimal.ZERO, WarehouseReleaseToWarehouseMonthVO::getSumQty, BigDecimal::add)));
             for (WarehouseReleaseToWarehouseMonthVO item : warehouseReleaseToWarehouses) {
            	 BigDecimal sumQty = item.getSumQty();
            	 String oemCode = item.getOemCode();
            	 String itemCode = item.getItemCode();
            	 if (toNewOldMap.containsKey(itemCode)) {
            		 String xItemCode = toNewOldMap.get(itemCode);
            		 String xKey = String.join(Constants.DELIMITER, oemCode, xItemCode);
            		 sumQty = sumQty.add(toQtyMap.getOrDefault(xKey, BigDecimal.ZERO));
            	 }
            	 if (toOldNewMap.containsKey(itemCode)) {
            		 String yItemCode = toOldNewMap.get(itemCode);
            		 String yKey = String.join(Constants.DELIMITER, oemCode, yItemCode);
            		 sumQty = sumQty.add(toQtyMap.getOrDefault(yKey, BigDecimal.ZERO));
 		        	}
            	 item.setSumQty(sumQty);
             }
             Map<String, BigDecimal> releaseToQtyMap = warehouseReleaseToWarehouses.stream().collect(Collectors
                     .groupingBy(item -> String.join(Constants.DELIMITER, item.getOemCode(),
                             item.getItemCode()), Collectors.reducing(BigDecimal.ZERO,
                            		 WarehouseReleaseToWarehouseMonthVO::getSumQty, BigDecimal::add)));
             //查询最新已发布的一致性需求预测版本
             ConsistenceDemandForecastVersionVO versionInfo = consistenceDemandForecastVersionService
            		 .selectOneMaxVersionByParams(ImmutableMap.of(
            				 "versionStatus" , PublishStatusEnum.PUBLISHED.getCode()));
             if(versionInfo == null) {
            	 throw new BusinessException("未获取到最新已发布版本的一致性需求预测版本!");
             }
             List<ConsistenceDemandForecastDataVO> cdfdList = consistenceDemandForecastDataService.selectByParams(ImmutableMap.of(
            		 "versionId" , versionInfo.getId(),
            		 "enabled", YesOrNoEnum.YES.getCode(),
            		 "demandCategory" , ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode(),
            		 "oemCodeList" , oemCodes,
            		 "productCodeList" , productCodes));
             if(CollectionUtils.isEmpty(cdfdList)) {
            	 List<String> throwMsgs = new ArrayList<>();
            	 for (Entry<String, String> deliveryPlanEntry : deliveryPlanMap.entrySet()) {
            		 String oemCode = deliveryPlanEntry.getKey().split("&")[0];
                	 String productCode = deliveryPlanEntry.getKey().split("&")[1];
                	 throwMsgs.add("主机厂编码"+ oemCode +"，产品编码"+ productCode +"没有最新的一致性需求预测，请发布一致性需求预测后重试");
            	 }
            	 throw new BusinessException(String.join("<br/>", throwMsgs));
             }
             Map<String,String> cdfdMap = cdfdList.stream()
        			 .collect(Collectors.toMap(e -> String.join("&", e.getOemCode(), e.getProductCode()),
        					 ConsistenceDemandForecastDataVO::getId,(v1, v2) -> v1));
             List<String> cdfdIdList = cdfdList.stream().map(ConsistenceDemandForecastDataVO::getId).collect(Collectors.toList());
             List<ConsistenceDemandForecastDataDetailVO> cdfdDetailList = consistenceDemandForecastDataDetailService.selectByParams(ImmutableMap.of(
            		 "consistenceDemandForecastDataIdList" , cdfdIdList,
            		 "enabled", YesOrNoEnum.YES.getCode(),
            		 "forecastTimeYearMonth" , DateUtils.dateToString(new Date(), DateUtils.YEAR_MONTH)));
             Map<String, ConsistenceDemandForecastDataDetailVO> cdfdDetailMap = cdfdDetailList.stream()
            		 .collect(Collectors.toMap(ConsistenceDemandForecastDataDetailVO::getConsistenceDemandForecastDataId,
            		 Function.identity(), (v1, v2) -> v1));

             //校验偏差率
             List<String> throwMsgs = new ArrayList<>();
             List<ConsistenceDemandForecastCheckVO> forecastCheckList = new ArrayList<>();
             for (Entry<String, String> deliveryPlanEntry : deliveryPlanMap.entrySet()) {
            	 String oemCode = deliveryPlanEntry.getKey().split("&")[0];
            	 String productCode = deliveryPlanEntry.getKey().split("&")[1];
            	 //获取预测值
            	 String cdfdId = cdfdMap.get(deliveryPlanEntry.getKey());
            	 if(StringUtils.isEmpty(cdfdId)) {
            		 //主机厂编码1，产品编码1没有最新的一致性需求预测，请发布一致性需求预测后重试
            		 throwMsgs.add("主机厂编码"+ oemCode +"，产品编码"+ productCode +"没有最新的一致性需求预测，请发布一致性需求预测后重试");
            		 continue;
            	 }
            	 ConsistenceDemandForecastDataDetailVO consistenceDemandForecastDataDetailVO = cdfdDetailMap.get(cdfdId);
            	 if(consistenceDemandForecastDataDetailVO == null) {
            		 throwMsgs.add("主机厂编码"+ oemCode +"，产品编码"+ productCode +"没有最新的一致性需求预测，请发布一致性需求预测后重试");
            		 continue;
            	 }
            	 BigDecimal demandForecast = consistenceDemandForecastDataDetailVO.getForecastQuantity();
            	 Integer waitDeliveryQty = deliveryPlanDetailMap.get(deliveryPlanEntry.getValue());
            	 waitDeliveryQty = waitDeliveryQty == null ? 0 : waitDeliveryQty;
            	 //获取仓库+中转库的发货数量
            	 BigDecimal deliveryQty = releaseQtyMap
            			 .getOrDefault(String.join(Constants.DELIMITER, oemCode, productCode), BigDecimal.ZERO)
            	 	.add(releaseToQtyMap.getOrDefault(String.join(Constants.DELIMITER, oemCode, productCode), BigDecimal.ZERO));
            	 BigDecimal totalDeliveryQty = deliveryQty.add(BigDecimal.valueOf(waitDeliveryQty));
            	 totalDeliveryQty = totalDeliveryQty.subtract(demandForecast);
            	 //获取预测值
            	 BigDecimal deviationRate = BigDecimal.ZERO;
            	 if (BigDecimal.ZERO.compareTo(demandForecast) < 0) {
            		 deviationRate = BigDecimal.valueOf(totalDeliveryQty.multiply(BigDecimal.valueOf(100)).divide(demandForecast, 0,
                             RoundingMode.HALF_DOWN).intValue()).abs();
            	 }else if(BigDecimal.ZERO.compareTo(demandForecast) == 0 && BigDecimal.ZERO.compareTo(totalDeliveryQty) != 0){
            		 //当需求预测值为0时，当月已发+待发不为0时，偏差率默认100%
            		 deviationRate = BigDecimal.valueOf(100);
            	 }
            	 //如果有偏差率的绝对值大于等于15%进行弹窗提示
            	 if(deviationRate.compareTo(BigDecimal.valueOf(15)) >= 0) {
            		 ConsistenceDemandForecastCheckVO forecastCheck = new ConsistenceDemandForecastCheckVO();
            		 forecastCheck.setId(consistenceDemandForecastDataDetailVO.getId());
            		 forecastCheck.setOemCode(oemCode);
            		 forecastCheck.setProductCode(productCode);
            		 forecastCheck.setDeliveryQty(deliveryQty);
            		 forecastCheck.setWaitDeliveryQty(BigDecimal.valueOf(waitDeliveryQty));
            		 forecastCheck.setForecastQuantity(demandForecast);
            		 forecastCheck.setDeviationRate(deviationRate.stripTrailingZeros().toPlainString() + "%");
            		 forecastCheckList.add(forecastCheck);
            	 }
             }
             if(CollectionUtils.isNotEmpty(throwMsgs)) {
            	 throw new BusinessException(String.join("<br/>", throwMsgs));
             }
             if(CollectionUtils.isNotEmpty(forecastCheckList)) {
            	 returnVO.setCheckMsg("存在当月发货计划和预测偏差超过15%的数据，请点击取消按钮更新装车需求，或点击发布按钮更新预测数据并发布");
            	 returnVO.setForecastCheckList(forecastCheckList);
             }
        }
	}

	@Override
	public List<DeliveryPlanVO> pageDeliveryReport(Pagination pagination, String sortParam, String queryCriteriaParam) {
		PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
		List<DeliveryPlanVO> dataList = deliveryPlanDao.selectDeliveryReport(sortParam, queryCriteriaParam);
		if (CollectionUtils.isEmpty(dataList))
            return dataList;
        List<String> deliveryPlanIds = dataList.stream().map(DeliveryPlanVO::getId).collect(Collectors.toList());
        List<DeliveryPlanDetailPO> deliveryPlanDetails = deliveryPlanDetailDao.selectByDeliveryPlanIds(deliveryPlanIds);
        Map<String, List<DeliveryPlanDetailPO>> detailMap =
                deliveryPlanDetails.stream().collect(Collectors.groupingBy(DeliveryPlanDetailPO::getDeliveryPlanDataId));
        //获取计划员信息
        List<String> userIds = new ArrayList<>();
        for (DeliveryPlanVO item : dataList) {
        	userIds.addAll(Arrays.asList(item.getOrderPlanner().split(",")));
		}
        userIds = new ArrayList<>(new HashSet<>(userIds));
        // 查询用户列表
        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.IPS.getCode(), TenantCodeEnum.FYQB.getCode());
        String scenario = defaultScenario.getData();
        Map<String, String> userMap = ipsNewFeign.selectUserByParams(scenario, ImmutableMap.of(
    			"ids" , userIds)).stream().collect(Collectors
                .toMap(User::getId, User::getCnName, (t1, t2) -> t2));
        dataList.forEach( data -> {
        	data.setDetailList(DeliveryPlanDetailConvertor.INSTANCE.po2Vos(detailMap.get(data.getId())));
        	//处理计划员
        	if(StringUtils.isNotEmpty(data.getOrderPlanner())) {
        		List<String> currUserIds = Arrays.asList(data.getOrderPlanner().split(","));
        		List<String> userNames = new ArrayList<>();
        		for (String userId : currUserIds) {
					if(userMap.containsKey(userId)){
						userNames.add(userMap.get(userId));
					}
				}
        		data.setOrderPlanner(String.join(",", userNames));
        	}
        });
        return dataList;
	}

    @Override
    public BaseResponse executionSend() {
        //获取最初的数据
        List<DeliveryPlanVO> deliveryPlanVOS = this.pageDeliveryReportSync();
        // 获取ExecutionMonitorResultVO存在的字段
        Class<? extends DeliveryPlanVO> aClass = deliveryPlanVOS.get(0).getClass();
        Field[] declaredFields = aClass.getDeclaredFields();
        // 设置所有字段可访问
        for (Field field : declaredFields) {
            field.setAccessible(true);
        }
        // 需求类型
        String proCode = ProductionDemandTypeEnum.PROJECT_DEMAND.getCode();
        String proDesc = ProductionDemandTypeEnum.PROJECT_DEMAND.getDesc();
        String outCode = ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode();
        String outDesc = ProductionDemandTypeEnum.OUTPUT_DEMAND.getDesc();
        deliveryPlanVOS.stream().forEach(item -> {
            if (proCode.equals(item.getDemandCategory())){
                item.setDemandCategory(proDesc);
            } else if (outCode.equals(item.getDemandCategory())){
                item.setDemandCategory(outDesc);
            }
        });
        //按照订单员，主机厂，物料排序
        deliveryPlanVOS
                .sort(Comparator.comparing(DeliveryPlanVO::getOrderPlanner)
                        .thenComparing(DeliveryPlanVO::getOemCode)
                        .thenComparing(DeliveryPlanVO::getProductCode));
        //将数据转化为List<map>
        List<Map<String,Object>> excelList = excelDataProcessor(deliveryPlanVOS, declaredFields);

        // 获取预警配置文件
        List<WarningSqlSettingVO> warningSqlSettingVo = ipsNewFeign.getWarningSql("BPIM-YJ-A00600");
        if (warningSqlSettingVo.isEmpty()) {
            log.error("匹配不到预警编码为BPIM-YJ-A00600的预警配置数据，请让管理员进行配置。");
            return BaseResponse.error("匹配不到预警编码为BPIM-YJ-A00600的预警配置数据，请让管理员进行配置。");
        } else {
            WarningSqlSettingVO warningSqlSettingVO = warningSqlSettingVo.get(0);
            Map<String, Object> map = new HashMap<>();
            map.put("data", excelList);
            map.put("mainData", warningSqlSettingVO);
            BaseResponse<String> dcpBaseResponse = newDcpFeign.callExternalApi(TenantCodeEnum.FYQB.getCode(),
                    ApiSourceEnum.FONE.getCode(), ApiCategoryEnum.EMAIL_MESSAGE.getCode(), map);
            if (Boolean.TRUE.equals(dcpBaseResponse.getSuccess())) {
                return BaseResponse.success("发送成功.");
            } else {
                return dcpBaseResponse;
            }
        }
    }

    private static List<Map<String, Object>> excelDataProcessor(List<DeliveryPlanVO> executionMonitorResults,
                                                                Field[] declaredFields) {
        List<Map<String, Object>> excelList = new ArrayList<>();
        try {
            SimpleDateFormat format = new SimpleDateFormat("MM-dd");
            //当天0刻
            Date startOfToday = Date.from(LocalDate.now()
                    .atStartOfDay(ZoneId.systemDefault())
                    .toInstant());
            Date oneMonthDate = Date.from(LocalDateTime.now()
                    .plusMonths(1)
                    .plusMonths(1)
                    .with(LocalTime.of(23, 59, 59))
                    .atZone(ZoneId.systemDefault())
                    .toInstant());
            for (DeliveryPlanVO vo : executionMonitorResults) {
                Map<String, Object> rowMap = new LinkedHashMap<>();
                for (Field field : declaredFields) {
                    Object value = field.get(vo);
                    if (!"serialVersionUID".equals(field.getName()) && !"versionId".equals(field.getName()) && !"detailList".equals(field.getName()) && value != null ) {
                        ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
                        rowMap.put(annotation != null ? annotation.value() : field.getName(), value);
                    }
                }
                List<DeliveryPlanDetailVO> detailList = vo.getDetailList();
                Collections.sort(detailList,Comparator.comparing(DeliveryPlanDetailVO::getDemandTime));
                detailList.stream()
                        .filter(item -> item.getDemandTime().compareTo(startOfToday) >= 0
                                && item.getDemandTime().compareTo(oneMonthDate) <= 0)
                        .forEach(item -> rowMap.put(format.format(item.getDemandTime()), item.getDemandQuantity()));
                excelList.add(rowMap);
            }
            return excelList;
        } catch (IllegalAccessException e) {
            throw new BusinessException("反射访问字段失败：" + e.getMessage());
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * 用于预警报表生成的数据
     * @return
     */
    private List<DeliveryPlanVO> pageDeliveryReportSync() {
        //获取全部数据
        List<DeliveryPlanVO> dataList = deliveryPlanDao.selectDeliveryReport(null, null);
        //获取最新版本
        DeliveryPlanVersionVO deliveryPlanVersionVO = deliveryPlanVersionService.selectLatestVersionByParams(ImmutableMap.of());
        //过滤最新版本的数据
        dataList = dataList.parallelStream()
                .filter(item -> deliveryPlanVersionVO.getId().equals(item.getVersionId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dataList)){
            return dataList;
        }
        //获取所有主键ID并组成集合去详情表查询
        List<String> deliveryPlanIds = dataList.stream().map(DeliveryPlanVO::getId).collect(Collectors.toList());
        List<DeliveryPlanDetailPO> deliveryPlanDetails = deliveryPlanDetailDao.selectByDeliveryPlanIds(deliveryPlanIds);
        Map<String, List<DeliveryPlanDetailPO>> detailMap =
                deliveryPlanDetails.stream().collect(Collectors.groupingBy(DeliveryPlanDetailPO::getDeliveryPlanDataId));
        //获取计划员信息
        List<String> userIds = new ArrayList<>();
        for (DeliveryPlanVO item : dataList) {
            userIds.addAll(Arrays.asList(item.getOrderPlanner().split(",")));
        }
        userIds = new ArrayList<>(new HashSet<>(userIds));
        // 查询用户列表
        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.IPS.getCode(), TenantCodeEnum.FYQB.getCode());
        String scenario = defaultScenario.getData();
        Map<String, String> userMap = ipsNewFeign.selectUserByParams(scenario, ImmutableMap.of(
                "ids" , userIds)).stream().collect(Collectors
                .toMap(User::getId, User::getCnName, (t1, t2) -> t2));
        dataList.forEach( data -> {
            data.setDetailList(DeliveryPlanDetailConvertor.INSTANCE.po2Vos(detailMap.get(data.getId())));
            //处理计划员
            if(StringUtils.isNotEmpty(data.getOrderPlanner())) {
                List<String> currUserIds = Arrays.asList(data.getOrderPlanner().split(","));
                List<String> userNames = new ArrayList<>();
                for (String userId : currUserIds) {
                    if(userMap.containsKey(userId)){
                        userNames.add(userMap.get(userId));
                    }
                }
                data.setOrderPlanner(String.join(",", userNames));
            }
        });
        return dataList;
    }

}