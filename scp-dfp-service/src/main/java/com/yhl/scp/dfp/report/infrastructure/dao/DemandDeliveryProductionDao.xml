<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.report.infrastructure.dao.DemandDeliveryProductionDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.report.vo.DeliveryPlanPublishStatisticVO">
        <!--@Table fdp_delivery_plan-->
        <result column="demand_category" jdbcType="VARCHAR" property="demandCategory"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
        <result column="order_planner" jdbcType="VARCHAR" property="orderPlanner"/>
    </resultMap>
    <resultMap id="AggregationResultMap" extends="BaseResultMap"
               type="com.yhl.scp.dfp.report.vo.DeliveryPlanPublishStatisticVO">
        <result column="oem_name" jdbcType="VARCHAR" property="oemName"/>
        <result column="market_type" jdbcType="VARCHAR" property="marketType"/>
    </resultMap>
    <resultMap id="StatisticsResultMap" extends="BaseResultMap"
               type="com.yhl.scp.dfp.report.vo.DeliveryPlanPublishStatisticVO">
        <result column="publish_status" jdbcType="VARCHAR" property="publishStatus"/>
        <result column="counts" jdbcType="INTEGER" property="counts"/>
        <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        demand_category,oem_code,vehicle_model_code,order_planner
    </sql>
    <sql id="Aggregation_Column_List">
        <include refid="Base_Column_List"/>,oem_name,market_type
    </sql>
    <sql id="Statistics_Column_List">
        <include refid="Base_Column_List"/>,publish_status,counts,last_update_time
    </sql>
    <select id="selectAll" resultMap="AggregationResultMap">
        select
        <include refid="Aggregation_Column_List"/>
        from v_fdp_delivery_plan_publish_aggregation
    </select>
    <select id="selectByCondition" resultMap="AggregationResultMap">
        select
        <include refid="Aggregation_Column_List"/>
        from v_fdp_delivery_plan_publish_aggregation
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <select id="selectStatistics" resultMap="StatisticsResultMap">
        select
        <include refid="Statistics_Column_List"/>
        from v_fdp_delivery_plan_publish_statistics
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
</mapper>