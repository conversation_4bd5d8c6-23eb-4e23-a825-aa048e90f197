package com.yhl.scp.dfp.newProduct.service.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.util.MapUtils;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.common.constants.Constants;
import com.yhl.scp.common.excel.enums.ImportObjectEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.dfp.common.constants.DfpConstants;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.common.enums.SubmissionTypeEnum;
import com.yhl.scp.dfp.enums.ObjectTypeEnum;
import com.yhl.scp.dfp.newProduct.convertor.NewProductTrialSubmissionConvertor;
import com.yhl.scp.dfp.newProduct.convertor.NewProductTrialSubmissionDetailConvertor;
import com.yhl.scp.dfp.newProduct.domain.entity.NewProductTrialSubmissionDO;
import com.yhl.scp.dfp.newProduct.domain.entity.NewProductTrialSubmissionDetailDO;
import com.yhl.scp.dfp.newProduct.domain.service.NewProductTrialSubmissionDetailDomainService;
import com.yhl.scp.dfp.newProduct.domain.service.NewProductTrialSubmissionDomainService;
import com.yhl.scp.dfp.newProduct.dto.NewProductTrialSubmissionDTO;
import com.yhl.scp.dfp.newProduct.dto.NewProductTrialSubmissionDetailDTO;
import com.yhl.scp.dfp.newProduct.dto.NewProductTrialSubmissionExcelDTO;
import com.yhl.scp.dfp.newProduct.dto.NewProductTrialSubmissionInsertDTO;
import com.yhl.scp.dfp.newProduct.infrastructure.dao.NewProductTrialSubmissionDao;
import com.yhl.scp.dfp.newProduct.infrastructure.dao.NewProductTrialSubmissionDetailDao;
import com.yhl.scp.dfp.newProduct.infrastructure.po.NewProductTrialSubmissionDetailPO;
import com.yhl.scp.dfp.newProduct.infrastructure.po.NewProductTrialSubmissionPO;
import com.yhl.scp.dfp.newProduct.service.NewProductTrialSubmissionDetailService;
import com.yhl.scp.dfp.newProduct.service.NewProductTrialSubmissionService;
import com.yhl.scp.dfp.newProduct.vo.NewProductTrialSubmissionDetailVO;
import com.yhl.scp.dfp.newProduct.vo.NewProductTrialSubmissionVO;
import com.yhl.scp.dfp.part.service.PartRelationMapService;
import com.yhl.scp.dfp.part.vo.PartRelationMapVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>NewProductTrialSubmissionServiceImpl</code>
 * <p>
 * 新品试制提报应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:47:11
 */
@Slf4j
@Service
public class NewProductTrialSubmissionServiceImpl extends AbstractService implements NewProductTrialSubmissionService {

    @Resource
    private NewProductTrialSubmissionDao newProductTrialSubmissionDao;

    @Resource
    private NewProductTrialSubmissionDomainService newProductTrialSubmissionDomainService;

    @Resource
    private NewProductTrialSubmissionDetailDomainService detailDomainService;

    @Resource
    private NewProductTrialSubmissionDetailService detailService;

    @Resource
    private NewProductTrialSubmissionDetailDao detailDao;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private PartRelationMapService partRelationMapService;

    @Override
    public BaseResponse<Void> doCreate(NewProductTrialSubmissionDTO submissionDTO) {
        NewProductTrialSubmissionPO submissionPO = new NewProductTrialSubmissionPO();
        NewProductTrialSubmissionDetailPO detailPO = new NewProductTrialSubmissionDetailPO();
        submissionPO.setOemCode(submissionDTO.getDeptName());
        submissionPO.setProjectNumber(submissionDTO.getProjectNumber());
        submissionPO.setProjectName(submissionDTO.getProjectName());
        submissionPO.setTrialProduct(submissionDTO.getTrialProduct());
        submissionPO.setTrialPurpose(submissionDTO.getTrialPurpose());
        submissionPO.setTrialProcess(submissionDTO.getTrialProcess());
//        submissionPO.setProductCode(submissionDTO.getProductCode());
//        submissionPO.setLoadingPosition(submissionDTO.getLoadingPosition());
//        submissionPO.setCuttingDrawingNumber(submissionDTO.getCuttingDrawingNumber());
//        submissionPO.setPartName(submissionDTO.getPartName());

        // 1.数据校验
        NewProductTrialSubmissionDO submissionDO = NewProductTrialSubmissionConvertor.INSTANCE.po2Do(submissionPO);
        newProductTrialSubmissionDomainService.validation(submissionDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(submissionPO);
        newProductTrialSubmissionDao.insertWithPrimaryKey(submissionPO);

        detailPO.setSubmissionId(submissionPO.getId());
        detailPO.setFeedingUnit(submissionDTO.getFeedingUnit());
        detailPO.setDemandTime(submissionDTO.getDemandTime());
        detailPO.setDemandQuantity(submissionDTO.getDemandQuantity());
        detailPO.setFeedingQuantity(submissionDTO.getFeedingQuantity());
//        detailPO.setDemandType(submissionDTO.getDemandType());
        // 1.数据校验
        NewProductTrialSubmissionDetailDO detailDO = NewProductTrialSubmissionDetailConvertor.INSTANCE.po2Do(detailPO);
        detailDomainService.validation(detailDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(detailPO);
        detailDao.insertWithPrimaryKey(detailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(NewProductTrialSubmissionDTO submissionDTO) {
        NewProductTrialSubmissionPO submissionPO = new NewProductTrialSubmissionPO();
        NewProductTrialSubmissionDetailPO detailPO = new NewProductTrialSubmissionDetailPO();
        submissionPO.setOemCode(submissionDTO.getDeptName());
        submissionPO.setProjectNumber(submissionDTO.getProjectNumber());
        submissionPO.setProjectName(submissionDTO.getProjectName());
        submissionPO.setTrialProduct(submissionDTO.getTrialProduct());
        submissionPO.setTrialPurpose(submissionDTO.getTrialPurpose());
        submissionPO.setTrialProcess(submissionDTO.getTrialProcess());
//        submissionPO.setProductCode(submissionDTO.getProductCode());
//        submissionPO.setLoadingPosition(submissionDTO.getLoadingPosition());
//        submissionPO.setCuttingDrawingNumber(submissionDTO.getCuttingDrawingNumber());
//        submissionPO.setPartName(submissionDTO.getPartName());
        // 2.数据持久化
        BasePOUtils.updateFiller(submissionPO);
        newProductTrialSubmissionDao.update(submissionPO);
        Map<String,Object> queryMap = new HashMap<>();
        queryMap.put("submission_id",submissionPO.getId());
        List<NewProductTrialSubmissionDetailPO> detailPOList = detailDao.selectByParams(queryMap);
        for (NewProductTrialSubmissionDetailPO detailPo : detailPOList) {
            String date = DateUtils.dateToString(detailPo.getDemandTime(), "yyyyMMdd");
            if (submissionDTO.getDateNumMap().containsKey(date)){
                detailPO.setDemandQuantity(submissionDTO.getDateNumMap().get(date));
                // 数据持久化
                BasePOUtils.updateFiller(detailPo);
                detailDao.update(detailPo);
            }
        }
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<NewProductTrialSubmissionDTO> list) {
        List<NewProductTrialSubmissionPO> newList = NewProductTrialSubmissionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        newProductTrialSubmissionDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<NewProductTrialSubmissionDTO> list) {
        List<NewProductTrialSubmissionPO> newList = NewProductTrialSubmissionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        newProductTrialSubmissionDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            detailDao.deleteBatchBySubmissionIds(idList);
            return newProductTrialSubmissionDao.deleteBatch(idList);
        }
        detailDao.deleteBySubmissionId(idList.get(0));
        return newProductTrialSubmissionDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public NewProductTrialSubmissionVO selectByPrimaryKey(String id) {
        NewProductTrialSubmissionPO po = newProductTrialSubmissionDao.selectByPrimaryKey(id);
        return NewProductTrialSubmissionConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_fdp_new_product_trial_submission")
    public List<NewProductTrialSubmissionVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        List<NewProductTrialSubmissionVO> submissionVOList = this.selectByCondition(sortParam, queryCriteriaParam);
//        if (CollectionUtils.isEmpty(submissionVOList)) return submissionVOList;
        // 获取主键id查询详情表
//        List<String> submissionIdList = submissionVOList.stream().map(NewProductTrialSubmissionVO::getId).collect(Collectors.toList());
//        List<NewProductTrialSubmissionDetailVO> detailVOList = detailDao.selectBySubmissionIdList(submissionIdList);
//        List<Date> dateList = detailVOList.stream().map(NewProductTrialSubmissionDetailVO::getDemandTime).distinct().collect(Collectors.toList());
//        Map<String, List<NewProductTrialSubmissionDetailVO>> detailGroup = detailVOList.stream().collect(Collectors.groupingBy(k -> k.getSubmissionId() + Constants.DELIMITER + k.getDemandTime()));
//        Map<String, List<NewProductTrialSubmissionDetailVO>> detailGroup = detailVOList.stream().collect(Collectors.groupingBy(NewProductTrialSubmissionDetailVO::getSubmissionId));
        // map赋值
//        for (NewProductTrialSubmissionVO submissionVO : submissionVOList) {
//            //获取详情数据
//            List<NewProductTrialSubmissionDetailVO> newProductTrialSubmissionDetailVOS = detailGroup.get(submissionVO.getId());
//            submissionVO.setDemandDateList(dateList);
//            submissionVO.setDetailVOList(newProductTrialSubmissionDetailVOS);
//            Map<String, BigDecimal> dateNumMap = new HashMap<>();
//            for (Date date : dateList) {
//                String key = submissionVO.getId() + Constants.DELIMITER + date;
//                String strDate = DateUtils.dateToString(date, "yyyyMMdd");
//                BigDecimal reduce = BigDecimal.ZERO;
//                if (detailGroup.containsKey(key)){
//                    reduce = detailGroup.get(key).stream().filter(k->k.getDemandQuantity() != null).map(NewProductTrialSubmissionDetailVO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
//                }
//                dateNumMap.put(strDate,reduce);
//            }
//            submissionVO.setDateNumMap(dateNumMap);
//        }

        return submissionVOList;
    }

    @Override
    @Expression(value = "v_fdp_new_product_trial_submission")
    public List<NewProductTrialSubmissionVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<NewProductTrialSubmissionVO> dataList = newProductTrialSubmissionDao.selectByCondition(sortParam, queryCriteriaParam);
        NewProductTrialSubmissionServiceImpl target = SpringBeanUtils.getBean(NewProductTrialSubmissionServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<NewProductTrialSubmissionVO> selectByParams(Map<String, Object> params) {
        List<NewProductTrialSubmissionPO> list = newProductTrialSubmissionDao.selectByParams(params);
        return NewProductTrialSubmissionConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<NewProductTrialSubmissionVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public BaseResponse<Void> doCreateSubmission(NewProductTrialSubmissionInsertDTO submissionInsertDTO) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.NEW_PRODUCT_TRIAL_SUBMISSION.getCode();
    }

    @Override
    public List<NewProductTrialSubmissionVO> invocation(List<NewProductTrialSubmissionVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList) {
        if (CollectionUtils.isNotEmpty(versionDTOList)) {
            return newProductTrialSubmissionDao.deleteBatchVersion(versionDTOList);
        }
        return 0;
    }

    @Override
    public void syncSubmission(Date beginTime, Date endTime, String submissionType, String companyName, String tenantCode) {
        if (SubmissionTypeEnum.API.getCode().equals(submissionType)) {
            HashMap<String, Object> map = MapUtil.newHashMap();
            map.put("beginTime",beginTime);
            map.put("endTime",endTime);
            try{
                if (StringUtils.isEmpty(tenantCode)){
                    tenantCode = SystemHolder.getTenantCode();
                    SystemHolder.getCompanyCode();
                    List<Scenario> data = ipsNewFeign.selectDefaultByTenantId(SystemHolder.getTenantCode());
                    String organization = data.get(0).getAlternativeColumn();
                    List<NewStockPointVO> newStockPointVOS = newMdsFeign.selectAllStockPoint(SystemHolder.getScenario());
                    Optional<NewStockPointVO> first = newStockPointVOS.stream().filter(x -> organization.equals(x.getOrganizeId())).findFirst();
                    if (first.isPresent()){
                        companyName = first.get().getCompanyName();
                    }else{
                        log.error("没有找到当前租户下对应的组织id");
                        throw new BusinessException("没有找到当前租户下对应的组织id");
                    }
                    map.put("triggerType", YesOrNoEnum.YES.getCode());
                }
                map.put("companyName", companyName);
                newDcpFeign.callExternalApi(tenantCode, ApiSourceEnum.OA.getCode(),
                        ApiCategoryEnum.NEW_PRODUCT_TRAIL.getCode(), map);
            }catch (Exception e){
                log.error("同步数据报错",e.getMessage());
                throw new BusinessException("同步数据报错",e.getMessage());
            }

        } else {
            throw new BusinessException("提报类型错误");
        }
    }

    @SneakyThrows
    @Override
    /**
     * 导入方法废弃
     */
    public void importData(MultipartFile file) {
        List<NewProductTrialSubmissionExcelDTO> insertList = EasyExcel.read(file.getInputStream())
                .head(NewProductTrialSubmissionExcelDTO.class)
                .sheet()
                .doReadSync();
        // 主机厂编码 本厂编码不能为空
        if (CollectionUtils.isNotEmpty(insertList.stream().filter(k-> StringUtils.isEmpty(k.getDeptName())).collect(Collectors.toList()))){
            throw new BusinessException("发起部门不能为空");
        }
        if (CollectionUtils.isNotEmpty(insertList.stream().filter(k-> StringUtils.isEmpty(k.getProductCode())).collect(Collectors.toList()))){
            throw new BusinessException("本厂编码不能为空");
        }

        //发起帮部门
        List<NewProductTrialSubmissionPO> insertSubmissionPOList = new ArrayList<>();
        List<NewProductTrialSubmissionPO> updateSubmissionPOList = new ArrayList<>();
        List<NewProductTrialSubmissionDetailPO> insertSubmissionDetailPOList = new ArrayList<>();
        List<NewProductTrialSubmissionDetailPO> updateSubmissionDetailPOList = new ArrayList<>();
        Map<String, List<NewProductTrialSubmissionExcelDTO>> insertGroup = insertList.stream().collect(Collectors.groupingBy(k -> k.getDeptName() + Constants.DELIMITER + k.getProductCode()));

        //查询数据库的数据
        List<NewProductTrialSubmissionPO> newProductTrialSubmissionPOS = newProductTrialSubmissionDao.selectByParams(new HashMap<>());
//        Map<String, NewProductTrialSubmissionPO> newProductTrialSubmissionPOMapOfJoinKey = newProductTrialSubmissionPOS.stream().collect(Collectors.toMap(item -> String.join("&", item.getOemCode(), item.getProductCode()), Function.identity(), (k1, k2) -> k2));

        //查询详情数据
        List<NewProductTrialSubmissionDetailPO> newProductTrialSubmissionDetailPOS = detailDao.selectByParams(new HashMap<>());
        Map<String, NewProductTrialSubmissionDetailPO> collect = newProductTrialSubmissionDetailPOS.stream().collect(Collectors.toMap(item -> String.join("&", item.getSubmissionId(), DateUtils.dateToString(item.getDemandTime(), DateUtils.COMMON_DATE_STR3)), Function.identity(), (k1, k2) -> k2));

        //查询库存点数据
        String scenario = SystemHolder.getScenario();
        List<NewStockPointVO> newStockPointVOS = newMdsFeign.selectAllStockPoint(scenario);

        for (String key : insertGroup.keySet()) {
            NewProductTrialSubmissionExcelDTO submissionDTO = insertGroup.get(key).get(0);
            NewProductTrialSubmissionPO submissionPO = new NewProductTrialSubmissionPO();
            String companyName = submissionDTO.getCompanyName();
            String oemCode = "";
            String oemName = "";
            if (StringUtils.isNotEmpty(companyName)){
                Optional<NewStockPointVO> first = newStockPointVOS.stream().filter(o -> companyName.equals(o.getCompanyName())).findFirst();
                if (first.isPresent()){
                    oemCode = first.get().getStockPointCode();
                    oemName = first.get().getStockPointName();
                }
            }

//            if (newProductTrialSubmissionPOMapOfJoinKey.containsKey(String.join("&", oemCode, submissionDTO.getProductCode()))) {
//                submissionPO = newProductTrialSubmissionPOMapOfJoinKey.get(String.join("&", oemCode, submissionDTO.getProductCode()));
//            }

            submissionPO.setOemCode(oemCode);
            submissionPO.setOemName(oemName);
            submissionPO.setCompanyName(submissionDTO.getCompanyName());
            submissionPO.setDeptName(submissionDTO.getDeptName());
            submissionPO.setUserName(submissionDTO.getUserName());
            submissionPO.setProjectNumber(submissionDTO.getProjectNumber());
            submissionPO.setProjectName(submissionDTO.getProjectName());
            submissionPO.setTrialProduct(submissionDTO.getTrialProduct());
            submissionPO.setTrialPurpose(submissionDTO.getTrialPurpose());
            submissionPO.setTrialProcess(submissionDTO.getTrialProcess());
//            submissionPO.setProductCode(submissionDTO.getProductCode());
//            submissionPO.setLoadingPosition(submissionDTO.getLoadingPosition());
//            submissionPO.setCuttingDrawingNumber(submissionDTO.getCuttingDrawingNumber());
//            submissionPO.setItemName(submissionDTO.getItemName());
//            submissionPO.setPartName(submissionDTO.getPartName());
            submissionPO.setVersionValue(1);

            // 1.数据校验
            NewProductTrialSubmissionDO submissionDO = NewProductTrialSubmissionConvertor.INSTANCE.po2Do(submissionPO);
            newProductTrialSubmissionDomainService.validation(submissionDO);
            if (org.apache.commons.lang3.StringUtils.isNotBlank(submissionPO.getId())){
                //更新
                BasePOUtils.updateFiller(submissionPO);
                updateSubmissionPOList.add(submissionPO);
            }else {
                //新增
                submissionPO.setVersionValue(1);
                // 2.数据持久化
                BasePOUtils.insertFiller(submissionPO);
                insertSubmissionPOList.add(submissionPO);
            }

            for (NewProductTrialSubmissionExcelDTO newSubmissionDTO : insertGroup.get(key)) {
                NewProductTrialSubmissionDetailPO detailPO = new NewProductTrialSubmissionDetailPO();
                String joinKey = String.join("&", submissionPO.getId(), DateUtils.dateToString(newSubmissionDTO.getDemandTime(), DateUtils.COMMON_DATE_STR3));
                if (collect.containsKey(joinKey)){
                    detailPO = collect.get(joinKey);
                }
                detailPO.setSubmissionId(submissionPO.getId());
                detailPO.setFeedingUnit(newSubmissionDTO.getFeedingUnit());
                detailPO.setDemandTime(newSubmissionDTO.getDemandTime());
                detailPO.setDemandQuantity(newSubmissionDTO.getDemandQuantity());
                detailPO.setFeedingQuantity(newSubmissionDTO.getFeedingQuantity());
//                detailPO.setDemandType(newSubmissionDTO.getDemandType());
                // 1.数据校验
                NewProductTrialSubmissionDetailDO detailDO = NewProductTrialSubmissionDetailConvertor.INSTANCE.po2Do(detailPO);
                if (org.apache.commons.lang3.StringUtils.isNotBlank(detailDO.getId())){
                    //修改
                    BasePOUtils.updateFiller(detailPO);
                    updateSubmissionDetailPOList.add(detailPO);
                }else {
                    //新增
                    detailDomainService.validation(detailDO);
                    BasePOUtils.insertFiller(detailPO);
                    insertSubmissionDetailPOList.add(detailPO);
                }
            }
        }
        //
        if (CollectionUtils.isNotEmpty(updateSubmissionPOList)){
            newProductTrialSubmissionDao.updateBatch(updateSubmissionPOList);
        }
        if (CollectionUtils.isNotEmpty(insertSubmissionPOList)){
            newProductTrialSubmissionDao.insertBatchWithPrimaryKey(insertSubmissionPOList);
        }

        if (CollectionUtils.isNotEmpty(insertSubmissionDetailPOList)){
            detailDao.insertBatchWithPrimaryKey(insertSubmissionDetailPOList);
        }
        if (CollectionUtils.isNotEmpty(updateSubmissionDetailPOList)){
            detailDao.updateBatch(updateSubmissionDetailPOList);
        }
    }

    @SneakyThrows
    @Override
    public void downloadTemplate(HttpServletResponse response) {
        String fileName = URLEncoder.encode(ObjectTypeEnum.NEW_PRODUCT_TRIAL_SUBMISSION.getDesc() + DfpConstants.EXCEL_TEMPLATE_END, "UTF-8");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), NewProductTrialSubmissionExcelDTO.class).sheet(ImportObjectEnum.NEW_PRODUCT_TRIAL_SUBMISSION.getDesc()).doWrite(new ArrayList<>());
    }

    @Override
    public BaseResponse<Void> updateNewProductTrialSubmissionData(List<NewProductTrialSubmissionDTO> list) {
        List<NewProductTrialSubmissionDTO> addDtoS = new ArrayList<>();
        List<NewProductTrialSubmissionDTO> updateDtoS = new ArrayList<>();
        List<NewProductTrialSubmissionDetailDTO> addDetailDtoS = new ArrayList<>();
        List<NewProductTrialSubmissionDetailDTO> updateDetailDtoS = new ArrayList<>();
        //按照billion分组
        Map<String, List<NewProductTrialSubmissionDTO>> collect = list.stream()
                .collect(Collectors.groupingBy(NewProductTrialSubmissionDTO::getBillNo));
        //获取所有的物料编码
        Set<String> productCodeSet = list.stream().map(NewProductTrialSubmissionDTO::getProductCode).collect(Collectors.toSet());
        HashMap<String, Object> map = MapUtils.newHashMap();
        map.put("sourceType","MDM");
        map.put("productCodeList",productCodeSet);
        map.put("enabled",YesOrNoEnum.YES.getCode());
        List<PartRelationMapVO> partRelationMapVOS = partRelationMapService.selectByParams(map);
        //获取相同编码中版本最高的数据
        partRelationMapVOS = partRelationMapVOS.stream()
                .collect(Collectors.groupingBy(PartRelationMapVO::getProductCode))
                .entrySet().stream()
                .map(entry -> entry.getValue().stream()
                        .max(Comparator.comparing(NewProductTrialSubmissionServiceImpl::parseVersion))
                        .orElse(null))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        //获取OA号集合
        Set<String> billnoSet = collect.keySet();
        //根据OA号查询是否已插入
        HashMap<String, Object> map1 = MapUtils.newHashMap();
        map1.put("billNos",billnoSet);
        List<NewProductTrialSubmissionPO> newProductTrialSubmissionPOS = newProductTrialSubmissionDao.selectByParams(map1);
        //获取旧数据的billNo集合
        Map<String, List<NewProductTrialSubmissionPO>> collect1 = newProductTrialSubmissionPOS.stream().collect(Collectors.groupingBy(NewProductTrialSubmissionPO::getBillNo));
        Set<String> oldBillNoList = collect1.keySet();
        //通过头表的id获取详情数据
        List<String> ids = newProductTrialSubmissionPOS.stream().map(NewProductTrialSubmissionPO::getId).collect(Collectors.toList());
        List<NewProductTrialSubmissionDetailVO> newProductTrialSubmissionDetailVOS = detailService.selectByParams(ImmutableMap.of("submissionIds", ids));
        //按照主id+物料编码+需求时间分类
        Map<String, List<NewProductTrialSubmissionDetailVO>> detailMap = newProductTrialSubmissionDetailVOS.stream()
                .collect(Collectors.groupingBy(item -> item.getSubmissionId() + "-" + item.getDemandTime() + "-" + item.getProductCode()));
        //遍历集合数据
        for (Map.Entry<String,List<NewProductTrialSubmissionDTO>> entry : collect.entrySet()) {
            String keyBillNo = entry.getKey();
            List<NewProductTrialSubmissionDTO> value = entry.getValue();
            NewProductTrialSubmissionDTO newProductTrialSubmissionDTO = value.get(0);
            if (!oldBillNoList.contains(keyBillNo)) {
                //设置主表的头id
                String uuid = UUIDUtil.getUUID();
                newProductTrialSubmissionDTO.setId(uuid);
                //设置试制类型
                String productType = newProductTrialSubmissionDTO.getProductType();
                if(productType.startsWith("订单下达试制类")){
                    if (productType.contains("新设备调试")){
                        newProductTrialSubmissionDTO.setProductType("XSB");
                    } else if (productType.contains("有本厂编码的实验及认证调试")) {
                        newProductTrialSubmissionDTO.setProductType("SYTS");
                    } else{
                        newProductTrialSubmissionDTO.setProductType("SZ");
                    }
                } else {
                    newProductTrialSubmissionDTO.setProductType(null);
                }
                addDtoS.add(newProductTrialSubmissionDTO);
                //遍历下面的详情对象
                for (NewProductTrialSubmissionDTO dto : value ) {
                    NewProductTrialSubmissionDetailDTO detailDTO = new NewProductTrialSubmissionDetailDTO();
                    detailDTO.setSubmissionId(uuid);
                    detailDTO.setDemandTime(dto.getDemandTime());
                    detailDTO.setDemandQuantity(dto.getDemandQuantity());
                    detailDTO.setFeedingQuantity(dto.getFeedingQuantity());
                    detailDTO.setFeedingUnit(dto.getFeedingUnit());
                    detailDTO.setProductCode(dto.getProductCode());
                    detailDTO.setProductName(dto.getItemName()); // 后面换成从bpim匹配
                    detailDTO.setPretreatment(dto.getPretreatment());
                    detailDTO.setShaping(dto.getShaping());
                    detailDTO.setLamination(dto.getLamination());
                    detailDTO.setAssembly(dto.getAssembly());
                    Optional<PartRelationMapVO> first = partRelationMapVOS.stream()
                            .filter(x -> dto.getProductCode().equals(x.getProductCode())).findFirst();
                    detailDTO.setPartCode(first.isPresent() ? first.get().getPartNumber() : null);//后面换成从bpim匹配
                    detailDTO.setLoadingPosition(dto.getLoadingPosition());
                    addDetailDtoS.add(detailDTO);
                }
            } else {
                NewProductTrialSubmissionPO oldPO = collect1.get(keyBillNo).get(0);
                if (ObjectUtils.isEmpty(oldPO.getLastUpdateDate()) || newProductTrialSubmissionDTO.getLastUpdateDate().compareTo(oldPO.getLastUpdateDate()) > 0) {
                    //设置试制类型
                    String productType = newProductTrialSubmissionDTO.getProductType();
                    if(productType.startsWith("订单下达试制类")){
                        if (productType.contains("新设备调试")){
                            newProductTrialSubmissionDTO.setProductType("XSB");
                        } else if (productType.contains("有本厂编码的实验及认证调试")) {
                            newProductTrialSubmissionDTO.setProductType("SYTS");
                        } else{
                            newProductTrialSubmissionDTO.setProductType("SZ");
                        }
                    } else {
                        newProductTrialSubmissionDTO.setProductType(null);
                    }
                    newProductTrialSubmissionDTO.setId(oldPO.getId());
                    newProductTrialSubmissionDTO.setCreateTime(oldPO.getCreateTime());
                    newProductTrialSubmissionDTO.setCreator(oldPO.getCreator());
                    newProductTrialSubmissionDTO.setVersionValue(oldPO.getVersionValue());
                    newProductTrialSubmissionDTO.setEnabled(oldPO.getEnabled());
                    updateDtoS.add(newProductTrialSubmissionDTO);
                    //遍历下面的详情对象
                    for (NewProductTrialSubmissionDTO dto : value ) {
                        NewProductTrialSubmissionDetailDTO detailDTO = new NewProductTrialSubmissionDetailDTO();
                        detailDTO.setSubmissionId(oldPO.getId());
                        detailDTO.setDemandTime(dto.getDemandTime());
                        detailDTO.setDemandQuantity(dto.getDemandQuantity());
                        detailDTO.setFeedingQuantity(dto.getFeedingQuantity());
                        detailDTO.setFeedingUnit(dto.getFeedingUnit());
                        detailDTO.setProductCode(dto.getProductCode());
                        detailDTO.setProductName(dto.getItemName()); // 后面换成从bpim匹配
                        detailDTO.setPretreatment(dto.getPretreatment());
                        detailDTO.setShaping(dto.getShaping());
                        detailDTO.setLamination(dto.getLamination());
                        detailDTO.setAssembly(dto.getAssembly());
                        Optional<PartRelationMapVO> first = partRelationMapVOS.stream()
                                .filter(x -> dto.getProductCode().equals(x.getProductCode())).findFirst();
                        detailDTO.setPartCode(first.isPresent() ? first.get().getPartNumber() : null);//后面换成从bpim匹配
                        detailDTO.setLoadingPosition(dto.getLoadingPosition());
                        //判断详情里面的数据是新增还是更新
                        if (!detailMap.containsKey(oldPO.getId()+"-"+dto.getDemandTime()+"-"+dto.getProductCode())){
                            addDetailDtoS.add(detailDTO);
                        } else {
                            NewProductTrialSubmissionDetailVO oldVo = detailMap.get(oldPO.getId() + "-" + dto.getDemandTime() + "-" + dto.getProductCode()).get(0);
                            detailDTO.setId(oldVo.getId());
                            detailDTO.setCreateTime(oldVo.getCreateTime());
                            detailDTO.setCreator(oldVo.getCreator());
                            detailDTO.setEnabled(oldVo.getEnabled());
                            detailDTO.setVersionValue(oldVo.getVersionValue());
                            updateDetailDtoS.add(detailDTO);
                        }
                    }
                }
            }
        }
        if (!addDtoS.isEmpty()) {
            addDtoS.forEach(t->t.setFinishedFlag(YesOrNoEnum.NO.getCode()));
            doCreateBatch(addDtoS);
        }
        if (!updateDtoS.isEmpty()){
            doUpdateBatch(updateDtoS);
        }
        if (!addDetailDtoS.isEmpty()){
            detailService.doCreateBatch(addDetailDtoS);
        }
        if (!updateDetailDtoS.isEmpty()){
            detailService.doUpdateBatch(updateDetailDtoS);
        }
        return BaseResponse.success();
    }

    private static double parseVersion(PartRelationMapVO obj) {
        String versionStr = obj.getRelationVersion();
        if (versionStr.startsWith("V")) {
            versionStr = versionStr.substring(1); // 去掉前缀 "V"
        }
        return Double.parseDouble(versionStr); // 转换为数值
    }

}
