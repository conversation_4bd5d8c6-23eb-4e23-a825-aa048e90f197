package com.yhl.scp.dfp.supplier.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.supplier.convertor.SupplierSubmissionDetailConvertor;
import com.yhl.scp.dfp.supplier.domain.entity.SupplierSubmissionDetailDO;
import com.yhl.scp.dfp.supplier.domain.service.SupplierSubmissionDetailDomainService;
import com.yhl.scp.dfp.supplier.dto.SupplierSubmissionDetailDTO;
import com.yhl.scp.dfp.supplier.infrastructure.dao.SupplierSubmissionDetailDao;
import com.yhl.scp.dfp.supplier.infrastructure.po.SupplierSubmissionDetailPO;
import com.yhl.scp.dfp.supplier.service.SupplierSubmissionDetailService;
import com.yhl.scp.dfp.supplier.vo.SupplierSubmissionDetailVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>SupplierSubmissionDetailServiceImpl</code>
 * <p>
 * 车型全供应商提报明细应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-10 09:32:33
 */
@Slf4j
@Service
public class SupplierSubmissionDetailServiceImpl extends AbstractService implements SupplierSubmissionDetailService {

    @Resource
    private SupplierSubmissionDetailDao supplierSubmissionDetailDao;

    @Resource
    private SupplierSubmissionDetailDomainService supplierSubmissionDetailDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(SupplierSubmissionDetailDTO supplierSubmissionDetailDTO) {
        // 0.数据转换
        SupplierSubmissionDetailDO supplierSubmissionDetailDO = SupplierSubmissionDetailConvertor.INSTANCE.dto2Do(supplierSubmissionDetailDTO);
        SupplierSubmissionDetailPO supplierSubmissionDetailPO = SupplierSubmissionDetailConvertor.INSTANCE.dto2Po(supplierSubmissionDetailDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        supplierSubmissionDetailDomainService.validation(supplierSubmissionDetailDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(supplierSubmissionDetailPO);
        supplierSubmissionDetailDao.insert(supplierSubmissionDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(SupplierSubmissionDetailDTO supplierSubmissionDetailDTO) {
        // 0.数据转换
        SupplierSubmissionDetailDO supplierSubmissionDetailDO = SupplierSubmissionDetailConvertor.INSTANCE.dto2Do(supplierSubmissionDetailDTO);
        SupplierSubmissionDetailPO supplierSubmissionDetailPO = SupplierSubmissionDetailConvertor.INSTANCE.dto2Po(supplierSubmissionDetailDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        supplierSubmissionDetailDomainService.validation(supplierSubmissionDetailDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(supplierSubmissionDetailPO);
        supplierSubmissionDetailDao.update(supplierSubmissionDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<SupplierSubmissionDetailDTO> list) {
        List<SupplierSubmissionDetailPO> newList = SupplierSubmissionDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        supplierSubmissionDetailDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<SupplierSubmissionDetailDTO> list) {
        List<SupplierSubmissionDetailPO> newList = SupplierSubmissionDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        supplierSubmissionDetailDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return supplierSubmissionDetailDao.deleteBatch(idList);
        }
        return supplierSubmissionDetailDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public SupplierSubmissionDetailVO selectByPrimaryKey(String id) {
        SupplierSubmissionDetailPO po = supplierSubmissionDetailDao.selectByPrimaryKey(id);
        return SupplierSubmissionDetailConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "SUPPLIER_SUBMISSION_DETAIL")
    public List<SupplierSubmissionDetailVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "SUPPLIER_SUBMISSION_DETAIL")
    public List<SupplierSubmissionDetailVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<SupplierSubmissionDetailVO> dataList = supplierSubmissionDetailDao.selectByCondition(sortParam, queryCriteriaParam);
        SupplierSubmissionDetailServiceImpl target = SpringBeanUtils.getBean(SupplierSubmissionDetailServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<SupplierSubmissionDetailVO> selectByParams(Map<String, Object> params) {
        List<SupplierSubmissionDetailPO> list = supplierSubmissionDetailDao.selectByParams(params);
        return SupplierSubmissionDetailConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<SupplierSubmissionDetailVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList) {
        if (CollectionUtils.isNotEmpty(versionDTOList)) {
            return 0;
        }
        supplierSubmissionDetailDomainService.checkDelete(versionDTOList);
        return supplierSubmissionDetailDao.deleteBatchVersion(versionDTOList);
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.SUPPLIER_SUBMISSION_DETAIL.getCode();
    }

    @Override
    public List<SupplierSubmissionDetailVO> invocation(List<SupplierSubmissionDetailVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
