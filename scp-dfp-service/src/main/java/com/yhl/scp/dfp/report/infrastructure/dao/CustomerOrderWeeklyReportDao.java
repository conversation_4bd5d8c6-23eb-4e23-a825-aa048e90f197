package com.yhl.scp.dfp.report.infrastructure.dao;

import com.yhl.platform.common.LabelValue;
import com.yhl.scp.dfp.report.vo.CustomerOrderWeeklyReportSimpleVO;
import com.yhl.scp.dfp.report.vo.CustomerOrderWeeklyReportVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <code>CustomerOrderWeeklyReportDao</code>
 * <p>
 * CustomerOrderWeeklyReportDao
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-12 16:36:46
 */
public interface CustomerOrderWeeklyReportDao {

    List<LabelValue<String>> selectVersionDropdown();

    List<CustomerOrderWeeklyReportVO> selectByVersionId(@Param("versionId") String versionId);

    List<CustomerOrderWeeklyReportVO> selectByCondition(@Param("sortParam") String sortParam,
                                                        @Param("queryCriteriaParam") String queryCriteriaParam,
                                                        @Param("planPeriodFirstDay") String planPeriodFirstDay,
                                                        @Param("planPeriodLastDay") String planPeriodLastDay);

    List<CustomerOrderWeeklyReportSimpleVO> selectWarehouseReleaseRecordList(@Param("oemCodes") List<String> oemCodes,
                                                                             @Param("vehicleModelCodes") List<String> vehicleModelCodes,
                                                                             @Param("startDate") String startDate,
                                                                             @Param("endDate") String endDate);

    List<CustomerOrderWeeklyReportSimpleVO> selectWarehouseToWarehouseRecordList(@Param("oemCodes") List<String> oemCodes,
                                                                                 @Param("vehicleModelCodes") List<String> vehicleModelCodes,
                                                                                 @Param("startDate") String startDate,
                                                                                 @Param("endDate") String endDate);

    List<CustomerOrderWeeklyReportSimpleVO> selectDeliveryPlanPublishedList(@Param("oemCodes") List<String> oemCodes,
                                                                            @Param("vehicleModelCodes") List<String> vehicleModelCodes,
                                                                            @Param("startDate") String startDate,
                                                                            @Param("endDate") String endDate);
}