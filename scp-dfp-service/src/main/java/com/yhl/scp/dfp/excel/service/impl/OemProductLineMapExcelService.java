package com.yhl.scp.dfp.excel.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.platform.common.ddd.BasePO;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.scp.common.excel.DataImportInfo;
import com.yhl.scp.common.excel.ImportDataTypeEnum;
import com.yhl.scp.common.excel.model.ImportAnalysisResultHolder;
import com.yhl.scp.common.excel.model.ImportContext;
import com.yhl.scp.common.excel.model.ImportRelatedDataHolder;
import com.yhl.scp.common.excel.service.AbstractExcelService;
import com.yhl.scp.common.vo.SimpleVO;
import com.yhl.scp.dfp.oem.convertor.OemProductLineMapConvertor;
import com.yhl.scp.dfp.oem.dto.OemProductLineMapDTO;
import com.yhl.scp.dfp.oem.infrastructure.dao.OemProductLineMapDao;
import com.yhl.scp.dfp.oem.infrastructure.po.OemProductLineMapPO;
import com.yhl.scp.dfp.oem.service.OemProductLineMapService;
import com.yhl.scp.dfp.oem.service.OemProductLineService;
import com.yhl.scp.dfp.oem.service.OemVehicleModelService;
import com.yhl.scp.dfp.oem.vo.OemProductLineMapVO;
import com.yhl.scp.dfp.oem.vo.OemProductLineVO;
import com.yhl.scp.dfp.oem.vo.OemVehicleModelVO;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>OemProductLineMapExcelService</code>
 * <p>
 * 产线映射关系excel
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-24 22:12:23
 */
@Service
public class OemProductLineMapExcelService extends AbstractExcelService<OemProductLineMapDTO, OemProductLineMapPO, OemProductLineMapVO> {

    @Resource
    private OemProductLineMapDao lineMapDao;

    @Resource
    private OemProductLineMapService lineMapService;

    @Resource
    private OemVehicleModelService oemVehicleModelService;

    @Resource
    private OemProductLineService oemProductLineService;

    @Override
    public BaseDao<OemProductLineMapPO, OemProductLineMapVO> getBaseDao() {
        return lineMapDao;
    }

    @Override
    public Function<OemProductLineMapDTO, OemProductLineMapPO> getDTO2POConvertor() {
        return OemProductLineMapConvertor.INSTANCE::dto2Po;
    }

    @Override
    public Class<OemProductLineMapDTO> getDTOClass() {
        return OemProductLineMapDTO.class;
    }

    @Override
    public BaseService<OemProductLineMapDTO, OemProductLineMapVO> getBaseService() {
        return lineMapService;
    }

    @Override
    protected void fillIdForUpdateData(List<OemProductLineMapDTO> updateList, Map<String, OemProductLineMapPO> existingDataMap) {
        for (OemProductLineMapDTO modelDTO : updateList) {
            OemProductLineMapPO modelPO = existingDataMap.get(modelDTO.getOemCode() + "&" + modelDTO.getLineCode() + "&" + modelDTO.getVehicleModelCode());
            if (modelPO != null) {
                modelDTO.setId(modelPO.getId());
                modelDTO.setEnabled(modelPO.getEnabled());
                modelDTO.setVersionValue(modelPO.getVersionValue());
            }
        }
    }

    @Override
    protected ImportRelatedDataHolder<OemProductLineMapPO> prepareData(List<OemProductLineMapDTO> oemProductLineMapDTOS) {
        // 找到数据库现在所有的数据
        List<OemProductLineMapPO> alreadyExitData = lineMapDao.selectByParams(new HashMap<>(2));
        Map<String, OemProductLineMapPO> codeToPOMap = alreadyExitData.stream().collect(Collectors.toMap(x -> x.getOemCode() + "&" + x.getLineCode() + "&" + x.getVehicleModelCode(), Function.identity(), (v1, v2) -> v1));
        // 组成唯一键的字段
        List<String> uniqueKeys = ListUtil.of("oemCode", "lineCode", "vehicleModelCode");
        // 外键字段
        List<String> foreignKeys = ListUtil.of();
        Map<String, List<SimpleVO>> foreignDataMap = new HashMap<>();
        return ImportRelatedDataHolder.<OemProductLineMapPO>builder()
                .existingData(alreadyExitData)
                .mainKeys(uniqueKeys)
                .foreignKeys(foreignKeys)
                .foreignDataMap(foreignDataMap)
                .existingDataMap(codeToPOMap)
                .build();
    }

    @Override
    protected void doInsert(ImportAnalysisResultHolder<OemProductLineMapDTO, OemProductLineMapPO> resultHolder, ImportContext importContext) {
        List<OemProductLineMapDTO> insertList = resultHolder.getInsertList();
        List<OemProductLineMapDTO> updateList = resultHolder.getUpdateList();
        List<OemProductLineMapPO> deteleList = resultHolder.getDeleteList();
        if (com.yhl.platform.common.utils.CollectionUtils.isNotEmpty(deteleList)) {
            List<String> ids = (List)deteleList.stream().map(BasePO::getId).collect(Collectors.toList());
            this.getBaseService().doDelete(ids);
        }

        if (com.yhl.platform.common.utils.CollectionUtils.isNotEmpty(insertList)) {
            this.getBaseService().doCreateBatch(insertList);
        }

        if (com.yhl.platform.common.utils.CollectionUtils.isNotEmpty(updateList)) {
            this.getBaseService().doUpdateBatch(updateList);
        }

    }

    @Override
    protected void specialVerification(ImportAnalysisResultHolder<OemProductLineMapDTO, OemProductLineMapPO> resultHolder, ImportContext importContext) {
        List<OemProductLineMapDTO> insertList = resultHolder.getInsertList();
        List<OemProductLineMapDTO> updateList = resultHolder.getUpdateList();
        verifyPaternity(insertList, resultHolder.getImportLogList());
        verifyPaternity(updateList, resultHolder.getImportLogList());
        resultHolder.setInsertList(insertList);
        resultHolder.setUpdateList(updateList);
    }

    private void verifyPaternity(List<OemProductLineMapDTO> checkList, List<DataImportInfo> importLogList) {
        Map<String, Object> queryParams = MapUtil.newHashMap();
        queryParams.put("enabled", YesOrNoEnum.YES.getCode());
        List<OemVehicleModelVO> oemVehicleModelVOS = oemVehicleModelService.selectByParams(queryParams);
        List<OemProductLineVO> oemProductLineVOS = oemProductLineService.selectByParams(queryParams);
        Map<String, OemVehicleModelVO> oemVehicleModelMap = CollectionUtils.isEmpty(oemVehicleModelVOS) ? MapUtil.newHashMap() :
                oemVehicleModelVOS.stream().collect(Collectors.toMap(x -> x.getOemCode() + "&" + x.getOemVehicleModelCode(), Function.identity(), (v1, v2) -> v1));
        Map<String, OemProductLineVO> oemProductLineVOMap = CollectionUtils.isEmpty(oemProductLineVOS) ? MapUtil.newHashMap() :
                oemProductLineVOS.stream().collect(Collectors.toMap(x -> x.getOemCode() + "&" + x.getLineCode(), Function.identity(), (v1, v2) -> v1));
        Iterator<OemProductLineMapDTO> iterator = checkList.iterator();
        while (iterator.hasNext()) {
            OemProductLineMapDTO oemProductLineMapDTO = iterator.next();
            if (!oemVehicleModelMap.containsKey(oemProductLineMapDTO.getOemCode() + "&" + oemProductLineMapDTO.getVehicleModelCode())) {
                // 主机厂车型信息不存在
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + oemProductLineMapDTO.getRowIndex() + ";主机厂车型信息不存在");
                dataImportInfo.setDisplayIndex(oemProductLineMapDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (!oemProductLineVOMap.containsKey(oemProductLineMapDTO.getOemCode() + "&" + oemProductLineMapDTO.getLineCode())) {
                // 主机厂产线信息不存在
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + oemProductLineMapDTO.getRowIndex() + ";主机厂产线信息不存在");
                dataImportInfo.setDisplayIndex(oemProductLineMapDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
            }
        }
    }
}
