package com.yhl.scp.dfp.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dfp.stock.service.InventoryBatchDetailService;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>InventoryBatchDetailJob</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-16 12:52:34
 */
@Component
@Slf4j
public class InventoryBatchDetailJob {
    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private InventoryBatchDetailService inventoryBatchDetailService;

    @XxlJob("inventoryBatchDetailJob")
    public ReturnT<String> inventoryBatchDetailJob() {
        List<Scenario> scenarios = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.DFP.getCode()).getData();
        if (CollectionUtils.isEmpty(scenarios)) {
            XxlJobHelper.log("租户下不存在DFP模块信息");
            return ReturnT.SUCCESS;
        }
        for (Scenario scenario : scenarios) {
            XxlJobHelper.log("开始处理scenario：{}下的同步库存批次明细job", scenario);
            DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());
            inventoryBatchDetailService.syncStockBatchDetail(scenario,null);
            DynamicDataSourceContextHolder.clearDataSource();
            XxlJobHelper.log("scenario：{}下的同步库存批次明细job结束", scenario);
        }
        return ReturnT.SUCCESS;
    }

}
