package com.yhl.scp.dfp.stock.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.stock.dto.CurrentBatchQuantityDTO;
import com.yhl.scp.dfp.stock.service.CurrentBatchQuantityService;
import com.yhl.scp.dfp.stock.vo.CurrentBatchQuantityVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>CurrentBatchQuantityController</code>
 * <p>
 * 批次现有量数据控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-29 11:25:59
 */
@Slf4j
@Api(tags = "批次现有量数据控制器")
@RestController
@RequestMapping("currentBatchQuantity")
public class CurrentBatchQuantityController extends BaseController {

    @Resource
    private CurrentBatchQuantityService currentBatchQuantityService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<CurrentBatchQuantityVO>> page() {
        List<CurrentBatchQuantityVO> currentBatchQuantityList = currentBatchQuantityService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<CurrentBatchQuantityVO> pageInfo = new PageInfo<>(currentBatchQuantityList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody CurrentBatchQuantityDTO currentBatchQuantityDTO) {
        return currentBatchQuantityService.doCreate(currentBatchQuantityDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody CurrentBatchQuantityDTO currentBatchQuantityDTO) {
        return currentBatchQuantityService.doUpdate(currentBatchQuantityDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        currentBatchQuantityService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<CurrentBatchQuantityVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, currentBatchQuantityService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "根据版本删除")
    @PostMapping(value = "deleteByVersion")
    public BaseResponse<Void> deleteByVersion(@RequestBody List<RemoveVersionDTO> versionDTOList) {
        currentBatchQuantityService.doDeleteByVersion(versionDTOList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

}
