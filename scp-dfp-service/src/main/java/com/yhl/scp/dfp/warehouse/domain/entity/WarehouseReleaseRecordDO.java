package com.yhl.scp.dfp.warehouse.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>WarehouseReleaseRecordDO</code>
 * <p>
 * 仓库发货记录DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-31 10:17:29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class WarehouseReleaseRecordDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 158697910119840014L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 工厂
     */
    private String plantCode;
    /**
     * 是否已接收
     */
    private String isReceive;
    /**
     * 发货清单号
     */
    private String listNum;
    /**
     * 发货计划单号
     */
    private String reqNum;
    /**
     * 条码号
     */
    private String barNum;
    /**
     * 箱号
     */
    private String boxNum;
    /**
     * 本厂编码
     */
    private String itemCode;
    /**
     * 描述
     */
    private String descriptions;
    /**
     * 发货数量
     */
    private BigDecimal sumQty;
    /**
     * 柜号
     */
    private String containerNum;
    /**
     * 提单号
     */
    private String billOfLadingNum;
    /**
     * 船公司
     */
    private String shipCompany;
    /**
     * 进仓工厂
     */
    private String instockSource;
    /**
     * 来源仓库
     */
    private String attribute1;
    /**
     * 来源货位
     */
    private String attribute2;
    /**
     * 目标仓库
     */
    private String shipmentWarehouseCode;
    /**
     * 目标货位
     */
    private String shipmentLocatorCode;
    /**
     * 计划单号
     */
    private String req;
    /**
     * 行号
     */
    private String lineNum;
    /**
     * 物流器具小类
     */
    private String typeCoode;
    /**
     * 发货时间
     */
    private Date creationDate;
    /**
     * 发货人
     */
    private String consigner;
    /**
     * 入仓时间
     */
    private Date inWarehouseTime;
    /**
     * 单片面积
     */
    private BigDecimal acreage;
    /**
     * 总面积
     */
    private BigDecimal acreageSum;
    /**
     * mes数据ID
     */
    private String kid;
    /**
     * 批次
     */
    private String lotNumber;
    /**
     * 客户编码
     */
    private String customerNumber;
    /**
     * 地址ID
     */
    private String ebsSiteId;
    /**
     * 客户零件号
     */
    private String custPart;
    /**
     * 客户po号
     */
    private String custPo;
    /**
     * 来源
     */
    private String sourceType;
    /**
     * 预计到港时间
     */
    private Date estimatedArrivePortTime;
    /**
     * 实际到港时间
     */
    private Date actualArrivePortTime;
    /**
     * 预计完成时间
     */
    private Date estimatedCompletionTime;
    /**
     * 实际完成时间
     */
    private Date actualCompletionTime;
    /**
     * 车牌号
     */
    private String carNum;

}
