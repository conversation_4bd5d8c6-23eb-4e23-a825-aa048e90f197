package com.yhl.scp.dfp.clean.service.impl;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.CustomThreadPoolFactory;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.calendar.service.DfpResourceCalendarService;
import com.yhl.scp.dfp.calendar.vo.ResourceCalendarVO;
import com.yhl.scp.dfp.clean.convertor.CleanDemandDataConvertor;
import com.yhl.scp.dfp.clean.domain.entity.CleanDemandDataDO;
import com.yhl.scp.dfp.clean.domain.service.CleanDemandDataDomainService;
import com.yhl.scp.dfp.clean.dto.CleanDemandDataDTO;
import com.yhl.scp.dfp.clean.dto.CleanDemandDataDetailDTO;
import com.yhl.scp.dfp.clean.infrastructure.dao.CleanDemandDataDao;
import com.yhl.scp.dfp.clean.infrastructure.dao.CleanDemandDataDetailDao;
import com.yhl.scp.dfp.clean.infrastructure.po.CleanDemandDataDetailPO;
import com.yhl.scp.dfp.clean.infrastructure.po.CleanDemandDataPO;
import com.yhl.scp.dfp.clean.service.CleanDemandDataDetailService;
import com.yhl.scp.dfp.clean.service.CleanDemandDataService;
import com.yhl.scp.dfp.clean.vo.CleanDemandDataVO;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.common.enums.DemandTypeEnum;
import com.yhl.scp.dfp.common.enums.GranularityEnum;
import com.yhl.scp.dfp.common.enums.PublishStatusEnum;
import com.yhl.scp.dfp.common.enums.VersionTypeEnum;
import com.yhl.scp.dfp.consistence.infrastructure.dao.ConsistenceDemandForecastDataDao;
import com.yhl.scp.dfp.consistence.infrastructure.dao.ConsistenceDemandForecastDataDetailDao;
import com.yhl.scp.dfp.consistence.infrastructure.dao.ConsistenceDemandForecastVersionDao;
import com.yhl.scp.dfp.consistence.infrastructure.po.ConsistenceDemandForecastDataDetailPO;
import com.yhl.scp.dfp.consistence.infrastructure.po.ConsistenceDemandForecastDataPO;
import com.yhl.scp.dfp.delivery.domain.service.DeliveryPlanDomainService;
import com.yhl.scp.dfp.demand.infrastructure.dao.DemandVersionDao;
import com.yhl.scp.dfp.demand.infrastructure.po.DemandVersionPO;
import com.yhl.scp.dfp.enums.ObjectTypeEnum;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionDetailService;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionService;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionDetailVO;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionFutureVO;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionVO;
import com.yhl.scp.dfp.oem.service.OemProductLineMapService;
import com.yhl.scp.dfp.oem.service.OemVehicleModelService;
import com.yhl.scp.dfp.oem.vo.OemProductLineMapVO;
import com.yhl.scp.dfp.origin.infrastructure.dao.OriginDemandVersionDao;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseRecordService;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseToWarehouseService;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordMonthVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseMonthVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;

import cn.hutool.core.map.MapUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>CleanDemandDataServiceImpl</code>
 * <p>
 * 日需求数据应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:46:57
 */
@Slf4j
@Service
public class CleanDemandDataServiceImpl extends AbstractService implements CleanDemandDataService {

    @Resource
    private CleanDemandDataDao cleanDemandDataDao;

    @Resource
    private CleanDemandDataDetailDao cleanDemandDataDetailDao;

    @Resource
    private DemandVersionDao demandVersionDao;

    @Resource
    private OriginDemandVersionDao originDemandVersionDao;

    @Resource
    private CleanDemandDataDomainService cleanDemandDataDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private ConsistenceDemandForecastDataDao consistenceDemandForecastDataDao;

    @Resource
    private ConsistenceDemandForecastDataDetailDao consistenceDemandForecastDataDetailDao;

    @Resource
    private ConsistenceDemandForecastVersionDao consistenceDemandForecastVersionDao;

    @Resource
    private LoadingDemandSubmissionService loadingDemandSubmissionService;

    @Resource
    private LoadingDemandSubmissionDetailService loadingDemandSubmissionDetailService;

    @Resource
    private CleanDemandDataService cleanDemandDataService;

    @Resource
    private CleanDemandDataDetailService cleanDemandDataDetailService;

    public static final String YMD_PATTERN = "yyyyMMdd";

    public static final String YM_PATTERN = "yyyyMM";

    @Resource
    protected NewMdsFeign newMdsFeign;

    @Resource
    private OemProductLineMapService oemProductLineMapService;

    @Resource
    private WarehouseReleaseRecordService warehouseReleaseRecordService;

    @Resource
    private OemVehicleModelService oemVehicleModelService;

    @Resource
    DfpResourceCalendarService dfpResourceCalendarService;

    @Resource
    private DeliveryPlanDomainService deliveryPlanDomainService;
    
    @Resource
    private WarehouseReleaseToWarehouseService warehouseReleaseToWarehouseService;

    @Override
    public BaseResponse<Void> doCreate(CleanDemandDataDTO cleanDemandDataDTO) {
        // 0.数据转换
        CleanDemandDataDO cleanDemandDataDO = CleanDemandDataConvertor.INSTANCE.dto2Do(cleanDemandDataDTO);
        CleanDemandDataPO cleanDemandDataPO = CleanDemandDataConvertor.INSTANCE.dto2Po(cleanDemandDataDTO);
        // 1.数据校验
        cleanDemandDataDomainService.validation(cleanDemandDataDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(cleanDemandDataPO);
        cleanDemandDataDao.insertWithPrimaryKey(cleanDemandDataPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(CleanDemandDataDTO cleanDemandDataDTO) {
        // 0.数据转换
        CleanDemandDataDO cleanDemandDataDO = CleanDemandDataConvertor.INSTANCE.dto2Do(cleanDemandDataDTO);
        CleanDemandDataPO cleanDemandDataPO = CleanDemandDataConvertor.INSTANCE.dto2Po(cleanDemandDataDTO);
        // 1.数据校验
        cleanDemandDataDomainService.validation(cleanDemandDataDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(cleanDemandDataPO);
        cleanDemandDataDao.update(cleanDemandDataPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<CleanDemandDataDTO> list) {
        List<CleanDemandDataPO> newList = CleanDemandDataConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        cleanDemandDataDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<CleanDemandDataDTO> list) {
        List<CleanDemandDataPO> newList = CleanDemandDataConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        cleanDemandDataDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return cleanDemandDataDao.deleteBatch(idList);
        }
        return cleanDemandDataDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public CleanDemandDataVO selectByPrimaryKey(String id) {
        CleanDemandDataPO po = cleanDemandDataDao.selectByPrimaryKey(id);
        return CleanDemandDataConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_fdp_clean_demand_data")
    public List<CleanDemandDataVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_fdp_clean_demand_data")
    public List<CleanDemandDataVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<CleanDemandDataVO> dataList = cleanDemandDataDao.selectByCondition(sortParam, queryCriteriaParam);
        CleanDemandDataServiceImpl target = SpringBeanUtils.getBean(CleanDemandDataServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<CleanDemandDataVO> selectByParams(Map<String, Object> params) {
        List<CleanDemandDataPO> list = cleanDemandDataDao.selectByParams(params);
        return CleanDemandDataConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<CleanDemandDataVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.CLEAN_DEMAND_DATA.getCode();
    }

    @Override
    public List<CleanDemandDataVO> invocation(List<CleanDemandDataVO> dataList, Map<String, Object> params, String invocation) {
        if (CollectionUtils.isEmpty(dataList)) {
            return dataList;
        }
        // 设置明细数据
        List<String> demandIds = dataList.stream().map(CleanDemandDataVO::getId).collect(Collectors.toList());

        List<CleanDemandDataDetailPO> cleanDemandDataDetailList = cleanDemandDataDetailDao.selectByCleanDemandDataIds(demandIds);
        Map<String, List<CleanDemandDataDetailPO>> detailMap = cleanDemandDataDetailList.stream()
                .collect(Collectors.groupingBy(CleanDemandDataDetailPO::getCleanDemandDataId));
        for (CleanDemandDataVO data : dataList) {
            List<CleanDemandDataDetailPO> details = detailMap.get(data.getId());
            if (CollectionUtils.isNotEmpty(details)) {
                Map<String, String> detailList = details.stream().collect(Collectors
                        .toMap(x -> DateUtils.dateToString(x.getDemandTime(), YMD_PATTERN),
                                x -> x.getDemandQuantity() == null ? "" : x.getDemandQuantity().toString(),
                                (t1, t2) -> t2));
                data.setDetailList(detailList);
            }
        }
        return dataList;
    }

    /**
     * 重新计算是在创建日需求版本之后，如果主机厂重新替换了原始需求，需要重新计算
     */
    @Override
    public void doRecalculate(String versionCode) {
        Map<String, Object> params = new HashMap<>(2);
        params.put("versionCode", versionCode);
        params.put("versionType", VersionTypeEnum.CLEAN_DEMAND.getCode());
        DemandVersionPO demandVersionPO = demandVersionDao.selectVersionInfoByParams(params);
        if (demandVersionPO == null) {
            throw new BusinessException("没有找到需要重新计算的日需求版本");
        }
        processDataByVersionId(demandVersionPO.getId());
    }

    // 自动计算，定时任务
    @Override
    public void autoCalculate() {
        // 获取最新原始版本号
        String originVersionId = originDemandVersionDao.selectLatestVersionId();
        // 获取最新版本号
        Map<String, Object> params = new HashMap<>(2);
        params.put("originVersionId", originVersionId);
        params.put("versionType", VersionTypeEnum.CLEAN_DEMAND.getCode());
        DemandVersionPO demandVersionPO = demandVersionDao.selectVersionInfoByParams(params);
        if (demandVersionPO == null) {
            throw new BusinessException("没有找到自动生成的日需求版本");
        }
        processDataByVersionId(demandVersionPO.getId());
    }

    /**
     * 根据版本号处理数据
     *
     * @param versionId 版本ID
     */
    @SneakyThrows
    public void processDataByVersionId(String versionId) {
    	// 查询当前用户负责的物料数据
        List<NewProductStockPointVO> newProductStockPointVOS =
                this.newMdsFeign.selectProductStockPointByParams(SystemHolder.getScenario(),
                        ImmutableMap.of("orderPlanner", SystemHolder.getUserId(),
                        		"enabled", YesOrNoEnum.YES.getCode()));
        if (CollectionUtils.isEmpty(newProductStockPointVOS)) {
            throw new BusinessException("当前用户没有配置物料数据权限");
        }
        List<String> orderPlannerProductCodes = newProductStockPointVOS.stream()
        		.map(NewProductStockPointVO::getProductCode).distinct().collect(Collectors.toList());
    	ThreadPoolExecutor threadPoolExecutor = CustomThreadPoolFactory.instance();
        // 获取对应版本数据
        List<CleanDemandDataPO> cleanDemandDataPOList = cleanDemandDataDao.selectByParams(ImmutableMap
        		.of("versionId", versionId, "productCodeList", orderPlannerProductCodes));
        List<String> deletedDataIds = cleanDemandDataPOList.stream().map(CleanDemandDataPO::getId)
    			.collect(Collectors.toList());
        //过滤无效物料权限数据
        List<String> unEnabledProductCodes = newProductStockPointVOS.stream()
        		.filter( e-> YesOrNoEnum.NO.getCode().equals(e.getEnabled()))
        		.map(NewProductStockPointVO::getProductCode).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(unEnabledProductCodes)) {
        	orderPlannerProductCodes.removeAll(unEnabledProductCodes);
        	if(CollectionUtils.isEmpty(orderPlannerProductCodes)) {
        		//执行删除逻辑，不计算
        		cleanDemandDataService.doDelete(deletedDataIds);
                cleanDemandDataDetailDao.deleteByDataIds(deletedDataIds);
                return;
        	}
        }
        
        //查询装车需求提报数据
        DemandVersionPO demandVersionPO = demandVersionDao.selectByPrimaryKey(versionId);
        Map<String, Object> params = new HashMap<>();
        params.put("versionId", demandVersionPO.getOriginVersionId());
        params.put("productCodes", orderPlannerProductCodes);
        List<LoadingDemandSubmissionVO> loadingDemandSubmissionVOS = loadingDemandSubmissionService.selectByParams(params)
                .stream().filter(item -> StringUtils.isNotBlank(item.getProductCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(loadingDemandSubmissionVOS)) {
            throw new BusinessException("装车需求提报数据为空");
        }
        Map<String,List<LoadingDemandSubmissionVO>> loadingDemandSubmissionDemandMap =
                loadingDemandSubmissionVOS.stream().collect(Collectors.groupingBy(LoadingDemandSubmissionVO::getDemandCategory));
        //获取主机厂
        List<String> oemCodeList = loadingDemandSubmissionVOS.stream().map(LoadingDemandSubmissionVO::getOemCode)
                .distinct().collect(Collectors.toList());
        //获取装车需求提报主表id
        List<String> submissionIds = loadingDemandSubmissionVOS.stream().map(LoadingDemandSubmissionVO::getId).collect(Collectors.toList());
        // 汇总当月当天前仓库收发货数据
        List<String> productCodeList = loadingDemandSubmissionVOS.stream().map(LoadingDemandSubmissionVO::getProductCode)
                .distinct().collect(Collectors.toList());
        Map<String, Object> productParams = new HashMap<>();
        productParams.put("productCodeList", productCodeList);
        Map<String, String> productVehicleModelMap = newMdsFeign.selectProductVehicleModel(SystemHolder.getScenario(), productParams);
        List<String> vehicleModeCodes = Lists.newArrayList(productVehicleModelMap.values());
        String currentMonthFirstDay = DateUtils.dateToString(DateUtils.getCurrentMonthFirstDay(), DateUtils.COMMON_DATE_STR3);
        Date currentDate = new Date();
        String currentMonth = DateUtils.dateToString(currentDate, DateUtils.YEAR_MONTH);
        String currentMonthCurrentDay = DateUtils.dateToString(currentDate, DateUtils.COMMON_DATE_STR3);
        try {    
        	//主机厂，车型，产品编码对应的发货数量(获取仓库发货数量)
	        CompletableFuture<Map<String, BigDecimal>> releaseMapFuture = CompletableFuture.supplyAsync(
	                () -> getWarehouseReleaseRecordDeliveryQtyMap(productCodeList,
	        				productVehicleModelMap, vehicleModeCodes, currentMonthFirstDay, currentMonthCurrentDay), threadPoolExecutor);
	        
	        //主机厂，车型，产品编码对应的发货数量(获取仓库发货至中转库的发货数量)
	        Map<String, BigDecimal> releaseToMap = getWarehouseReleaseToWarehouseDeliveryQtyMap(productCodeList,
					productVehicleModelMap, vehicleModeCodes, currentMonthFirstDay, currentMonthCurrentDay);
	
			//异步查询装车需求提报数据，资源日历
	        CompletableFuture<LoadingDemandSubmissionFutureVO> loadingDemandSubmissionDetailFuture = CompletableFuture.supplyAsync(() -> {
	            	LoadingDemandSubmissionFutureVO result = new LoadingDemandSubmissionFutureVO();
	            	Map<String, Object> detailQueryMap = MapUtil.newHashMap();
	        		detailQueryMap.put("submissionIds", submissionIds);
	        		List<LoadingDemandSubmissionDetailVO> loadingDemandSubmissionDetailVOS = loadingDemandSubmissionDetailService.selectByParams(detailQueryMap);
	        		//日装车需求提报数据
	                List<LoadingDemandSubmissionDetailVO> loadingDemandSubmissionDetailVOSDay = loadingDemandSubmissionDetailVOS.stream()
	                        .filter(item -> StringUtils.equals(GranularityEnum.DAY.getCode(), item.getSubmissionType())).collect(Collectors.toList());
	                Map<String, List<LoadingDemandSubmissionDetailVO>> detailDayMap = CollectionUtils.isEmpty(loadingDemandSubmissionDetailVOSDay)
	                        ? MapUtil.newHashMap() : loadingDemandSubmissionDetailVOSDay.stream().collect(Collectors.groupingBy(LoadingDemandSubmissionDetailVO::getSubmissionId));
	                result.setDetailDayMap(detailDayMap);
	                
	                //获取未维护的资源日历（按月分组）
	                String format1 = "\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}";
	                loadingDemandSubmissionDetailVOSDay.forEach(item -> {
	                    if (StringUtils.isNotBlank(item.getDemandTime()) && item.getDemandTime().matches(format1)){
	                        item.setDemandTime(DateUtils.dateToString(DateUtils.stringToDate(item.getDemandTime(),
	                                DateUtils.COMMON_DATE_STR1), DateUtils.COMMON_DATE_STR3));
	                    }
	                });
	                Map<String, List<ResourceCalendarVO>> monthCalendarMap = getMonthCalendarMap(loadingDemandSubmissionDetailVOSDay);
	                result.setMonthCalendarMap(monthCalendarMap);
	                
	                //月装车需求提报数据
	                List<LoadingDemandSubmissionDetailVO> loadingDemandSubmissionDetailVOSMonth = loadingDemandSubmissionDetailVOS.stream()
	                        .filter(item -> StringUtils.equals(GranularityEnum.MONTH.getCode(), item.getSubmissionType())).collect(Collectors.toList());
	                Map<String, List<LoadingDemandSubmissionDetailVO>> detailMonthMap = CollectionUtils.isEmpty(loadingDemandSubmissionDetailVOSMonth)
	                        ? MapUtil.newHashMap() : loadingDemandSubmissionDetailVOSMonth.stream().collect(Collectors.groupingBy(LoadingDemandSubmissionDetailVO::getSubmissionId));
	                result.setDetailMonthMap(detailMonthMap);
	        		return result;
	        }, threadPoolExecutor);
	
	        
	        // 第一层key获取一致性需求预测的data、第二层key获取一致性需求预测的dataDetail
	        CompletableFuture<Map<String, Map<String, ConsistenceDemandForecastDataDetailPO>>> consistenceRealtionMapFuture = CompletableFuture.supplyAsync(() -> {
	        	// 根据计划周期取已发布的最新版一致性业务预测版本
	            String forecastVersionId = consistenceDemandForecastVersionDao.selectLatestPublishedVersionId(null);
	            // 根据版本ID和预测类型和年月获取数据
	            Map<String, Object> consistenceVersionParams = new HashMap<>();
	            consistenceVersionParams.put("versionId", forecastVersionId);
	            List<ConsistenceDemandForecastDataPO> consistenceDataList = consistenceDemandForecastDataDao.selectByParams(consistenceVersionParams);
	        	Map<String, Map<String, ConsistenceDemandForecastDataDetailPO>> consistenceRealtionMap = new HashMap<>();
	            if (CollectionUtils.isNotEmpty(consistenceDataList)) {
	                List<String> demandForecastIds = consistenceDataList.stream().map(ConsistenceDemandForecastDataPO::getId)
	                        .collect(Collectors.toList());
	                List<ConsistenceDemandForecastDataDetailPO> consistenceDetailList = consistenceDemandForecastDataDetailDao
	                        .selectByConsistenceDemandForecastDataIds(demandForecastIds).stream()
	                        .filter(item -> item.getForecastTime() != null).collect(Collectors.toList());
	                Map<String, Map<String, ConsistenceDemandForecastDataDetailPO>> consistenceDetailMap;
	                if (CollectionUtils.isNotEmpty(consistenceDetailList)) {
	                    consistenceDetailMap = consistenceDetailList.stream().collect(Collectors
	                            .groupingBy(ConsistenceDemandForecastDataDetailPO::getConsistenceDemandForecastDataId, Collectors
	                                    .toMap(x -> DateUtils.dateToString(x.getForecastTime(), DateUtils.YEAR_MONTH),
	                                            Function.identity(),
	                                            (k1, k2) -> k1)));
	
	                    // 获取主机厂、车型、产品编码和一致性业务预测数据的关联关系
	                    Map<String, Map<String, ConsistenceDemandForecastDataDetailPO>> finalConsistenceDetailMap = consistenceDetailMap;
	                    consistenceRealtionMap = consistenceDataList.stream().collect(Collectors
	                            .toMap(x -> String.join("&", x.getOemCode(), x.getVehicleModelCode(), x.getProductCode()),
	                                    y -> finalConsistenceDetailMap.get(y.getId()),
	                                    (k1, k2) -> k1));
	                }
	            }
	            return consistenceRealtionMap;
	        }, threadPoolExecutor);
	        Map<String, List<OemProductLineMapVO>> oemProductLineMapVOMap = new HashMap<>();
	        //查询主机厂厂线信息
	        List<OemProductLineMapVO> oemProductLineMapVOS = oemProductLineMapService.selectByOemCodeList(oemCodeList);
	        CompletableFuture<Map<String, List<ResourceCalendarVO>>> resourceCodeCalendarMapFuture = null;
	        if (CollectionUtils.isNotEmpty(oemProductLineMapVOS)) {
	            oemProductLineMapVOMap = oemProductLineMapVOS.stream().collect(Collectors
	                    .groupingBy(item -> String.join("&", item.getOemCode(), item.getVehicleModelCode())));
	            List<String> lineCodeList = oemProductLineMapVOS.stream().map(OemProductLineMapVO::getLineCode)
	                    .distinct().collect(Collectors.toList());
	            resourceCodeCalendarMapFuture = CompletableFuture.supplyAsync(() -> {
	            	// 查询装车日历
	            	Map<String, List<ResourceCalendarVO>> resourceCodeCalendarMap = new HashMap<>();
	                List<ResourceCalendarVO> resourceCalendarVOS =
	                        dfpResourceCalendarService.selectResourceByStandardResourceIds(lineCodeList);
	                if (CollectionUtils.isNotEmpty(resourceCalendarVOS)) {
	                	resourceCodeCalendarMap = resourceCalendarVOS.stream().collect(Collectors
	                            .groupingBy(item -> String.join("&", item.getStandardResourceId(), item.getPhysicalResourceId())));
	                }
	                return resourceCodeCalendarMap;
	            }, threadPoolExecutor);
	        }
	        //获取异步查询的装车需求提报数据，资源日历
	        LoadingDemandSubmissionFutureVO loadingDemandSubmissionFutureVO = loadingDemandSubmissionDetailFuture.join();
	        Map<String, List<ResourceCalendarVO>> monthCalendarMap = loadingDemandSubmissionFutureVO.getMonthCalendarMap();
	        Map<String, List<LoadingDemandSubmissionDetailVO>> detailDayMap = loadingDemandSubmissionFutureVO.getDetailDayMap();
	        Map<String, List<LoadingDemandSubmissionDetailVO>> detailMonthMap = loadingDemandSubmissionFutureVO.getDetailMonthMap();
	        //获取主机厂、车型、产品编码和一致性业务预测数据的关联关系
	        Map<String, Map<String, ConsistenceDemandForecastDataDetailPO>> consistenceRealtionMap = consistenceRealtionMapFuture.get();
	        //主机厂，车型，产品编码对应的发货数量(获取仓库发货数量)
	        Map<String, BigDecimal> releaseMap = releaseMapFuture.join();
	        //获取装车日历
	        Map<String, List<ResourceCalendarVO>> resourceCodeCalendarMap = new HashMap<>();
	        if (CollectionUtils.isNotEmpty(oemProductLineMapVOS)) {
	        	resourceCodeCalendarMap = resourceCodeCalendarMapFuture.get();
	        }
	        //重新计算后需要新增的数据
	        List<CleanDemandDataDTO> cleanDemandDataDTOS = Collections.synchronizedList(new LinkedList<>());
	        List<CleanDemandDataDetailDTO> cleanDemandDataDetailDTOS = Collections.synchronizedList(new LinkedList<>());
	        for (List<LoadingDemandSubmissionVO> list : loadingDemandSubmissionDemandMap.values()){
	            processDataByDemandCategory(demandVersionPO.getId(),list,productVehicleModelMap,
	                    oemProductLineMapVOMap,resourceCodeCalendarMap,detailDayMap,detailMonthMap,
	                    consistenceRealtionMap,currentMonth,releaseMap, releaseToMap, monthCalendarMap,
	                    cleanDemandDataDTOS, cleanDemandDataDetailDTOS);
	        }
        	List<CompletableFuture<Integer>> completableFutureList = new ArrayList<>();
        	if (CollectionUtils.isNotEmpty(cleanDemandDataDTOS)) {
    			Lists.partition(cleanDemandDataDTOS, 1000).forEach(subList -> {
    				CompletableFuture<Integer> insertDataFuture = CompletableFuture.supplyAsync(() -> {
    					this.doCreateBatch(subList);
    					return 1;
    				}, threadPoolExecutor);
    				completableFutureList.add(insertDataFuture);
    			});
    		}
        	if (CollectionUtils.isNotEmpty(cleanDemandDataDetailDTOS)) {
    			Lists.partition(cleanDemandDataDetailDTOS, 2000).forEach(subList -> {
    				CompletableFuture<Integer> insertDetailFuture = CompletableFuture.supplyAsync(() -> {
    					cleanDemandDataDetailService.doCreateBatch(subList);
    					return 1;
    				}, threadPoolExecutor);
    				completableFutureList.add(insertDetailFuture);
    			});
    		}
        	//等待插入结束
        	completableFutureList.forEach( e -> {
        		e.join();
        	});
        	//异步执行删除
//        	CompletableFuture.runAsync(() -> {
        		if (CollectionUtils.isNotEmpty(deletedDataIds)) {
        			//删除原有数据
                    cleanDemandDataService.doDelete(deletedDataIds);
                    cleanDemandDataDetailDao.deleteByDataIds(deletedDataIds);
        		}
//        	});
		} catch (Exception e) {
			 log.error("零件需求汇总-日需求计算失败：", e);
			 throw new BusinessException("零件需求汇总-日需求计算失败");
		}finally {
			CustomThreadPoolFactory.closeOrShutdown(threadPoolExecutor);
		}
    }

	private Map<String, List<ResourceCalendarVO>> getMonthCalendarMap(
			List<LoadingDemandSubmissionDetailVO> loadingDemandSubmissionDetailVOSDay) {
		List<String> monthList = loadingDemandSubmissionDetailVOSDay.stream()
                .map(item -> DateUtils.dateToString(DateUtils.stringToDate(item.getDemandTime(), "yyyy-MM-dd"), "yyyy-MM"))
                .distinct().collect(Collectors.toList());
        Collections.sort(monthList);
        Date firstMonthDate = DateUtils.stringToDate(monthList.get(0),DateUtils.YEAR_MONTH);
        Date lastMonthDate = DateUtils.stringToDate(monthList.get(monthList.size() - 1),DateUtils.YEAR_MONTH);
        Date firstDay = DateUtils.getMonthFirstDay(firstMonthDate);
        Date lastDay = DateUtils.getMonthLastDay(lastMonthDate);
        List<Date> dateList = DateUtils.getIntervalDates(firstDay,lastDay);
        List<ResourceCalendarVO> resourceCalendarVOS = new ArrayList<>();
        for (Date d : dateList){
            if (isWeekend(d)){
                continue;
            }
            ResourceCalendarVO resourceCalendarVO = new ResourceCalendarVO();
            resourceCalendarVO.setWorkDay(d);
            resourceCalendarVOS.add(resourceCalendarVO);
        }
        //按照月份分组
        Map<String, List<ResourceCalendarVO>> monthCalendarMap = resourceCalendarVOS.stream()
        		.collect(Collectors.groupingBy(e -> DateUtils.dateToString(e.getWorkDay(), DateUtils.YEAR_MONTH)));
		return monthCalendarMap;
	}
    
    protected boolean isWeekend(Date date){
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        DayOfWeek dayOfWeek = localDate.getDayOfWeek();
        return dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY;

    }

    /**
     * 主机厂，车型，产品编码对应的发货数量(获取仓库发货数量)
     * @param productCodeList
     * @param productVehicleModelMap
     * @param vehicleModeCodes
     * @param currentMonthFirstDay
     * @param currentMonthCurrentDay
     * @return
     */
	private Map<String, BigDecimal> getWarehouseReleaseRecordDeliveryQtyMap(List<String> productCodeList,
			Map<String, String> productVehicleModelMap, List<String> vehicleModeCodes, String currentMonthFirstDay,
			String currentMonthCurrentDay) {
		Map<String, BigDecimal> releaseMap = new HashMap<>();
        if (!currentMonthCurrentDay.equals(currentMonthFirstDay)) {
            Map<String, Object> releaseParams = new HashMap<>();
            releaseParams.put("beginDate", currentMonthFirstDay);
            releaseParams.put("endDate", currentMonthCurrentDay);
            releaseParams.put("productCodes", productCodeList);
            List<WarehouseReleaseRecordMonthVO> warehouseReleaseRecords = warehouseReleaseRecordService.selectMonthVOByParams(releaseParams);
            warehouseReleaseRecords.forEach(item -> {
                String itemCode = item.getItemCode();
                String vehicleModelCode = productVehicleModelMap.getOrDefault(itemCode, itemCode);
                item.setVehicleModelCode(vehicleModelCode);
            });
            releaseMap = warehouseReleaseRecords.stream().collect(Collectors
                    .toMap(item -> String.join("&", item.getOemCode(), item.getVehicleModelCode(), item.getItemCode(), item.getYearMonth()),
                            WarehouseReleaseRecordMonthVO::getSumQty, (k1, k2) -> k1));
        }
		return releaseMap;
	}
	
	/**
     * 主机厂，车型，产品编码对应的发货数量(获取仓库发货至中转库的发货数量)
     * @param productCodeList
     * @param productVehicleModelMap
     * @param vehicleModeCodes
     * @param currentMonthFirstDay
     * @param currentMonthCurrentDay
     * @return
     */
	private Map<String, BigDecimal> getWarehouseReleaseToWarehouseDeliveryQtyMap(List<String> productCodeList,
			Map<String, String> productVehicleModelMap, List<String> vehicleModeCodes, String currentMonthFirstDay,
			String currentMonthCurrentDay) {
		Map<String, BigDecimal> releaseMap = new HashMap<>();
        if (!currentMonthCurrentDay.equals(currentMonthFirstDay)) {
            Map<String, Object> releaseParams = new HashMap<>();
            releaseParams.put("beginDate", currentMonthFirstDay);
            releaseParams.put("endDate", currentMonthCurrentDay);
            releaseParams.put("productCodes", productCodeList);
            List<WarehouseReleaseToWarehouseMonthVO> warehouseReleaseToWarehouses = warehouseReleaseToWarehouseService.selectMonthVOByParams(releaseParams);
            warehouseReleaseToWarehouses.forEach(item -> {
                String itemCode = item.getItemCode();
                String vehicleModelCode = productVehicleModelMap.getOrDefault(itemCode, itemCode);
                item.setVehicleModelCode(vehicleModelCode);
            });
            releaseMap = warehouseReleaseToWarehouses.stream().collect(Collectors
                    .toMap(item -> String.join("&", item.getOemCode(), item.getVehicleModelCode(), item.getItemCode(), item.getYearMonth()),
                    		WarehouseReleaseToWarehouseMonthVO::getSumQty, (k1, k2) -> k1));
        }
		return releaseMap;
	}

    public void processDataByDemandCategory(String versionId,
                                            List<LoadingDemandSubmissionVO> loadingDemandSubmissionVOS,
                                            Map<String, String> productVehicleModelMap,
                                            Map<String, List<OemProductLineMapVO>> oemProductLineMapVOMap,
                                            Map<String, List<ResourceCalendarVO>> resourceCodeCalendarMap,
                                            Map<String, List<LoadingDemandSubmissionDetailVO>> detailDayMap,
                                            Map<String, List<LoadingDemandSubmissionDetailVO>> detailMonthMap,
                                            Map<String, Map<String, ConsistenceDemandForecastDataDetailPO>> consistenceRealtionMap,
                                            String currentMonth,Map<String, BigDecimal> releaseMap, Map<String, BigDecimal> releaseToMap, 
                                            Map<String, List<ResourceCalendarVO>> monthCalendarMap,
                                            List<CleanDemandDataDTO> cleanDemandDataDTOS, List<CleanDemandDataDetailDTO> cleanDemandDataDetailDTOS){
        List<String> addedLoadDemandDetailIdList = new ArrayList<>();
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern(DateUtils.COMMON_DATE_STR1);
        //todo 前置做了demandTime的数据处理，此处应该不需要再单独做判断
        String format1 = "\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}";
        String format2 = "\\d{4}-\\d{2}-\\d{2}";
        Random random = new Random();
        loadingDemandSubmissionVOS.parallelStream().forEach( loadingDemandSubmissionVO -> {
            String oemCode = loadingDemandSubmissionVO.getOemCode();
            String productCode = loadingDemandSubmissionVO.getProductCode();
            String vehicleModelCode = productVehicleModelMap.get(productCode);
            // 获取对应的厂线数据 主机厂 + 车型
            List<OemProductLineMapVO> oemProductLineMapVOS = oemProductLineMapVOMap.get(String.join("&", oemCode,
                    vehicleModelCode));

            //获取主机厂装车日历数据
            //需要根据主机厂 + 厂线去获取装车日历数据
            // -->变成产线 + 车型获取装车日历数据
            Map<String, List<ResourceCalendarVO>> oemResourceCalendarsMapOfMonth = new HashMap<>();
            if (CollectionUtils.isNotEmpty(oemProductLineMapVOS)) {
                List<ResourceCalendarVO> resourceCalendarVOS = new ArrayList<>();
                oemProductLineMapVOS.forEach(oemProductLineMapVO -> {
                    List<ResourceCalendarVO> list = resourceCodeCalendarMap.get(String
                            .join("&", oemProductLineMapVO.getLineCode(),vehicleModelCode));
                    if (CollectionUtils.isNotEmpty(list)){
                        //一天当中有多个日历,进行去重,只留一天
                        Map<String,ResourceCalendarVO> map =
                                list.stream().collect(Collectors.toMap(
                                        item -> DateUtils.dateToString(item.getWorkDay()),
                                item -> item,(existing, replacement) -> existing));
                        resourceCalendarVOS.addAll(new ArrayList<>(map.values()));
                    }
                });
                if (CollectionUtils.isNotEmpty(resourceCalendarVOS)) {
                    oemResourceCalendarsMapOfMonth = resourceCalendarVOS.stream().collect(Collectors
                            .groupingBy(item -> DateUtils.dateToString(item.getWorkDay(), "yyyy-MM")));
                }
            }
            String submissionId = UUIDUtil.getUUID();
            CleanDemandDataDTO cleanDemandDataDTO = new CleanDemandDataDTO();
            cleanDemandDataDTO.setId(submissionId);
            cleanDemandDataDTO.setVersionId(versionId);
            cleanDemandDataDTO.setDemandCategory(loadingDemandSubmissionVO.getDemandCategory());
            cleanDemandDataDTO.setOemCode(oemCode);
            cleanDemandDataDTO.setVehicleModelCode(vehicleModelCode);
            cleanDemandDataDTO.setProductCode(productCode);
            cleanDemandDataDTO.setPartName(loadingDemandSubmissionVO.getPartNumber());
            cleanDemandDataDTO.setDemandType(DemandTypeEnum.LOADING_DEMAND.getCode());
            cleanDemandDataDTO.setVersionStatus(PublishStatusEnum.UNPUBLISH.getCode());
            cleanDemandDataDTOS.add(cleanDemandDataDTO);
            List<LoadingDemandSubmissionDetailVO> submissionDetailVOS = detailDayMap.get(loadingDemandSubmissionVO.getId());
            if (CollectionUtils.isNotEmpty(submissionDetailVOS)) {
            	//获取一致性预测需求数据
                Map<String, ConsistenceDemandForecastDataDetailPO> consistenceDemandForecastDataDetailPOMap = consistenceRealtionMap
                        .get(String.join("&", cleanDemandDataDTO.getOemCode(),
                                cleanDemandDataDTO.getVehicleModelCode(), cleanDemandDataDTO.getProductCode()));

                //获取月份数据
                List<LoadingDemandSubmissionDetailVO> monthSubmissionDetailVOList = detailMonthMap.get(loadingDemandSubmissionVO.getId());
                Map<String, List<LoadingDemandSubmissionDetailVO>> monthDetailMapOfMonth = new HashMap<>();
                if (CollectionUtils.isNotEmpty(monthSubmissionDetailVOList)) {
                    monthDetailMapOfMonth = monthSubmissionDetailVOList.stream().collect(Collectors
                            .groupingBy(LoadingDemandSubmissionDetailVO::getDemandTime));
                }
                //根据月份数据去重
                List<String> monthList = submissionDetailVOS.stream()
                        .map(item -> DateUtils.dateToString(DateUtils.stringToDate(item.getDemandTime(), "yyyy-MM-dd"), "yyyy-MM"))
                        .distinct().collect(Collectors.toList());

                Map<String, List<LoadingDemandSubmissionDetailVO>> dayDetailMapOfMonth = submissionDetailVOS.stream().collect(Collectors
                        .groupingBy(item -> DateUtils
                                .dateToString(DateUtils.stringToDate(item.getDemandTime(), "yyyy-MM-dd"), "yyyy-MM")));
                //by需求时间分组，如果时间不为空，不进行均分，则用此map数据创建日需求
                Map<String, List<LoadingDemandSubmissionDetailVO>> detailVOMapOfDemandTime =
                        submissionDetailVOS.stream().collect(Collectors.groupingBy(LoadingDemandSubmissionDetailVO::getDemandTime));
                for (String month : monthList) {
                    // 获取月份的天数
                    List<LocalDate> allDaysInMonth = getAllDaysInMonth(month);
                    List<Date> dayDateList = new ArrayList<>();

                    BigDecimal daySummaryQuantity = BigDecimal.ZERO;
                    List<LoadingDemandSubmissionDetailVO> detailVOList = dayDetailMapOfMonth.get(month);
                    List<LoadingDemandSubmissionDetailVO> avgQuantityDayDetailList = new ArrayList<>();
                    if (CollectionUtils.isEmpty(detailVOList)) {
                        continue;
                    }
                    List<String> demandTimeList =
                            detailVOList.stream().map(LoadingDemandSubmissionDetailVO::getDemandTime).distinct().collect(Collectors.toList());
                    // 获取日期最早的数据
                    LoadingDemandSubmissionDetailVO earliestDetailVO = detailVOList.stream().min(Comparator.comparing(LoadingDemandSubmissionDetailVO::getDemandTime)).get();
                    LocalDate earliestLocalDate = null;
                    if (earliestDetailVO.getDemandTime().matches(format1)){
                        earliestLocalDate = LocalDate.parse(earliestDetailVO.getDemandTime(), formatter2);
                    }else if (earliestDetailVO.getDemandTime().matches(format2)){
                        earliestLocalDate = LocalDate.parse(earliestDetailVO.getDemandTime(), formatter);
                    }else {
                        throw new BusinessException("日期格式和预期不匹配:"+earliestDetailVO.getDemandTime());
                    }
                    List<ResourceCalendarVO> resourceCalendarVOList = oemResourceCalendarsMapOfMonth.get(month);
                    if (CollectionUtils.isEmpty(resourceCalendarVOList)) {
                    	resourceCalendarVOList = monthCalendarMap.get(month);
//                            resourceCalendarVOList = dfpResourceCalendarService.getMonthFillResourceCalendar(month);
                    }
                    //过滤resourceCalendarVOList在earliestLocalDate等于或者之后的日期
                    resourceCalendarVOList = resourceCalendarVOList.stream().filter(item -> item.getWorkDay().after(DateUtils.stringToDate(earliestDetailVO.getDemandTime(), DateUtils.COMMON_DATE_STR3))
                    || item.getWorkDay().equals(DateUtils.stringToDate(earliestDetailVO.getDemandTime(), DateUtils.COMMON_DATE_STR3))).collect(Collectors.toList());
                    //装车日历时间范围
                    Map<Date, List<ResourceCalendarVO>> resourceCalendarVOMap =
                            resourceCalendarVOList.stream().collect(Collectors.groupingBy(ResourceCalendarVO::getWorkDay));
                    List<String> oemResourceCalendars = resourceCalendarVOList.stream().map(item -> DateUtils
                            .dateToString(item.getWorkDay())).distinct().collect(Collectors.toList());

                    // 日需求值为null的数量
                    // int nullCount = 0;
                    //Date earliest = null,lastest = null;
                    //取月份剩余天数
                    int valueCount = 0;
                    for (LoadingDemandSubmissionDetailVO loadingDemandSubmissionDetailVO : detailVOList) {
                        Date demandDate = DateUtils.stringToDate(loadingDemandSubmissionDetailVO.getDemandTime(),
                                DateUtils.COMMON_DATE_STR3);
                        //计算需要平摊的日需求数据，需求数量为null并且存在装车日历中(如果装车日历不为空)
                        if (null == loadingDemandSubmissionDetailVO.getDemandQuantity()
                                && (CollectionUtils.isEmpty(oemResourceCalendars)
                                || oemResourceCalendars.contains(loadingDemandSubmissionDetailVO.getDemandTime()))) {
                            avgQuantityDayDetailList.add(loadingDemandSubmissionDetailVO);
                            dayDateList.add(demandDate);
                            if (resourceCalendarVOMap.get(demandDate)!=null){
                                valueCount += resourceCalendarVOMap.get(demandDate).size();
                            }
                        } else {
                            if (null != loadingDemandSubmissionDetailVO.getDemandQuantity()) {
                                daySummaryQuantity = daySummaryQuantity.add(loadingDemandSubmissionDetailVO.getDemandQuantity());
//                                    nullCount--;
                                //valueCount--;
                            }
                        }
                    }
                    // 需要填充本月没有的预测数据
                    for (LocalDate localDate : allDaysInMonth) {
                        // 日期需要在装车需求提报中不存在的并且是往后的数据
                        String dateStr = localDate.format(formatter);
                        if (!demandTimeList.contains(dateStr) && earliestLocalDate.isBefore(localDate)){
                            if (oemResourceCalendars.contains(dateStr)){
                                Date d = DateUtils.stringToDate(dateStr, DateUtils.COMMON_DATE_STR3);
                                dayDateList.add(d);
                                if (resourceCalendarVOMap.get(d)!=null){
                                    valueCount += resourceCalendarVOMap.get(d).size();
                                }
                            }else {
                                CleanDemandDataDetailDTO cleanDemandDataDetailDTO = new CleanDemandDataDetailDTO();
                                cleanDemandDataDetailDTO.setId(UUIDUtil.getUUID() + "-" + (100000 + random.nextInt(900000)));
                                cleanDemandDataDetailDTO.setCleanDemandDataId(submissionId);
                                cleanDemandDataDetailDTO.setDemandTime(DateUtils.stringToDate(dateStr,
                                        DateUtils.COMMON_DATE_STR3));
                                cleanDemandDataDetailDTOS.add(cleanDemandDataDetailDTO);
                            }

                        }
                    }

                    if (CollectionUtils.isNotEmpty(dayDateList)) {
                        //先获取一致性预测需求的数据，如果为空则取装车提报的数据
                        BigDecimal monthQuantity = BigDecimal.ZERO;
                        if (null != consistenceDemandForecastDataDetailPOMap
                                && consistenceDemandForecastDataDetailPOMap.containsKey(month)) {
                            ConsistenceDemandForecastDataDetailPO consistenceDemandForecastDataDetailPO = consistenceDemandForecastDataDetailPOMap.get(month);
                            monthQuantity = consistenceDemandForecastDataDetailPO.getForecastQuantity() != null
                                    ? consistenceDemandForecastDataDetailPO.getForecastQuantity() : BigDecimal.ZERO;
                        } else {
                            List<LoadingDemandSubmissionDetailVO> monthLoadingDemandSubmissionDetailVO =
                                    monthDetailMapOfMonth.get(month);
                            if (CollectionUtils.isNotEmpty(monthLoadingDemandSubmissionDetailVO)) {
                                for (LoadingDemandSubmissionDetailVO vo : monthLoadingDemandSubmissionDetailVO){
                                    monthQuantity = vo.getDemandQuantity() != null
                                            ? vo.getDemandQuantity() : BigDecimal.ZERO;
                                }
                            }
                        }

                        //向上取整
                        if (null == monthQuantity || monthQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                            for (LoadingDemandSubmissionDetailVO demandSubmissionDetailVO : avgQuantityDayDetailList) {
                                CleanDemandDataDetailDTO cleanDemandDataDetailDTO = new CleanDemandDataDetailDTO();
                                cleanDemandDataDetailDTO.setId(UUIDUtil.getUUID() + "-" + (100000 + random.nextInt(900000)));
                                cleanDemandDataDetailDTO.setCleanDemandDataId(submissionId);
                                if (StringUtils.isNotBlank(demandSubmissionDetailVO.getDemandTime())) {
                                    cleanDemandDataDetailDTO.setDemandTime(DateUtils.stringToDate(demandSubmissionDetailVO.getDemandTime(), DateUtils.COMMON_DATE_STR3));
                                }
                                cleanDemandDataDetailDTO.setDemandQuantity(0);
                                cleanDemandDataDetailDTOS.add(cleanDemandDataDetailDTO);
                                addedLoadDemandDetailIdList.add(demandSubmissionDetailVO.getId());
                            }
                        } else {
                            BigDecimal subtract = monthQuantity.subtract(daySummaryQuantity);
                            if (month.equals(currentMonth)) {
                                String uniqueKey = String.join("&", oemCode, vehicleModelCode, productCode, month);
                                //扣减仓库发货数量
                                subtract = subtract.subtract(releaseMap.getOrDefault(uniqueKey, BigDecimal.ZERO));
                                //扣减仓库发货至中转库发货数量
                                BigDecimal orDefault = releaseToMap.getOrDefault(uniqueKey, BigDecimal.ZERO);
                                subtract = subtract.subtract(orDefault);
                            }
                            List<Integer> shardNumList = new ArrayList<>();
                            if (subtract.compareTo(BigDecimal.ZERO) > 0) {
//                                    log.info("主机厂:{}, 本厂编码;{}, 月份:{}, 预测数量:{}, 平摊天数:{}", oemCode, productCode, month, subtract, valueCount);
                                if (valueCount > 0){
                                    shardNumList = distributeEvenly(subtract.intValue(), valueCount);
                                }

                            }

                            // 循环日期
                            // 排序dayDateList
                            dayDateList.sort(Comparator.comparing(Date::getTime));

                            int index = 0;
                            for (int i = 0; i < dayDateList.size(); i++) {
                                // 新增
                                Date date = dayDateList.get(i);
                                List<LoadingDemandSubmissionDetailVO> loadingDemandSubmissionDetailVOS =
                                        detailVOMapOfDemandTime.get(DateUtils.dateToString(dayDateList.get(i), DateUtils.COMMON_DATE_STR3));
                                CleanDemandDataDetailDTO cleanDemandDataDetailDTO = new CleanDemandDataDetailDTO();
                                cleanDemandDataDetailDTO.setId(UUIDUtil.getUUID() + "-" + (100000 + random.nextInt(900000)));
                                cleanDemandDataDetailDTO.setDemandTime(DateUtils.stringToDate(DateUtils.dateToString(dayDateList.get(i), DateUtils.COMMON_DATE_STR3), DateUtils.COMMON_DATE_STR3));
                                cleanDemandDataDetailDTO.setCleanDemandDataId(submissionId);
                                Integer demandQuantity = 0;
                                if (shardNumList.size() - 1 >= index) {
                                    demandQuantity = shardNumList.get(index);
                                }
                                if (resourceCalendarVOMap.get(date)!=null){
                                    demandQuantity = demandQuantity * resourceCalendarVOMap.get(date).size();
                                    index += resourceCalendarVOMap.get(date).size();
                                }else {
                                    index++;
                                }
                                if (CollectionUtils.isEmpty(loadingDemandSubmissionDetailVOS) && demandQuantity < 0){
                                    continue;
                                }
                                cleanDemandDataDetailDTO.setDemandQuantity(demandQuantity);
                                cleanDemandDataDetailDTOS.add(cleanDemandDataDetailDTO);
                                if (CollectionUtils.isNotEmpty(loadingDemandSubmissionDetailVOS)){
                                    for (LoadingDemandSubmissionDetailVO loadingDemandSubmissionDetailVO : loadingDemandSubmissionDetailVOS) {
                                        addedLoadDemandDetailIdList.add(loadingDemandSubmissionDetailVO.getId());
                                    }
                                    //addedLoadDemandDetailIdList.add(loadingDemandSubmissionDetailVO.getId());
                                }
                            }
                        }
                    }
                }

                for (Map.Entry<String, List<LoadingDemandSubmissionDetailVO>> entry : detailVOMapOfDemandTime.entrySet()) {
                    List<LoadingDemandSubmissionDetailVO> loadingDemandSubmissionDetailVOList = entry.getValue();
                    //避免重复添加
                    boolean exist = false;
                    for (LoadingDemandSubmissionDetailVO loadingDemandSubmissionDetailVO : loadingDemandSubmissionDetailVOList){
                        if (addedLoadDemandDetailIdList.contains(loadingDemandSubmissionDetailVO.getId())) {
                            exist = true;
                            break;
                        }
                    }
                    if (exist){
                        continue;
                    }
                    CleanDemandDataDetailDTO cleanDemandDataDetailDTO = new CleanDemandDataDetailDTO();
                    cleanDemandDataDetailDTO.setId(UUIDUtil.getUUID() + "-" + (100000 + random.nextInt(900000)));
                    cleanDemandDataDetailDTO.setCleanDemandDataId(submissionId);
                    LoadingDemandSubmissionDetailVO loadingDemandSubmissionDetailVO = loadingDemandSubmissionDetailVOList.get(0);
                    if (StringUtils.isNotBlank(loadingDemandSubmissionDetailVO.getDemandTime())) {
                        cleanDemandDataDetailDTO.setDemandTime(DateUtils
                                .stringToDate(loadingDemandSubmissionDetailVO.getDemandTime(), DateUtils.COMMON_DATE_STR3));
                    }
                    int demandQuantity = 0;
                    for (LoadingDemandSubmissionDetailVO detailVO : loadingDemandSubmissionDetailVOList){
                        if (Objects.nonNull(detailVO.getDemandQuantity())) {
                            demandQuantity = demandQuantity + detailVO.getDemandQuantity().intValue();
                        }
                    }
                    cleanDemandDataDetailDTO.setDemandQuantity(demandQuantity);

                    cleanDemandDataDetailDTOS.add(cleanDemandDataDetailDTO);
                }
            }

        });
    }
    
    /**
     * 获取指定月份的所有天数
     *
     * @param yearMonthStr 指定的年份和月份，格式为"yyyy-MM"
     * @return 该月份的所有日期列表
     */
    public static List<LocalDate> getAllDaysInMonth(String yearMonthStr) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        YearMonth yearMonth = YearMonth.parse(yearMonthStr, formatter);
        int daysInMonth = yearMonth.lengthOfMonth();
        List<LocalDate> dates = new ArrayList<>();
        for (int i = 1; i <= daysInMonth; i++) {
            LocalDate date = yearMonth.atDay(i);
            dates.add(date);
        }
        return dates;
    }
    
    public static List<Integer> distributeEvenly(int total, int parts) {
        List<Integer> distribution = new ArrayList<>();
        int baseValue = total / parts;  // 每个部分的基础值
        int remainder = total % parts;  // 处理余数

        for (int i = 0; i < parts; i++) {
            if (i < remainder) {
                // 前面的部分每个加1，以处理余数
                distribution.add(baseValue + 1);
            } else {
                distribution.add(baseValue);
            }
        }

        return distribution;
    }

    @Override
    public int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList) {
        if (CollectionUtils.isNotEmpty(versionDTOList)) {
            return cleanDemandDataDao.deleteBatchVersion(versionDTOList);
        }
        return 0;
    }

    @Override
    public void doDeleteByVersionId(String versionId) {
        cleanDemandDataDao.deleteByVersionId(versionId);
    }

    @Override
    public List<CleanDemandDataVO> selectByPrimaryKeys(List<String> ids) {
            List<CleanDemandDataPO> cleanDemandDataPOList = cleanDemandDataDao.selectByPrimaryKeys(ids);
        return CleanDemandDataConvertor.INSTANCE.po2Vos(cleanDemandDataPOList);
    }

    @Override
    public List<LabelValue<String>> selectOemDropdown(String versionId) {
        return cleanDemandDataDao.selectOemDropdown(versionId).stream()
                .map(x -> new LabelValue<>(x.getLabel() + "(" + x.getValue() + ")", x.getValue()))
                .sorted(Comparator.comparing(LabelValue::getLabel)).collect(Collectors.toList());
    }
}