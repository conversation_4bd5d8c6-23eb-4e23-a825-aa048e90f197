<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.demand.infrastructure.dao.DemandForecastEstablishmentDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.demand.infrastructure.po.DemandForecastEstablishmentPO">
        <!--@Table fdp_demand_forecast_establishment-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="forecast_version_id" jdbcType="VARCHAR" property="forecastVersionId"/>
        <result column="demand_category" jdbcType="VARCHAR" property="demandCategory"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="risk_level" jdbcType="VARCHAR" property="riskLevel"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
        <result column="access_position" jdbcType="VARCHAR" property="accessPosition"/>
        <result column="material_risk_level" jdbcType="VARCHAR" property="materialRiskLevel"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="part_name" jdbcType="VARCHAR" property="partName"/>
        <result column="forecast_time" jdbcType="TIMESTAMP" property="forecastTime"/>
        <result column="customer_forecast" jdbcType="VARCHAR" property="customerForecast"/>
        <result column="algorithm_forecast" jdbcType="VARCHAR" property="algorithmForecast"/>
        <result column="demand_forecast" jdbcType="VARCHAR" property="demandForecast"/>
        <result column="delivery_num" jdbcType="VARCHAR" property="deliveryNum"/>
        <result column="customer_precision" jdbcType="VARCHAR" property="customerPrecision"/>
        <result column="algorithm_precision" jdbcType="VARCHAR" property="algorithmPrecision"/>
        <result column="forecast_precision" jdbcType="VARCHAR" property="forecastPrecision"/>
        <result column="change_value" jdbcType="VARCHAR" property="changeValue"/>
        <result column="change_type" jdbcType="VARCHAR" property="changeType"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="date_type" jdbcType="VARCHAR" property="dateType"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.dfp.demand.vo.DemandForecastEstablishmentVO">
        <result column="oem_name" jdbcType="VARCHAR" property="oemName"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="plan_period" jdbcType="VARCHAR" property="planPeriod"/>
    </resultMap>
    <resultMap id="OverviewVOMap" type="com.yhl.scp.dfp.demand.vo.DemandForecastOverViewVO">
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="oem_name" jdbcType="VARCHAR" property="oemName"/>
        <result column="risk_level" jdbcType="VARCHAR" property="riskLevel"/>
    </resultMap>
    <resultMap id="DetailVOMap" type="com.yhl.scp.dfp.demand.vo.DemandForecastEstablishmentVehicleVO">
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
        <result column="access_position" jdbcType="VARCHAR" property="accessPosition"/>
    </resultMap>
    <resultMap id="LineChartMap" type="com.yhl.scp.dfp.release.dto.ReleaseLineChartDTO">
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="forecast_time" jdbcType="VARCHAR" property="forecastTime"/>
        <result column="demand_forecast" jdbcType="INTEGER" property="demandForecast"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,forecast_version_id,demand_category,oem_code,risk_level,vehicle_model_code,access_position,material_risk_level,
        product_code,part_name,forecast_time,customer_forecast,algorithm_forecast,demand_forecast,delivery_num,
        customer_precision,algorithm_precision,forecast_precision,change_value,change_type,
        remark,enabled,creator,create_time,modifier,modify_time,version_value,date_type
    </sql>
    <sql id="VO_Column_List">
        <include refid="Base_Column_List"/>,oem_name,product_name
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.forecastVersionId != null and params.forecastVersionId != ''">
                and forecast_version_id = #{params.forecastVersionId,jdbcType=VARCHAR}
            </if>
            <if test="params.demandCategory != null and params.demandCategory != ''">
                and demand_category = #{params.demandCategory,jdbcType=VARCHAR}
            </if>
            <if test="params.versionIds != null and params.versionIds.size() > 0">
                and forecast_version_id in
                <foreach collection="params.versionIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.oemCode != null and params.oemCode != ''">
                and oem_code = #{params.oemCode,jdbcType=VARCHAR}
            </if>
            <if test="params.oemCodes != null and params.oemCodes.size() > 0">
                and oem_code in
                <foreach collection="params.oemCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.riskLevel != null and params.riskLevel != ''">
                and risk_level = #{params.riskLevel,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleModelCode != null and params.vehicleModelCode != ''">
                and vehicle_model_code = #{params.vehicleModelCode,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleModelCodeList != null and params.vehicleModelCodeList.size() > 0">
                and vehicle_model_code in
                <foreach collection="params.vehicleModelCodeList" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.accessPosition != null and params.accessPosition != ''">
                and access_position = #{params.accessPosition,jdbcType=VARCHAR}
            </if>
            <if test="params.materialRiskLevel != null and params.materialRiskLevel != ''">
                and material_risk_level = #{params.materialRiskLevel,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCodeList != null and params.productCodeList.size() > 0">
                and product_code in
                <foreach collection="params.productCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.partName != null and params.partName != ''">
                and part_name = #{params.partName,jdbcType=VARCHAR}
            </if>
            <if test="params.forecastTime != null">
                and forecast_time = #{params.forecastTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.customerForecast != null">
                and customer_forecast = #{params.customerForecast,jdbcType=VARCHAR}
            </if>
            <if test="params.algorithmForecast != null">
                and algorithm_forecast = #{params.algorithmForecast,jdbcType=VARCHAR}
            </if>
            <if test="params.demandForecast != null">
                and demand_forecast = #{params.demandForecast,jdbcType=VARCHAR}
            </if>
            <if test="params.deliveryNum != null">
                and delivery_num = #{params.deliveryNum,jdbcType=VARCHAR}
            </if>
            <if test="params.customerPrecision != null">
                and customer_precision = #{params.customerPrecision,jdbcType=VARCHAR}
            </if>
            <if test="params.algorithmPrecision != null">
                and algorithm_precision = #{params.algorithmPrecision,jdbcType=VARCHAR}
            </if>
            <if test="params.forecastPrecision != null">
                and forecast_precision = #{params.forecastPrecision,jdbcType=VARCHAR}
            </if>
            <if test="params.changeValue != null">
                and change_value = #{params.changeValue,jdbcType=VARCHAR}
            </if>
            <if test="params.changeType != null and params.changeType != ''">
                and change_type = #{params.changeType,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.dateType != null and params.dateType != ''">
                and date_type = #{params.dateType,jdbcType=VARCHAR}
            </if>
            <if test="params.forecastVersionIdList != null and params.forecastVersionIdList.size() > 0">
                and forecast_version_id in
                <foreach collection="params.forecastVersionIdList" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.forecastStartTime != null">
                and forecast_time <![CDATA[ >= ]]> #{params.forecastStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.forecastEndTime != null">
                and forecast_time <![CDATA[ <= ]]> #{params.forecastEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.productCodeLike != null and params.productCodeLike != ''">
                and product_code like concat('%', #{params.productCodeLike,jdbcType=VARCHAR}, '%')
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_demand_forecast_establishment
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_demand_forecast_establishment
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_fdp_demand_forecast_establishment
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_demand_forecast_establishment
        <include refid="Base_Where_Condition"/>
    </select>
    <select id="selectDataPermissionByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_demand_forecast_establishment fdfe
        <include refid="Base_Where_Condition"/>
    </select>
    <select id="selectByParams4ForecastReview" resultMap="BaseResultMap">
        select oem_code, vehicle_model_code, product_code, forecast_time, demand_forecast
        from fdp_demand_forecast_establishment
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_fdp_demand_forecast_establishment
        <include refid="Base_Where_Condition"/>
    </select>
    <select id="selectLastVersionCode" resultType="java.lang.String">
        select version_code
        from fdp_demand_forecast_establishment
        where version_code like concat('%', #{versionCodePre,jdbcType=VARCHAR}, '%')
    </select>
    <select id="selectVersionCode" resultType="java.lang.String">
        select DISTINCT t.forecast_version_id
        from
        (
        select
        <include refid="Base_Column_List"/>
        FROM
        fdp_demand_forecast_establishment f
        ORDER BY
        create_time DESC
        ) t
    </select>
    <select id="selectPreMonthDemandForecastDataVersionCode" resultType="java.lang.String">
        SELECT MAX(version_code)
        FROM fdp_demand_forecast_establishment
        WHERE DATE_FORMAT(create_time, '%Y-%m') = DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y-%m');
    </select>

    <select id="selectByVersionAndOem" resultType="com.yhl.scp.dfp.release.vo.ReleaseVO">
        select id,
               product_code        as productCode,
               core_process        as coreProcess,
               product_special     as productSpecial,
               vehicle_model_code  as vehicleModelCode,
               material_risk_level as materialRiskLevel,
               forecast_time       as forecastTime,
               demand_forecast     as quantity,
               version_value       as versionValue
        from v_fdp_release_product_detail
        where 1 = 1
          and parent_version_id = #{versionId,jdbcType=VARCHAR}
          and oem_code = #{oemCode,jdbcType=VARCHAR}
    </select>
    <select id="selectOemWithRisk" parameterType="com.yhl.scp.dfp.demand.dto.DemandForecastOverViewQueryDTO"
            resultMap="OverviewVOMap">
        select distinct fdfe.oem_code, mo.oem_name, forl.risk_level
        from fdp_demand_forecast_establishment fdfe
        left join mds_oem mo on mo.oem_code = fdfe.oem_code
        left join fdp_oem_risk_level forl on forl.oem_code = fdfe.oem_code and forl.estimate_time =
        #{param.planPeriod,jdbcType=VARCHAR}
        where 1 = 1
        and fdfe.demand_category=#{param.demandType,jdbcType=VARCHAR}
        and fdfe.forecast_version_id = #{param.versionId,jdbcType=VARCHAR}
        <if test="param.oemCodeList != null and param.oemCodeList.size() > 0">
            and fdfe.oem_code in
            <foreach collection="param.oemCodeList" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="param.riskLevelSet != null and param.riskLevelSet.size() > 0">
            and forl.risk_level in
            <foreach collection="param.riskLevelSet" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="selectDataByCondition" resultMap="DetailVOMap">
        select distinct
        vehicle_model_code,
        access_position
        from v_fdp_demand_forecast_establishment_vehicle
        where forecast_version_id = #{param.versionId}
        and oem_code = #{param.oemCode}
        and vehicle_model_code is not null
        <if test="param.productCodeList != null and param.productCodeList.size() > 0">
            and product_code in
            <foreach collection="param.productCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="param.vehicleModelCodeList != null and param.vehicleModelCodeList.size() > 0">
            and vehicle_model_code in
            <foreach collection="param.vehicleModelCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="selectProductDataByCondition" resultMap="DetailVOMap">
        select distinct mpsp.product_code,
        mpsp.product_name
        from mds_product_stock_point mpsp
        where 1 = 1
        and exists (select 1
        from fdp_demand_forecast_establishment fdfe
        where fdfe.forecast_version_id = #{param.versionId}
        and fdfe.oem_code = #{param.oemCode}
        and fdfe.demand_category = #{param.demandCategory}
        <if test="param.vehicleModelCodeList != null and param.vehicleModelCodeList.size() > 0">
            and vehicle_model_code in
            <foreach collection="param.vehicleModelCodeList" item="item" index="index" open="(" separator=","
                     close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        and fdfe.product_code = mpsp.product_code)
    </select>

    <select id="selectLineChartByYearAndOem" resultMap="LineChartMap">O
        select *
        from v_fdp_demand_forecast_line_chart
        where forecast_time like concat(#{calcYear}, '%')
          and oem_code = #{oemCode,jdbcType=VARCHAR}
    </select>
    <select id="selectOemDropdown" parameterType="java.lang.String"
            resultType="com.yhl.scp.dfp.demand.vo.DemandForecastEstablishmentVO">
        select distinct
            e.oem_code as oemCode,
            o.oem_name as oemName
        from fdp_demand_forecast_establishment e
            inner join mds_oem o on e.oem_code = o.oem_code
        where e.forecast_version_id = #{versionId,jdbcType=VARCHAR}
          and e.demand_category = #{demandCategory,jdbcType=VARCHAR}
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.demand.infrastructure.po.DemandForecastEstablishmentPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into fdp_demand_forecast_establishment(
        id,
        forecast_version_id,
        demand_category,
        oem_code,
        risk_level,
        vehicle_model_code,
        access_position,
        material_risk_level,
        product_code,
        part_name,
        forecast_time,
        customer_forecast,
        algorithm_forecast,
        demand_forecast,
        delivery_num,
        customer_precision,
        algorithm_precision,
        forecast_precision,
        change_value,
        change_type,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        date_type)
        values (
        #{id,jdbcType=VARCHAR},
        #{forecastVersionId,jdbcType=VARCHAR},
        #{demandCategory,jdbcType=VARCHAR},
        #{oemCode,jdbcType=VARCHAR},
        #{riskLevel,jdbcType=VARCHAR},
        #{vehicleModelCode,jdbcType=VARCHAR},
        #{accessPosition,jdbcType=VARCHAR},
        #{materialRiskLevel,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{partName,jdbcType=VARCHAR},
        #{forecastTime,jdbcType=TIMESTAMP},
        #{customerForecast,jdbcType=VARCHAR},
        #{algorithmForecast,jdbcType=VARCHAR},
        #{demandForecast,jdbcType=VARCHAR},
        #{deliveryNum,jdbcType=VARCHAR},
        #{customerPrecision,jdbcType=VARCHAR},
        #{algorithmPrecision,jdbcType=VARCHAR},
        #{forecastPrecision,jdbcType=VARCHAR},
        #{changeValue,jdbcType=VARCHAR},
        #{changeType,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{dateType,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.dfp.demand.infrastructure.po.DemandForecastEstablishmentPO">
        insert into fdp_demand_forecast_establishment(id,
                                                      forecast_version_id,
                                                      demand_category,
                                                      oem_code,
                                                      risk_level,
                                                      vehicle_model_code,
                                                      access_position,
                                                      material_risk_level,
                                                      product_code,
                                                      part_name,
                                                      forecast_time,
                                                      customer_forecast,
                                                      algorithm_forecast,
                                                      demand_forecast,
                                                      delivery_num,
                                                      customer_precision,
                                                      algorithm_precision,
                                                      forecast_precision,
                                                      change_value,
                                                      change_type,
                                                      remark,
                                                      enabled,
                                                      creator,
                                                      create_time,
                                                      modifier,
                                                      modify_time,
                                                      version_value,
                                                      date_type)
        values (#{id,jdbcType=VARCHAR},
                #{forecastVersionId,jdbcType=VARCHAR},
                #{demandCategory,jdbcType=VARCHAR},
                #{oemCode,jdbcType=VARCHAR},
                #{riskLevel,jdbcType=VARCHAR},
                #{vehicleModelCode,jdbcType=VARCHAR},
                #{accessPosition,jdbcType=VARCHAR},
                #{materialRiskLevel,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{partName,jdbcType=VARCHAR},
                #{forecastTime,jdbcType=TIMESTAMP},
                #{customerForecast,jdbcType=VARCHAR},
                #{algorithmForecast,jdbcType=VARCHAR},
                #{demandForecast,jdbcType=VARCHAR},
                #{deliveryNum,jdbcType=VARCHAR},
                #{customerPrecision,jdbcType=VARCHAR},
                #{algorithmPrecision,jdbcType=VARCHAR},
                #{forecastPrecision,jdbcType=VARCHAR},
                #{changeValue,jdbcType=VARCHAR},
                #{changeType,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER},
                #{dateType,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into fdp_demand_forecast_establishment(
        id,
        forecast_version_id,
        demand_category,
        oem_code,
        risk_level,
        vehicle_model_code,
        access_position,
        material_risk_level,
        product_code,
        part_name,
        forecast_time,
        customer_forecast,
        algorithm_forecast,
        demand_forecast,
        delivery_num,
        customer_precision,
        algorithm_precision,
        forecast_precision,
        change_value,
        change_type,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        date_type)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.id,jdbcType=VARCHAR},
            #{entity.forecastVersionId,jdbcType=VARCHAR},
            #{entity.demandCategory,jdbcType=VARCHAR},
            #{entity.oemCode,jdbcType=VARCHAR},
            #{entity.riskLevel,jdbcType=VARCHAR},
            #{entity.vehicleModelCode,jdbcType=VARCHAR},
            #{entity.accessPosition,jdbcType=VARCHAR},
            #{entity.materialRiskLevel,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.partName,jdbcType=VARCHAR},
            #{entity.forecastTime,jdbcType=TIMESTAMP},
            #{entity.customerForecast,jdbcType=VARCHAR},
            #{entity.algorithmForecast,jdbcType=VARCHAR},
            #{entity.demandForecast,jdbcType=VARCHAR},
            #{entity.deliveryNum,jdbcType=VARCHAR},
            #{entity.customerPrecision,jdbcType=VARCHAR},
            #{entity.algorithmPrecision,jdbcType=VARCHAR},
            #{entity.forecastPrecision,jdbcType=VARCHAR},
            #{entity.changeValue,jdbcType=VARCHAR},
            #{entity.changeType,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER},
            #{entity.dateType,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into fdp_demand_forecast_establishment(
        id,
        forecast_version_id,
        demand_category,
        oem_code,
        risk_level,
        vehicle_model_code,
        access_position,
        material_risk_level,
        product_code,
        part_name,
        forecast_time,
        customer_forecast,
        algorithm_forecast,
        demand_forecast,
        delivery_num,
        customer_precision,
        algorithm_precision,
        forecast_precision,
        change_value,
        change_type,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        date_type)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.forecastVersionId,jdbcType=VARCHAR},
            #{entity.demandCategory,jdbcType=VARCHAR},
            #{entity.oemCode,jdbcType=VARCHAR},
            #{entity.riskLevel,jdbcType=VARCHAR},
            #{entity.vehicleModelCode,jdbcType=VARCHAR},
            #{entity.accessPosition,jdbcType=VARCHAR},
            #{entity.materialRiskLevel,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.partName,jdbcType=VARCHAR},
            #{entity.forecastTime,jdbcType=TIMESTAMP},
            #{entity.customerForecast,jdbcType=VARCHAR},
            #{entity.algorithmForecast,jdbcType=VARCHAR},
            #{entity.demandForecast,jdbcType=VARCHAR},
            #{entity.deliveryNum,jdbcType=VARCHAR},
            #{entity.customerPrecision,jdbcType=VARCHAR},
            #{entity.algorithmPrecision,jdbcType=VARCHAR},
            #{entity.forecastPrecision,jdbcType=VARCHAR},
            #{entity.changeValue,jdbcType=VARCHAR},
            #{entity.changeType,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER},
            #{entity.dateType,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.demand.infrastructure.po.DemandForecastEstablishmentPO">
        update fdp_demand_forecast_establishment
        set forecast_version_id = #{forecastVersionId,jdbcType=VARCHAR},
            demand_category     = #{demandCategory,jdbcType=VARCHAR},
            oem_code            = #{oemCode,jdbcType=VARCHAR},
            risk_level          = #{riskLevel,jdbcType=VARCHAR},
            vehicle_model_code  = #{vehicleModelCode,jdbcType=VARCHAR},
            access_position     = #{accessPosition,jdbcType=VARCHAR},
            material_risk_level = #{materialRiskLevel,jdbcType=VARCHAR},
            product_code        = #{productCode,jdbcType=VARCHAR},
            part_name           = #{partName,jdbcType=VARCHAR},
            forecast_time       = #{forecastTime,jdbcType=TIMESTAMP},
            customer_forecast   = #{customerForecast,jdbcType=VARCHAR},
            algorithm_forecast  = #{algorithmForecast,jdbcType=VARCHAR},
            demand_forecast     = #{demandForecast,jdbcType=VARCHAR},
            delivery_num        = #{deliveryNum,jdbcType=VARCHAR},
            customer_precision  = #{customerPrecision,jdbcType=VARCHAR},
            algorithm_precision = #{algorithmPrecision,jdbcType=VARCHAR},
            forecast_precision  = #{forecastPrecision,jdbcType=VARCHAR},
            change_value        = #{changeValue,jdbcType=VARCHAR},
            change_type         = #{changeType,jdbcType=VARCHAR},
            remark              = #{remark,jdbcType=VARCHAR},
            enabled             = #{enabled,jdbcType=VARCHAR},
            modifier            = #{modifier,jdbcType=VARCHAR},
            modify_time         = #{modifyTime,jdbcType=TIMESTAMP},
            version_value       = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER};
        update fdp_demand_forecast_establishment
        set version_value = version_value + 1
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.dfp.demand.infrastructure.po.DemandForecastEstablishmentPO">
        update fdp_demand_forecast_establishment
        <set>
            <if test="item.forecastVersionId != null and item.forecastVersionId != ''">
                forecast_version_id = #{item.forecastVersionId,jdbcType=VARCHAR},
            </if>
            <if test="item.demandCategory != null and item.demandCategory != ''">
                demand_category = #{item.demandCategory,jdbcType=VARCHAR},
            </if>
            <if test="item.oemCode != null and item.oemCode != ''">
                oem_code = #{item.oemCode,jdbcType=VARCHAR},
            </if>
            <if test="item.riskLevel != null and item.riskLevel != ''">
                risk_level = #{item.riskLevel,jdbcType=VARCHAR},
            </if>
            <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
            </if>
            <if test="item.accessPosition != null and item.accessPosition != ''">
                access_position = #{item.accessPosition,jdbcType=VARCHAR},
            </if>
            <if test="item.materialRiskLevel != null and item.materialRiskLevel != ''">
                material_risk_level = #{item.materialRiskLevel,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.partName != null and item.partName != ''">
                part_name = #{item.partName,jdbcType=VARCHAR},
            </if>
            <if test="item.forecastTime != null">
                forecast_time = #{item.forecastTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.customerForecast != null">
                customer_forecast = #{item.customerForecast,jdbcType=VARCHAR},
            </if>
            <if test="item.algorithmForecast != null">
                algorithm_forecast = #{item.algorithmForecast,jdbcType=VARCHAR},
            </if>
            <if test="item.demandForecast != null">
                demand_forecast = #{item.demandForecast,jdbcType=VARCHAR},
            </if>
            <if test="item.deliveryNum != null">
                delivery_num = #{item.deliveryNum,jdbcType=VARCHAR},
            </if>
            <if test="item.customerPrecision != null">
                customer_precision = #{item.customerPrecision,jdbcType=VARCHAR},
            </if>
            <if test="item.algorithmPrecision != null">
                algorithm_precision = #{item.algorithmPrecision,jdbcType=VARCHAR},
            </if>
            <if test="item.forecastPrecision != null">
                forecast_precision = #{item.forecastPrecision,jdbcType=VARCHAR},
            </if>
            <if test="item.changeValue != null">
                change_value = #{item.changeValue,jdbcType=VARCHAR},
            </if>
            <if test="item.changeType != null and item.changeType != ''">
                change_type = #{item.changeType,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.dateType != null and item.dateType != ''">
                date_type = #{item.dateType,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
        and version_value = #{versionValue,jdbcType=INTEGER};
        update fdp_demand_forecast_establishment set
        version_value = version_value + 1
        where id = #{id,jdbcType=VARCHAR}
        and version_value = #{versionValue,jdbcType=INTEGER}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update fdp_demand_forecast_establishment
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="forecast_version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=VARCHAR} then #{item.forecastVersionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_category = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=VARCHAR} then #{item.demandCategory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="oem_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=VARCHAR} then #{item.oemCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="risk_level = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=VARCHAR} then #{item.riskLevel,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="vehicle_model_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=VARCHAR} then #{item.vehicleModelCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="access_position = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=VARCHAR} then #{item.accessPosition,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_risk_level = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=VARCHAR} then #{item.materialRiskLevel,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="part_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=VARCHAR} then #{item.partName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="forecast_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=VARCHAR} then #{item.forecastTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="customer_forecast = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=VARCHAR} then #{item.customerForecast,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="algorithm_forecast = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=VARCHAR} then #{item.algorithmForecast,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_forecast = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=VARCHAR} then #{item.demandForecast,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="delivery_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=VARCHAR} then #{item.deliveryNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="customer_precision = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=VARCHAR} then #{item.customerPrecision,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="algorithm_precision = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=VARCHAR} then #{item.algorithmPrecision,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="forecast_precision = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=VARCHAR} then #{item.forecastPrecision,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="change_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=VARCHAR} then #{item.changeValue,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="change_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=VARCHAR} then #{item.changeType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="date_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=VARCHAR} then #{item.dateType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    version_value + 1
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量修改 -->
    <update id="updateNewBatch" parameterType="java.util.List">
        update fdp_demand_forecast_establishment
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="demand_category = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=VARCHAR} then #{item.demandCategory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="customer_forecast = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=VARCHAR} then #{item.customerForecast,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_forecast = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=VARCHAR} then #{item.demandForecast,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    version_value + 1
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update fdp_demand_forecast_establishment
            <set>
                <if test="item.forecastVersionId != null and item.forecastVersionId != ''">
                    forecast_version_id = #{item.forecastVersionId,jdbcType=VARCHAR},
                </if>
                <if test="item.demandCategory != null and item.demandCategory != ''">
                    demand_category = #{item.demandCategory,jdbcType=VARCHAR},
                </if>
                <if test="item.oemCode != null and item.oemCode != ''">
                    oem_code = #{item.oemCode,jdbcType=VARCHAR},
                </if>
                <if test="item.riskLevel != null and item.riskLevel != ''">
                    risk_level = #{item.riskLevel,jdbcType=VARCHAR},
                </if>
                <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                    vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
                </if>
                <if test="item.accessPosition != null and item.accessPosition != ''">
                    access_position = #{item.accessPosition,jdbcType=VARCHAR},
                </if>
                <if test="item.materialRiskLevel != null and item.materialRiskLevel != ''">
                    material_risk_level = #{item.materialRiskLevel,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.partName != null and item.partName != ''">
                    part_name = #{item.partName,jdbcType=VARCHAR},
                </if>
                <if test="item.forecastTime != null">
                    forecast_time = #{item.forecastTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.customerForecast != null">
                    customer_forecast = #{item.customerForecast,jdbcType=VARCHAR},
                </if>
                <if test="item.algorithmForecast != null">
                    algorithm_forecast = #{item.algorithmForecast,jdbcType=VARCHAR},
                </if>
                demand_forecast = #{item.demandForecast,jdbcType=VARCHAR},
                <if test="item.deliveryNum != null">
                    delivery_num = #{item.deliveryNum,jdbcType=VARCHAR},
                </if>
                <if test="item.customerPrecision != null">
                    customer_precision = #{item.customerPrecision,jdbcType=VARCHAR},
                </if>
                <if test="item.algorithmPrecision != null">
                    algorithm_precision = #{item.algorithmPrecision,jdbcType=VARCHAR},
                </if>
                <if test="item.forecastPrecision != null">
                    forecast_precision = #{item.forecastPrecision,jdbcType=VARCHAR},
                </if>
                <if test="item.changeValue != null">
                    change_value = #{item.changeValue,jdbcType=VARCHAR},
                </if>
                <if test="item.changeType != null and item.changeType != ''">
                    change_type = #{item.changeType,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
                <if test="item.dateType != null and item.dateType != ''">
                    date_type = #{item.dateType,jdbcType=VARCHAR},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER};
            update fdp_demand_forecast_establishment set
            version_value = version_value + 1
            where id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <update id="updateByVersionCondition" parameterType="com.yhl.scp.dfp.release.dto.ReleaseProductItemDTO">
        update fdp_demand_forecast_establishment
        set demand_forecast = #{item.quantity,jdbcType=DECIMAL},
            version_value   = version_value + 1
        where id = #{item.dataId,jdbcType=VARCHAR}
          and version_value = #{item.versionValue,jdbcType=INTEGER}
    </update>
    <update id="updateForecastValueById" parameterType="com.yhl.scp.dfp.demand.dto.DemandForecastModifyDetailDTO">
        update fdp_demand_forecast_establishment
        set demand_forecast = #{detailDTO.forecastValue, jdbcType=DECIMAL},
            modify_time     = #{detailDTO.modifyTime,jdbcType=TIMESTAMP}
        where id = #{detailDTO.id,jdbcType=VARCHAR}
          and version_value = #{detailDTO.versionValue,jdbcType=INTEGER}
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from fdp_demand_forecast_establishment
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from fdp_demand_forecast_establishment where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <!-- 批量id+版本删除 -->
    <delete id="deleteBatchVersion" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            delete from fdp_demand_forecast_establishment where
            id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </delete>

    <select id="selectAssIgnFieldByByParams" resultMap="VOResultMap">
        select
        ${assIgnFieldStr}
        from fdp_demand_forecast_establishment
        <include refid="Base_Where_Condition"/>
    </select>
    
    <select id="selectComparePage" resultType="com.yhl.scp.dfp.demand.vo.DeliveryForecastCompareVO">
        select
        demand_category demandCategory,
        oem_code oemCode,
        vehicle_model_code vehicleModelCode,
        product_code productCode,
        forecast_version_id forecastVersionId
        from v_fdp_demand_forecast_compare
        where 1=1
        <if test="params.vehicleModelCode != null and params.vehicleModelCode != ''">
            and vehicle_model_code = #{params.vehicleModelCode,jdbcType=VARCHAR}
        </if>
		 <if test="params.productCode != null and params.productCode != ''">
            and product_code = #{params.productCode,jdbcType=VARCHAR}
        </if>
		<if test="params.oemCode != null and params.oemCode != ''">
            and oem_code = #{params.oemCode,jdbcType=VARCHAR}
        </if>
		<if test="params.demandCategory != null and params.demandCategory != ''">
            and demand_category = #{params.demandCategory,jdbcType=VARCHAR}
        </if>
    </select>
    
    <select id="selectMonthDemandForecast" resultMap="VOResultMap">
        SELECT
			demand_category,
			oem_code,
			vehicle_model_code,
			product_code,
			DATE_FORMAT( forecast_time, '%Y-%m' ) plan_period,
			sum( demand_forecast ) demand_forecast 
		FROM
			fdp_demand_forecast_establishment 
		WHERE
			forecast_version_id = #{forecastVersionId,jdbcType=VARCHAR}
			<if test="startTime != null">
                and forecast_time <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                and forecast_time <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
            </if>
		GROUP BY
			demand_category,
			oem_code,
			vehicle_model_code,
			product_code,
			DATE_FORMAT( forecast_time, '%Y-%m' )
    </select>	
    
</mapper>
