package com.yhl.scp.dfp.car.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.car.dto.CarPriceListDTO;
import com.yhl.scp.dfp.car.service.CarPriceListService;
import com.yhl.scp.dfp.car.vo.CarPriceListVO;
import com.yhl.scp.dfp.oem.service.OemVehicleModelMapService;
import com.yhl.scp.dfp.oem.vo.OemVehicleModelMapVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.dfp.car.convertor.CarPriceListImportConvertor;
import com.yhl.scp.dfp.car.domain.entity.CarPriceListImportDO;
import com.yhl.scp.dfp.car.domain.service.CarPriceListImportDomainService;
import com.yhl.scp.dfp.car.dto.CarPriceListImportDTO;
import com.yhl.scp.dfp.car.infrastructure.dao.CarPriceListImportDao;
import com.yhl.scp.dfp.car.infrastructure.po.CarPriceListImportPO;
import com.yhl.scp.dfp.car.service.CarPriceListImportService;
import com.yhl.scp.dfp.car.vo.CarPriceListImportVO;
import lombok.extern.slf4j.Slf4j;
import com.yhl.scp.dfp.enums.ObjectTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>CarPriceListImportServiceImpl</code>
 * <p>
 * 汽车价格导入临时表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-10 16:04:32
 */
@Slf4j
@Service
public class CarPriceListImportServiceImpl extends AbstractService implements CarPriceListImportService {

    @Resource
    private CarPriceListImportDao carPriceListImportDao;

    @Resource
    private CarPriceListImportDomainService carPriceListImportDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private CarPriceListService carPriceListService;

    @Resource
    private OemVehicleModelMapService oemVehicleModelMapService;

    @Override
    public BaseResponse<Void> doCreate(CarPriceListImportDTO carPriceListImportDTO) {
        // 0.数据转换
        CarPriceListImportDO carPriceListImportDO = CarPriceListImportConvertor.INSTANCE.dto2Do(carPriceListImportDTO);
        CarPriceListImportPO carPriceListImportPO = CarPriceListImportConvertor.INSTANCE.dto2Po(carPriceListImportDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        carPriceListImportDomainService.validation(carPriceListImportDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(carPriceListImportPO);
        carPriceListImportDao.insert(carPriceListImportPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(CarPriceListImportDTO carPriceListImportDTO) {
        // 0.数据转换
        CarPriceListImportDO carPriceListImportDO = CarPriceListImportConvertor.INSTANCE.dto2Do(carPriceListImportDTO);
        CarPriceListImportPO carPriceListImportPO = CarPriceListImportConvertor.INSTANCE.dto2Po(carPriceListImportDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        carPriceListImportDomainService.validation(carPriceListImportDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(carPriceListImportPO);
        carPriceListImportDao.update(carPriceListImportPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<CarPriceListImportDTO> list) {
        List<CarPriceListImportPO> newList = CarPriceListImportConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        carPriceListImportDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<CarPriceListImportDTO> list) {
        List<CarPriceListImportPO> newList = CarPriceListImportConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        carPriceListImportDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return carPriceListImportDao.deleteBatch(idList);
        }
        return carPriceListImportDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public CarPriceListImportVO selectByPrimaryKey(String id) {
        CarPriceListImportPO po = carPriceListImportDao.selectByPrimaryKey(id);
        return CarPriceListImportConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "CAR_PRICE_LIST_IMPORT")
    public List<CarPriceListImportVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "CAR_PRICE_LIST_IMPORT")
    public List<CarPriceListImportVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<CarPriceListImportVO> dataList = carPriceListImportDao.selectByCondition(sortParam, queryCriteriaParam);
        CarPriceListImportServiceImpl target = springBeanUtils.getBean(CarPriceListImportServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<CarPriceListImportVO> selectByParams(Map<String, Object> params) {
        List<CarPriceListImportPO> list = carPriceListImportDao.selectByParams(params);
        return CarPriceListImportConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<CarPriceListImportVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public void deleteByYearMonth(String yearMonth) {
        carPriceListImportDao.deleteByYearMonth(yearMonth);
    }

    @Override
    public void updateCarData(CarPriceListImportDTO carPriceListImportDTO) {
        //获取导入的数据
        String statrYearMonth = carPriceListImportDTO.getStatrYearMonth();
        String endYearMonth = carPriceListImportDTO.getEndYearMonth();
        Date startTime = DateUtils.getMonthFirstDay(DateUtil.parse(statrYearMonth, "yyyyMM"));
        Date etartTime = DateUtils.getMonthLastDay(DateUtil.parse(endYearMonth, "yyyyMM"));
        List<CarPriceListImportVO> importList = this.selectByParams(ImmutableMap.of(
                "enabled", YesOrNoEnum.YES.getCode(),
                "startTime", startTime,
                "etartTime", etartTime));
        importList = importList.stream()
                .filter(vo -> vo.getPriceDate() != null &&
                        !vo.getPriceDate().before(startTime) &&
                        !vo.getPriceDate().after(etartTime))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(importList)) {
            throw new BusinessException("请先导入乘用车价格信息数据");
        }
        //获取存量数据
        List<CarPriceListVO> oldImportList = carPriceListService.selectByParams(ImmutableMap.of(
                "enabled", YesOrNoEnum.YES.getCode(),
                "startTime", startTime,
                "etartTime", etartTime));
        List<String> oldIds = oldImportList.stream()
        		.filter(e -> StringUtils.isNotEmpty(e.getVehicleModelCode())
                        && StringUtils.isNotEmpty(e.getVehicleModelBrand()))
        		.map(CarPriceListVO::getId).collect(Collectors.toList());
        Map<String, CarPriceListVO> oldCarPriceListMap = CollectionUtils.isEmpty(oldImportList)
                ? MapUtil.newHashMap() :
                oldImportList.stream()
                        .filter(e -> StringUtils.isNotEmpty(e.getVehicleModelCode())
                                && StringUtils.isNotEmpty(e.getVehicleModelBrand()))
                        .collect(Collectors.toMap(x -> StringUtils.join("_", x.getVehicleModelBrand(),
                                        DateUtils.dateToString(x.getPriceDate(), "yyyyMM"), x.getVehicleModelCode(), x.getVehicleModel()),
                                Function.identity(), (v1, v2) -> v1));
        Map<String, CarPriceListVO> oldEnptyVehicleModelCodeMap = CollectionUtils.isEmpty(oldImportList)
                ? MapUtil.newHashMap() :
                oldImportList.stream()
                        .filter(e -> StringUtils.isEmpty(e.getVehicleModelCode()))
                        .collect(Collectors.toMap(x -> StringUtils.join("_", x.getVehicleModelBrand(),
                                        DateUtils.dateToString(x.getPriceDate(), "yyyyMM"), x.getVehicleModel()),
                                Function.identity(), (v1, v2) -> v1));
        
        List<OemVehicleModelMapVO> oemVehicleModelMapVOS = oemVehicleModelMapService.selectAll();
        Map<String, List<OemVehicleModelMapVO>> vehicleMap = oemVehicleModelMapVOS.stream()
                .filter(x -> org.apache.commons.lang3.StringUtils.isNotBlank(x.getVehicleModelBrand())
                		&& org.apache.commons.lang3.StringUtils.isNotBlank(x.getVehicleModel())
                		&& YesOrNoEnum.YES.getCode().equals(x.getEnabled()))
                .collect(Collectors.groupingBy(x -> StringUtils.join("_", x.getVehicleModelBrand(), x.getVehicleModel())));
        Set<String> uniqueInsertKeys = new HashSet<>();
        Set<String> uniqueUpdateKeys = new HashSet<>();
        List<CarPriceListDTO> insertCarPriceList = Lists.newArrayList();
        List<CarPriceListDTO> updateCarPriceList = Lists.newArrayList();
        for (CarPriceListImportVO improtData : importList) {
            String vehicleModelBrand = improtData.getVehicleModelBrand();
            String vehicleModel = improtData.getVehicleModel();
            String key1 = StringUtils.join("_", vehicleModelBrand, improtData.getVehicleModel());
            // 获取当前车型品牌和型号对应的多个车型映射
            List<OemVehicleModelMapVO> oemVehicleModelMap = vehicleMap
                    .getOrDefault(key1, Collections.emptyList());
            for (OemVehicleModelMapVO oemVehicleModelMapVO : oemVehicleModelMap) {
                CarPriceListDTO carPriceListDTO = new CarPriceListDTO();
                carPriceListDTO.setMsrp(improtData.getMsrp());
                carPriceListDTO.setPriceDate(improtData.getPriceDate());
                carPriceListDTO.setVehicleModel(improtData.getVehicleModel());
                carPriceListDTO.setVehicleModelBrand(vehicleModelBrand);
                carPriceListDTO.setVehicleModelCode(oemVehicleModelMapVO.getVehicleModelCode());
                carPriceListDTO.setOemCode(oemVehicleModelMapVO.getOemCode());
                String yearMonth = DateUtils.dateToString(carPriceListDTO.getPriceDate(), "yyyyMM");
                String key = StringUtils.join("_", vehicleModelBrand, yearMonth, oemVehicleModelMapVO.getVehicleModelCode(), vehicleModel);
                CarPriceListVO oldCarPriceListVO = oldCarPriceListMap.get(key);
                if (Objects.nonNull(oldCarPriceListVO)) {
                    carPriceListDTO.setId(oldCarPriceListVO.getId());
                    if (uniqueUpdateKeys.add(key)) {
                        carPriceListDTO.setRemark(oldCarPriceListVO.getRemark());
                        carPriceListDTO.setEnabled(oldCarPriceListVO.getEnabled());
                        carPriceListDTO.setCreator(oldCarPriceListVO.getCreator());
                        carPriceListDTO.setCreateTime(oldCarPriceListVO.getCreateTime());
                        carPriceListDTO.setVersionValue(oldCarPriceListVO.getVersionValue());
                        updateCarPriceList.add(carPriceListDTO);
                    }
                } else {
                    if (uniqueInsertKeys.add(key)) {
                        insertCarPriceList.add(carPriceListDTO);
                    }
                }
            }
            
            if(CollectionUtils.isEmpty(oemVehicleModelMap)) {
            	//通过车型品牌，型号未匹配上主机厂车型映射关系的数据
            	CarPriceListDTO carPriceListDTO = new CarPriceListDTO();
                carPriceListDTO.setMsrp(improtData.getMsrp());
                carPriceListDTO.setPriceDate(improtData.getPriceDate());
                carPriceListDTO.setVehicleModel(improtData.getVehicleModel());
                carPriceListDTO.setVehicleModelBrand(vehicleModelBrand);
                String yearMonth = DateUtils.dateToString(carPriceListDTO.getPriceDate(), "yyyyMM");
                String key = StringUtils.join("_", vehicleModelBrand, yearMonth, vehicleModel);
                CarPriceListVO oldCarPriceListVO = oldEnptyVehicleModelCodeMap.get(key);
                if (Objects.nonNull(oldCarPriceListVO)) {
                    carPriceListDTO.setId(oldCarPriceListVO.getId());
                    if (uniqueUpdateKeys.add(key)) {
                        carPriceListDTO.setRemark(oldCarPriceListVO.getRemark());
                        carPriceListDTO.setEnabled(YesOrNoEnum.YES.getCode());
                        carPriceListDTO.setCreator(oldCarPriceListVO.getCreator());
                        carPriceListDTO.setCreateTime(oldCarPriceListVO.getCreateTime());
                        carPriceListDTO.setVersionValue(oldCarPriceListVO.getVersionValue());
                        updateCarPriceList.add(carPriceListDTO);
                    }
                } else {
                    if (uniqueInsertKeys.add(key)) {
                        insertCarPriceList.add(carPriceListDTO);
                    }
                }
            }
        }
        //数据入库
        if (CollectionUtils.isNotEmpty(insertCarPriceList)) {
            carPriceListService.doCreateBatch(insertCarPriceList);
        }
        if (CollectionUtils.isNotEmpty(updateCarPriceList)) {
            List<String> updateIds = updateCarPriceList.stream().map(CarPriceListDTO::getId)
                    .collect(Collectors.toList());
            oldIds.removeAll(updateIds);
            carPriceListService.doUpdateBatch(updateCarPriceList);
        }
        if (CollectionUtils.isNotEmpty(oldIds)) {
            //存量数据未跟新的进行删除处理
            carPriceListService.doUpdateEnableNo(oldIds);
        }
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.CAR_PRICE_LIST_IMPORT.getCode();
    }

    @Override
    public List<CarPriceListImportVO> invocation(List<CarPriceListImportVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
