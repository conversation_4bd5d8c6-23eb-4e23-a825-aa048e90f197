package com.yhl.scp.dfp.queue.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.queue.dto.QueuePlanDTO;
import com.yhl.scp.dfp.queue.service.QueuePlanService;
import com.yhl.scp.dfp.queue.vo.QueuePlanVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>QueuePlanController</code>
 * <p>
 * 排车计划控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:47:37
 */
@Slf4j
@Api(tags = "排车计划控制器")
@RestController
@RequestMapping("queuePlan")
public class QueuePlanController extends BaseController {

    @Resource
    private QueuePlanService queuePlanService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<QueuePlanVO>> page() {
        List<QueuePlanVO> queuePlanList = queuePlanService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<QueuePlanVO> pageInfo = new PageInfo<>(queuePlanList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody QueuePlanDTO queuePlanDTO) {
        return queuePlanService.doCreate(queuePlanDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody QueuePlanDTO queuePlanDTO) {
        return queuePlanService.doUpdate(queuePlanDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        queuePlanService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<QueuePlanVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, queuePlanService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "根据版本删除")
    @PostMapping(value = "deleteByVersion")
    public BaseResponse<Void> deleteByVersion(@RequestBody List<RemoveVersionDTO> versionDTOList) {
        queuePlanService.doDeleteByVersion(versionDTOList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

}
