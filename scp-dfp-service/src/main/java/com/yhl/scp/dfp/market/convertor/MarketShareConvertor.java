package com.yhl.scp.dfp.market.convertor;

import com.yhl.scp.dfp.market.domain.entity.MarketShareDO;
import com.yhl.scp.dfp.market.dto.MarketShareDTO;
import com.yhl.scp.dfp.market.infrastructure.po.MarketSharePO;
import com.yhl.scp.dfp.market.vo.MarketShareVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>MarketShareConvertor</code>
 * <p>
 * 市场占有率转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:47:11
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, builder = @org.mapstruct.Builder(disableBuilder = true))
public interface MarketShareConvertor {

    MarketShareConvertor INSTANCE = Mappers.getMapper(MarketShareConvertor.class);

    MarketShareDO dto2Do(MarketShareDTO obj);

    List<MarketShareDO> dto2Dos(List<MarketShareDTO> list);

    MarketShareDTO do2Dto(MarketShareDO obj);

    List<MarketShareDTO> do2Dtos(List<MarketShareDO> list);

    MarketShareDTO vo2Dto(MarketShareVO obj);

    List<MarketShareDTO> vo2Dtos(List<MarketShareVO> list);

    MarketShareVO po2Vo(MarketSharePO obj);

    List<MarketShareVO> po2Vos(List<MarketSharePO> list);

    MarketSharePO dto2Po(MarketShareDTO obj);

    List<MarketSharePO> dto2Pos(List<MarketShareDTO> obj);

    MarketShareVO do2Vo(MarketShareDO obj);

    MarketSharePO do2Po(MarketShareDO obj);

    MarketShareDO po2Do(MarketSharePO obj);

}
