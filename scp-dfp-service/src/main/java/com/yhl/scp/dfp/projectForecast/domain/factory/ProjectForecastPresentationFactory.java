package com.yhl.scp.dfp.projectForecast.domain.factory;

import com.yhl.scp.dfp.projectForecast.domain.entity.ProjectForecastPresentationDO;
import com.yhl.scp.dfp.projectForecast.dto.ProjectForecastPresentationDTO;
import com.yhl.scp.dfp.projectForecast.infrastructure.dao.ProjectForecastPresentationDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>ProjectForecastPresentationFactory</code>
 * <p>
 * 项目预测提报领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-03 16:11:28
 */
@Component
public class ProjectForecastPresentationFactory {

    @Resource
    private ProjectForecastPresentationDao projectForecastPresentationDao;

    ProjectForecastPresentationDO create(ProjectForecastPresentationDTO dto) {
        // TODO
        return null;
    }

}
