package com.yhl.scp.dfp.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanService;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * description: 未发布发货计划预警
 * author：李杰
 * email: <EMAIL>
 * date: 2025/4/28
 */
@Component
@Slf4j
public class UnpublishDeliveryPlanJob {
    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private DeliveryPlanService deliveryPlanService;

    @XxlJob("unpublishDeliveryPlanJob")
    public ReturnT<String> unpublishDeliveryPlanJob() {
        List<Scenario> scenarios = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.DFP.getCode()).getData();
        if (CollectionUtils.isEmpty(scenarios)) {
            XxlJobHelper.log("租户下不存在DFP模块信息");
            return ReturnT.SUCCESS;
        }
        for (Scenario scenario : scenarios) {
            XxlJobHelper.log("开始处理执行未发布发货计划预警的数据", scenario.getDataBaseName());
            DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());
            deliveryPlanService.executionSend();
            DynamicDataSourceContextHolder.clearDataSource();
            XxlJobHelper.log("结束处理执行未发布发货计划预警的数据", scenario);
        }
        return ReturnT.SUCCESS;
    }
}
