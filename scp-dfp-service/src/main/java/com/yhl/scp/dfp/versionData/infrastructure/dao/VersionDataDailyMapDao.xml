<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.versionData.infrastructure.dao.VersionDataDailyMapDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.versionData.infrastructure.po.VersionDataDailyMapPO">
        <!--@Table fdp_version_data_daily_map-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="version_id" jdbcType="VARCHAR" property="versionId"/>
        <result column="demand_id" jdbcType="VARCHAR" property="demandId"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.dfp.versionData.vo.VersionDataDailyMapVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id,version_id,demand_id,remark,creator,create_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.versionId != null and params.versionId != ''">
                and version_id = #{params.versionId,jdbcType=VARCHAR}
            </if>
            <if test="params.demandId != null and params.demandId != ''">
                and demand_id = #{params.demandId,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_version_data_daily_map
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_version_data_daily_map
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from fdp_version_data_daily_map
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_version_data_daily_map
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.versionData.infrastructure.po.VersionDataDailyMapPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into fdp_version_data_daily_map(
        id,
        version_id,
        demand_id,
        remark,
        creator,
        create_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{versionId,jdbcType=VARCHAR},
        #{demandId,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.dfp.versionData.infrastructure.po.VersionDataDailyMapPO">
        insert into fdp_version_data_daily_map(
        id,
        version_id,
        demand_id,
        remark,
        creator,
        create_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{versionId,jdbcType=VARCHAR},
        #{demandId,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into fdp_version_data_daily_map(
        id,
        version_id,
        demand_id,
        remark,
        creator,
        create_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.versionId,jdbcType=VARCHAR},
        #{entity.demandId,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into fdp_version_data_daily_map(
        id,
        version_id,
        demand_id,
        remark,
        creator,
        create_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.versionId,jdbcType=VARCHAR},
        #{entity.demandId,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.versionData.infrastructure.po.VersionDataDailyMapPO">
        update fdp_version_data_daily_map set
        version_id = #{versionId,jdbcType=VARCHAR},
        demand_id = #{demandId,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        version_value = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER};
        update fdp_version_data_daily_map set
        version_value = version_value + 1
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.dfp.versionData.infrastructure.po.VersionDataDailyMapPO">
        update fdp_version_data_daily_map
        <set>
            <if test="item.versionId != null and item.versionId != ''">
                version_id = #{item.versionId,jdbcType=VARCHAR},
            </if>
            <if test="item.demandId != null and item.demandId != ''">
                demand_id = #{item.demandId,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
          and version_value = #{item.versionValue,jdbcType=INTEGER};
        update fdp_version_data_daily_map set
        version_value = version_value + 1
        where id = #{item.id,jdbcType=VARCHAR}
          and version_value = #{item.versionValue,jdbcType=INTEGER}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update fdp_version_data_daily_map
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
          and version_value in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.versionValue,jdbcType=INTEGER}
        </foreach>;
        update fdp_version_data_daily_map set
        version_value = version_value + 1
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
          and version_value in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update fdp_version_data_daily_map 
        <set>
            <if test="item.versionId != null and item.versionId != ''">
                version_id = #{item.versionId,jdbcType=VARCHAR},
            </if>
            <if test="item.demandId != null and item.demandId != ''">
                demand_id = #{item.demandId,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}   
          and version_value = #{item.versionValue,jdbcType=INTEGER};
        update fdp_version_data_daily_map set
        version_value = version_value + 1
        where id = #{item.id,jdbcType=VARCHAR}
          and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from fdp_version_data_daily_map where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from fdp_version_data_daily_map where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
	
	<!-- 批量id+版本删除 -->
	<delete id="deleteBatchVersion" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            delete from fdp_version_data_daily_map where
            id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </delete>
</mapper>
