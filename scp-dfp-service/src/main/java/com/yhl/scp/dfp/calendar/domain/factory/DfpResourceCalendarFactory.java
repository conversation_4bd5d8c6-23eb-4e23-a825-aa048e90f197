package com.yhl.scp.dfp.calendar.domain.factory;

import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.dfp.calendar.domain.entity.CalendarRuleDO;
import com.yhl.scp.dfp.calendar.domain.entity.ResourceCalendarDO;
import com.yhl.scp.dfp.calendar.domain.entity.ShiftDO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <code>ResourceCalendarFactory</code>
 * <p>
 * 资源日历工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-10-30 15:43:18
 */
@Component
public class DfpResourceCalendarFactory {

    /**
     * 根据 日历规则 创建单天资源日历明细
     *
     * @param currentDayRules 当天匹配到日历规则列表
     * @return List<ResourceCalendarDO>
     */
    public List<ResourceCalendarDO> createByCalendarRule(
            Date planDate,
            String organizationId,
            String physicalResourceId,
            Map<String, String> physicalIdToStandardIdMap,
            List<CalendarRuleDO> currentDayRules) {

        List<ResourceCalendarDO> resourceCalendarList = new ArrayList<>();
        for (CalendarRuleDO currentDayRule : currentDayRules) {

            for (ShiftDO shiftDO : currentDayRule.getShiftDOS()) {
                if (shiftDO == null) {
                    throw new BusinessException("该日历规则{0}的班次不存在，请检查数据", currentDayRule.getRuleName());
                }
                // 正班工时
                BigDecimal mainHour = currentDayRule.getWorkHours() == null ?
                        BigDecimal.ZERO : currentDayRule.getWorkHours();
                // 加班工时
                BigDecimal overTimeHour = currentDayRule.getOvertimeHours() == null ?
                        BigDecimal.ZERO : currentDayRule.getOvertimeHours();
                ResourceCalendarDO resourceCalendarDO = ResourceCalendarDO.builder()
                        .organizationId(organizationId)
                        .standardResourceId(physicalIdToStandardIdMap.get(physicalResourceId))
                        .physicalResourceId(physicalResourceId)
                        .ruleId(currentDayRule.getId())
                        .shiftId(shiftDO.getId())
                        .shiftPattern(shiftDO.getShiftPattern())
                        .workHours(mainHour)
                        .overtimeHours(overTimeHour)
                        .workDay(DateUtils.truncateTimeOfDate(planDate))
                        .priority(currentDayRule.getPriority())
                        .calendarType(StringUtils.isEmpty(currentDayRule.getCalendarType()) ?
                                shiftDO.getShiftType() : currentDayRule.getCalendarType())
                        .efficiency(currentDayRule.getEfficiency())
                        .resourceQuantity(currentDayRule.getResourceQuantity())
                        .build();
                resourceCalendarList.add(resourceCalendarDO);

            }
        }
        return resourceCalendarList;
    }


}
