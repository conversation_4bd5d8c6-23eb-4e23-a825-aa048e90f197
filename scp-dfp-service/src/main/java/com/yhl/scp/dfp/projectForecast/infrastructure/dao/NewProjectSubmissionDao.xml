<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.projectForecast.infrastructure.dao.NewProjectSubmissionDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.projectForecast.infrastructure.po.NewProjectSubmissionPO">
        <!--@Table fdp_new_project_submission-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="sale_type" jdbcType="VARCHAR" property="saleType"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
        <result column="part_number" jdbcType="VARCHAR" property="partNumber"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="coating_flag" jdbcType="VARCHAR" property="coatingFlag"/>
        <result column="crimp_flag" jdbcType="VARCHAR" property="crimpFlag"/>
        <result column="dimming_flag" jdbcType="VARCHAR" property="dimmingFlag"/>
        <result column="new_product_type" jdbcType="VARCHAR" property="newProductType"/>
        <result column="material_procurement_coefficient" jdbcType="VARCHAR" property="materialProcurementCoefficient"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="organization" jdbcType="VARCHAR" property="organization"/>
        <result column="part_name" jdbcType="VARCHAR" property="partName"/>
        <result column="sop" jdbcType="VARCHAR" property="sop"/>
        <result column="equipment_category" jdbcType="VARCHAR" property="equipmentCategory"/>
        <result column="sales_man" jdbcType="VARCHAR" property="salesMan"/>
        <result column="demand_type" jdbcType="VARCHAR" property="demandType"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.dfp.projectForecast.vo.NewProjectSubmissionVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id,oem_code,sale_type,vehicle_model_code,part_number,product_code,coating_flag,crimp_flag,dimming_flag,new_product_type,material_procurement_coefficient,remark,enabled,creator,create_time,modifier,modify_time,organization,part_name,sop,equipment_category,sales_man,demand_type,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.oemCode != null and params.oemCode != ''">
                and oem_code = #{params.oemCode,jdbcType=VARCHAR}
            </if>
            <if test="params.saleType != null and params.saleType != ''">
                and sale_type = #{params.saleType,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleModelCode != null and params.vehicleModelCode != ''">
                and vehicle_model_code = #{params.vehicleModelCode,jdbcType=VARCHAR}
            </if>
            <if test="params.partNumber != null and params.partNumber != ''">
                and part_number = #{params.partNumber,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.coatingFlag != null and params.coatingFlag != ''">
                and coating_flag = #{params.coatingFlag,jdbcType=VARCHAR}
            </if>
            <if test="params.crimpFlag != null and params.crimpFlag != ''">
                and crimp_flag = #{params.crimpFlag,jdbcType=VARCHAR}
            </if>
            <if test="params.dimmingFlag != null and params.dimmingFlag != ''">
                and dimming_flag = #{params.dimmingFlag,jdbcType=VARCHAR}
            </if>
            <if test="params.newProductType != null and params.newProductType != ''">
                and new_product_type = #{params.newProductType,jdbcType=VARCHAR}
            </if>
            <if test="params.materialProcurementCoefficient != null and params.materialProcurementCoefficient != ''">
                and material_procurement_coefficient = #{params.materialProcurementCoefficient,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.organization != null and params.organization != ''">
                and organization = #{params.organization,jdbcType=VARCHAR}
            </if>
            <if test="params.partName != null and params.partName != ''">
                and part_name = #{params.partName,jdbcType=VARCHAR}
            </if>
            <if test="params.sop != null and params.sop != ''">
                and sop = #{params.sop,jdbcType=VARCHAR}
            </if>
            <if test="params.equipmentCategory != null and params.equipmentCategory != ''">
                and equipment_category = #{params.equipmentCategory,jdbcType=VARCHAR}
            </if>
            <if test="params.salesMan != null and params.salesMan != ''">
                and sales_man = #{params.salesMan,jdbcType=VARCHAR}
            </if>
            <if test="params.demandType != null and params.demandType != ''">
                and demand_type = #{params.demandType,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_new_project_submission
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_new_project_submission
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_fdp_new_project_submission
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_new_project_submission
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_fdp_new_project_submission
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.projectForecast.infrastructure.po.NewProjectSubmissionPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into fdp_new_project_submission(
        id,
        oem_code,
        sale_type,
        vehicle_model_code,
        part_number,
        product_code,
        coating_flag,
        crimp_flag,
        dimming_flag,
        new_product_type,
        material_procurement_coefficient,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        organization,
        part_name,
        sop,
        equipment_category,
        sales_man,
        demand_type,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{oemCode,jdbcType=VARCHAR},
        #{saleType,jdbcType=VARCHAR},
        #{vehicleModelCode,jdbcType=VARCHAR},
        #{partNumber,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{coatingFlag,jdbcType=VARCHAR},
        #{crimpFlag,jdbcType=VARCHAR},
        #{dimmingFlag,jdbcType=VARCHAR},
        #{newProductType,jdbcType=VARCHAR},
        #{materialProcurementCoefficient,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{organization,jdbcType=VARCHAR},
        #{partName,jdbcType=VARCHAR},
        #{sop,jdbcType=VARCHAR},
        #{equipmentCategory,jdbcType=VARCHAR},
        #{salesMan,jdbcType=VARCHAR},
        #{demandType,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.dfp.projectForecast.infrastructure.po.NewProjectSubmissionPO">
        insert into fdp_new_project_submission(
        id,
        oem_code,
        sale_type,
        vehicle_model_code,
        part_number,
        product_code,
        coating_flag,
        crimp_flag,
        dimming_flag,
        new_product_type,
        material_procurement_coefficient,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        organization,
        part_name,
        sop,
        equipment_category,
        sales_man,
        demand_type,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{oemCode,jdbcType=VARCHAR},
        #{saleType,jdbcType=VARCHAR},
        #{vehicleModelCode,jdbcType=VARCHAR},
        #{partNumber,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{coatingFlag,jdbcType=VARCHAR},
        #{crimpFlag,jdbcType=VARCHAR},
        #{dimmingFlag,jdbcType=VARCHAR},
        #{newProductType,jdbcType=VARCHAR},
        #{materialProcurementCoefficient,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{organization,jdbcType=VARCHAR},
        #{partName,jdbcType=VARCHAR},
        #{sop,jdbcType=VARCHAR},
        #{equipmentCategory,jdbcType=VARCHAR},
        #{salesMan,jdbcType=VARCHAR},
        #{demandType,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into fdp_new_project_submission(
        id,
        oem_code,
        sale_type,
        vehicle_model_code,
        part_number,
        product_code,
        coating_flag,
        crimp_flag,
        dimming_flag,
        new_product_type,
        material_procurement_coefficient,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        organization,
        part_name,
        sop,
        equipment_category,
        sales_man,
        demand_type,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.oemCode,jdbcType=VARCHAR},
        #{entity.saleType,jdbcType=VARCHAR},
        #{entity.vehicleModelCode,jdbcType=VARCHAR},
        #{entity.partNumber,jdbcType=VARCHAR},
        #{entity.productCode,jdbcType=VARCHAR},
        #{entity.coatingFlag,jdbcType=VARCHAR},
        #{entity.crimpFlag,jdbcType=VARCHAR},
        #{entity.dimmingFlag,jdbcType=VARCHAR},
        #{entity.newProductType,jdbcType=VARCHAR},
        #{entity.materialProcurementCoefficient,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.organization,jdbcType=VARCHAR},
        #{entity.partName,jdbcType=VARCHAR},
        #{entity.sop,jdbcType=VARCHAR},
        #{entity.equipmentCategory,jdbcType=VARCHAR},
        #{entity.salesMan,jdbcType=VARCHAR},
        #{entity.demandType,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into fdp_new_project_submission(
        id,
        oem_code,
        sale_type,
        vehicle_model_code,
        part_number,
        product_code,
        coating_flag,
        crimp_flag,
        dimming_flag,
        new_product_type,
        material_procurement_coefficient,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        organization,
        part_name,
        sop,
        equipment_category,
        sales_man,
        demand_type,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.oemCode,jdbcType=VARCHAR},
        #{entity.saleType,jdbcType=VARCHAR},
        #{entity.vehicleModelCode,jdbcType=VARCHAR},
        #{entity.partNumber,jdbcType=VARCHAR},
        #{entity.productCode,jdbcType=VARCHAR},
        #{entity.coatingFlag,jdbcType=VARCHAR},
        #{entity.crimpFlag,jdbcType=VARCHAR},
        #{entity.dimmingFlag,jdbcType=VARCHAR},
        #{entity.newProductType,jdbcType=VARCHAR},
        #{entity.materialProcurementCoefficient,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.organization,jdbcType=VARCHAR},
        #{entity.partName,jdbcType=VARCHAR},
        #{entity.sop,jdbcType=VARCHAR},
        #{entity.equipmentCategory,jdbcType=VARCHAR},
        #{entity.salesMan,jdbcType=VARCHAR},
        #{entity.demandType,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.projectForecast.infrastructure.po.NewProjectSubmissionPO">
        update fdp_new_project_submission set
        oem_code = #{oemCode,jdbcType=VARCHAR},
        sale_type = #{saleType,jdbcType=VARCHAR},
        vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
        part_number = #{partNumber,jdbcType=VARCHAR},
        product_code = #{productCode,jdbcType=VARCHAR},
        coating_flag = #{coatingFlag,jdbcType=VARCHAR},
        crimp_flag = #{crimpFlag,jdbcType=VARCHAR},
        dimming_flag = #{dimmingFlag,jdbcType=VARCHAR},
        new_product_type = #{newProductType,jdbcType=VARCHAR},
        material_procurement_coefficient = #{materialProcurementCoefficient,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        organization = #{organization,jdbcType=VARCHAR},
        part_name = #{partName,jdbcType=VARCHAR},
        sop = #{sop,jdbcType=VARCHAR},
        equipment_category = #{equipmentCategory,jdbcType=VARCHAR},
        sales_man = #{salesMan,jdbcType=VARCHAR},
        demand_type = #{demandType,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER};
        update fdp_new_project_submission set
        version_value = version_value + 1
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.dfp.projectForecast.infrastructure.po.NewProjectSubmissionPO">
        update fdp_new_project_submission
        <set>
            <if test="item.oemCode != null and item.oemCode != ''">
                oem_code = #{item.oemCode,jdbcType=VARCHAR},
            </if>
            <if test="item.saleType != null and item.saleType != ''">
                sale_type = #{item.saleType,jdbcType=VARCHAR},
            </if>
            <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
            </if>
            <if test="item.partNumber != null and item.partNumber != ''">
                part_number = #{item.partNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.coatingFlag != null and item.coatingFlag != ''">
                coating_flag = #{item.coatingFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.crimpFlag != null and item.crimpFlag != ''">
                crimp_flag = #{item.crimpFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.dimmingFlag != null and item.dimmingFlag != ''">
                dimming_flag = #{item.dimmingFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.newProductType != null and item.newProductType != ''">
                new_product_type = #{item.newProductType,jdbcType=VARCHAR},
            </if>
            <if test="item.materialProcurementCoefficient != null and item.materialProcurementCoefficient != ''">
                material_procurement_coefficient = #{item.materialProcurementCoefficient,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.organization != null and item.organization != ''">
                organization = #{item.organization,jdbcType=VARCHAR},
            </if>
            <if test="item.partName != null and item.partName != ''">
                part_name = #{item.partName,jdbcType=VARCHAR},
            </if>
            <if test="item.sop != null and item.sop != ''">
                sop = #{item.sop,jdbcType=VARCHAR},
            </if>
            <if test="item.equipmentCategory != null and item.equipmentCategory != ''">
                equipment_category = #{item.equipmentCategory,jdbcType=VARCHAR},
            </if>
            <if test="item.salesMan != null and item.salesMan != ''">
                sales_man = #{item.salesMan,jdbcType=VARCHAR},
            </if>
            <if test="item.demandType != null and item.demandType != ''">
                demand_type = #{item.demandType,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
          and version_value = #{item.versionValue,jdbcType=INTEGER};
        update fdp_new_project_submission set
        version_value = version_value + 1
        where id = #{item.id,jdbcType=VARCHAR}
          and version_value = #{item.versionValue,jdbcType=INTEGER}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update fdp_new_project_submission
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="oem_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oemCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sale_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.saleType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="vehicle_model_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.vehicleModelCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="part_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.partNumber,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="coating_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.coatingFlag,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="crimp_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.crimpFlag,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="dimming_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.dimmingFlag,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="new_product_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.newProductType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_procurement_coefficient = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialProcurementCoefficient,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="organization = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.organization,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="part_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.partName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sop = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="equipment_category = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.equipmentCategory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sales_man = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.salesMan,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandType,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
        and version_value in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.versionValue,jdbcType=INTEGER}
        </foreach>;
        update fdp_new_project_submission set
        version_value = version_value + 1
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
        and version_value in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.versionValue,jdbcType=INTEGER}
        </foreach>

    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update fdp_new_project_submission 
        <set>
            <if test="item.oemCode != null and item.oemCode != ''">
                oem_code = #{item.oemCode,jdbcType=VARCHAR},
            </if>
            <if test="item.saleType != null and item.saleType != ''">
                sale_type = #{item.saleType,jdbcType=VARCHAR},
            </if>
            <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
            </if>
            <if test="item.partNumber != null and item.partNumber != ''">
                part_number = #{item.partNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.coatingFlag != null and item.coatingFlag != ''">
                coating_flag = #{item.coatingFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.crimpFlag != null and item.crimpFlag != ''">
                crimp_flag = #{item.crimpFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.dimmingFlag != null and item.dimmingFlag != ''">
                dimming_flag = #{item.dimmingFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.newProductType != null and item.newProductType != ''">
                new_product_type = #{item.newProductType,jdbcType=VARCHAR},
            </if>
            <if test="item.materialProcurementCoefficient != null and item.materialProcurementCoefficient != ''">
                material_procurement_coefficient = #{item.materialProcurementCoefficient,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.organization != null and item.organization != ''">
                organization = #{item.organization,jdbcType=VARCHAR},
            </if>
            <if test="item.partName != null and item.partName != ''">
                part_name = #{item.partName,jdbcType=VARCHAR},
            </if>
            <if test="item.sop != null and item.sop != ''">
                sop = #{item.sop,jdbcType=VARCHAR},
            </if>
            <if test="item.equipmentCategory != null and item.equipmentCategory != ''">
                equipment_category = #{item.equipmentCategory,jdbcType=VARCHAR},
            </if>
            <if test="item.salesMan != null and item.salesMan != ''">
                sales_man = #{item.salesMan,jdbcType=VARCHAR},
            </if>
            <if test="item.demandType != null and item.demandType != ''">
                demand_type = #{item.demandType,jdbcType=VARCHAR},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}   
          and version_value = #{item.versionValue,jdbcType=INTEGER};
        update fdp_new_project_submission set
        version_value = version_value + 1
        where id = #{item.id,jdbcType=VARCHAR}
          and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from fdp_new_project_submission where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from fdp_new_project_submission where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
	
	<!-- 批量id+版本删除 -->
	<delete id="deleteBatchVersion" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            delete from fdp_new_project_submission where
            id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </delete>
</mapper>
