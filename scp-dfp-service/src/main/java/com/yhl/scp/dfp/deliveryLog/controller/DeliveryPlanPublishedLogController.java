package com.yhl.scp.dfp.deliveryLog.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.deliveryLog.dto.DeliveryPlanPublishedLogDTO;
import com.yhl.scp.dfp.deliveryLog.service.DeliveryPlanPublishedLogService;
import com.yhl.scp.dfp.deliveryLog.vo.DeliveryPlanPublishedLogVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>DeliveryPlanPublishedLogController</code>
 * <p>
 * 发货计划发布追踪表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-20 17:21:48
 */
@Slf4j
@Api(tags = "发货计划发布追踪表控制器")
@RestController
@RequestMapping("deliveryPlanPublishedLog")
public class DeliveryPlanPublishedLogController extends BaseController {

    @Resource
    private DeliveryPlanPublishedLogService deliveryPlanPublishedLogService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<DeliveryPlanPublishedLogVO>> page() {
        List<DeliveryPlanPublishedLogVO> deliveryPlanPublishedLogList = deliveryPlanPublishedLogService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<DeliveryPlanPublishedLogVO> pageInfo = new PageInfo<>(deliveryPlanPublishedLogList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody DeliveryPlanPublishedLogDTO deliveryPlanPublishedLogDTO) {
        return deliveryPlanPublishedLogService.doCreate(deliveryPlanPublishedLogDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody DeliveryPlanPublishedLogDTO deliveryPlanPublishedLogDTO) {
        return deliveryPlanPublishedLogService.doUpdate(deliveryPlanPublishedLogDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        deliveryPlanPublishedLogService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<DeliveryPlanPublishedLogVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, deliveryPlanPublishedLogService.selectByPrimaryKey(id));
    }

}
