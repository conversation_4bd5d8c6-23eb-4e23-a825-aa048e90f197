<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.annual.infrastructure.dao.AnnualForecastSubmissionDetailDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.annual.infrastructure.po.AnnualForecastSubmissionDetailPO">
        <!--@Table fdp_annual_forecast_submission_detail-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="submission_id" jdbcType="VARCHAR" property="submissionId"/>
        <result column="submission_year" jdbcType="INTEGER" property="submissionYear"/>
        <result column="month1_quantity" jdbcType="DECIMAL" property="month1Quantity"/>
        <result column="month2_quantity" jdbcType="DECIMAL" property="month2Quantity"/>
        <result column="month3_quantity" jdbcType="DECIMAL" property="month3Quantity"/>
        <result column="month4_quantity" jdbcType="DECIMAL" property="month4Quantity"/>
        <result column="month5_quantity" jdbcType="DECIMAL" property="month5Quantity"/>
        <result column="month6_quantity" jdbcType="DECIMAL" property="month6Quantity"/>
        <result column="month7_quantity" jdbcType="DECIMAL" property="month7Quantity"/>
        <result column="month8_quantity" jdbcType="DECIMAL" property="month8Quantity"/>
        <result column="month9_quantity" jdbcType="DECIMAL" property="month9Quantity"/>
        <result column="month10_quantity" jdbcType="DECIMAL" property="month10Quantity"/>
        <result column="month11_quantity" jdbcType="DECIMAL" property="month11Quantity"/>
        <result column="month12_quantity" jdbcType="DECIMAL" property="month12Quantity"/>
        <result column="year2_quantity" jdbcType="DECIMAL" property="year2Quantity"/>
        <result column="year3_quantity" jdbcType="DECIMAL" property="year3Quantity"/>
        <result column="year4_quantity" jdbcType="DECIMAL" property="year4Quantity"/>
        <result column="year5_quantity" jdbcType="DECIMAL" property="year5Quantity"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.dfp.annual.vo.AnnualForecastSubmissionDetailVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,submission_id,submission_year,month1_quantity,month2_quantity,month3_quantity,month4_quantity,month5_quantity,month6_quantity,month7_quantity,month8_quantity,month9_quantity,month10_quantity,month11_quantity,month12_quantity,year2_quantity,year3_quantity,year4_quantity,year5_quantity,remark,enabled,creator,create_time,modifier,modify_time,version_value

    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.submissionId != null and params.submissionId != ''">
                and submission_id = #{params.submissionId,jdbcType=VARCHAR}
            </if>
            <if test="params.submissionYear != null">
                and submission_year = #{params.submissionYear,jdbcType=INTEGER}
            </if>
            <if test="params.month1Quantity != null">
                and month1_quantity = #{params.month1Quantity,jdbcType=VARCHAR}
            </if>
            <if test="params.month2Quantity != null">
                and month2_quantity = #{params.month2Quantity,jdbcType=VARCHAR}
            </if>
            <if test="params.month3Quantity != null">
                and month3_quantity = #{params.month3Quantity,jdbcType=VARCHAR}
            </if>
            <if test="params.month4Quantity != null">
                and month4_quantity = #{params.month4Quantity,jdbcType=VARCHAR}
            </if>
            <if test="params.month5Quantity != null">
                and month5_quantity = #{params.month5Quantity,jdbcType=VARCHAR}
            </if>
            <if test="params.month6Quantity != null">
                and month6_quantity = #{params.month6Quantity,jdbcType=VARCHAR}
            </if>
            <if test="params.month7Quantity != null">
                and month7_quantity = #{params.month7Quantity,jdbcType=VARCHAR}
            </if>
            <if test="params.month8Quantity != null">
                and month8_quantity = #{params.month8Quantity,jdbcType=VARCHAR}
            </if>
            <if test="params.month9Quantity != null">
                and month9_quantity = #{params.month9Quantity,jdbcType=VARCHAR}
            </if>
            <if test="params.month10Quantity != null">
                and month10_quantity = #{params.month10Quantity,jdbcType=VARCHAR}
            </if>
            <if test="params.month11Quantity != null">
                and month11_quantity = #{params.month11Quantity,jdbcType=VARCHAR}
            </if>
            <if test="params.month12Quantity != null">
                and month12_quantity = #{params.month12Quantity,jdbcType=VARCHAR}
            </if>
            <if test="params.year2Quantity != null">
                and year2_quantity = #{params.year2Quantity,jdbcType=VARCHAR}
            </if>
            <if test="params.year3Quantity != null">
                and year3_quantity = #{params.year3Quantity,jdbcType=VARCHAR}
            </if>
            <if test="params.year4Quantity != null">
                and year4_quantity = #{params.year4Quantity,jdbcType=VARCHAR}
            </if>
            <if test="params.year5Quantity != null">
                and year5_quantity = #{params.year5Quantity,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_annual_forecast_submission_detail
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_annual_forecast_submission_detail
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from fdp_annual_forecast_submission_detail
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_annual_forecast_submission_detail
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.annual.infrastructure.po.AnnualForecastSubmissionDetailPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into fdp_annual_forecast_submission_detail(
        id,
        submission_id,
        submission_year,
        month1_quantity,
        month2_quantity,
        month3_quantity,
        month4_quantity,
        month5_quantity,
        month6_quantity,
        month7_quantity,
        month8_quantity,
        month9_quantity,
        month10_quantity,
        month11_quantity,
        month12_quantity,
        year2_quantity,
        year3_quantity,
        year4_quantity,
        year5_quantity,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{submissionId,jdbcType=VARCHAR},
        #{submissionYear,jdbcType=INTEGER},
        #{month1Quantity,jdbcType=VARCHAR},
        #{month2Quantity,jdbcType=VARCHAR},
        #{month3Quantity,jdbcType=VARCHAR},
        #{month4Quantity,jdbcType=VARCHAR},
        #{month5Quantity,jdbcType=VARCHAR},
        #{month6Quantity,jdbcType=VARCHAR},
        #{month7Quantity,jdbcType=VARCHAR},
        #{month8Quantity,jdbcType=VARCHAR},
        #{month9Quantity,jdbcType=VARCHAR},
        #{month10Quantity,jdbcType=VARCHAR},
        #{month11Quantity,jdbcType=VARCHAR},
        #{month12Quantity,jdbcType=VARCHAR},
        #{year2Quantity,jdbcType=VARCHAR},
        #{year3Quantity,jdbcType=VARCHAR},
        #{year4Quantity,jdbcType=VARCHAR},
        #{year5Quantity,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.dfp.annual.infrastructure.po.AnnualForecastSubmissionDetailPO">
        insert into fdp_annual_forecast_submission_detail(id,
                                                          submission_id,
                                                          submission_year,
                                                          month1_quantity,
                                                          month2_quantity,
                                                          month3_quantity,
                                                          month4_quantity,
                                                          month5_quantity,
                                                          month6_quantity,
                                                          month7_quantity,
                                                          month8_quantity,
                                                          month9_quantity,
                                                          month10_quantity,
                                                          month11_quantity,
                                                          month12_quantity,
                                                          year2_quantity,
                                                          year3_quantity,
                                                          year4_quantity,
                                                          year5_quantity,
                                                          remark,
                                                          enabled,
                                                          creator,
                                                          create_time,
                                                          modifier,
                                                          modify_time,
                                                          version_value)
        values (#{id,jdbcType=VARCHAR},
                #{submissionId,jdbcType=VARCHAR},
                #{submissionYear,jdbcType=INTEGER},
                #{month1Quantity,jdbcType=VARCHAR},
                #{month2Quantity,jdbcType=VARCHAR},
                #{month3Quantity,jdbcType=VARCHAR},
                #{month4Quantity,jdbcType=VARCHAR},
                #{month5Quantity,jdbcType=VARCHAR},
                #{month6Quantity,jdbcType=VARCHAR},
                #{month7Quantity,jdbcType=VARCHAR},
                #{month8Quantity,jdbcType=VARCHAR},
                #{month9Quantity,jdbcType=VARCHAR},
                #{month10Quantity,jdbcType=VARCHAR},
                #{month11Quantity,jdbcType=VARCHAR},
                #{month12Quantity,jdbcType=VARCHAR},
                #{year2Quantity,jdbcType=VARCHAR},
                #{year3Quantity,jdbcType=VARCHAR},
                #{year4Quantity,jdbcType=VARCHAR},
                #{year5Quantity,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into fdp_annual_forecast_submission_detail(
        id,
        submission_id,
        submission_year,
        month1_quantity,
        month2_quantity,
        month3_quantity,
        month4_quantity,
        month5_quantity,
        month6_quantity,
        month7_quantity,
        month8_quantity,
        month9_quantity,
        month10_quantity,
        month11_quantity,
        month12_quantity,
        year2_quantity,
        year3_quantity,
        year4_quantity,
        year5_quantity,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.submissionId,jdbcType=VARCHAR},
            #{entity.submissionYear,jdbcType=INTEGER},
            #{entity.month1Quantity,jdbcType=DECIMAL},
            #{entity.month2Quantity,jdbcType=DECIMAL},
            #{entity.month3Quantity,jdbcType=DECIMAL},
            #{entity.month4Quantity,jdbcType=DECIMAL},
            #{entity.month5Quantity,jdbcType=DECIMAL},
            #{entity.month6Quantity,jdbcType=DECIMAL},
            #{entity.month7Quantity,jdbcType=DECIMAL},
            #{entity.month8Quantity,jdbcType=DECIMAL},
            #{entity.month9Quantity,jdbcType=DECIMAL},
            #{entity.month10Quantity,jdbcType=DECIMAL},
            #{entity.month11Quantity,jdbcType=DECIMAL},
            #{entity.month12Quantity,jdbcType=DECIMAL},
            #{entity.year2Quantity,jdbcType=DECIMAL},
            #{entity.year3Quantity,jdbcType=DECIMAL},
            #{entity.year4Quantity,jdbcType=DECIMAL},
            #{entity.year5Quantity,jdbcType=DECIMAL},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into fdp_annual_forecast_submission_detail(
        id,
        submission_id,
        submission_year,
        month1_quantity,
        month2_quantity,
        month3_quantity,
        month4_quantity,
        month5_quantity,
        month6_quantity,
        month7_quantity,
        month8_quantity,
        month9_quantity,
        month10_quantity,
        month11_quantity,
        month12_quantity,
        year2_quantity,
        year3_quantity,
        year4_quantity,
        year5_quantity,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.submissionId,jdbcType=VARCHAR},
            #{entity.submissionYear,jdbcType=INTEGER},
            #{entity.month1Quantity,jdbcType=VARCHAR},
            #{entity.month2Quantity,jdbcType=VARCHAR},
            #{entity.month3Quantity,jdbcType=VARCHAR},
            #{entity.month4Quantity,jdbcType=VARCHAR},
            #{entity.month5Quantity,jdbcType=VARCHAR},
            #{entity.month6Quantity,jdbcType=VARCHAR},
            #{entity.month7Quantity,jdbcType=VARCHAR},
            #{entity.month8Quantity,jdbcType=VARCHAR},
            #{entity.month9Quantity,jdbcType=VARCHAR},
            #{entity.month10Quantity,jdbcType=VARCHAR},
            #{entity.month11Quantity,jdbcType=VARCHAR},
            #{entity.month12Quantity,jdbcType=VARCHAR},
            #{entity.year2Quantity,jdbcType=VARCHAR},
            #{entity.year3Quantity,jdbcType=VARCHAR},
            #{entity.year4Quantity,jdbcType=VARCHAR},
            #{entity.year5Quantity,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.annual.infrastructure.po.AnnualForecastSubmissionDetailPO">
        update fdp_annual_forecast_submission_detail
        set submission_id    = #{submissionId,jdbcType=VARCHAR},
            submission_year  = #{submissionYear,jdbcType=INTEGER},
            month1_quantity  = #{month1Quantity,jdbcType=VARCHAR},
            month2_quantity  = #{month2Quantity,jdbcType=VARCHAR},
            month3_quantity  = #{month3Quantity,jdbcType=VARCHAR},
            month4_quantity  = #{month4Quantity,jdbcType=VARCHAR},
            month5_quantity  = #{month5Quantity,jdbcType=VARCHAR},
            month6_quantity  = #{month6Quantity,jdbcType=VARCHAR},
            month7_quantity  = #{month7Quantity,jdbcType=VARCHAR},
            month8_quantity  = #{month8Quantity,jdbcType=VARCHAR},
            month9_quantity  = #{month9Quantity,jdbcType=VARCHAR},
            month10_quantity = #{month10Quantity,jdbcType=VARCHAR},
            month11_quantity = #{month11Quantity,jdbcType=VARCHAR},
            month12_quantity = #{month12Quantity,jdbcType=VARCHAR},
            year2_quantity   = #{year2Quantity,jdbcType=VARCHAR},
            year3_quantity   = #{year3Quantity,jdbcType=VARCHAR},
            year4_quantity   = #{year4Quantity,jdbcType=VARCHAR},
            year5_quantity   = #{year5Quantity,jdbcType=VARCHAR},
            remark           = #{remark,jdbcType=VARCHAR},
            enabled          = #{enabled,jdbcType=VARCHAR},
            modifier         = #{modifier,jdbcType=VARCHAR},
            modify_time      = #{modifyTime,jdbcType=TIMESTAMP},
            version_value    = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.dfp.annual.infrastructure.po.AnnualForecastSubmissionDetailPO">
        update fdp_annual_forecast_submission_detail
        <set>
            <if test="item.submissionId != null and item.submissionId != ''">
                submission_id = #{item.submissionId,jdbcType=VARCHAR},
            </if>
            <if test="item.submissionYear != null">
                submission_year = #{item.submissionYear,jdbcType=INTEGER},
            </if>
            <if test="item.month1Quantity != null">
                month1_quantity = #{item.month1Quantity,jdbcType=VARCHAR},
            </if>
            <if test="item.month2Quantity != null">
                month2_quantity = #{item.month2Quantity,jdbcType=VARCHAR},
            </if>
            <if test="item.month3Quantity != null">
                month3_quantity = #{item.month3Quantity,jdbcType=VARCHAR},
            </if>
            <if test="item.month4Quantity != null">
                month4_quantity = #{item.month4Quantity,jdbcType=VARCHAR},
            </if>
            <if test="item.month5Quantity != null">
                month5_quantity = #{item.month5Quantity,jdbcType=VARCHAR},
            </if>
            <if test="item.month6Quantity != null">
                month6_quantity = #{item.month6Quantity,jdbcType=VARCHAR},
            </if>
            <if test="item.month7Quantity != null">
                month7_quantity = #{item.month7Quantity,jdbcType=VARCHAR},
            </if>
            <if test="item.month8Quantity != null">
                month8_quantity = #{item.month8Quantity,jdbcType=VARCHAR},
            </if>
            <if test="item.month9Quantity != null">
                month9_quantity = #{item.month9Quantity,jdbcType=VARCHAR},
            </if>
            <if test="item.month10Quantity != null">
                month10_quantity = #{item.month10Quantity,jdbcType=VARCHAR},
            </if>
            <if test="item.month11Quantity != null">
                month11_quantity = #{item.month11Quantity,jdbcType=VARCHAR},
            </if>
            <if test="item.month12Quantity != null">
                month12_quantity = #{item.month12Quantity,jdbcType=VARCHAR},
            </if>
            <if test="item.year2Quantity != null">
                year2_quantity = #{item.year2Quantity,jdbcType=VARCHAR},
            </if>
            <if test="item.year3Quantity != null">
                year3_quantity = #{item.year3Quantity,jdbcType=VARCHAR},
            </if>
            <if test="item.year4Quantity != null">
                year4_quantity = #{item.year4Quantity,jdbcType=VARCHAR},
            </if>
            <if test="item.year5Quantity != null">
                year5_quantity = #{item.year5Quantity,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update fdp_annual_forecast_submission_detail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="submission_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.submissionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="submission_year = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.submissionYear,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="month1_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.month1Quantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="month2_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.month2Quantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="month3_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.month3Quantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="month4_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.month4Quantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="month5_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.month5Quantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="month6_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.month6Quantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="month7_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.month7Quantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="month8_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.month8Quantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="month9_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.month9Quantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="month10_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.month10Quantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="month11_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.month11Quantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="month12_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.month12Quantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="year2_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.year2Quantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="year3_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.year3Quantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="year4_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.year4Quantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="year5_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.year5Quantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update fdp_annual_forecast_submission_detail
            <set>
                <if test="item.submissionId != null and item.submissionId != ''">
                    submission_id = #{item.submissionId,jdbcType=VARCHAR},
                </if>
                <if test="item.submissionYear != null">
                    submission_year = #{item.submissionYear,jdbcType=INTEGER},
                </if>
                <if test="item.month1Quantity != null">
                    month1_quantity = #{item.month1Quantity,jdbcType=VARCHAR},
                </if>
                <if test="item.month2Quantity != null">
                    month2_quantity = #{item.month2Quantity,jdbcType=VARCHAR},
                </if>
                <if test="item.month3Quantity != null">
                    month3_quantity = #{item.month3Quantity,jdbcType=VARCHAR},
                </if>
                <if test="item.month4Quantity != null">
                    month4_quantity = #{item.month4Quantity,jdbcType=VARCHAR},
                </if>
                <if test="item.month5Quantity != null">
                    month5_quantity = #{item.month5Quantity,jdbcType=VARCHAR},
                </if>
                <if test="item.month6Quantity != null">
                    month6_quantity = #{item.month6Quantity,jdbcType=VARCHAR},
                </if>
                <if test="item.month7Quantity != null">
                    month7_quantity = #{item.month7Quantity,jdbcType=VARCHAR},
                </if>
                <if test="item.month8Quantity != null">
                    month8_quantity = #{item.month8Quantity,jdbcType=VARCHAR},
                </if>
                <if test="item.month9Quantity != null">
                    month9_quantity = #{item.month9Quantity,jdbcType=VARCHAR},
                </if>
                <if test="item.month10Quantity != null">
                    month10_quantity = #{item.month10Quantity,jdbcType=VARCHAR},
                </if>
                <if test="item.month11Quantity != null">
                    month11_quantity = #{item.month11Quantity,jdbcType=VARCHAR},
                </if>
                <if test="item.month12Quantity != null">
                    month12_quantity = #{item.month12Quantity,jdbcType=VARCHAR},
                </if>
                <if test="item.year2Quantity != null">
                    year2_quantity = #{item.year2Quantity,jdbcType=VARCHAR},
                </if>
                <if test="item.year3Quantity != null">
                    year3_quantity = #{item.year3Quantity,jdbcType=VARCHAR},
                </if>
                <if test="item.year4Quantity != null">
                    year4_quantity = #{item.year4Quantity,jdbcType=VARCHAR},
                </if>
                <if test="item.year5Quantity != null">
                    year5_quantity = #{item.year5Quantity,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from fdp_annual_forecast_submission_detail
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from fdp_annual_forecast_submission_detail where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <delete id="deleteBySubmissionId">
        delete
        from fdp_annual_forecast_submission_detail
        where submission_id = #{submissionId}
          and submission_year = #{submissionYear}
    </delete>
</mapper>
