package com.yhl.scp.dfp.deliveryLog.domain.service;

import com.yhl.scp.dfp.deliveryLog.domain.entity.DeliveryPlanPublishedLogDO;
import com.yhl.scp.dfp.deliveryLog.infrastructure.dao.DeliveryPlanPublishedLogDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <code>DeliveryPlanPublishedLogDomainService</code>
 * <p>
 * 发货计划发布追踪表领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-20 17:22:01
 */
@Service
public class DeliveryPlanPublishedLogDomainService {

    @Resource
    private DeliveryPlanPublishedLogDao deliveryPlanPublishedLogDao;

    /**
     * 数据校验
     *
     * @param deliveryPlanPublishedLogDO 领域对象
     */
    public void validation(DeliveryPlanPublishedLogDO deliveryPlanPublishedLogDO) {
        checkNotNull(deliveryPlanPublishedLogDO);
        checkUniqueCode(deliveryPlanPublishedLogDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param deliveryPlanPublishedLogDO 领域对象
     */
    private void checkNotNull(DeliveryPlanPublishedLogDO deliveryPlanPublishedLogDO) {

    }

    /**
     * 唯一性校验
     *
     * @param deliveryPlanPublishedLogDO 领域对象
     */
    private void checkUniqueCode(DeliveryPlanPublishedLogDO deliveryPlanPublishedLogDO) {

    }

}
