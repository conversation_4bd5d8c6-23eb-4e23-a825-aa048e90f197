package com.yhl.scp.dfp.versionData.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.versionData.infrastructure.po.VersionDataMonthlyMapPO;
import com.yhl.scp.dfp.versionData.vo.VersionDataMonthlyMapVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <code>VersionDataMonthlyMapDao</code>
 * <p>
 * 滚动预测版本数据映射关系DAO
 * </p>
 *
 * @version 1.0
 * @since 2024-08-02 17:28:53
 */
public interface VersionDataMonthlyMapDao extends BaseDao<VersionDataMonthlyMapPO, VersionDataMonthlyMapVO> {

    /**
     * 根据版本批量删除
     * @param versionDTOList
     * @return
     */
    int deleteBatchVersion(@Param("list") List<RemoveVersionDTO> versionDTOList);

}
