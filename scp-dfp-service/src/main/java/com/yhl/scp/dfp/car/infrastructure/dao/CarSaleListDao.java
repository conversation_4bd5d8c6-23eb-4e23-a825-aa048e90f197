package com.yhl.scp.dfp.car.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dfp.car.infrastructure.po.CarSaleListPO;
import com.yhl.scp.dfp.car.vo.CarSaleListVO;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.market.vo.MarketShareVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <code>FdpCarSaleListDao</code>
 * <p>
 * 汽车销量表DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-07 14:58:29
 */
public interface CarSaleListDao extends BaseDao<CarSaleListPO, CarSaleListVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link MarketShareVO}
     */
    List<CarSaleListVO> selectVOByParams(@Param("params") Map<String, Object> params);


    /**
     * 根据版本批量删除
     * @param versionDTOList
     * @return
     */
    int deleteBatchVersion(@Param("list") List<RemoveVersionDTO> versionDTOList);

    /**
     * 根据年月删除
     * @param dateByYearAndMonth
     */
    int deleteBatchByDate(Date dateByYearAndMonth);
}
