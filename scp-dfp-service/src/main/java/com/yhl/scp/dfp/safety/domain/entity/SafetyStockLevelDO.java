package com.yhl.scp.dfp.safety.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <code>SafetyStockLevelDO</code>
 * <p>
 * 安全库存水位DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-19 17:33:40
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SafetyStockLevelDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = -51627108983166512L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 库存点ID
     */
    private String stockPointId;
    /**
     * 库存点编码
     */
    private String stockCode;
    /**
     * 主机厂编码
     */
    private String oemCode;
    /**
     * 主机厂名称
     */
    private String oemName;
    /**
     * 供应类型
     */
    private String supplyType;
    /**
     * 主机厂风险等级
     */
    private String oemRiskLevel;
    /**
     * 车型编码
     */
    private String vehicleModelCode;
    /**
     * 零件风险等级
     */
    private String materialRiskLevel;
    /**
     * 本厂编码
     */
    private String productCode;
    /**
     * 零件名称
     */
    private String materialName;
    /**
     * 最小库存天数
     */
    private BigDecimal minStockDay;
    /**
     * 标准库存天数
     */
    private BigDecimal standardStockDay;

}
