package com.yhl.scp.dfp.oem.domain.service;

import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.common.constants.Constants;
import com.yhl.scp.dfp.oem.domain.entity.OemTransportTimeDO;
import com.yhl.scp.dfp.oem.infrastructure.dao.OemTransportTimeDao;
import com.yhl.scp.dfp.oem.infrastructure.po.OemTransportTimePO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>OemTransportTimeDomainService</code>
 * <p>
 * 主机厂运输时间领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-11 09:59:53
 */
@Service
public class OemTransportTimeDomainService {

    @Resource
    private OemTransportTimeDao oemTransportTimeDao;

    /**
     * 数据校验
     *
     * @param oemTransportTimeDO 领域对象
     */
    public void validation(OemTransportTimeDO oemTransportTimeDO) {
        checkNotNull(oemTransportTimeDO);
        checkUniqueCode(oemTransportTimeDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param oemTransportTimeDO 领域对象
     */
    private void checkNotNull(OemTransportTimeDO oemTransportTimeDO) {
        if (StringUtils.isBlank(oemTransportTimeDO.getOemCode())) {
            throw new BusinessException("主机厂编码，不能为空");
        }
        if (StringUtils.isBlank(oemTransportTimeDO.getOrganizeCode())) {
            throw new BusinessException("组织代码，不能为空");
        }
    }

    /**
     * 唯一性校验
     *
     * @param oemTransportTimeDO 领域对象
     */
    private void checkUniqueCode(OemTransportTimeDO oemTransportTimeDO) {
        Map<String, Object> params = new HashMap<>(4);
        params.put("oemCode", oemTransportTimeDO.getOemCode());
        params.put("priority", oemTransportTimeDO.getPriority());
        if (StringUtils.isBlank(oemTransportTimeDO.getId())) {
            List<OemTransportTimePO> list = oemTransportTimeDao.selectByParams(params);
            if (CollectionUtils.isNotEmpty(list)) {
                throw new BusinessException("新增失败，主机厂编码与优先级组合已存在：" + oemTransportTimeDO.getOemCode() + Constants.DELIMITER + oemTransportTimeDO.getPriority());
            }
        } else {
            OemTransportTimePO old = oemTransportTimeDao.selectByPrimaryKey(oemTransportTimeDO.getId());
            if (!oemTransportTimeDO.getOemCode().equals(old.getOemCode()) || !oemTransportTimeDO.getPriority().equals(old.getPriority())) {
                List<OemTransportTimePO> list = oemTransportTimeDao.selectByParams(params);
                if (CollectionUtils.isNotEmpty(list)) {
                    throw new BusinessException("修改失败，主机厂编码与优先级组合已存在：" + oemTransportTimeDO.getOemCode() + Constants.DELIMITER + oemTransportTimeDO.getPriority());
                }
            }
        }
    }

}
