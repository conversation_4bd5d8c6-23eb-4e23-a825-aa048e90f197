package com.yhl.scp.dfp.deliveryLog.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>DeliveryPlanPublishedLogPO</code>
 * <p>
 * 发货计划发布追踪表PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-20 17:21:58
 */
public class DeliveryPlanPublishedLogPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 339540225331314791L;

    /**
     * 发货计划版本ID
     */
    private String deliveryVersionId;
    /**
     * 主表ID
     */
    private String deliveryPlanDataId;
    /**
     * 需求类型
     */
    private String demandCategory;
    /**
     * 主机厂编码
     */
    private String oemCode;
    /**
     * 本厂编码
     */
    private String productCode;
    /**
     * 发货日期
     */
    private Date demandTime;
    /**
     * 需求数量
     */
    private Integer demandQuantity;
    /**
     * 箱数
     */
    private Integer boxQuantity;
    /**
     * 发版人
     */
    private String publisher;
    /**
     * 发版时间
     */
    private Date publishTime;

    public String getDeliveryVersionId() {
        return deliveryVersionId;
    }

    public void setDeliveryVersionId(String deliveryVersionId) {
        this.deliveryVersionId = deliveryVersionId;
    }

    public String getDeliveryPlanDataId() {
        return deliveryPlanDataId;
    }

    public void setDeliveryPlanDataId(String deliveryPlanDataId) {
        this.deliveryPlanDataId = deliveryPlanDataId;
    }

    public String getDemandCategory() {
        return demandCategory;
    }

    public void setDemandCategory(String demandCategory) {
        this.demandCategory = demandCategory;
    }

    public String getOemCode() {
        return oemCode;
    }

    public void setOemCode(String oemCode) {
        this.oemCode = oemCode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public Date getDemandTime() {
        return demandTime;
    }

    public void setDemandTime(Date demandTime) {
        this.demandTime = demandTime;
    }

    public Integer getDemandQuantity() {
        return demandQuantity;
    }

    public void setDemandQuantity(Integer demandQuantity) {
        this.demandQuantity = demandQuantity;
    }

    public Integer getBoxQuantity() {
        return boxQuantity;
    }

    public void setBoxQuantity(Integer boxQuantity) {
        this.boxQuantity = boxQuantity;
    }

    public String getPublisher() {
        return publisher;
    }

    public void setPublisher(String publisher) {
        this.publisher = publisher;
    }

    public Date getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = publishTime;
    }

}
