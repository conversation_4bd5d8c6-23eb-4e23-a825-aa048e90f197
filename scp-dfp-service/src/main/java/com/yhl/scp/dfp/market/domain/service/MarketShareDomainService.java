package com.yhl.scp.dfp.market.domain.service;

import com.yhl.scp.dfp.market.domain.entity.MarketShareDO;
import com.yhl.scp.dfp.market.infrastructure.dao.MarketShareDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <code>MarketShareDomainService</code>
 * <p>
 * 市场占有率领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:47:11
 */
@Service
public class MarketShareDomainService {

    @Resource
    private MarketShareDao marketShareDao;

    /**
     * 数据校验
     *
     * @param marketShareDO 领域对象
     */
    public void validation(MarketShareDO marketShareDO) {
        checkNotNull(marketShareDO);
        checkUniqueCode(marketShareDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param marketShareDO 领域对象
     */
    private void checkNotNull(MarketShareDO marketShareDO) {
        // TODO
    }

    /**
     * 唯一性校验
     *
     * @param marketShareDO 领域对象
     */
    private void checkUniqueCode(MarketShareDO marketShareDO) {
        // TODO
    }

}
