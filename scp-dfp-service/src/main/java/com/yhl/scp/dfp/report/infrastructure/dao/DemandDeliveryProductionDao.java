package com.yhl.scp.dfp.report.infrastructure.dao;

import com.yhl.scp.dfp.report.vo.DemandDeliveryProductionVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DemandDeliveryProductionDao {
    List<DemandDeliveryProductionVO> selectByCondition(@Param("sortParam") String sortParam,
                                                       @Param("queryCriteriaParam") String queryCriteriaParam);
}
