<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.stock.infrastructure.dao.InventoryRealTimeDataDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.stock.infrastructure.po.InventoryRealTimeDataPO">
        <!--@Table fdp_inventory_real_time_data-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="stock_point_name" jdbcType="VARCHAR" property="stockPointName"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="operation_code" jdbcType="VARCHAR" property="operationCode"/>
        <result column="subinventory" jdbcType="VARCHAR" property="subinventory"/>
        <result column="subinventory_description" jdbcType="VARCHAR" property="subinventoryDescription"/>
        <result column="freight_space" jdbcType="VARCHAR" property="freightSpace"/>
        <result column="freight_space_description" jdbcType="VARCHAR" property="freightSpaceDescription"/>
        <result column="available_quantity" jdbcType="INTEGER" property="availableQuantity"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.dfp.stock.vo.InventoryRealTimeDataVO">
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
    </resultMap>
    <sql id="Base_Column_List">
id,stock_point_code,stock_point_name,product_code,product_name,operation_code,subinventory,subinventory_description,freight_space,freight_space_description,available_quantity,unit,update_time,remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCodes != null and params.stockPointCodes.size() > 0">
                and stock_point_code in
                <foreach collection="params.stockPointCodes" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.stockPointName != null and params.stockPointName != ''">
                and stock_point_name = #{params.stockPointName,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productName != null and params.productName != ''">
                and product_name = #{params.productName,jdbcType=VARCHAR}
            </if>
            <if test="params.operationCode != null and params.operationCode != ''">
                and operation_code = #{params.operationCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCodes != null and params.productCodes.size() > 0">
                and product_code in
                <foreach collection="params.productCodes" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.subinventory != null and params.subinventory != ''">
                and subinventory = #{params.subinventory,jdbcType=VARCHAR}
            </if>
            <if test="params.subinventoryDescription != null and params.subinventoryDescription != ''">
                and subinventory_description = #{params.subinventoryDescription,jdbcType=VARCHAR}
            </if>
            <if test="params.freightSpace != null and params.freightSpace != ''">
                and freight_space = #{params.freightSpace,jdbcType=VARCHAR}
            </if>
            <if test="params.freightSpaceDescription != null and params.freightSpaceDescription != ''">
                and freight_space_description = #{params.freightSpaceDescription,jdbcType=VARCHAR}
            </if>
            <if test="params.availableQuantity != null">
                and available_quantity = #{params.availableQuantity,jdbcType=INTEGER}
            </if>
            <if test="params.unit != null and params.unit != ''">
                and unit = #{params.unit,jdbcType=VARCHAR}
            </if>
            <if test="params.updateTime != null">
                and update_time = #{params.updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_inventory_real_time_data
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_inventory_real_time_data
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from fdp_inventory_real_time_data
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_inventory_real_time_data
        <include refid="Base_Where_Condition" />
    </select>
    <select id="selectInventoryByProductCodes" resultType="com.yhl.scp.dfp.stock.dto.InventoryDataDTO">
        SELECT
        product_code AS productCode,
        SUM(IF( operation_code IS NOT NULL, 0, available_quantity )) AS finishInventory,
        SUM(IF( operation_code IS NULL, 0, available_quantity )) AS semifinishedInventory
        FROM `fdp_inventory_real_time_data`
        WHERE
        product_code in
        <foreach collection="productCodes" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        GROUP BY
        product_code
    </select>
    <select id="selectRealTimeInventory" resultMap="VOResultMap">
        SELECT
            t3.oem_code,
            t1.product_code,
            SUM( t1.available_quantity ) available_quantity
        FROM
            fdp_inventory_real_time_data t1
            LEFT JOIN mds_stock_point t2 ON t2.stock_point_code = t1.stock_point_code
            LEFT JOIN mds_oem_stock_point_map t3 ON t3.stock_point_code = t1.stock_point_code
        WHERE
            t2.stock_point_type IN ( "GN", "CK" ) AND t1.available_quantity &gt;= 0
        GROUP BY
            t3.oem_code,
            t1.product_code
    </select>
    <!-- 没有工序代码是成品，有工序代码是半成品 -->
    <select id="selectInventoryByParams" resultType="com.yhl.scp.dfp.delivery.vo.RealTimeInventoryVO">
        SELECT
        product_code AS productCode,
        stock_point_code AS stockPointCode,
        DATE(update_time) AS updateTime,  -- 使用 DATE() 函数将时间格式化为年月日
        SUM(
        IF(operation_code IS  NULL, 0, available_quantity)
        ) AS semiFinishedInventory,
        SUM(
        IF(operation_code IS NOT NULL, 0, available_quantity)
        ) AS finishedInventory
        FROM
        fdp_inventory_real_time_data
        <include refid="Base_Where_Condition" />
        GROUP BY
        product_code,
        stock_point_code,
        DATE(update_time);  -- 按年月日分组

    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.stock.infrastructure.po.InventoryRealTimeDataPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into fdp_inventory_real_time_data(
        id,
        stock_point_code,
        stock_point_name,
        product_code,
        product_name,
        operation_code,
        subinventory,
        subinventory_description,
        freight_space,
        freight_space_description,
        available_quantity,
        unit,
        update_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{stockPointName,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{productName,jdbcType=VARCHAR},
        #{operationCode,jdbcType=VARCHAR},
        #{subinventory,jdbcType=VARCHAR},
        #{subinventoryDescription,jdbcType=VARCHAR},
        #{freightSpace,jdbcType=VARCHAR},
        #{freightSpaceDescription,jdbcType=VARCHAR},
        #{availableQuantity,jdbcType=INTEGER},
        #{unit,jdbcType=VARCHAR},
        #{updateTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.dfp.stock.infrastructure.po.InventoryRealTimeDataPO">
        insert into fdp_inventory_real_time_data(
        id,
        stock_point_code,
        stock_point_name,
        product_code,
        product_name,
        operation_code,
        subinventory,
        subinventory_description,
        freight_space,
        freight_space_description,
        available_quantity,
        unit,
        update_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{stockPointName,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{productName,jdbcType=VARCHAR},
        #{operationCode,jdbcType=VARCHAR},
        #{subinventory,jdbcType=VARCHAR},
        #{subinventoryDescription,jdbcType=VARCHAR},
        #{freightSpace,jdbcType=VARCHAR},
        #{freightSpaceDescription,jdbcType=VARCHAR},
        #{availableQuantity,jdbcType=INTEGER},
        #{unit,jdbcType=VARCHAR},
        #{updateTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into fdp_inventory_real_time_data(
        id,
        stock_point_code,
        stock_point_name,
        product_code,
        product_name,
        operation_code,
        subinventory,
        subinventory_description,
        freight_space,
        freight_space_description,
        available_quantity,
        unit,
        update_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.stockPointCode,jdbcType=VARCHAR},
        #{entity.stockPointName,jdbcType=VARCHAR},
        #{entity.productCode,jdbcType=VARCHAR},
        #{entity.productName,jdbcType=VARCHAR},
        #{entity.operationCode,jdbcType=VARCHAR},
        #{entity.subinventory,jdbcType=VARCHAR},
        #{entity.subinventoryDescription,jdbcType=VARCHAR},
        #{entity.freightSpace,jdbcType=VARCHAR},
        #{entity.freightSpaceDescription,jdbcType=VARCHAR},
        #{entity.availableQuantity,jdbcType=INTEGER},
        #{entity.unit,jdbcType=VARCHAR},
        #{entity.updateTime,jdbcType=TIMESTAMP},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into fdp_inventory_real_time_data(
        id,
        stock_point_code,
        stock_point_name,
        product_code,
        product_name,
        operation_code,
        subinventory,
        subinventory_description,
        freight_space,
        freight_space_description,
        available_quantity,
        unit,
        update_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.stockPointCode,jdbcType=VARCHAR},
        #{entity.stockPointName,jdbcType=VARCHAR},
        #{entity.productCode,jdbcType=VARCHAR},
        #{entity.productName,jdbcType=VARCHAR},
        #{entity.operationCode,jdbcType=VARCHAR},
        #{entity.subinventory,jdbcType=VARCHAR},
        #{entity.subinventoryDescription,jdbcType=VARCHAR},
        #{entity.freightSpace,jdbcType=VARCHAR},
        #{entity.freightSpaceDescription,jdbcType=VARCHAR},
        #{entity.availableQuantity,jdbcType=INTEGER},
        #{entity.unit,jdbcType=VARCHAR},
        #{entity.updateTime,jdbcType=TIMESTAMP},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.stock.infrastructure.po.InventoryRealTimeDataPO">
        update fdp_inventory_real_time_data set
        stock_point_code = #{stockPointCode,jdbcType=VARCHAR},
        stock_point_name = #{stockPointName,jdbcType=VARCHAR},
        product_code = #{productCode,jdbcType=VARCHAR},
        product_name = #{productName,jdbcType=VARCHAR},
        operation_code = #{operationCode,jdbcType=VARCHAR},
        subinventory = #{subinventory,jdbcType=VARCHAR},
        subinventory_description = #{subinventoryDescription,jdbcType=VARCHAR},
        freight_space = #{freightSpace,jdbcType=VARCHAR},
        freight_space_description = #{freightSpaceDescription,jdbcType=VARCHAR},
        available_quantity = #{availableQuantity,jdbcType=INTEGER},
        unit = #{unit,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        version_value = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.dfp.stock.infrastructure.po.InventoryRealTimeDataPO">
        update fdp_inventory_real_time_data
        <set>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointName != null and item.stockPointName != ''">
                stock_point_name = #{item.stockPointName,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productName != null and item.productName != ''">
                product_name = #{item.productName,jdbcType=VARCHAR},
            </if>
            <if test="item.operationCode != null and item.operationCode != ''">
                operation_code = #{item.operationCode,jdbcType=VARCHAR},
            </if>
            <if test="item.subinventory != null and item.subinventory != ''">
                subinventory = #{item.subinventory,jdbcType=VARCHAR},
            </if>
            <if test="item.subinventoryDescription != null and item.subinventoryDescription != ''">
                subinventory_description = #{item.subinventoryDescription,jdbcType=VARCHAR},
            </if>
            <if test="item.freightSpace != null and item.freightSpace != ''">
                freight_space = #{item.freightSpace,jdbcType=VARCHAR},
            </if>
            <if test="item.freightSpaceDescription != null and item.freightSpaceDescription != ''">
                freight_space_description = #{item.freightSpaceDescription,jdbcType=VARCHAR},
            </if>
            <if test="item.availableQuantity != null">
                available_quantity = #{item.availableQuantity,jdbcType=INTEGER},
            </if>
            <if test="item.unit != null and item.unit != ''">
                unit = #{item.unit,jdbcType=VARCHAR},
            </if>
            <if test="item.updateTime != null">
                update_time = #{item.updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update fdp_inventory_real_time_data
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="stock_point_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="operation_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.operationCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="subinventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.subinventory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="subinventory_description = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.subinventoryDescription,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="freight_space = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.freightSpace,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="freight_space_description = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.freightSpaceDescription,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="available_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.availableQuantity,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="unit = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.unit,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update fdp_inventory_real_time_data 
        <set>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointName != null and item.stockPointName != ''">
                stock_point_name = #{item.stockPointName,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productName != null and item.productName != ''">
                product_name = #{item.productName,jdbcType=VARCHAR},
            </if>
            <if test="item.operationCode != null and item.operationCode != ''">
                operation_code = #{item.operationCode,jdbcType=VARCHAR},
            </if>
            <if test="item.subinventory != null and item.subinventory != ''">
                subinventory = #{item.subinventory,jdbcType=VARCHAR},
            </if>
            <if test="item.subinventoryDescription != null and item.subinventoryDescription != ''">
                subinventory_description = #{item.subinventoryDescription,jdbcType=VARCHAR},
            </if>
            <if test="item.freightSpace != null and item.freightSpace != ''">
                freight_space = #{item.freightSpace,jdbcType=VARCHAR},
            </if>
            <if test="item.freightSpaceDescription != null and item.freightSpaceDescription != ''">
                freight_space_description = #{item.freightSpaceDescription,jdbcType=VARCHAR},
            </if>
            <if test="item.availableQuantity != null">
                available_quantity = #{item.availableQuantity,jdbcType=INTEGER},
            </if>
            <if test="item.unit != null and item.unit != ''">
                unit = #{item.unit,jdbcType=VARCHAR},
            </if>
            <if test="item.updateTime != null">
                update_time = #{item.updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from fdp_inventory_real_time_data where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from fdp_inventory_real_time_data where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <!-- 批量id+版本删除 -->
    <delete id="deleteBatchVersion" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            delete from fdp_inventory_real_time_data where
            id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </delete>
</mapper>
