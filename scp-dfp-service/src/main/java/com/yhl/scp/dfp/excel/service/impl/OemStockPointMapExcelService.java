package com.yhl.scp.dfp.excel.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.scp.common.excel.DataImportInfo;
import com.yhl.scp.common.excel.ImportDataTypeEnum;
import com.yhl.scp.common.excel.model.ImportAnalysisResultHolder;
import com.yhl.scp.common.excel.model.ImportContext;
import com.yhl.scp.common.excel.model.ImportRelatedDataHolder;
import com.yhl.scp.common.excel.service.AbstractExcelService;
import com.yhl.scp.common.vo.SimpleVO;
import com.yhl.scp.dfp.oem.convertor.OemStockPointMapConvertor;
import com.yhl.scp.dfp.oem.dto.OemStockPointMapDTO;
import com.yhl.scp.dfp.oem.infrastructure.dao.OemStockPointMapDao;
import com.yhl.scp.dfp.oem.infrastructure.po.OemStockPointMapPO;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.service.OemStockPointMapService;
import com.yhl.scp.dfp.oem.vo.OemStockPointMapVO;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>OemVehicleModelMapExcelService</code>
 * <p>
 * 主机厂与库存点关系excel导出
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-24 22:05:10
 */
@Service
public class OemStockPointMapExcelService extends AbstractExcelService<OemStockPointMapDTO, OemStockPointMapPO, OemStockPointMapVO> {

    @Resource
    private OemStockPointMapDao oemStockPointMapDao;

    @Resource
    private OemStockPointMapService oemStockPointMapService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private OemService oemService;

    @Override
    public BaseDao<OemStockPointMapPO, OemStockPointMapVO> getBaseDao() {
        return oemStockPointMapDao;
    }

    @Override
    public Function<OemStockPointMapDTO, OemStockPointMapPO> getDTO2POConvertor() {
        return OemStockPointMapConvertor.INSTANCE::dto2Po;
    }

    @Override
    public Class<OemStockPointMapDTO> getDTOClass() {
        return OemStockPointMapDTO.class;
    }

    @Override
    public BaseService<OemStockPointMapDTO, OemStockPointMapVO> getBaseService() {
        return oemStockPointMapService;
    }

    @Override
    protected void fillIdForUpdateData(List<OemStockPointMapDTO> updateList, Map<String, OemStockPointMapPO> existingDataMap) {
        for (OemStockPointMapDTO oemStockPointMapDTO : updateList) {
            OemStockPointMapPO oemStockPointMapPO = existingDataMap.get(oemStockPointMapDTO.getOemCode() + "&" + oemStockPointMapDTO.getStockPointCode());
            if (Objects.isNull(oemStockPointMapPO)) {
                continue;
            }
            oemStockPointMapDTO.setId(oemStockPointMapPO.getId());
        }
    }

    @Override
    protected void specialVerification(ImportAnalysisResultHolder<OemStockPointMapDTO, OemStockPointMapPO> resultHolder, ImportContext importContext) {
        List<OemStockPointMapDTO> insertList = resultHolder.getInsertList();
        List<OemStockPointMapDTO> updateList = resultHolder.getUpdateList();
        verifyPaternity(insertList, resultHolder.getImportLogList());
        verifyPaternity(updateList, resultHolder.getImportLogList());
        resultHolder.setInsertList(insertList);
        resultHolder.setUpdateList(updateList);
    }

    private void verifyPaternity(List<OemStockPointMapDTO> checkList, List<DataImportInfo> importLogList) {
        Map<String, Object> queryParams = MapUtil.newHashMap();
        queryParams.put("enabled", YesOrNoEnum.YES.getCode());
        List<OemVO> oemVOS = oemService.selectByParams(queryParams);
        Map<String, List<OemVO>> oemListMap = oemVOS.stream().collect(Collectors.groupingBy(OemVO::getOemCode));
        Iterator<OemStockPointMapDTO> iterator = checkList.iterator();
        while (iterator.hasNext()) {
            OemStockPointMapDTO oemStockPointMapDTO = iterator.next();
            if (isFieldEmpty(oemStockPointMapDTO.getOemCode())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + oemStockPointMapDTO.getRowIndex() + ";[主机厂编码]未填写");
                dataImportInfo.setDisplayIndex(oemStockPointMapDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (isFieldEmpty(oemStockPointMapDTO.getStockPointCode())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + oemStockPointMapDTO.getRowIndex() + ";[库存点编码]未填写");
                dataImportInfo.setDisplayIndex(oemStockPointMapDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (!"YES".equals(oemStockPointMapDTO.getEnabled()) && !"NO".equals(oemStockPointMapDTO.getEnabled())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + oemStockPointMapDTO.getRowIndex() + ";[是否有效]只能是YES或NO");
                dataImportInfo.setDisplayIndex(oemStockPointMapDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (!oemListMap.containsKey(oemStockPointMapDTO.getOemCode())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + oemStockPointMapDTO.getRowIndex() + ";[主机厂编码]不存在");
                dataImportInfo.setDisplayIndex(oemStockPointMapDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
            }
        }
    }

    private boolean isFieldEmpty(String value) {
        return value == null || value.trim().isEmpty();
    }

    @Override
    protected ImportRelatedDataHolder<OemStockPointMapPO> prepareData(List<OemStockPointMapDTO> oemVehicleModelMapDTOS) {
        // 找到数据库现在所有的数据
        List<OemStockPointMapPO> alreadyExitData = oemStockPointMapDao.selectByParams(new HashMap<>(2));
        Map<String, OemStockPointMapPO> codeToPOMap = alreadyExitData.stream().collect(Collectors.toMap(x -> x.getOemCode() + "&" + x.getStockPointCode(), Function.identity(), (v1, v2) -> v1));
        // 组成唯一键的字段
        List<String> uniqueKeys = ListUtil.of("oemCode", "stockPointCode");
        // 外键字段
        List<String> foreignKeys = ListUtil.of();
        Map<String, Object> queryMap = new HashMap<>();
        Map<String, List<SimpleVO>> foreignDataMap = new HashMap<>();
        return ImportRelatedDataHolder.<OemStockPointMapPO>builder()
                .existingData(alreadyExitData)
                .mainKeys(uniqueKeys)
                .foreignKeys(foreignKeys)
                .foreignDataMap(foreignDataMap)
                .existingDataMap(codeToPOMap)
                .build();
    }

}
