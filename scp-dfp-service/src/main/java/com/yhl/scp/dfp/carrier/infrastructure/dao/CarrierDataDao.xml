<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.carrier.infrastructure.dao.CarrierDataDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.carrier.infrastructure.po.CarrierDataPO">
        <!--@Table dfp_carrier_data-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="carrier_id" jdbcType="INTEGER" property="carrierId"/>
        <result column="organization_code" jdbcType="VARCHAR" property="organizationCode"/>
        <result column="ebs_org_id" jdbcType="VARCHAR" property="ebsOrgId"/>
        <result column="carrier_name" jdbcType="VARCHAR" property="carrierName"/>
        <result column="carrier_code" jdbcType="VARCHAR" property="carrierCode"/>
        <result column="carrier_addr" jdbcType="VARCHAR" property="carrierAddr"/>
        <result column="carrier_duns" jdbcType="VARCHAR" property="carrierDuns"/>
        <result column="carrier_contact" jdbcType="VARCHAR" property="carrierContact"/>
        <result column="carrier_phone" jdbcType="VARCHAR" property="carrierPhone"/>
        <result column="transport_mode_code" jdbcType="VARCHAR" property="transportModeCode"/>
        <result column="transport_mode_name" jdbcType="VARCHAR" property="transportModeName"/>
        <result column="transport_partner_no" jdbcType="VARCHAR" property="transportPartnerNo"/>
        <result column="conveyance_code" jdbcType="VARCHAR" property="conveyanceCode"/>
        <result column="conveyance_name" jdbcType="VARCHAR" property="conveyanceName"/>
        <result column="departure" jdbcType="VARCHAR" property="departure"/>
        <result column="arrival" jdbcType="VARCHAR" property="arrival"/>
        <result column="source_type" jdbcType="VARCHAR" property="sourceType"/>
        <result column="creation_date" jdbcType="TIMESTAMP" property="creationDate"/>
        <result column="last_update_date" jdbcType="TIMESTAMP" property="lastUpdateDate"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.dfp.carrier.vo.CarrierDataVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
id,carrier_id,organization_code,ebs_org_id,carrier_name,carrier_code,carrier_addr,carrier_duns,carrier_contact,carrier_phone,transport_mode_code,transport_mode_name,transport_partner_no,conveyance_code,conveyance_name,departure,arrival,source_type,creation_date,last_update_date,remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.carrierId != null">
                and carrier_id = #{params.carrierId,jdbcType=INTEGER}
            </if>
            <if test="params.organizationCode != null and params.organizationCode != ''">
                and organization_code = #{params.organizationCode,jdbcType=VARCHAR}
            </if>
            <if test="params.ebsOrgId != null and params.ebsOrgId != ''">
                and ebs_org_id = #{params.ebsOrgId,jdbcType=VARCHAR}
            </if>
            <if test="params.carrierName != null and params.carrierName != ''">
                and carrier_name = #{params.carrierName,jdbcType=VARCHAR}
            </if>
            <if test="params.carrierCode != null and params.carrierCode != ''">
                and carrier_code = #{params.carrierCode,jdbcType=VARCHAR}
            </if>
            <if test="params.carrierAddr != null and params.carrierAddr != ''">
                and carrier_addr = #{params.carrierAddr,jdbcType=VARCHAR}
            </if>
            <if test="params.carrierDuns != null and params.carrierDuns != ''">
                and carrier_duns = #{params.carrierDuns,jdbcType=VARCHAR}
            </if>
            <if test="params.carrierContact != null and params.carrierContact != ''">
                and carrier_contact = #{params.carrierContact,jdbcType=VARCHAR}
            </if>
            <if test="params.carrierPhone != null and params.carrierPhone != ''">
                and carrier_phone = #{params.carrierPhone,jdbcType=VARCHAR}
            </if>
            <if test="params.transportModeCode != null and params.transportModeCode != ''">
                and transport_mode_code = #{params.transportModeCode,jdbcType=VARCHAR}
            </if>
            <if test="params.transportModeName != null and params.transportModeName != ''">
                and transport_mode_name = #{params.transportModeName,jdbcType=VARCHAR}
            </if>
            <if test="params.transportPartnerNo != null and params.transportPartnerNo != ''">
                and transport_partner_no = #{params.transportPartnerNo,jdbcType=VARCHAR}
            </if>
            <if test="params.conveyanceCode != null and params.conveyanceCode != ''">
                and conveyance_code = #{params.conveyanceCode,jdbcType=VARCHAR}
            </if>
            <if test="params.conveyanceName != null and params.conveyanceName != ''">
                and conveyance_name = #{params.conveyanceName,jdbcType=VARCHAR}
            </if>
            <if test="params.departure != null and params.departure != ''">
                and departure = #{params.departure,jdbcType=VARCHAR}
            </if>
            <if test="params.arrival != null and params.arrival != ''">
                and arrival = #{params.arrival,jdbcType=VARCHAR}
            </if>
            <if test="params.sourceType != null and params.sourceType != ''">
                and source_type = #{params.sourceType,jdbcType=VARCHAR}
            </if>
            <if test="params.creationDate != null">
                and creation_date = #{params.creationDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.lastUpdateDate != null">
                and last_update_date = #{params.lastUpdateDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from dfp_carrier_data
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from dfp_carrier_data
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from dfp_carrier_data
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from dfp_carrier_data
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.carrier.infrastructure.po.CarrierDataPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into dfp_carrier_data(
        id,
        carrier_id,
        organization_code,
        ebs_org_id,
        carrier_name,
        carrier_code,
        carrier_addr,
        carrier_duns,
        carrier_contact,
        carrier_phone,
        transport_mode_code,
        transport_mode_name,
        transport_partner_no,
        conveyance_code,
        conveyance_name,
        departure,
        arrival,
        source_type,
        creation_date,
        last_update_date,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{carrierId,jdbcType=INTEGER},
        #{organizationCode,jdbcType=VARCHAR},
        #{ebsOrgId,jdbcType=VARCHAR},
        #{carrierName,jdbcType=VARCHAR},
        #{carrierCode,jdbcType=VARCHAR},
        #{carrierAddr,jdbcType=VARCHAR},
        #{carrierDuns,jdbcType=VARCHAR},
        #{carrierContact,jdbcType=VARCHAR},
        #{carrierPhone,jdbcType=VARCHAR},
        #{transportModeCode,jdbcType=VARCHAR},
        #{transportModeName,jdbcType=VARCHAR},
        #{transportPartnerNo,jdbcType=VARCHAR},
        #{conveyanceCode,jdbcType=VARCHAR},
        #{conveyanceName,jdbcType=VARCHAR},
        #{departure,jdbcType=VARCHAR},
        #{arrival,jdbcType=VARCHAR},
        #{sourceType,jdbcType=VARCHAR},
        #{creationDate,jdbcType=TIMESTAMP},
        #{lastUpdateDate,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.dfp.carrier.infrastructure.po.CarrierDataPO">
        insert into dfp_carrier_data(
        id,
        carrier_id,
        organization_code,
        ebs_org_id,
        carrier_name,
        carrier_code,
        carrier_addr,
        carrier_duns,
        carrier_contact,
        carrier_phone,
        transport_mode_code,
        transport_mode_name,
        transport_partner_no,
        conveyance_code,
        conveyance_name,
        departure,
        arrival,
        source_type,
        creation_date,
        last_update_date,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{carrierId,jdbcType=INTEGER},
        #{organizationCode,jdbcType=VARCHAR},
        #{ebsOrgId,jdbcType=VARCHAR},
        #{carrierName,jdbcType=VARCHAR},
        #{carrierCode,jdbcType=VARCHAR},
        #{carrierAddr,jdbcType=VARCHAR},
        #{carrierDuns,jdbcType=VARCHAR},
        #{carrierContact,jdbcType=VARCHAR},
        #{carrierPhone,jdbcType=VARCHAR},
        #{transportModeCode,jdbcType=VARCHAR},
        #{transportModeName,jdbcType=VARCHAR},
        #{transportPartnerNo,jdbcType=VARCHAR},
        #{conveyanceCode,jdbcType=VARCHAR},
        #{conveyanceName,jdbcType=VARCHAR},
        #{departure,jdbcType=VARCHAR},
        #{arrival,jdbcType=VARCHAR},
        #{sourceType,jdbcType=VARCHAR},
        #{creationDate,jdbcType=TIMESTAMP},
        #{lastUpdateDate,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into dfp_carrier_data(
        id,
        carrier_id,
        organization_code,
        ebs_org_id,
        carrier_name,
        carrier_code,
        carrier_addr,
        carrier_duns,
        carrier_contact,
        carrier_phone,
        transport_mode_code,
        transport_mode_name,
        transport_partner_no,
        conveyance_code,
        conveyance_name,
        departure,
        arrival,
        source_type,
        creation_date,
        last_update_date,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.carrierId,jdbcType=INTEGER},
        #{entity.organizationCode,jdbcType=VARCHAR},
        #{entity.ebsOrgId,jdbcType=VARCHAR},
        #{entity.carrierName,jdbcType=VARCHAR},
        #{entity.carrierCode,jdbcType=VARCHAR},
        #{entity.carrierAddr,jdbcType=VARCHAR},
        #{entity.carrierDuns,jdbcType=VARCHAR},
        #{entity.carrierContact,jdbcType=VARCHAR},
        #{entity.carrierPhone,jdbcType=VARCHAR},
        #{entity.transportModeCode,jdbcType=VARCHAR},
        #{entity.transportModeName,jdbcType=VARCHAR},
        #{entity.transportPartnerNo,jdbcType=VARCHAR},
        #{entity.conveyanceCode,jdbcType=VARCHAR},
        #{entity.conveyanceName,jdbcType=VARCHAR},
        #{entity.departure,jdbcType=VARCHAR},
        #{entity.arrival,jdbcType=VARCHAR},
        #{entity.sourceType,jdbcType=VARCHAR},
        #{entity.creationDate,jdbcType=TIMESTAMP},
        #{entity.lastUpdateDate,jdbcType=TIMESTAMP},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into dfp_carrier_data(
        id,
        carrier_id,
        organization_code,
        ebs_org_id,
        carrier_name,
        carrier_code,
        carrier_addr,
        carrier_duns,
        carrier_contact,
        carrier_phone,
        transport_mode_code,
        transport_mode_name,
        transport_partner_no,
        conveyance_code,
        conveyance_name,
        departure,
        arrival,
        source_type,
        creation_date,
        last_update_date,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.carrierId,jdbcType=INTEGER},
        #{entity.organizationCode,jdbcType=VARCHAR},
        #{entity.ebsOrgId,jdbcType=VARCHAR},
        #{entity.carrierName,jdbcType=VARCHAR},
        #{entity.carrierCode,jdbcType=VARCHAR},
        #{entity.carrierAddr,jdbcType=VARCHAR},
        #{entity.carrierDuns,jdbcType=VARCHAR},
        #{entity.carrierContact,jdbcType=VARCHAR},
        #{entity.carrierPhone,jdbcType=VARCHAR},
        #{entity.transportModeCode,jdbcType=VARCHAR},
        #{entity.transportModeName,jdbcType=VARCHAR},
        #{entity.transportPartnerNo,jdbcType=VARCHAR},
        #{entity.conveyanceCode,jdbcType=VARCHAR},
        #{entity.conveyanceName,jdbcType=VARCHAR},
        #{entity.departure,jdbcType=VARCHAR},
        #{entity.arrival,jdbcType=VARCHAR},
        #{entity.sourceType,jdbcType=VARCHAR},
        #{entity.creationDate,jdbcType=TIMESTAMP},
        #{entity.lastUpdateDate,jdbcType=TIMESTAMP},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.carrier.infrastructure.po.CarrierDataPO">
        update dfp_carrier_data set
        carrier_id = #{carrierId,jdbcType=INTEGER},
        organization_code = #{organizationCode,jdbcType=VARCHAR},
        ebs_org_id = #{ebsOrgId,jdbcType=VARCHAR},
        carrier_name = #{carrierName,jdbcType=VARCHAR},
        carrier_code = #{carrierCode,jdbcType=VARCHAR},
        carrier_addr = #{carrierAddr,jdbcType=VARCHAR},
        carrier_duns = #{carrierDuns,jdbcType=VARCHAR},
        carrier_contact = #{carrierContact,jdbcType=VARCHAR},
        carrier_phone = #{carrierPhone,jdbcType=VARCHAR},
        transport_mode_code = #{transportModeCode,jdbcType=VARCHAR},
        transport_mode_name = #{transportModeName,jdbcType=VARCHAR},
        transport_partner_no = #{transportPartnerNo,jdbcType=VARCHAR},
        conveyance_code = #{conveyanceCode,jdbcType=VARCHAR},
        conveyance_name = #{conveyanceName,jdbcType=VARCHAR},
        departure = #{departure,jdbcType=VARCHAR},
        arrival = #{arrival,jdbcType=VARCHAR},
        source_type = #{sourceType,jdbcType=VARCHAR},
        creation_date = #{creationDate,jdbcType=TIMESTAMP},
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        version_value = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.dfp.carrier.infrastructure.po.CarrierDataPO">
        update dfp_carrier_data
        <set>
            <if test="item.carrierId != null">
                carrier_id = #{item.carrierId,jdbcType=INTEGER},
            </if>
            <if test="item.organizationCode != null and item.organizationCode != ''">
                organization_code = #{item.organizationCode,jdbcType=VARCHAR},
            </if>
            <if test="item.ebsOrgId != null and item.ebsOrgId != ''">
                ebs_org_id = #{item.ebsOrgId,jdbcType=VARCHAR},
            </if>
            <if test="item.carrierName != null and item.carrierName != ''">
                carrier_name = #{item.carrierName,jdbcType=VARCHAR},
            </if>
            <if test="item.carrierCode != null and item.carrierCode != ''">
                carrier_code = #{item.carrierCode,jdbcType=VARCHAR},
            </if>
            <if test="item.carrierAddr != null and item.carrierAddr != ''">
                carrier_addr = #{item.carrierAddr,jdbcType=VARCHAR},
            </if>
            <if test="item.carrierDuns != null and item.carrierDuns != ''">
                carrier_duns = #{item.carrierDuns,jdbcType=VARCHAR},
            </if>
            <if test="item.carrierContact != null and item.carrierContact != ''">
                carrier_contact = #{item.carrierContact,jdbcType=VARCHAR},
            </if>
            <if test="item.carrierPhone != null and item.carrierPhone != ''">
                carrier_phone = #{item.carrierPhone,jdbcType=VARCHAR},
            </if>
            <if test="item.transportModeCode != null and item.transportModeCode != ''">
                transport_mode_code = #{item.transportModeCode,jdbcType=VARCHAR},
            </if>
            <if test="item.transportModeName != null and item.transportModeName != ''">
                transport_mode_name = #{item.transportModeName,jdbcType=VARCHAR},
            </if>
            <if test="item.transportPartnerNo != null and item.transportPartnerNo != ''">
                transport_partner_no = #{item.transportPartnerNo,jdbcType=VARCHAR},
            </if>
            <if test="item.conveyanceCode != null and item.conveyanceCode != ''">
                conveyance_code = #{item.conveyanceCode,jdbcType=VARCHAR},
            </if>
            <if test="item.conveyanceName != null and item.conveyanceName != ''">
                conveyance_name = #{item.conveyanceName,jdbcType=VARCHAR},
            </if>
            <if test="item.departure != null and item.departure != ''">
                departure = #{item.departure,jdbcType=VARCHAR},
            </if>
            <if test="item.arrival != null and item.arrival != ''">
                arrival = #{item.arrival,jdbcType=VARCHAR},
            </if>
            <if test="item.sourceType != null and item.sourceType != ''">
                source_type = #{item.sourceType,jdbcType=VARCHAR},
            </if>
            <if test="item.creationDate != null">
                creation_date = #{item.creationDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lastUpdateDate != null">
                last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update dfp_carrier_data
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="carrier_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.carrierId,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="organization_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.organizationCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="ebs_org_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.ebsOrgId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="carrier_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.carrierName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="carrier_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.carrierCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="carrier_addr = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.carrierAddr,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="carrier_duns = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.carrierDuns,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="carrier_contact = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.carrierContact,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="carrier_phone = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.carrierPhone,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transport_mode_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transportModeCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transport_mode_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transportModeName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transport_partner_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transportPartnerNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="conveyance_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.conveyanceCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="conveyance_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.conveyanceName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="departure = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.departure,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="arrival = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.arrival,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="source_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sourceType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creation_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creationDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="last_update_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lastUpdateDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update dfp_carrier_data 
        <set>
            <if test="item.carrierId != null">
                carrier_id = #{item.carrierId,jdbcType=INTEGER},
            </if>
            <if test="item.organizationCode != null and item.organizationCode != ''">
                organization_code = #{item.organizationCode,jdbcType=VARCHAR},
            </if>
            <if test="item.ebsOrgId != null and item.ebsOrgId != ''">
                ebs_org_id = #{item.ebsOrgId,jdbcType=VARCHAR},
            </if>
            <if test="item.carrierName != null and item.carrierName != ''">
                carrier_name = #{item.carrierName,jdbcType=VARCHAR},
            </if>
            <if test="item.carrierCode != null and item.carrierCode != ''">
                carrier_code = #{item.carrierCode,jdbcType=VARCHAR},
            </if>
            <if test="item.carrierAddr != null and item.carrierAddr != ''">
                carrier_addr = #{item.carrierAddr,jdbcType=VARCHAR},
            </if>
            <if test="item.carrierDuns != null and item.carrierDuns != ''">
                carrier_duns = #{item.carrierDuns,jdbcType=VARCHAR},
            </if>
            <if test="item.carrierContact != null and item.carrierContact != ''">
                carrier_contact = #{item.carrierContact,jdbcType=VARCHAR},
            </if>
            <if test="item.carrierPhone != null and item.carrierPhone != ''">
                carrier_phone = #{item.carrierPhone,jdbcType=VARCHAR},
            </if>
            <if test="item.transportModeCode != null and item.transportModeCode != ''">
                transport_mode_code = #{item.transportModeCode,jdbcType=VARCHAR},
            </if>
            <if test="item.transportModeName != null and item.transportModeName != ''">
                transport_mode_name = #{item.transportModeName,jdbcType=VARCHAR},
            </if>
            <if test="item.transportPartnerNo != null and item.transportPartnerNo != ''">
                transport_partner_no = #{item.transportPartnerNo,jdbcType=VARCHAR},
            </if>
            <if test="item.conveyanceCode != null and item.conveyanceCode != ''">
                conveyance_code = #{item.conveyanceCode,jdbcType=VARCHAR},
            </if>
            <if test="item.conveyanceName != null and item.conveyanceName != ''">
                conveyance_name = #{item.conveyanceName,jdbcType=VARCHAR},
            </if>
            <if test="item.departure != null and item.departure != ''">
                departure = #{item.departure,jdbcType=VARCHAR},
            </if>
            <if test="item.arrival != null and item.arrival != ''">
                arrival = #{item.arrival,jdbcType=VARCHAR},
            </if>
            <if test="item.sourceType != null and item.sourceType != ''">
                source_type = #{item.sourceType,jdbcType=VARCHAR},
            </if>
            <if test="item.creationDate != null">
                creation_date = #{item.creationDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lastUpdateDate != null">
                last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from dfp_carrier_data where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from dfp_carrier_data where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
