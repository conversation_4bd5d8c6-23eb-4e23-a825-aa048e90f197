package com.yhl.scp.dfp.deliveryLog.convertor;

import com.yhl.scp.dfp.deliveryLog.domain.entity.DeliveryPlanPublishedLogDO;
import com.yhl.scp.dfp.deliveryLog.dto.DeliveryPlanPublishedLogDTO;
import com.yhl.scp.dfp.deliveryLog.infrastructure.po.DeliveryPlanPublishedLogPO;
import com.yhl.scp.dfp.deliveryLog.vo.DeliveryPlanPublishedLogVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>DeliveryPlanPublishedLogConvertor</code>
 * <p>
 * 发货计划发布追踪表转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-20 17:22:04
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DeliveryPlanPublishedLogConvertor {

    DeliveryPlanPublishedLogConvertor INSTANCE = Mappers.getMapper(DeliveryPlanPublishedLogConvertor.class);

    DeliveryPlanPublishedLogDO dto2Do(DeliveryPlanPublishedLogDTO obj);

    DeliveryPlanPublishedLogDTO do2Dto(DeliveryPlanPublishedLogDO obj);

    List<DeliveryPlanPublishedLogDO> dto2Dos(List<DeliveryPlanPublishedLogDTO> list);

    List<DeliveryPlanPublishedLogDTO> do2Dtos(List<DeliveryPlanPublishedLogDO> list);

    DeliveryPlanPublishedLogVO do2Vo(DeliveryPlanPublishedLogDO obj);

    DeliveryPlanPublishedLogVO po2Vo(DeliveryPlanPublishedLogPO obj);

    List<DeliveryPlanPublishedLogVO> po2Vos(List<DeliveryPlanPublishedLogPO> list);

    DeliveryPlanPublishedLogPO do2Po(DeliveryPlanPublishedLogDO obj);

    DeliveryPlanPublishedLogDO po2Do(DeliveryPlanPublishedLogPO obj);

    DeliveryPlanPublishedLogPO dto2Po(DeliveryPlanPublishedLogDTO obj);

    List<DeliveryPlanPublishedLogPO> dto2Pos(List<DeliveryPlanPublishedLogDTO> obj);

}
