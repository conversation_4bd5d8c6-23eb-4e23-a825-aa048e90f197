package com.yhl.scp.dfp.delivery.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanPublishedCompareDayDTO;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanPublishedCompareDayService;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedCompareDayVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedCompareDayVO2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>DeliveryPlanPublishedCompareDayController</code>
 * <p>
 * 发货计划按天对比表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-13 14:14:16
 */
@Slf4j
@Api(tags = "发货计划按天对比表控制器")
@RestController
@RequestMapping("deliveryPlanPublishedCompareDay")
public class DeliveryPlanPublishedCompareDayController extends BaseController {

    @Resource
    private DeliveryPlanPublishedCompareDayService deliveryPlanPublishedCompareDayService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<DeliveryPlanPublishedCompareDayVO>> page() {
        List<DeliveryPlanPublishedCompareDayVO> deliveryPlanPublishedCompareDayList = deliveryPlanPublishedCompareDayService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<DeliveryPlanPublishedCompareDayVO> pageInfo = new PageInfo<>(deliveryPlanPublishedCompareDayList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody DeliveryPlanPublishedCompareDayDTO deliveryPlanPublishedCompareDayDTO) {
        return deliveryPlanPublishedCompareDayService.doCreate(deliveryPlanPublishedCompareDayDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody DeliveryPlanPublishedCompareDayDTO deliveryPlanPublishedCompareDayDTO) {
        return deliveryPlanPublishedCompareDayService.doUpdate(deliveryPlanPublishedCompareDayDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        deliveryPlanPublishedCompareDayService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<DeliveryPlanPublishedCompareDayVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, deliveryPlanPublishedCompareDayService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "变更通知查询")
    @GetMapping(value = "deliveryPlanPublishedCompareDayView")
    public BaseResponse<List<DeliveryPlanPublishedCompareDayVO2>> deliveryPlanPublishedCompareDayView() {
        List<DeliveryPlanPublishedCompareDayVO2> deliveryPlanPublishedCompareDayList = deliveryPlanPublishedCompareDayService.deliveryPlanPublishedCompareDayView();
        return BaseResponse.success(BaseResponse.OP_SUCCESS, deliveryPlanPublishedCompareDayList);
    }

}
