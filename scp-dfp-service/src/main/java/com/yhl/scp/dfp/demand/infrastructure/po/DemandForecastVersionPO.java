package com.yhl.scp.dfp.demand.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;

/**
 * <code>DemandForecastVersionPO</code>
 * <p>
 * 业务预测版本PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:46:57
 */
public class DemandForecastVersionPO extends BasePO implements Serializable {

    private static final long serialVersionUID = -23978470504271058L;

    /**
     * 计划周期
     */
    private String planPeriod;
    /**
     * 计划跨度
     */
    private Integer planHorizon;
    /**
     * 颗粒度
     */
    private String planGranularity;
    /**
     * 版本号
     */
    private String versionCode;
    /**
     * 父版本ID
     */
    private String parentVersionId;
    /**
     * 主机厂编码
     */
    private String oemCode;
    /**
     * 关联滚动预测版本
     */
    private String rollingVersionId;
    /**
     * 关联预测算法版本
     */
    private String algorithmVersionId;
    /**
     * 生成方式
     */
    private String generateType;
    /**
     * 状态：未发布/已发布
     */
    private String versionStatus;

    public String getPlanPeriod() {
        return planPeriod;
    }

    public void setPlanPeriod(String planPeriod) {
        this.planPeriod = planPeriod;
    }

    public Integer getPlanHorizon() {
        return planHorizon;
    }

    public void setPlanHorizon(Integer planHorizon) {
        this.planHorizon = planHorizon;
    }

    public String getPlanGranularity() {
        return planGranularity;
    }

    public void setPlanGranularity(String planGranularity) {
        this.planGranularity = planGranularity;
    }

    public String getVersionCode() {
        return versionCode;
    }

    public void setVersionCode(String versionCode) {
        this.versionCode = versionCode;
    }

    public String getParentVersionId() {
        return parentVersionId;
    }

    public void setParentVersionId(String parentVersionId) {
        this.parentVersionId = parentVersionId;
    }

    public String getOemCode() {
        return oemCode;
    }

    public void setOemCode(String oemCode) {
        this.oemCode = oemCode;
    }

    public String getRollingVersionId() {
        return rollingVersionId;
    }

    public void setRollingVersionId(String rollingVersionId) {
        this.rollingVersionId = rollingVersionId;
    }

    public String getAlgorithmVersionId() {
        return algorithmVersionId;
    }

    public void setAlgorithmVersionId(String algorithmVersionId) {
        this.algorithmVersionId = algorithmVersionId;
    }

    public String getGenerateType() {
        return generateType;
    }

    public void setGenerateType(String generateType) {
        this.generateType = generateType;
    }

    public String getVersionStatus() {
        return versionStatus;
    }

    public void setVersionStatus(String versionStatus) {
        this.versionStatus = versionStatus;
    }

}
