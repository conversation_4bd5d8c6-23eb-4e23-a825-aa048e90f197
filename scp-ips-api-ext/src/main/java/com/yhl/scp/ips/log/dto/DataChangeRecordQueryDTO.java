package com.yhl.scp.ips.log.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ApiModel(value = "log_data_change_record查询请求参数")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DataChangeRecordQueryDTO extends BaseDTO implements Serializable {

    @ApiModelProperty(value = "表名")
    private String dataTableName;
    /**
     * 数据ID
     */
    @ApiModelProperty(value = "数据ID")
    private String primaryId;
}
