package com.yhl.scp.ips.log.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@ApiModel(value = "VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataChangeRecordVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -35575487900012889L;

    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    @FieldInterpretation(value = "表名")
    private String dataTableName;
    /**
     * 数据ID
     */
    @ApiModelProperty(value = "数据ID")
    @FieldInterpretation(value = "数据ID")
    private String primaryId;
    /**
     * 修改前数据
     */
    @ApiModelProperty(value = "修改前数据")
    @FieldInterpretation(value = "修改前数据")
    private String beforeData;
    /**
     * 修改后数据
     */
    @ApiModelProperty(value = "修改后数据")
    @FieldInterpretation(value = "修改后数据")
    private String afterData;
    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    @FieldInterpretation(value = "操作人")
    private String operateUser;
    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    @FieldInterpretation(value = "操作类型")
    private String operateType;

    @Override
    public void clean() {

    }
}
