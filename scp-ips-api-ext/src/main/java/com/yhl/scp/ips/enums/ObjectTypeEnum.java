package com.yhl.scp.ips.enums;

import com.yhl.platform.common.enums.CommonEnum2;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <code>ObjectTypeEnum</code>
 * <p>
 * 业务对象枚举
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-07-20 13:12:01
 */
public enum ObjectTypeEnum implements CommonEnum2 {

    /**
     * 场景
     */
    // SCENARIO("auth_scenario", "场景", "com.yhl.scp.ips.system.vo.DemandPriorityVO"),
    /**
     * Excel配置组
     */
    // EXCEL_CONFIG_GROUP("exl_excel_config_group", "Excel配置组", "com.yhl.scp.ips.excel.vo.ExcelConfigGroupVO"),
    /**
     * Excel样式配置
     */
    // EXCEL_STYLE_CONFIG("exl_excel_style_config", "Excel样式配置", "com.yhl.scp.ips.excel.vo.ExcelStyleConfigVO"),
    /**
     * Excel外键配置
     */
    // EXCEL_FOREIGN_KEY_CONFIG("exl_excel_foreign_key_config", "Excel外键配置", "com.yhl.scp.ips.excel.vo.ExcelForeignKeyConfigVO"),
    /**
     * Excel对象配置
     */
    // EXCEL_OBJECT_CONFIG("exl_excel_object_config", "Excel对象配置", "com.yhl.scp.ips.excel.vo.ExcelObjectConfigVO"),
    /**
     * Excel字段配置
     */
    // EXCEL_FIELD_CONFIG("exl_excel_field_config", "Excel字段配置", "com.yhl.scp.ips.excel.vo.ExcelFieldConfigVO"),
    /**
     * 数据权限映射
     */
    // DATA_PERMISSION_MAPPER("auth_rbac_data_permission_mapper", "数据权限映射", "com.yhl.scp.ips.rbac.vo.DataPermissionMapperVO"),
    /**
     * 数据权限规则
     */
    // DATA_PERMISSION_RULE("auth_rbac_data_permission_rule", "数据权限规则", "com.yhl.scp.ips.rbac.vo.DataPermissionRuleVO"),
    /**
     * 数据权限配置
     */
    DATA_PERMISSION_CONFIG("auth_rbac_data_permission_config", "数据权限配置", "com.yhl.scp.ips.rbac.vo.DataPermissionConfigVO"),
    /**
     * 数据权限配置明细
     */
    DATA_PERMISSION_DETAIL("auth_rbac_data_permission_detail", "数据权限配置明细", "com.yhl.scp.ips.rbac.vo.DataPermissionDetailVO"),
    /**
     * 数据权限配置关联对象
     */
    DATA_PERMISSION_RELATION("auth_rbac_data_permission_relation", "数据权限配置关联对象", "com.yhl.scp.ips.rbac.vo.DataPermissionRelationVO"),

    /**
     * 角色任务
     */
    ROLE_TASK("log_role_task", "角色任务", "com.yhl.scp.ips.log.vo.RoleTaskVO"),

    /**
     * 用户任务
     */
    USER_TASK("log_user_task", "用户任务", "com.yhl.scp.ips.log.vo.UserTaskVO"),

    /**
     * 用户任务历史
     */
    USER_TASK_HISTORY("LOG_USER_TASK_HISTORY", "用户任务历史", "com.yhl.scp.ips.log.vo.UserTaskHistoryVO"),
    /**
     * redis key管理
     */
    REDIS_KEY_MANAGE("REDIS_KEY_MANAGE", "rediskey管理", "com.yhl.scp.ips.redisKeyManage.vo.RedisKeyManageVO"),

    /**
     * 系统登录日志
     */
    // LOGIN_LOG("v_sys_login_log", "系统登录日志", "com.yhl.scp.ips.system.vo.LoginLogVO"),
    ;

    ObjectTypeEnum(String code, String desc, String mappingValue) {
        this.code = code;
        this.desc = desc;
        this.mappingValue = mappingValue;
    }

    /**
     * 表名 / 视图名
     */
    private String code;

    /**
     * 描述
     */
    private String desc;

    /**
     * 映射类
     */
    private String mappingValue;

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    void setCode(String code) {
        this.code = code;
    }

    void setDesc(String desc) {
        this.desc = desc;
    }

    @Override
    public String getMappingValue() {
        return mappingValue;
    }

    void setMappingValue(String mappingValue) {
        this.mappingValue = mappingValue;
    }

    /**
     * 对象类型表名映射
     */
    public static final Map<String, String> OBJECT_TABLE_MAP = Arrays.stream(ObjectTypeEnum.values()).sequential()
            .collect(Collectors.toMap(ObjectTypeEnum::name, ObjectTypeEnum::getCode, (t1, t2) -> t2));

}