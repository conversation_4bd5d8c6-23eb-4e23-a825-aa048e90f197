package com.yhl.scp.ips.log.service;

import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.ips.log.dto.DataChangeRecordDTO;
import com.yhl.scp.ips.log.dto.DataChangeRecordQueryDTO;
import com.yhl.scp.ips.log.vo.DataChangeRecordVO;
import org.springframework.data.domain.Page;

import java.util.List;

public interface DataChangeRecordService extends BaseService<DataChangeRecordDTO, DataChangeRecordVO> {

    /**
     * 查询所有
     *
     * @return list {@link DataChangeRecordVO}
     */
    List<DataChangeRecordVO> selectAll();

    void handle(String data);

    Page<DataChangeRecordDTO> selectByPage(Pagination pagination, DataChangeRecordQueryDTO queryDTO);
}
