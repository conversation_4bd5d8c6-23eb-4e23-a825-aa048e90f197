import pandas as pd
import numpy as np
from datetime import timedelta, datetime
import logging


class DataProcessor:
    def __init__(self, reader):
        # Use the DataReader object to get the raw data
        self.data_history_order = reader.data_history_order
        self.data_passenger_car_market_info = reader.data_passenger_car_market_info
        self.data_project_mapping = reader.data_project_mapping
        self.data_oem_project_info = reader.data_oem_project_info
        self.data_oem_stock_info = reader.data_oem_stock_info
        self.data_market_info = reader.data_market_info
        self.data_policy_info = reader.data_policy_info
        self.data_oem_forecast = reader.data_oem_forecast
        self.data_competition_group = reader.data_competition_group
        self.data_oem_vehicle_model = reader.data_oem_vehicle_model
        self.forecast_periods = range(1, 14)

    def sale_data_process(self):
        # 将仓库收发货数据（historyOrder.csv）按月份进行统计
        logging.info("start sale_data_process")
        df_history_order = pd.DataFrame()
        df_history_order = pd.concat([df_history_order, self.data_history_order])
        df_history_order['createDate'] = pd.to_datetime(df_history_order['createDate'], errors='coerce')

        # 添加字段年、月
        df_history_order['年'] = df_history_order['createDate'].dt.year
        df_history_order['月'] = df_history_order['createDate'].dt.month
        df_history_order['日'] = df_history_order['createDate'].dt.day

        # 读取数据时间跨度
        curr_date = df_history_order['createDate'].max()
        last_date = df_history_order['createDate'].max()
        last_date = last_date - pd.DateOffset(days=last_date.day)
        first_date = df_history_order['createDate'].min()
        st_year = first_date.year
        st_month = first_date.month  # 数据开始月份
        en_year = last_date.year
        en_month = last_date.month  # 数据结束月份

        # 选取前n种item进行分析处理
        top_n = 1000
        # 可以使用 start_date 作为时间筛选条件，提取过去一年内的数据记录
        start_date = last_date - pd.DateOffset(years=5)
        item_list = df_history_order[df_history_order['createDate'] >= start_date].value_counts('itemCode').head(top_n).index

        item_list = list(map(lambda x: str(x), item_list))

        # 记录各年哪些月份有数据出现
        if en_year - st_year == 0:
            time_ser = [str(st_year) + str(m).zfill(2) for m in range(st_month, en_month + 1)]
        else:
            time_ser = [str(st_year) + str(m).zfill(2) for m in range(st_month, 13)]
            for y in range(st_year + 1, en_year):
                time_ser = time_ser + [str(y) + str(m).zfill(2) for m in range(1, 13)]
            time_ser = time_ser + [str(en_year) + str(m).zfill(2) for m in range(1, en_month + 1)]

        history_sales = pd.DataFrame()
        history_sales['itemCode'] = item_list
        history_sales[time_ser] = 0.0

        df_history_order = df_history_order[df_history_order['itemCode'].isin(item_list)]
        df_history_order_sum = df_history_order.groupby(['itemCode', '年', '月'])['sumQty'].sum()

        for ind, val in df_history_order_sum.items():
            month = str(ind[1]) + str(ind[2]).zfill(2)
            if month in time_ser:
                row_ind = item_list.index(ind[0])
                col_ind = time_ser.index(str(ind[1]) + str(ind[2]).zfill(2)) + 1
                history_sales.iloc[row_ind, col_ind] = val

        # # 去掉2024年5月这一列
        # history_sales = history_sales.iloc[:, :-1]

        self.df_history_order_sum = df_history_order_sum
        self.item_list = item_list
        self.df_history_order = df_history_order
        self.history_sales = history_sales
        self.st_year = st_year
        self.en_year = en_year
        self.time_ser = time_ser

        history_sales = history_sales.rename(columns={history_sales.columns[0]: 'itemCode'})
        history_sales['itemCode'] = history_sales['itemCode'].astype(str)

        range_cum_lt = list()
        for i in range(1, 6):
            cum_days = i * 5
            order_in_range = df_history_order[df_history_order['日'] <= cum_days]
            range_cum = order_in_range[['itemCode', '年', '月', 'sumQty']].groupby(['itemCode', '年', '月']).sum()
            range_cum.columns = [cum_days]
            range_cum_lt.append(range_cum)
        range_cum_df = pd.concat(range_cum_lt, axis=1)
        range_cum_df['itemCode'] = range_cum_df.index.get_level_values(0)
        range_cum_df['年'] = range_cum_df.index.get_level_values(1)
        range_cum_df['月'] = range_cum_df.index.get_level_values(2)
        range_cum_df['curr_time'] = range_cum_df.apply(
            lambda r: str(r['年']) + str(r['月']).zfill(2)
            , axis=1
        )
        range_cum_df.index = pd.MultiIndex.from_frame(range_cum_df[['itemCode', 'curr_time']])
        range_cum_df.columns.name = 'day_passed'
        range_cum_df = range_cum_df[[i*5 for i in range(1, 6)]].fillna(0)
        range_cum_unstack = range_cum_df.T.unstack()
        coming_month_sales_history_df = range_cum_unstack.index.to_frame()
        coming_month_sales_history_df['coming_month_qty'] = range_cum_unstack
        coming_month_sales_history_df.index = range(len(coming_month_sales_history_df))

        order_in_coming_month = df_history_order[df_history_order['createDate'] > last_date]
        coming_month_sales_df = order_in_coming_month[['itemCode', 'sumQty']].groupby(['itemCode']).sum()
        coming_month_sales_df.columns = ['coming_month_qty']
        coming_month_sales_df['itemCode'] = coming_month_sales_df.index
        coming_month_sales_df['day_passed'] = curr_date.day
        coming_month_sales_df.index = range(len(coming_month_sales_df))

        return history_sales, coming_month_sales_history_df, coming_month_sales_df

    def other_data_process(self):
        logging.info("start other_data_process")
        # 为df_history_order添加“'globalProjectCode'”列（根据 `projectCode` 和 `oemCode` 进行合并）
        df_merged_history_order = pd.merge(self.df_history_order, self.data_project_mapping, on=['projectCode', 'oemCode'], how='left')
        df_itemcode_mapping = df_merged_history_order[['itemCode', 'projectCode', 'oemCode', 'globalProjectCode']].drop_duplicates()

        self.df_merged_history_order = df_merged_history_order
        self.df_itemcode_mapping = df_itemcode_mapping

        def merge_and_transfer_to_feature_data(df_info_name, df_related_feature_data_name, df_info, itemcode_projectcode_mapping):
            df_info['date'] = pd.to_datetime(df_info['date'], errors='coerce')
            # 添加字段年、月
            df_info['年'] = df_info['date'].dt.year
            df_info['月'] = df_info['date'].dt.month
            # df_info‘itemCode’列（根据 `globalProjectCode`进行合并）
            df_info = pd.merge(df_info, itemcode_projectcode_mapping[['globalProjectCode', 'itemCode']],
                               on='globalProjectCode', how='left')

            df_info = df_info[df_info['itemCode'].isin(self.item_list)]
            df_info_sum = df_info.groupby(['itemCode', '年', '月'])['qty'].sum()

            df_related_feature_data = pd.DataFrame()
            df_related_feature_data['itemCode'] = self.item_list
            df_related_feature_data[self.time_ser] = 0.0

            for ind, val in df_info_sum.items():
                time_str = str(ind[1]) + str(ind[2]).zfill(2)
                if time_str in self.time_ser:
                    row_ind = self.item_list.index(ind[0])
                    col_ind = self.time_ser.index(time_str) + 1
                    df_related_feature_data.iloc[row_ind, col_ind] = val

            # 动态设置self属性名称为df_info_name
            setattr(self, df_info_name, df_info)
            setattr(self, df_related_feature_data_name, df_related_feature_data)

            return df_related_feature_data

        df_passenger_car_market_info = self.data_passenger_car_market_info.copy()
        df_market_feature_data = merge_and_transfer_to_feature_data('df_passenger_car_market_info',
                                                                    'df_market_feature_data',
                                                                    df_passenger_car_market_info,
                                                                    df_itemcode_mapping)

        df_merged_stock_order = pd.merge(self.data_oem_stock_info, self.data_project_mapping,
                                         on=['projectCode', 'oemCode'], how='left')
        df_oem_stock_feature_data = merge_and_transfer_to_feature_data('df_merged_stock_order',
                                                                       'df_oem_stock_feature_data',
                                                                       df_merged_stock_order,
                                                                       df_itemcode_mapping)

        self.df_merged_stock_order = df_merged_stock_order
        self.df_oem_stock_feature_data = df_oem_stock_feature_data
        self.df_market_feature_data = df_market_feature_data

        df_market_feature_data = df_market_feature_data.rename(columns={df_market_feature_data.columns[0]: 'itemCode'})
        df_oem_stock_feature_data = df_oem_stock_feature_data.rename(columns={df_market_feature_data.columns[0]: 'itemCode'})
        df_market_feature_data['itemCode'] = df_market_feature_data['itemCode'].astype(str)
        df_oem_stock_feature_data['itemCode'] = df_oem_stock_feature_data['itemCode'].astype(str)

        return df_market_feature_data, df_oem_stock_feature_data

    def other_class_process(self):
        logging.info("start other_class_process")
        if self.data_market_info is not None:
            market_info_exists = True
            df_market_info = self.data_market_info.copy()
        else:
            market_info_exists = False
            df_market_info = pd.DataFrame()
        if self.data_policy_info is not None:
            policy_info_exists = True
            df_policy_info = self.data_policy_info.copy()
        else:
            policy_info_exists = False
            df_policy_info = pd.DataFrame()
        if self.data_oem_project_info is not None:
            oem_project_info_exists = True
            df_oem_project_info = self.data_oem_project_info.copy()
        else:
            oem_project_info_exists = False
            df_oem_project_info = pd.DataFrame()
        if self.data_competition_group is not None:
            data_competition_group = self.data_competition_group.copy()
        else:
            data_competition_group = pd.DataFrame()
        if self.data_oem_vehicle_model is not None:
            data_oem_vehicle_model = self.data_oem_vehicle_model.copy()
        else:
            data_oem_vehicle_model = pd.DataFrame()

        # 计算revisedStartDate
        def revise_start_date(date):
            if date.day > 15:
                if date.month == 12:
                    return pd.Timestamp(year=date.year + 1, month=date.month % 12 + 1, day=1)
                return pd.Timestamp(year=date.year, month=date.month % 12 + 1, day=1)
            else:
                return date

        # 来计算revisedEndDate
        def revise_end_date(date):
            if date.day < 15:
                # 获取当前月份的最后一天
                next_month = date + pd.offsets.MonthEnd(-1)
                return next_month
            else:
                return date

        def merge_and_transfer_to_feature_data(df_class_info_name, df_related_class_feature_data_name, df_class_info, itemcode_mapping):
            # 为类型信息加上itemCode维度
            # 1. 处理projectCode非空的情况
            df_non_null = df_class_info[df_class_info['projectCode'].notna()]
            df_non_null_merged = pd.merge(df_non_null,
                                          itemcode_mapping[['itemCode', 'projectCode', 'oemCode']],
                                          on=['projectCode', 'oemCode'],
                                          how='left')

            # 2. 处理projectCode为空的情况，只基于oemCode合并
            df_null = df_class_info[df_class_info['projectCode'].isna()]
            df_null_merged = pd.merge(df_null.drop(columns=['projectCode']),
                                      itemcode_mapping[['itemCode', 'oemCode']].drop_duplicates(),
                                      on='oemCode',
                                      how='left')

            df_class_info = pd.concat([df_non_null_merged, df_null_merged], ignore_index=True)

            df_class_info['startDate'] = pd.to_datetime(df_class_info['startDate'], errors='coerce')
            df_class_info['endDate'] = pd.to_datetime(df_class_info['endDate'], errors='coerce')
            # 填充endDate为空的情况，设置为当前日期
            df_class_info['endDate'] = df_class_info['endDate'].fillna(pd.Timestamp.today())

            # 生成新的列
            df_class_info['revisedStartDate'] = df_class_info['startDate'].apply(revise_start_date)
            df_class_info['revisedEndDate'] = df_class_info['endDate'].apply(revise_end_date)

            # 提取年月
            df_class_info['startYearMonth'] = df_class_info['revisedStartDate'].dt.to_period('M')
            df_class_info['endYearMonth'] = df_class_info['revisedEndDate'].dt.to_period('M')

            # 将eventType转换为类别型数据并编码
            df_class_info['eventType'] = df_class_info['eventType'].astype('category')
            df_class_info['eventTypeCode'] = df_class_info['eventType'].cat.codes + 1  # +1，避免编码从0开始

            df_related_class_feature_data = pd.DataFrame('missing', index=self.item_list, columns=self.time_ser)

            for ind, row in df_class_info.iterrows():
                item = row['itemCode']
                start_period = row['startYearMonth']  # 假设是字符串格式
                end_period = row['endYearMonth']  # 假设是字符串格式
                event_code = row['eventTypeCode']

                # 将 Period 转为字符串 '%Y%m'
                start_str = start_period.strftime('%Y%m')
                end_str = end_period.strftime('%Y%m')

                if item in self.item_list:
                    row_ind = self.item_list.index(item)
                else:
                    continue

                # 遍历time_ser，确保横轴一致
                for col_ind, date_str in enumerate(self.time_ser):
                    if start_str <= date_str <= end_str:
                        df_related_class_feature_data.iloc[row_ind, col_ind] = event_code
                    else:
                        df_related_class_feature_data.iloc[row_ind, col_ind] = 0  # 在范围外填充0

                # 动态设置self属性名称为df_class_info_name
                setattr(self, df_class_info_name, df_class_info)
                setattr(self, df_related_class_feature_data_name, df_related_class_feature_data)

            return df_related_class_feature_data.reset_index()

        if market_info_exists:
            df_market_class_feature_data = merge_and_transfer_to_feature_data('df_market_info',
                                                                    'df_market_class_feature_data',
                                                                    df_market_info,
                                                                    self.df_itemcode_mapping
                                                                    )
            # 给第一列命名为 'itemCode'
            df_market_class_feature_data.rename(columns={df_market_class_feature_data.columns[0]: 'itemCode'},
                                                inplace=True)
            df_market_class_feature_data['itemCode'] = df_market_class_feature_data['itemCode'].astype(str)
        else:
            df_market_class_feature_data = pd.DataFrame()

        if policy_info_exists:
            df_policy_class_feature_data = merge_and_transfer_to_feature_data('df_policy_info',
                                                                          'df_policy_class_feature_data',
                                                                          df_policy_info,
                                                                          self.df_itemcode_mapping)

            df_policy_class_feature_data.rename(columns={df_policy_class_feature_data.columns[0]: 'itemCode'}, inplace=True)
            df_policy_class_feature_data['itemCode'] = df_policy_class_feature_data['itemCode'].astype(str)
        else:
            df_policy_class_feature_data = pd.DataFrame()

        self.df_market_class_feature_data = df_market_class_feature_data
        self.df_policy_class_feature_data = df_policy_class_feature_data

        def oem_project_info_to_feature_data(df_oem_project_info_name, df_oem_project_feature_data_name, df_oem_project_info):

            df_oem_project_info['sopDate'] = pd.to_datetime(df_oem_project_info['sopDate'], errors='coerce')
            df_oem_project_info['eopDate'] = pd.to_datetime(df_oem_project_info['eopDate'], errors='coerce')

            # 生成新的列
            df_oem_project_info['revisedSopDate'] = df_oem_project_info['sopDate'].apply(revise_start_date)
            df_oem_project_info['revisedEopDate'] = df_oem_project_info['eopDate'].apply(revise_end_date)

            # 提取年月
            df_oem_project_info['startYearMonth'] = df_oem_project_info['revisedSopDate'].dt.to_period('M')
            df_oem_project_info['endYearMonth'] = df_oem_project_info['revisedEopDate'].dt.to_period('M')

            df_oem_project_feature_data = pd.DataFrame('missing', index=self.item_list, columns=self.time_ser)

            for ind, row in df_oem_project_info.iterrows():
                item = row['itemCode']
                start_period = row['startYearMonth']  # 假设是字符串格式
                end_period = row['endYearMonth']  # 假设是字符串格式

                if item in self.item_list:
                    row_ind = self.item_list.index(item)
                else:
                    continue

                # 遍历time_ser，确保横轴一致
                if pd.isna(start_period) and pd.isna(end_period):
                    df_oem_project_feature_data.iloc[row_ind] = '测试环节'
                elif pd.isna(start_period):
                    if end_period <= pd.Period(pd.Timestamp.today(), 'M'):
                        # end_period前填充量产阶段，end_period后为退市阶段
                        for col_ind, date_str in enumerate(self.time_ser):
                            date_period = pd.Period(date_str, 'M')
                            if date_period <= end_period:
                                df_oem_project_feature_data.iloc[row_ind, col_ind] = '量产阶段'
                            else:
                                df_oem_project_feature_data.iloc[row_ind, col_ind] = '退市阶段'
                    else:
                        df_oem_project_feature_data.iloc[row_ind] = '测试环节'
                elif pd.isna(end_period):
                    # 根据start_period确认测试、爬坡、量产
                    for col_ind, date_str in enumerate(self.time_ser):
                        date_period = pd.Period(date_str, 'M')
                        if date_period < start_period:
                            df_oem_project_feature_data.iloc[row_ind, col_ind] = '测试环节'
                        elif start_period <= date_period <= start_period + 2:
                            df_oem_project_feature_data.iloc[row_ind, col_ind] = '爬坡阶段'
                        else:
                            df_oem_project_feature_data.iloc[row_ind, col_ind] = '量产阶段'
                else:
                    for col_ind, date_str in enumerate(self.time_ser):
                        date_period = pd.Period(date_str, 'M')  # 将日期字符串转为 Period 类型
                        if date_period < start_period:
                            df_oem_project_feature_data.iloc[row_ind, col_ind] = '测试环节'
                            # df_oem_project_feature_data.iloc[row_ind, col_ind] = 0
                        elif start_period <= date_period <= start_period + 2:
                            df_oem_project_feature_data.iloc[row_ind, col_ind] = '爬坡阶段'
                            # df_oem_project_feature_data.iloc[row_ind, col_ind] = 1
                        elif start_period + 2 < date_period <= end_period:
                            df_oem_project_feature_data.iloc[row_ind, col_ind] = '量产阶段'
                            # df_oem_project_feature_data.iloc[row_ind, col_ind] = 2
                        elif date_period > end_period:
                            df_oem_project_feature_data.iloc[row_ind, col_ind] = '退市阶段'
                            # df_oem_project_feature_data.iloc[row_ind, col_ind] = 3
                # 动态设置self属性名称为df_class_info_name
                setattr(self, df_oem_project_info_name, df_oem_project_info)
                setattr(self, df_oem_project_feature_data_name, df_oem_project_feature_data)

            return df_oem_project_feature_data.reset_index()

        df_oem_project_feature_data = oem_project_info_to_feature_data('df_oem_project_info',
                                                                          'df_policy_class_feature_data',
                                                                       df_oem_project_info)
        # 给第一列命名为 'itemCode'
        df_oem_project_feature_data.rename(columns={df_oem_project_feature_data.columns[0]: 'itemCode'}, inplace=True)
        df_oem_project_feature_data['itemCode'] = df_oem_project_feature_data['itemCode'].astype(str)

        self.df_oem_project_feature_data = df_oem_project_feature_data


        data_oem_vehicle_model['vehicleModelPrice'] = data_oem_vehicle_model['vehicleModelPrice'].fillna('0-0')
        data_oem_vehicle_model['vehicleType'] = data_oem_vehicle_model['vehicleType'].fillna('')
        data_oem_vehicle_model['vehicleAvgPrice'] = data_oem_vehicle_model['vehicleModelPrice'].apply(
            lambda x: np.mean([float(i) for i in x.split('-')]))
        data_competition_group['type'] = data_competition_group['type'].fillna('')
        data_competition_group['priceUpperBound'] = data_competition_group['priceUpperBound'].fillna(1e9)

        def match_group(r):
            for i, g in data_competition_group.iterrows():
                if (
                        r['vehicleType'] == g['type'] and
                        g['priceLowerBound'] <= r['vehicleAvgPrice'] < g['priceUpperBound']
                ):
                    return g['group']
            return ''

        data_oem_vehicle_model['competitionGroup'] = data_oem_vehicle_model.apply(match_group, axis=1)
        project_group_map = {r['oemVehicleModelCode']: r['competitionGroup']
                             for i, r in data_oem_vehicle_model.iterrows()}
        item_project_map = {r['itemCode']: r['projectCode'] for i, r in self.df_history_order.iterrows()}
        item_group_map = {k: project_group_map.get(v, 'missing') for k, v in item_project_map.items()}

        def item_group_info_to_feature_data(item_group_map_name, df_item_group_feature_data_name, item_group_map):

            df_item_group_feature_data = pd.DataFrame('missing', index=self.item_list, columns=self.time_ser)
            for item, group in item_group_map.items():
                df_item_group_feature_data.loc[item] = group

            setattr(self, item_group_map_name, item_group_map)
            setattr(self, df_item_group_feature_data_name, df_item_group_feature_data)

            return df_item_group_feature_data.reset_index()

        df_item_group_feature_data = item_group_info_to_feature_data(
            'item_group_map',
            'df_item_group_feature_data',
            item_group_map
        )

        df_item_group_feature_data.rename(columns={df_item_group_feature_data.columns[0]: 'itemCode'}, inplace=True)
        df_item_group_feature_data['itemCode'] = df_item_group_feature_data['itemCode'].astype(str)

        self.item_group_map = item_group_map
        self.df_item_group_feature_data = df_item_group_feature_data

        def oem_project_start_feature_data(df_oem_project_info_name, df_oem_project_feature_data_name, df_oem_project_info):

            df_oem_project_info['sopDate'] = pd.to_datetime(df_oem_project_info['sopDate'], errors='coerce')
            df_oem_project_info['eopDate'] = pd.to_datetime(df_oem_project_info['eopDate'], errors='coerce')

            # 生成新的列
            df_oem_project_info['revisedSopDate'] = df_oem_project_info['sopDate'].apply(revise_start_date)
            df_oem_project_info['revisedEopDate'] = df_oem_project_info['eopDate'].apply(revise_end_date)

            # 提取年月
            df_oem_project_info['startYearMonth'] = df_oem_project_info['revisedSopDate'].dt.to_period('M')
            df_oem_project_info['endYearMonth'] = df_oem_project_info['revisedEopDate'].dt.to_period('M')

            df_oem_project_feature_data = pd.DataFrame(np.nan, index=self.item_list, columns=self.time_ser)

            for ind, row in df_oem_project_info.iterrows():
                item = row['itemCode']
                start_period = row['startYearMonth']  # 假设是字符串格式

                if item in self.item_list:
                    row_ind = self.item_list.index(item)
                else:
                    continue

                # 遍历time_ser，确保横轴一致
                if pd.isna(start_period):
                    start_period = pd.Period(self.time_ser[0], 'M')

                for col_ind, date_str in enumerate(self.time_ser):
                    date_period = pd.Period(date_str, 'M')  # 将日期字符串转为 Period 类型
                    df_oem_project_feature_data.iloc[row_ind, col_ind] = (date_period - start_period).n

                            # df_oem_project_feature_data.iloc[row_ind, col_ind] = 3
                # 动态设置self属性名称为df_class_info_name

            return df_oem_project_feature_data.reset_index()

        df_oem_project_start_feature_data = oem_project_start_feature_data(
            'df_oem_project_info',
            'df_orem_project_start_feature_data',
            df_oem_project_info
        )
        # 给第一列命名为 'itemCode'
        df_oem_project_start_feature_data.rename(columns={df_oem_project_start_feature_data.columns[0]: 'itemCode'}, inplace=True)
        df_oem_project_start_feature_data['itemCode'] = df_oem_project_start_feature_data['itemCode'].astype(str)

        self.df_oem_project_start_feature_data = df_oem_project_start_feature_data

        return (
            df_market_class_feature_data,
            df_policy_class_feature_data,
            df_oem_project_feature_data,
            df_item_group_feature_data,
            df_oem_project_start_feature_data
        )
    
    def time_process(self):
        logging.info("start time_process")
        data = pd.DataFrame()
        data['itemCode']  = self.item_list
        data[self.time_ser] = 0.0
        for t in self.time_ser:
            data.loc[:, t] = t[4:]
        return data

    def oem_product_process(self):
        logging.info("start oem_product_process")
        duplicates = self.df_itemcode_mapping[self.df_itemcode_mapping.duplicated(subset=['itemCode'], keep=False)]
        # 仅保留第一次出现的itemCode的行，避免多重映射关系
        self.df_itemcode_mapping_new = self.df_itemcode_mapping.drop_duplicates(subset=['itemCode'])

        data_oem = self.df_itemcode_mapping_new.copy()
        data_oem = data_oem[data_oem['itemCode'].isin(self.item_list)]
        oem_info = {col: data_oem['oemCode'] for col in self.time_ser}
        data_oem = pd.concat([data_oem, pd.DataFrame(oem_info)], axis=1)

        data_project = self.df_itemcode_mapping_new.copy()

        project_info = {col: data_project['projectCode'] for col in self.time_ser}
        data_project = pd.concat([data_project, pd.DataFrame(project_info)], axis=1)
        data_project = data_project[data_project['itemCode'].isin(self.item_list)]
        return data_oem[['itemCode'] + self.time_ser], data_project[['itemCode'] + self.time_ser]
    
    def oem_forecast_process(self):
        logging.info("start oem_forecast_process")
        """
        单独处理主机厂预测数据
        return:处理后的dataframe
        """
        data_df = self.data_oem_forecast.copy()
        data_df['itemCode'] = data_df['productCode']
        data_df['foreSaleDate'] = pd.to_datetime(data_df['foreSaleDate'])
        data_df['modifyTime'] = pd.to_datetime(data_df['modifyTime'])
        data_df['foreSaleyear'] = data_df['foreSaleDate'].dt.year
        data_df['foreSalemonth'] = data_df['foreSaleDate'].dt.month
        data_df['modifyYear'] = data_df['modifyTime'].dt.year
        data_df['modifyMonth'] = data_df['modifyTime'].dt.month

        def to_curr_time(date):
            return str(date.year) + str(date.month).zfill(2)

        data_df['curr_time'] = data_df['foreSaleDate'].apply(to_curr_time)
        data_df['pre_period'] = (
                (data_df['foreSaleyear'] - data_df['modifyYear']) * 12
                + data_df['foreSalemonth'] - data_df['modifyMonth'] + 1
        )
        data_df['pre_period'] = data_df['pre_period'].apply(lambda x: 1 if x < 1 else x)
        data_df['feature_value'] = data_df['foreQty'].fillna(0.0)
        data_df = data_df.groupby(['itemCode', 'curr_time', 'pre_period']).apply(
            lambda df: df.sort_values(by='modifyTime').iloc[-1]
        )
        df_oem_forecast_data = data_df[['itemCode', 'curr_time', 'pre_period', 'feature_value']]
        df_oem_forecast_data.index = range(len(df_oem_forecast_data))
        self.df_oem_forecast_data = df_oem_forecast_data
        return df_oem_forecast_data
    
    def oem_project_info_future_data_process(self):
        """
        构造并输出主机厂车型信息未来的特征（时间长度根据预测长度决定）
        return:pd.DataFrame：列为itemCode、pre_period、feature_value
        """
        logging.info("start oem_project_info_future_data_process")
        df_oem_project_info = self.data_oem_project_info.copy()
        # 计算revisedStartDate
        def revise_start_date(date):
            if date.day > 15:
                if date.month == 12:
                    return pd.Timestamp(year=date.year + 1, month=date.month % 12 + 1, day=1)
                return pd.Timestamp(year=date.year, month=date.month % 12 + 1, day=1)
            else:
                return date

        # 来计算revisedEndDate
        def revise_end_date(date):
            if date.day < 15:
                # 获取当前月份的最后一天
                next_month = date + pd.offsets.MonthEnd(-1)
                return next_month
            else:
                return date

        df_oem_project_info['sopDate'] = pd.to_datetime(df_oem_project_info['sopDate'], errors='coerce')
        df_oem_project_info['eopDate'] = pd.to_datetime(df_oem_project_info['eopDate'], errors='coerce')

        # 生成新的列
        df_oem_project_info['revisedSopDate'] = df_oem_project_info['sopDate'].apply(revise_start_date)
        df_oem_project_info['revisedEopDate'] = df_oem_project_info['eopDate'].apply(revise_end_date)

        # 提取年月
        df_oem_project_info['startYearMonth'] = df_oem_project_info['revisedSopDate'].dt.to_period('M')
        df_oem_project_info['endYearMonth'] = df_oem_project_info['revisedEopDate'].dt.to_period('M')

        last_year = int(self.time_ser[-1][:4])
        last_month = int(self.time_ser[-1][4:])
        future_time_ser = []
        for i in self.forecast_periods:
            last_month += 1
            if last_month > 12:
                last_month -= 12
                last_year += 1
            future_time_ser.append(str(last_year) + str(last_month).zfill(2))
        df_oem_project_feature_data = pd.DataFrame('missing', index=self.item_list, columns=future_time_ser)

        for ind, row in df_oem_project_info.iterrows():
            item = row['itemCode']
            start_period = row['startYearMonth']  # 假设是字符串格式
            end_period = row['endYearMonth']  # 假设是字符串格式

            if item in self.item_list:
                row_ind = self.item_list.index(item)
            else:
                continue

            # 遍历future_time_ser，确保横轴一致
            if pd.isna(start_period) and pd.isna(end_period):
                df_oem_project_feature_data.iloc[row_ind] = '测试环节'
            elif pd.isna(start_period):
                if end_period <= pd.Period(pd.Timestamp.today(), 'M'):
                    # end_period前填充量产阶段，end_period后为退市阶段
                    for col_ind, date_str in enumerate(future_time_ser):
                        date_period = pd.Period(date_str, 'M')
                        if date_period <= end_period:
                            df_oem_project_feature_data.iloc[row_ind, col_ind] = '量产阶段'
                        else:
                            df_oem_project_feature_data.iloc[row_ind, col_ind] = '退市阶段'
                else:
                    df_oem_project_feature_data.iloc[row_ind] = '测试环节'
            elif pd.isna(end_period):
                # 根据start_period确认测试、爬坡、量产
                for col_ind, date_str in enumerate(future_time_ser):
                    date_period = pd.Period(date_str, 'M')
                    if date_period < start_period:
                        df_oem_project_feature_data.iloc[row_ind, col_ind] = '测试环节'
                    elif start_period <= date_period <= start_period + 2:
                        df_oem_project_feature_data.iloc[row_ind, col_ind] = '爬坡阶段'
                    else:
                        df_oem_project_feature_data.iloc[row_ind, col_ind] = '量产阶段'
            else:
                for col_ind, date_str in enumerate(future_time_ser):
                    date_period = pd.Period(date_str, 'M')  # 将日期字符串转为 Period 类型
                    if date_period < start_period:
                        df_oem_project_feature_data.iloc[row_ind, col_ind] = '测试环节'
                    elif start_period <= date_period <= start_period + 2:
                        df_oem_project_feature_data.iloc[row_ind, col_ind] = '爬坡阶段'
                    elif start_period + 2 < date_period <= end_period:
                        df_oem_project_feature_data.iloc[row_ind, col_ind] = '量产阶段'
                    elif date_period > end_period:
                        df_oem_project_feature_data.iloc[row_ind, col_ind] = '退市阶段'

        # 再把以上生成的dataframe转化为预测时可拼接的dataframe
        df_list = []
        for i, item_code in enumerate(self.item_list):
            for m in self.forecast_periods:
                df_list.append(pd.DataFrame({'itemCode': [item_code],
                                             'pre_period': [m],
                                             'feature_value': [df_oem_project_feature_data.loc[item_code].iloc[m-1]]}
                                             ))
        
      
        return pd.concat(df_list, axis=0)

    def oem_project_start_info_future_data_process(self):
        """
        构造并输出主机厂车型信息未来的特征（时间长度根据预测长度决定）
        return:pd.DataFrame：列为itemCode、pre_period、feature_value
        """
        logging.info("start oem_project_start_info_future_data_process")
        df_oem_project_info = self.data_oem_project_info.copy()

        # 计算revisedStartDate
        def revise_start_date(date):
            if date.day > 15:
                if date.month == 12:
                    return pd.Timestamp(year=date.year + 1, month=date.month % 12 + 1, day=1)
                return pd.Timestamp(year=date.year, month=date.month % 12 + 1, day=1)
            else:
                return date

        # 来计算revisedEndDate
        def revise_end_date(date):
            if date.day < 15:
                # 获取当前月份的最后一天
                next_month = date + pd.offsets.MonthEnd(-1)
                return next_month
            else:
                return date

        df_oem_project_info['sopDate'] = pd.to_datetime(df_oem_project_info['sopDate'], errors='coerce')
        df_oem_project_info['eopDate'] = pd.to_datetime(df_oem_project_info['eopDate'], errors='coerce')

        # 生成新的列
        df_oem_project_info['revisedSopDate'] = df_oem_project_info['sopDate'].apply(revise_start_date)
        df_oem_project_info['revisedEopDate'] = df_oem_project_info['eopDate'].apply(revise_end_date)

        # 提取年月
        df_oem_project_info['startYearMonth'] = df_oem_project_info['revisedSopDate'].dt.to_period('M')
        df_oem_project_info['endYearMonth'] = df_oem_project_info['revisedEopDate'].dt.to_period('M')

        last_year = int(self.time_ser[-1][:4])
        last_month = int(self.time_ser[-1][4:])
        future_time_ser = []
        for i in self.forecast_periods:
            last_month += 1
            if last_month > 12:
                last_month -= 12
                last_year += 1
            future_time_ser.append(str(last_year) + str(last_month).zfill(2))
        df_oem_project_feature_data = pd.DataFrame(np.nan, index=self.item_list, columns=future_time_ser)

        for ind, row in df_oem_project_info.iterrows():
            item = row['itemCode']
            start_period = row['startYearMonth']  # 假设是字符串格式
            end_period = row['endYearMonth']  # 假设是字符串格式

            if item in self.item_list:
                row_ind = self.item_list.index(item)
            else:
                continue

            # 遍历future_time_ser，确保横轴一致
            if pd.isna(start_period):
                start_period = pd.Period(self.time_ser[0], 'M')

            for col_ind, date_str in enumerate(future_time_ser):
                date_period = pd.Period(date_str, 'M')  # 将日期字符串转为 Period 类型
                df_oem_project_feature_data.iloc[row_ind, col_ind] = (date_period - start_period).n

        # 再把以上生成的dataframe转化为预测时可拼接的dataframe
        df_list = []
        for i, item_code in enumerate(self.item_list):
            for m in self.forecast_periods:
                df_list.append(pd.DataFrame({'itemCode': [item_code],
                                             'pre_period': [m],
                                             'feature_value': [df_oem_project_feature_data.loc[item_code].iloc[0]]}
                                            ))

        return pd.concat(df_list, axis=0)

    def item_group_info_future_data_process(self):
        """
        构造并输出车型竞对信息
        return:pd.DataFrame：列为itemCode、pre_period、feature_value
        """
        logging.info("start item_group_info_future_data_process")
        df_list = []
        for i, item_code in enumerate(self.item_list):
            for m in self.forecast_periods:
                df_list.append(pd.DataFrame({'itemCode': [item_code],
                                             'pre_period': [m],
                                             'feature_value': [self.item_group_map.get(item_code, 'missing')]}
                                            ))

        return pd.concat(df_list, axis=0)

    def oem_forecast_future_process(self):
        """
        构造并输出主机厂预测的未来特征【预测时构造特征矩阵直接拼接】
        return:pd.DataFrame：列为itemCode、pre_period、feature_value
        """
        logging.info("start oem_forecast_future_process")
        data_df = self.df_oem_forecast_data.copy()

        last_year = int(self.time_ser[-1][:4])
        last_month = int(self.time_ser[-1][4:])
        future_time_ser = []
        for i in self.forecast_periods:
            last_month += 1
            if last_month > 12:
                last_month -= 12
                last_year += 1
            future_time_ser.append(str(last_year) + str(last_month).zfill(2))

        # 首先把预测时间在预测周期的行筛选出来并只保留最新一期预测（即pre_period最小的一行）
        data_df = data_df[data_df['curr_time'].isin(future_time_ser)]
        min_idx = data_df.groupby(['itemCode', 'curr_time'])['pre_period'].idxmin()
        data_df = data_df.loc[min_idx]

        # 还需根据curr_time匹配pre_period
        data_df['pre_period'] = data_df['curr_time'].apply(lambda x: future_time_ser.index(x) + 1)
        return data_df

    def time_future_process(self):
        """
        构造并输出预测所在月份时间的未来特征
        return:pd.Dataframe：列为itemCode、pre_period、feature_value
        """
        logging.info("start time_future_process")
        last_month = int(self.time_ser[-1][4:])
        df_list = []
        for m in self.forecast_periods:
            df_list.append(pd.DataFrame({'itemCode': self.item_list,
                                         'pre_period': [m] * len(self.item_list),
                                         'feature_value': [str((m+last_month-1)%12+1).zfill(2)] * len(self.item_list)}
                                             ))

        return pd.concat(df_list, axis=0)
        




