---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: bpim-xxx-service
  namespace: bpim-xxx-namespace
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bpim-xxx-service
  template:
    metadata:
      name: bpim-xxx-service
      labels:
        app: bpim-xxx-service
        tier: algorithm
      namespace: bpim-xxx-namespace
    spec:
      terminationGracePeriodSeconds: 240
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: app
                    operator: In
                    values: [ "bpim-xxx-service" ]
                  - key: tier
                    operator: In
                    values: [ "algorithm" ]
              topologyKey: "kubernetes.io/hostname"
      containers:
        - name: bpim-xxx-service
          image: bpim-xxx-image
          resources:
            requests:
              memory: "32Gi"
            limits:
              memory: "64Gi"
          imagePullPolicy: Always
          ports:
            - name: server-port
              containerPort: 8760
          livenessProbe:
            httpGet:
              path: /actuator/health
              port: 8760
            initialDelaySeconds: 600
            periodSeconds: 30
            timeoutSeconds: 10
            successThreshold: 1
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /actuator/health
              port: 8760
            initialDelaySeconds: 90
            periodSeconds: 10
            timeoutSeconds: 10
            successThreshold: 1
            failureThreshold: 3
          volumeMounts:
            - mountPath: /usr/local/dfp/workspace
              name: bpim-data-volume
              subPath: dfp/workspace
            - mountPath: /usr/local/mps/workspace
              name: bpim-data-volume
              subPath: mps/workspace
            - name: sw-agent
              mountPath: /path/to/sw-agent
            - name: bpim-data-volume
              mountPath: /path/to/bpim-data
      volumes:
        - name: sw-agent
          emptyDir: { }
        - name: bpim-data-volume
          persistentVolumeClaim:
            claimName: bpim-xxx-pvc
      imagePullSecrets:
        - name: harbor-registry
---
apiVersion: v1
kind: Service
metadata:
  namespace: bpim-xxx-namespace
  name: bpim-xxx-service
  labels:
    app: bpim-xxx-service
spec:
  ports:
    - port: 8760
      name: server
      targetPort: 8760
  selector:
    app: bpim-xxx-service