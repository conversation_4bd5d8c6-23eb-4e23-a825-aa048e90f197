<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mds.bom.infrastructure.dao.MdsProductBomDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mds.bom.infrastructure.po.ProductBomPO">
        <!--@Table mds_rou_product_bom-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="bom_version_id" jdbcType="VARCHAR" property="bomVersionId"/>
        <result column="row_num" jdbcType="INTEGER" property="rowNum"/>
        <result column="io_product_series_id" jdbcType="INTEGER" property="ioProductSeriesId"/>
        <result column="standard_step_id" jdbcType="VARCHAR" property="standardStepId"/>
        <result column="io_stock_point_id" jdbcType="VARCHAR" property="ioStockPointId"/>
        <result column="io_product_id" jdbcType="VARCHAR" property="ioProductId"/>
        <result column="io_type" jdbcType="VARCHAR" property="ioType"/>
        <result column="main_material" jdbcType="VARCHAR" property="mainMaterial"/>
        <result column="yield" jdbcType="VARCHAR" property="yield"/>
        <result column="scrap_strategy" jdbcType="VARCHAR" property="scrapStrategy"/>
        <result column="percentage_scrap_rate" jdbcType="VARCHAR" property="percentageScrapRate"/>
        <result column="scrap" jdbcType="VARCHAR" property="scrap"/>
        <result column="io_factor" jdbcType="VARCHAR" property="ioFactor"/>
        <result column="unit_production_cost" jdbcType="VARCHAR" property="unitProductionCost"/>
        <result column="unit_labor_cost" jdbcType="VARCHAR" property="unitLaborCost"/>
        <result column="unit_energy_cost" jdbcType="VARCHAR" property="unitEnergyCost"/>
        <result column="product_bom_allocation_id" jdbcType="VARCHAR" property="productBomAllocationId"/>
        <result column="alt_mode" jdbcType="VARCHAR" property="altMode"/>
        <result column="alt_ratio" jdbcType="VARCHAR" property="altRatio"/>
        <result column="alt_material_group" jdbcType="VARCHAR" property="altMaterialGroup"/>
        <result column="match_code" jdbcType="VARCHAR" property="matchCode"/>
        <result column="connection_task" jdbcType="VARCHAR" property="connectionTask"/>
        <result column="connection_type" jdbcType="VARCHAR" property="connectionType"/>
        <result column="max_connection_duration" jdbcType="INTEGER" property="maxConnectionDuration"/>
        <result column="min_connection_duration" jdbcType="INTEGER" property="minConnectionDuration"/>
        <result column="counting_unit_id" jdbcType="VARCHAR" property="countingUnitId"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="component_sequence_id" jdbcType="VARCHAR" property="componentSequenceId"/>
        <result column="wip_supply_type" jdbcType="VARCHAR" property="wipSupplyType"/>
        <result column="version_value" jdbcType="VARCHAR" property="versionValue"/>
        <result column="version_product_id" jdbcType="VARCHAR" property="versionProductId"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="last_update_date" jdbcType="TIMESTAMP" property="lastUpdateDate"/>
        <result column="io_product_sync_code" jdbcType="VARCHAR" property="ioProductSyncCode"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mds.bom.vo.ProductBomVO">
		<result column="bom_version" jdbcType="VARCHAR" property="bomVersion"/>
		<result column="product_code" jdbcType="VARCHAR" property="productCode"/>
		<result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="product_type" jdbcType="VARCHAR" property="productType"/>
        <result column="product_length" jdbcType="VARCHAR" property="productLength"/>
        <result column="product_width" jdbcType="VARCHAR" property="productWidth"/>
        <result column="product_area" jdbcType="VARCHAR" property="productArea"/>
		<result column="io_product_code" jdbcType="VARCHAR" property="ioProductCode"/>
		<result column="io_product_name" jdbcType="VARCHAR" property="ioProductName"/>
		<result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
		<result column="stock_point_name" jdbcType="VARCHAR" property="stockPointName"/>
		<result column="io_stock_point_code" jdbcType="VARCHAR" property="ioStockPointCode"/>
		<result column="io_stock_point_name" jdbcType="VARCHAR" property="ioStockPointName"/>
		<result column="standard_step_code" jdbcType="VARCHAR" property="standardStepCode"/>
		<result column="standard_step_name" jdbcType="VARCHAR" property="standardStepName"/>
		<result column="flag" jdbcType="VARCHAR" property="flag"/>
    </resultMap>
    <sql id="Base_Column_List">
		id,
		bom_version_id,
		row_num,
		io_product_series_id,
		standard_step_id,
		io_stock_point_id,
		io_product_id,
		io_type,
		main_material,
		yield,
		scrap_strategy,
		percentage_scrap_rate,
		scrap,
		io_factor,
		unit_production_cost,
		unit_labor_cost,
		unit_energy_cost,
		product_bom_allocation_id,
		alt_mode,
		alt_ratio,
		alt_material_group,
		match_code,
		connection_task,
		connection_type,
		max_connection_duration,
		min_connection_duration,
		counting_unit_id,
		remark,
		enabled,
		creator,
		create_time,
		modifier,
		modify_time,
		component_sequence_id,
		wip_supply_type,
		version_value,start_time,end_time,last_update_date,io_product_sync_code
    </sql>
    <sql id="VO_Column_List">
        <include refid="Base_Column_List" />,
        bom_version,
        product_code,
        product_name,
        product_type,
        product_length,
        product_width,
        product_area,
        io_product_code,
        io_product_name,
        io_stock_point_code,
        io_stock_point_name,
        standard_step_code,
        standard_step_name,
        flag
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.bomVersionId != null and params.bomVersionId != ''">
                and bom_version_id = #{params.bomVersionId,jdbcType=VARCHAR}
            </if>
            <if test="params.bomVersionIds != null and params.bomVersionIds != ''">
                and bom_version_id in
                <foreach collection="params.bomVersionIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.rowNum != null">
                and row_num = #{params.rowNum,jdbcType=INTEGER}
            </if>
            <if test="params.ioProductSeriesId != null">
                and io_product_series_id = #{params.ioProductSeriesId,jdbcType=INTEGER}
            </if>
            <if test="params.standardStepId != null and params.standardStepId != ''">
                and standard_step_id = #{params.standardStepId,jdbcType=VARCHAR}
            </if>
            <if test="params.ioStockPointId != null and params.ioStockPointId != ''">
                and io_stock_point_id = #{params.ioStockPointId,jdbcType=VARCHAR}
            </if>
            <if test="params.ioProductId != null and params.ioProductId != ''">
                and io_product_id = #{params.ioProductId,jdbcType=VARCHAR}
            </if>
            <if test="params.ioProductCode != null and params.ioProductCode != ''">
                and io_product_code = #{params.ioProductCode,jdbcType=VARCHAR}
            </if>
            <if test="params.ioProductCodes != null and params.ioProductCodes != ''">
                and io_product_code in
                <foreach collection="params.ioProductCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.ioProductIds != null and params.ioProductIds != ''">
                and io_product_id in
                <foreach collection="params.ioProductIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.ioProductIdIsNull != null and params.ioProductIdIsNull != ''">
                and io_product_id is null
            </if>
            <if test="params.ioType != null and params.ioType != ''">
                and io_type = #{params.ioType,jdbcType=VARCHAR}
            </if>
            <if test="params.mainMaterial != null and params.mainMaterial != ''">
                and main_material = #{params.mainMaterial,jdbcType=VARCHAR}
            </if>
            <if test="params.yield != null">
                and yield = #{params.yield,jdbcType=VARCHAR}
            </if>
            <if test="params.scrapStrategy != null and params.scrapStrategy != ''">
                and scrap_strategy = #{params.scrapStrategy,jdbcType=VARCHAR}
            </if>
            <if test="params.percentageScrapRate != null">
                and percentage_scrap_rate = #{params.percentageScrapRate,jdbcType=VARCHAR}
            </if>
            <if test="params.scrap != null">
                and scrap = #{params.scrap,jdbcType=VARCHAR}
            </if>
            <if test="params.ioFactor != null">
                and io_factor = #{params.ioFactor,jdbcType=VARCHAR}
            </if>
            <if test="params.unitProductionCost != null">
                and unit_production_cost = #{params.unitProductionCost,jdbcType=VARCHAR}
            </if>
            <if test="params.unitLaborCost != null">
                and unit_labor_cost = #{params.unitLaborCost,jdbcType=VARCHAR}
            </if>
            <if test="params.unitEnergyCost != null">
                and unit_energy_cost = #{params.unitEnergyCost,jdbcType=VARCHAR}
            </if>
            <if test="params.productBomAllocationId != null and params.productBomAllocationId != ''">
                and product_bom_allocation_id = #{params.productBomAllocationId,jdbcType=VARCHAR}
            </if>
            <if test="params.altMode != null and params.altMode != ''">
                and alt_mode = #{params.altMode,jdbcType=VARCHAR}
            </if>
            <if test="params.altRatio != null">
                and alt_ratio = #{params.altRatio,jdbcType=VARCHAR}
            </if>
            <if test="params.altMaterialGroup != null and params.altMaterialGroup != ''">
                and alt_material_group = #{params.altMaterialGroup,jdbcType=VARCHAR}
            </if>
            <if test="params.matchCode != null and params.matchCode != ''">
                and match_code = #{params.matchCode,jdbcType=VARCHAR}
            </if>
            <if test="params.connectionTask != null and params.connectionTask != ''">
                and connection_task = #{params.connectionTask,jdbcType=VARCHAR}
            </if>
            <if test="params.connectionType != null and params.connectionType != ''">
                and connection_type = #{params.connectionType,jdbcType=VARCHAR}
            </if>
            <if test="params.maxConnectionDuration != null">
                and max_connection_duration = #{params.maxConnectionDuration,jdbcType=INTEGER}
            </if>
            <if test="params.minConnectionDuration != null">
                and min_connection_duration = #{params.minConnectionDuration,jdbcType=INTEGER}
            </if>
            <if test="params.countingUnitId != null and params.countingUnitId != ''">
                and counting_unit_id = #{params.countingUnitId,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.componentSequenceId != null and params.componentSequenceId != ''">
                and component_sequence_id = #{params.componentSequenceId,jdbcType=VARCHAR}
            </if>
            <if test="params.componentSequenceIds != null and params.componentSequenceIds != ''">
                and component_sequence_id in
                <foreach collection="params.componentSequenceIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.wipSupplyType != null and params.wipSupplyType != ''">
                and wip_supply_type = #{params.wipSupplyType,jdbcType=VARCHAR}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=VARCHAR}
            </if>
            <if test="params.bomVersionIds != null and params.bomVersionIds != ''">
                and bom_version_id in
                <foreach collection="params.bomVersionIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.startTime != null">
                and start_time = #{params.startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.endTime != null">
                and end_time = #{params.endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.lastUpdateDate != null">
                and last_update_date = #{params.lastUpdateDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.ioProductSyncCode != null and params.ioProductSyncCode != ''">
                and io_product_sync_code = #{params.ioProductSyncCode,jdbcType=VARCHAR}
            </if>
            <if test="params.ioProductSyncCodeIsNotNull != null and params.ioProductSyncCodeIsNotNull != ''">
                and io_product_sync_code is not null
            </if>
            <if test="params.ioProductIdList != null and params.ioProductIdList != ''">
                and io_product_id in
                <foreach collection="params.ioProductIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_rou_product_bom
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_rou_product_bom
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_new_mds_rou_product_bom
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_rou_product_bom
        <include refid="Base_Where_Condition" />
    </select>

    <!-- 条件查询 VO -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List" />
        from v_new_mds_rou_product_bom
        <include refid="Base_Where_Condition" />
    </select>

    <select id="selectEveryFirstData" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM (
             SELECT
                 <include refid="Base_Column_List" />,
                 RANK() OVER (PARTITION BY `bom_version_id` ORDER BY `row_num` DESC) AS `rank`
             FROM
                 `scp_fysh`.`mds_rou_product_bom` mrpb
             where bom_version_id in
                 <foreach collection="bomVersionIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                 </foreach>
        ) AS `bomTable`
        WHERE
            `rank` = 1;
    </select>
    <select id="selectByBillBomVersionIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_rou_product_bom
        where enabled='YES'
            and bom_version_id in
        <foreach collection="bomVersionIds" item="bomVersionId" index="index" open="(" separator="," close=")">
            #{bomVersionId,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="selectByComponentSequenceIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_rou_product_bom
        where enabled='YES'
        and component_sequence_id in
        <foreach collection="componentSequenceIds" item="componentSequenceId" index="index" open="(" separator="," close=")">
            #{componentSequenceId,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mds.bom.infrastructure.po.ProductBomPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mds_rou_product_bom(
        id,
        bom_version_id,
        row_num,
        io_product_series_id,
        standard_step_id,
        io_stock_point_id,
        io_product_id,
        io_type,
        main_material,
        yield,
        scrap_strategy,
        percentage_scrap_rate,
        scrap,
        io_factor,
        unit_production_cost,
        unit_labor_cost,
        unit_energy_cost,
        product_bom_allocation_id,
        alt_mode,
        alt_ratio,
        alt_material_group,
        match_code,
        connection_task,
        connection_type,
        max_connection_duration,
        min_connection_duration,
        counting_unit_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        component_sequence_id,
        wip_supply_type,
        version_value,
        start_time,
        end_time,
        last_update_date,
        io_product_sync_code)
        values (
        #{id,jdbcType=VARCHAR},
        #{bomVersionId,jdbcType=VARCHAR},
        #{rowNum,jdbcType=INTEGER},
        #{ioProductSeriesId,jdbcType=INTEGER},
        #{standardStepId,jdbcType=VARCHAR},
        #{ioStockPointId,jdbcType=VARCHAR},
        #{ioProductId,jdbcType=VARCHAR},
        #{ioType,jdbcType=VARCHAR},
        #{mainMaterial,jdbcType=VARCHAR},
        #{yield,jdbcType=VARCHAR},
        #{scrapStrategy,jdbcType=VARCHAR},
        #{percentageScrapRate,jdbcType=VARCHAR},
        #{scrap,jdbcType=VARCHAR},
        #{ioFactor,jdbcType=VARCHAR},
        #{unitProductionCost,jdbcType=VARCHAR},
        #{unitLaborCost,jdbcType=VARCHAR},
        #{unitEnergyCost,jdbcType=VARCHAR},
        #{productBomAllocationId,jdbcType=VARCHAR},
        #{altMode,jdbcType=VARCHAR},
        #{altRatio,jdbcType=VARCHAR},
        #{altMaterialGroup,jdbcType=VARCHAR},
        #{matchCode,jdbcType=VARCHAR},
        #{connectionTask,jdbcType=VARCHAR},
        #{connectionType,jdbcType=VARCHAR},
        #{maxConnectionDuration,jdbcType=INTEGER},
        #{minConnectionDuration,jdbcType=INTEGER},
        #{countingUnitId,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{componentSequenceId,jdbcType=VARCHAR},
        #{wipSupplyType,jdbcType=VARCHAR},
        #{versionValue,jdbcType=VARCHAR},
        #{startTime,jdbcType=TIMESTAMP},
        #{endTime,jdbcType=TIMESTAMP},
        #{lastUpdateDate,jdbcType=TIMESTAMP},
        #{ioProductSyncCode,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mds.bom.infrastructure.po.ProductBomPO">
        insert into mds_rou_product_bom(
        id,
        bom_version_id,
        row_num,
        io_product_series_id,
        standard_step_id,
        io_stock_point_id,
        io_product_id,
        io_type,
        main_material,
        yield,
        scrap_strategy,
        percentage_scrap_rate,
        scrap,
        io_factor,
        unit_production_cost,
        unit_labor_cost,
        unit_energy_cost,
        product_bom_allocation_id,
        alt_mode,
        alt_ratio,
        alt_material_group,
        match_code,
        connection_task,
        connection_type,
        max_connection_duration,
        min_connection_duration,
        counting_unit_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        component_sequence_id,
        wip_supply_type,
        version_value,
        start_time,
        end_time,
        last_update_date,
        io_product_sync_code)
        values (
        #{id,jdbcType=VARCHAR},
        #{bomVersionId,jdbcType=VARCHAR},
        #{rowNum,jdbcType=INTEGER},
        #{ioProductSeriesId,jdbcType=INTEGER},
        #{standardStepId,jdbcType=VARCHAR},
        #{ioStockPointId,jdbcType=VARCHAR},
        #{ioProductId,jdbcType=VARCHAR},
        #{ioType,jdbcType=VARCHAR},
        #{mainMaterial,jdbcType=VARCHAR},
        #{yield,jdbcType=VARCHAR},
        #{scrapStrategy,jdbcType=VARCHAR},
        #{percentageScrapRate,jdbcType=VARCHAR},
        #{scrap,jdbcType=VARCHAR},
        #{ioFactor,jdbcType=VARCHAR},
        #{unitProductionCost,jdbcType=VARCHAR},
        #{unitLaborCost,jdbcType=VARCHAR},
        #{unitEnergyCost,jdbcType=VARCHAR},
        #{productBomAllocationId,jdbcType=VARCHAR},
        #{altMode,jdbcType=VARCHAR},
        #{altRatio,jdbcType=VARCHAR},
        #{altMaterialGroup,jdbcType=VARCHAR},
        #{matchCode,jdbcType=VARCHAR},
        #{connectionTask,jdbcType=VARCHAR},
        #{connectionType,jdbcType=VARCHAR},
        #{maxConnectionDuration,jdbcType=INTEGER},
        #{minConnectionDuration,jdbcType=INTEGER},
        #{countingUnitId,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{componentSequenceId,jdbcType=VARCHAR},
        #{wipSupplyType,jdbcType=VARCHAR},
        #{versionValue,jdbcType=VARCHAR},
        #{startTime,jdbcType=TIMESTAMP},
        #{endTime,jdbcType=TIMESTAMP},
        #{lastUpdateDate,jdbcType=TIMESTAMP},
        #{ioProductSyncCode,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mds_rou_product_bom(
        id,
        bom_version_id,
        row_num,
        io_product_series_id,
        standard_step_id,
        io_stock_point_id,
        io_product_id,
        io_type,
        main_material,
        yield,
        scrap_strategy,
        percentage_scrap_rate,
        scrap,
        io_factor,
        unit_production_cost,
        unit_labor_cost,
        unit_energy_cost,
        product_bom_allocation_id,
        alt_mode,
        alt_ratio,
        alt_material_group,
        match_code,
        connection_task,
        connection_type,
        max_connection_duration,
        min_connection_duration,
        counting_unit_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        component_sequence_id,
        wip_supply_type,
        version_value,
        start_time,
        end_time,
        last_update_date,
        io_product_sync_code)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.bomVersionId,jdbcType=VARCHAR},
        #{entity.rowNum,jdbcType=INTEGER},
        #{entity.ioProductSeriesId,jdbcType=INTEGER},
        #{entity.standardStepId,jdbcType=VARCHAR},
        #{entity.ioStockPointId,jdbcType=VARCHAR},
        #{entity.ioProductId,jdbcType=VARCHAR},
        #{entity.ioType,jdbcType=VARCHAR},
        #{entity.mainMaterial,jdbcType=VARCHAR},
        #{entity.yield,jdbcType=VARCHAR},
        #{entity.scrapStrategy,jdbcType=VARCHAR},
        #{entity.percentageScrapRate,jdbcType=VARCHAR},
        #{entity.scrap,jdbcType=VARCHAR},
        #{entity.ioFactor,jdbcType=VARCHAR},
        #{entity.unitProductionCost,jdbcType=VARCHAR},
        #{entity.unitLaborCost,jdbcType=VARCHAR},
        #{entity.unitEnergyCost,jdbcType=VARCHAR},
        #{entity.productBomAllocationId,jdbcType=VARCHAR},
        #{entity.altMode,jdbcType=VARCHAR},
        #{entity.altRatio,jdbcType=VARCHAR},
        #{entity.altMaterialGroup,jdbcType=VARCHAR},
        #{entity.matchCode,jdbcType=VARCHAR},
        #{entity.connectionTask,jdbcType=VARCHAR},
        #{entity.connectionType,jdbcType=VARCHAR},
        #{entity.maxConnectionDuration,jdbcType=INTEGER},
        #{entity.minConnectionDuration,jdbcType=INTEGER},
        #{entity.countingUnitId,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.componentSequenceId,jdbcType=VARCHAR},
        #{entity.wipSupplyType,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=VARCHAR},
        #{entity.startTime,jdbcType=TIMESTAMP},
        #{entity.endTime,jdbcType=TIMESTAMP},
        #{entity.lastUpdateDate,jdbcType=TIMESTAMP},
        #{entity.ioProductSyncCode,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mds_rou_product_bom(
        id,
        bom_version_id,
        row_num,
        io_product_series_id,
        standard_step_id,
        io_stock_point_id,
        io_product_id,
        io_type,
        main_material,
        yield,
        scrap_strategy,
        percentage_scrap_rate,
        scrap,
        io_factor,
        unit_production_cost,
        unit_labor_cost,
        unit_energy_cost,
        product_bom_allocation_id,
        alt_mode,
        alt_ratio,
        alt_material_group,
        match_code,
        connection_task,
        connection_type,
        max_connection_duration,
        min_connection_duration,
        counting_unit_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        component_sequence_id,
        wip_supply_type,
        version_value,
        start_time,
        end_time,
        last_update_date,
        io_product_sync_code)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.bomVersionId,jdbcType=VARCHAR},
        #{entity.rowNum,jdbcType=INTEGER},
        #{entity.ioProductSeriesId,jdbcType=INTEGER},
        #{entity.standardStepId,jdbcType=VARCHAR},
        #{entity.ioStockPointId,jdbcType=VARCHAR},
        #{entity.ioProductId,jdbcType=VARCHAR},
        #{entity.ioType,jdbcType=VARCHAR},
        #{entity.mainMaterial,jdbcType=VARCHAR},
        #{entity.yield,jdbcType=VARCHAR},
        #{entity.scrapStrategy,jdbcType=VARCHAR},
        #{entity.percentageScrapRate,jdbcType=VARCHAR},
        #{entity.scrap,jdbcType=VARCHAR},
        #{entity.ioFactor,jdbcType=VARCHAR},
        #{entity.unitProductionCost,jdbcType=VARCHAR},
        #{entity.unitLaborCost,jdbcType=VARCHAR},
        #{entity.unitEnergyCost,jdbcType=VARCHAR},
        #{entity.productBomAllocationId,jdbcType=VARCHAR},
        #{entity.altMode,jdbcType=VARCHAR},
        #{entity.altRatio,jdbcType=VARCHAR},
        #{entity.altMaterialGroup,jdbcType=VARCHAR},
        #{entity.matchCode,jdbcType=VARCHAR},
        #{entity.connectionTask,jdbcType=VARCHAR},
        #{entity.connectionType,jdbcType=VARCHAR},
        #{entity.maxConnectionDuration,jdbcType=INTEGER},
        #{entity.minConnectionDuration,jdbcType=INTEGER},
        #{entity.countingUnitId,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.componentSequenceId,jdbcType=VARCHAR},
        #{entity.wipSupplyType,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=VARCHAR},
        #{entity.startTime,jdbcType=TIMESTAMP},
        #{entity.endTime,jdbcType=TIMESTAMP},
        #{entity.lastUpdateDate,jdbcType=TIMESTAMP},
        #{entity.ioProductSyncCode,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mds.bom.infrastructure.po.ProductBomPO">
        update mds_rou_product_bom set
        bom_version_id = #{bomVersionId,jdbcType=VARCHAR},
        row_num = #{rowNum,jdbcType=INTEGER},
        io_product_series_id = #{ioProductSeriesId,jdbcType=INTEGER},
        standard_step_id = #{standardStepId,jdbcType=VARCHAR},
        io_stock_point_id = #{ioStockPointId,jdbcType=VARCHAR},
        io_product_id = #{ioProductId,jdbcType=VARCHAR},
        io_type = #{ioType,jdbcType=VARCHAR},
        main_material = #{mainMaterial,jdbcType=VARCHAR},
        yield = #{yield,jdbcType=VARCHAR},
        scrap_strategy = #{scrapStrategy,jdbcType=VARCHAR},
        percentage_scrap_rate = #{percentageScrapRate,jdbcType=VARCHAR},
        scrap = #{scrap,jdbcType=VARCHAR},
        io_factor = #{ioFactor,jdbcType=VARCHAR},
        unit_production_cost = #{unitProductionCost,jdbcType=VARCHAR},
        unit_labor_cost = #{unitLaborCost,jdbcType=VARCHAR},
        unit_energy_cost = #{unitEnergyCost,jdbcType=VARCHAR},
        product_bom_allocation_id = #{productBomAllocationId,jdbcType=VARCHAR},
        alt_mode = #{altMode,jdbcType=VARCHAR},
        alt_ratio = #{altRatio,jdbcType=VARCHAR},
        alt_material_group = #{altMaterialGroup,jdbcType=VARCHAR},
        match_code = #{matchCode,jdbcType=VARCHAR},
        connection_task = #{connectionTask,jdbcType=VARCHAR},
        connection_type = #{connectionType,jdbcType=VARCHAR},
        max_connection_duration = #{maxConnectionDuration,jdbcType=INTEGER},
        min_connection_duration = #{minConnectionDuration,jdbcType=INTEGER},
        counting_unit_id = #{countingUnitId,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        component_sequence_id = #{componentSequenceId,jdbcType=VARCHAR},
        wip_supply_type = #{wipSupplyType,jdbcType=VARCHAR},
        version_value = version_value + 1 ,
        start_time = #{startTime,jdbcType=TIMESTAMP},
        end_time = #{endTime,jdbcType=TIMESTAMP},
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
        io_product_sync_code = #{ioProductSyncCode,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mds.bom.infrastructure.po.ProductBomPO">
        update mds_rou_product_bom
        <set>
            <if test="item.bomVersionId != null and item.bomVersionId != ''">
                bom_version_id = #{item.bomVersionId,jdbcType=VARCHAR},
            </if>
            <if test="item.rowNum != null">
                row_num = #{item.rowNum,jdbcType=INTEGER},
            </if>
            <if test="item.ioProductSeriesId != null">
                io_product_series_id = #{item.ioProductSeriesId,jdbcType=INTEGER},
            </if>
            <if test="item.standardStepId != null and item.standardStepId != ''">
                standard_step_id = #{item.standardStepId,jdbcType=VARCHAR},
            </if>
            <if test="item.ioStockPointId != null and item.ioStockPointId != ''">
                io_stock_point_id = #{item.ioStockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.ioProductId != null and item.ioProductId != ''">
                io_product_id = #{item.ioProductId,jdbcType=VARCHAR},
            </if>
            <if test="item.ioType != null and item.ioType != ''">
                io_type = #{item.ioType,jdbcType=VARCHAR},
            </if>
            <if test="item.mainMaterial != null and item.mainMaterial != ''">
                main_material = #{item.mainMaterial,jdbcType=VARCHAR},
            </if>
            <if test="item.yield != null">
                yield = #{item.yield,jdbcType=VARCHAR},
            </if>
            <if test="item.scrapStrategy != null and item.scrapStrategy != ''">
                scrap_strategy = #{item.scrapStrategy,jdbcType=VARCHAR},
            </if>
            <if test="item.percentageScrapRate != null">
                percentage_scrap_rate = #{item.percentageScrapRate,jdbcType=VARCHAR},
            </if>
            <if test="item.scrap != null">
                scrap = #{item.scrap,jdbcType=VARCHAR},
            </if>
            <if test="item.ioFactor != null">
                io_factor = #{item.ioFactor,jdbcType=VARCHAR},
            </if>
            <if test="item.unitProductionCost != null">
                unit_production_cost = #{item.unitProductionCost,jdbcType=VARCHAR},
            </if>
            <if test="item.unitLaborCost != null">
                unit_labor_cost = #{item.unitLaborCost,jdbcType=VARCHAR},
            </if>
            <if test="item.unitEnergyCost != null">
                unit_energy_cost = #{item.unitEnergyCost,jdbcType=VARCHAR},
            </if>
            <if test="item.productBomAllocationId != null and item.productBomAllocationId != ''">
                product_bom_allocation_id = #{item.productBomAllocationId,jdbcType=VARCHAR},
            </if>
            <if test="item.altMode != null and item.altMode != ''">
                alt_mode = #{item.altMode,jdbcType=VARCHAR},
            </if>
            <if test="item.altRatio != null">
                alt_ratio = #{item.altRatio,jdbcType=VARCHAR},
            </if>
            <if test="item.altMaterialGroup != null and item.altMaterialGroup != ''">
                alt_material_group = #{item.altMaterialGroup,jdbcType=VARCHAR},
            </if>
            <if test="item.matchCode != null and item.matchCode != ''">
                match_code = #{item.matchCode,jdbcType=VARCHAR},
            </if>
            <if test="item.connectionTask != null and item.connectionTask != ''">
                connection_task = #{item.connectionTask,jdbcType=VARCHAR},
            </if>
            <if test="item.connectionType != null and item.connectionType != ''">
                connection_type = #{item.connectionType,jdbcType=VARCHAR},
            </if>
            <if test="item.maxConnectionDuration != null">
                max_connection_duration = #{item.maxConnectionDuration,jdbcType=INTEGER},
            </if>
            <if test="item.minConnectionDuration != null">
                min_connection_duration = #{item.minConnectionDuration,jdbcType=INTEGER},
            </if>
            <if test="item.countingUnitId != null and item.countingUnitId != ''">
                counting_unit_id = #{item.countingUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.componentSequenceId != null and item.componentSequenceId != ''">
                component_sequence_id = #{item.componentSequenceId,jdbcType=VARCHAR},
            </if>
            <if test="item.wipSupplyType != null and item.wipSupplyType != ''">
                wip_supply_type = #{item.wipSupplyType,jdbcType=VARCHAR},
            </if>
            <if test="item.startTime != null">
                start_time = #{item.startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.endTime != null">
                end_time = #{item.endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lastUpdateDate != null">
                last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.ioProductSyncCode != null and item.ioProductSyncCode != ''">
                io_product_sync_code = #{item.ioProductSyncCode,jdbcType=VARCHAR},
            </if>
            version_value = version_value + 1,
        </set>
        where id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mds_rou_product_bom
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="bom_version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.bomVersionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="row_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.rowNum,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="io_product_series_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.ioProductSeriesId,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="standard_step_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.standardStepId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="io_stock_point_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.ioStockPointId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="io_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.ioProductId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="io_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.ioType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="main_material = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.mainMaterial,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="yield = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.yield,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="scrap_strategy = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.scrapStrategy,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="percentage_scrap_rate = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.percentageScrapRate,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="scrap = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.scrap,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="io_factor = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.ioFactor,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="unit_production_cost = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.unitProductionCost,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="unit_labor_cost = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.unitLaborCost,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="unit_energy_cost = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.unitEnergyCost,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_bom_allocation_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productBomAllocationId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="alt_mode = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.altMode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="alt_ratio = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.altRatio,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="alt_material_group = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.altMaterialGroup,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="match_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.matchCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="connection_task = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.connectionTask,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="connection_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.connectionType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="max_connection_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.maxConnectionDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="min_connection_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.minConnectionDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="counting_unit_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.countingUnitId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="component_sequence_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.componentSequenceId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="wip_supply_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.wipSupplyType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.startTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="end_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.endTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="last_update_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lastUpdateDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="io_product_sync_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.ioProductSyncCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update mds_rou_product_bom
        <set>
            <if test="item.bomVersionId != null and item.bomVersionId != ''">
                bom_version_id = #{item.bomVersionId,jdbcType=VARCHAR},
            </if>
            <if test="item.rowNum != null">
                row_num = #{item.rowNum,jdbcType=INTEGER},
            </if>
            <if test="item.ioProductSeriesId != null">
                io_product_series_id = #{item.ioProductSeriesId,jdbcType=INTEGER},
            </if>
            <if test="item.standardStepId != null and item.standardStepId != ''">
                standard_step_id = #{item.standardStepId,jdbcType=VARCHAR},
            </if>
            <if test="item.ioStockPointId != null and item.ioStockPointId != ''">
                io_stock_point_id = #{item.ioStockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.ioProductId != null and item.ioProductId != ''">
                io_product_id = #{item.ioProductId,jdbcType=VARCHAR},
            </if>
            <if test="item.ioType != null and item.ioType != ''">
                io_type = #{item.ioType,jdbcType=VARCHAR},
            </if>
            <if test="item.mainMaterial != null and item.mainMaterial != ''">
                main_material = #{item.mainMaterial,jdbcType=VARCHAR},
            </if>
            <if test="item.yield != null">
                yield = #{item.yield,jdbcType=VARCHAR},
            </if>
            <if test="item.scrapStrategy != null and item.scrapStrategy != ''">
                scrap_strategy = #{item.scrapStrategy,jdbcType=VARCHAR},
            </if>
            <if test="item.percentageScrapRate != null">
                percentage_scrap_rate = #{item.percentageScrapRate,jdbcType=VARCHAR},
            </if>
            <if test="item.scrap != null">
                scrap = #{item.scrap,jdbcType=VARCHAR},
            </if>
            <if test="item.ioFactor != null">
                io_factor = #{item.ioFactor,jdbcType=VARCHAR},
            </if>
            <if test="item.unitProductionCost != null">
                unit_production_cost = #{item.unitProductionCost,jdbcType=VARCHAR},
            </if>
            <if test="item.unitLaborCost != null">
                unit_labor_cost = #{item.unitLaborCost,jdbcType=VARCHAR},
            </if>
            <if test="item.unitEnergyCost != null">
                unit_energy_cost = #{item.unitEnergyCost,jdbcType=VARCHAR},
            </if>
            <if test="item.productBomAllocationId != null and item.productBomAllocationId != ''">
                product_bom_allocation_id = #{item.productBomAllocationId,jdbcType=VARCHAR},
            </if>
            <if test="item.altMode != null and item.altMode != ''">
                alt_mode = #{item.altMode,jdbcType=VARCHAR},
            </if>
            <if test="item.altRatio != null">
                alt_ratio = #{item.altRatio,jdbcType=VARCHAR},
            </if>
            <if test="item.altMaterialGroup != null and item.altMaterialGroup != ''">
                alt_material_group = #{item.altMaterialGroup,jdbcType=VARCHAR},
            </if>
            <if test="item.matchCode != null and item.matchCode != ''">
                match_code = #{item.matchCode,jdbcType=VARCHAR},
            </if>
            <if test="item.connectionTask != null and item.connectionTask != ''">
                connection_task = #{item.connectionTask,jdbcType=VARCHAR},
            </if>
            <if test="item.connectionType != null and item.connectionType != ''">
                connection_type = #{item.connectionType,jdbcType=VARCHAR},
            </if>
            <if test="item.maxConnectionDuration != null">
                max_connection_duration = #{item.maxConnectionDuration,jdbcType=INTEGER},
            </if>
            <if test="item.minConnectionDuration != null">
                min_connection_duration = #{item.minConnectionDuration,jdbcType=INTEGER},
            </if>
            <if test="item.countingUnitId != null and item.countingUnitId != ''">
                counting_unit_id = #{item.countingUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.componentSequenceId != null and item.componentSequenceId != ''">
                component_sequence_id = #{item.componentSequenceId,jdbcType=VARCHAR},
            </if>
            <if test="item.wipSupplyType != null and item.wipSupplyType != ''">
                wip_supply_type = #{item.wipSupplyType,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.startTime != null">
                start_time = #{item.startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.endTime != null">
                end_time = #{item.endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lastUpdateDate != null">
                last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.ioProductSyncCode != null and item.ioProductSyncCode != ''">
                io_product_sync_code = #{item.ioProductSyncCode,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <update id="doLogicDeleteBatch">
        <foreach collection="deleteProductBomList" index="index" item="item" separator=";">
            update mds_rou_product_bom
            set
            enabled = 'NO',
            version_value = version_value + 1
            where id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from mds_rou_product_bom where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mds_rou_product_bom where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <select id="selectIoProductIdsByBomVersionId" resultType="java.lang.String">
        select io_product_id
        from v_mds_rou_product_bom
        where bom_version_id = #{bomVersionId}
    </select>

    <select id="selectComponentSequenceNotNull" resultMap="VOResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_rou_product_bom
        where component_sequence_id is not null
    </select>
</mapper>
