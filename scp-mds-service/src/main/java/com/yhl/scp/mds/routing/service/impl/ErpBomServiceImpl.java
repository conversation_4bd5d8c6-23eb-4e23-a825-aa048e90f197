package com.yhl.scp.mds.routing.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.extension.routing.dto.RoutingDTO;
import com.yhl.scp.mds.extension.routing.dto.RoutingStepDTO;
import com.yhl.scp.mds.newproduct.service.NewProductStockPointService;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.routing.convertor.ErpBomConvertor;
import com.yhl.scp.mds.routing.domain.entity.ErpBomDO;
import com.yhl.scp.mds.routing.domain.service.ErpBomDomainService;
import com.yhl.scp.mds.routing.dto.BomSyncDTO;
import com.yhl.scp.mds.routing.dto.ErpBomDTO;
import com.yhl.scp.mds.routing.infrastructure.dao.ErpBomDao;
import com.yhl.scp.mds.routing.infrastructure.po.ErpBomPO;
import com.yhl.scp.mds.routing.service.ErpBomService;
import com.yhl.scp.mds.routing.service.RoutingService;
import com.yhl.scp.mds.routing.service.RoutingStepService;
import com.yhl.scp.mds.routing.vo.ErpBomVO;
import com.yhl.scp.mds.stock.service.NewStockPointService;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <code>ErpBomServiceImpl</code>
 * <p>
 * erp同步bom主数据应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-26 15:06:06
 */
@Slf4j
@Service
public class ErpBomServiceImpl extends AbstractService implements ErpBomService {

    @Resource
    private ErpBomDao erpBomDao;

    @Resource
    private ErpBomDomainService erpBomDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private RoutingService routingService;
    @Resource
    private RoutingStepService routingStepService;

    @Resource
    private NewProductStockPointService productStockPointService;
    @Resource
    private NewStockPointService newStockPointService;

    private final static String yyyyMMdd="yyyy/MM/dd";
    @Override
    public BaseResponse<Void> doCreate(ErpBomDTO erpBomDTO) {
        // 0.数据转换
        ErpBomDO erpBomDO = ErpBomConvertor.INSTANCE.dto2Do(erpBomDTO);
        ErpBomPO erpBomPO = ErpBomConvertor.INSTANCE.dto2Po(erpBomDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        erpBomDomainService.validation(erpBomDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(erpBomPO);
        erpBomDao.insertWithPrimaryKey(erpBomPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(ErpBomDTO erpBomDTO) {
        // 0.数据转换
        ErpBomDO erpBomDO = ErpBomConvertor.INSTANCE.dto2Do(erpBomDTO);
        ErpBomPO erpBomPO = ErpBomConvertor.INSTANCE.dto2Po(erpBomDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        erpBomDomainService.validation(erpBomDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(erpBomPO);
        erpBomDao.update(erpBomPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<ErpBomDTO> list) {
        List<ErpBomPO> newList = ErpBomConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        erpBomDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<ErpBomDTO> list) {
        List<ErpBomPO> newList = ErpBomConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        erpBomDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return erpBomDao.deleteBatch(idList);
        }
        return erpBomDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public ErpBomVO selectByPrimaryKey(String id) {
        ErpBomPO po = erpBomDao.selectByPrimaryKey(id);
        return ErpBomConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mds_erp_bom")
    public List<ErpBomVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mds_erp_bom")
    public List<ErpBomVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<ErpBomVO> dataList = erpBomDao.selectByCondition(sortParam, queryCriteriaParam);
        ErpBomServiceImpl target = SpringBeanUtils.getBean(ErpBomServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<ErpBomVO> selectByParams(Map<String, Object> params) {
        List<ErpBomPO> list = erpBomDao.selectByParams(params);
        return ErpBomConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<ErpBomVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public void doSyncData(List<BomSyncDTO> bomSyncDTOList) {
        List<BomSyncDTO> headerList = bomSyncDTOList.stream().filter(e-> StringUtils.isEmpty(e.getRowId())).collect(Collectors.toList());
        Map<String, List<BomSyncDTO>> listMap = bomSyncDTOList.stream().filter(e -> StringUtils.isNotEmpty(e.getRowId())).collect(Collectors.groupingBy(BomSyncDTO::getHeaderId));
        List<String> productCodes = headerList.stream().map(BomSyncDTO::getProductCode).distinct().collect(Collectors.toList());
        List<NewProductStockPointVO> newProductStockPointVOS = productStockPointService.selectByProductCode(productCodes);
        HashMap<String, Object> map = new HashMap<>();
        map.put("stockPointCodes",newProductStockPointVOS.stream().map(NewProductStockPointVO::getStockPointCode).distinct().collect(Collectors.toList()));
        List<NewStockPointVO> newStockPointVOS = newStockPointService.selectByParams(map);
        Map<String, NewStockPointVO> stockMap = newStockPointVOS.stream().collect(Collectors.toMap(NewStockPointVO::getStockPointCode, (o -> o)));
        Map<String, NewProductStockPointVO> productMap = newProductStockPointVOS.stream().collect(Collectors.toMap(NewProductStockPointVO::getProductCode, (o -> o)));
        List<RoutingDTO> list = new ArrayList<>();
        List<RoutingStepDTO> stepList = new ArrayList<>();
        for (BomSyncDTO bomSyncDTO1 : headerList) {
            NewProductStockPointVO newProductStockPointVO = productMap.get(bomSyncDTO1.getProductCode());
            RoutingDTO routingDTO = new RoutingDTO();
            routingDTO.setId(UUIDUtil.getUUID());
            routingDTO.setProductId(newProductStockPointVO.getId());
            routingDTO.setRoutingCode(bomSyncDTO1.getProductCode()+"_RC");
            routingDTO.setStockPointId(stockMap.getOrDefault(newProductStockPointVO.getStockPointCode(),new NewStockPointVO()).getId());
            routingDTO.setRoutingHeadId(bomSyncDTO1.getHeaderId());
            routingDTO.setRoutingVersion(bomSyncDTO1.getVersionNo());
            routingDTO.setVersionEffectiveTime(DateUtils.stringToDate(bomSyncDTO1.getVersionValidDate(),yyyyMMdd));
            routingDTO.setLastUpdateTime(DateUtils.stringToDate(bomSyncDTO1.getLastUpdateTime(),DateUtils.COMMON_DATE_STR2));
            list.add(routingDTO);
            List<BomSyncDTO> value = listMap.getOrDefault(bomSyncDTO1.getHeaderId(),new ArrayList<>());
            for (BomSyncDTO bomSyncDTO : value) {
                RoutingStepDTO routingStepDTO=new RoutingStepDTO();
                routingStepDTO.setRoutingId(routingDTO.getId());
                routingStepDTO.setRoutingRowId(bomSyncDTO.getRowId());
                routingStepDTO.setSequenceNo(bomSyncDTO.getOperation());
                routingStepDTO.setLastUpdateTime(DateUtils.stringToDate(bomSyncDTO.getLastUpdateTime(),DateUtils.COMMON_DATE_STR2));
                routingStepDTO.setEffectiveBeginTime(DateUtils.stringToDate(bomSyncDTO.getValidFrom(),yyyyMMdd));
                routingStepDTO.setEffectiveEndTime(DateUtils.stringToDate(bomSyncDTO.getValidTo(),yyyyMMdd));
                stepList.add(routingStepDTO);
            }
        }
        if(!list.isEmpty()) {
            routingService.doCreateBatch(list);
        }
        if(!stepList.isEmpty()){
            routingStepService.doCreateBatch(stepList);
        }
        List<ErpBomPO> bomPOS = bomSyncDTOList.stream().map(e -> {
            ErpBomPO erpBomPO = new ErpBomPO();
            BeanUtils.copyProperties(e, erpBomPO);
            erpBomPO.setValidFrom(DateUtils.stringToDate(e.getValidFrom()));
            erpBomPO.setValidTo(DateUtils.stringToDate(e.getValidTo()));
            erpBomPO.setLastUpdateTime(DateUtils.stringToDate(e.getLastUpdateTime()));
            erpBomPO.setVersionValidDate(DateUtils.stringToDate(e.getVersionValidDate()));
            return erpBomPO;
        }).collect(Collectors.toList());
        BasePOUtils.insertBatchFiller(bomPOS);
        erpBomDao.insertBatch(bomPOS);
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.ERP_BOM.getCode();
    }

    @Override
    public List<ErpBomVO> invocation(List<ErpBomVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
