package com.yhl.scp.mds.routing.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.common.utils.BulkOperationUtils;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.baseResource.service.PhysicalResourceService;
import com.yhl.scp.mds.baseResource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.routing.convertor.NewRoutingStepResourceConvertor;
import com.yhl.scp.mds.routing.domain.entity.NewRoutingStepResourceDO;
import com.yhl.scp.mds.routing.domain.service.NewRoutingStepResourceDomainService;
import com.yhl.scp.mds.routing.dto.NewRoutingStepResourceDTO;
import com.yhl.scp.mds.routing.infrastructure.dao.NewProductCandidateResourceDao;
import com.yhl.scp.mds.routing.infrastructure.dao.NewRoutingStepResourceDao;
import com.yhl.scp.mds.routing.infrastructure.po.NewRoutingStepResourcePO;
import com.yhl.scp.mds.routing.infrastructure.po.ProductCandidateResourcePO;
import com.yhl.scp.mds.routing.service.NewRoutingService;
import com.yhl.scp.mds.routing.service.NewRoutingStepResourceService;
import com.yhl.scp.mds.routing.service.NewRoutingStepService;
import com.yhl.scp.mds.routing.vo.NewRoutingStepResourceVO;
import com.yhl.scp.mds.routing.vo.NewRoutingStepVO;
import com.yhl.scp.mds.routing.vo.NewRoutingVO;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;

import cn.hutool.core.date.StopWatch;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>NewRoutingStepResourceServiceImpl</code>
 * <p>
 * 新-生产路径步骤资源应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-20 14:58:54
 */
@Slf4j
@Service
public class NewRoutingStepResourceServiceImpl extends AbstractService implements NewRoutingStepResourceService {

	@Resource
	private NewRoutingStepResourceDao newRoutingStepResourceDao;

	@Resource
	private NewRoutingStepResourceDomainService newRoutingStepResourceDomainService;

	@Resource
	private NewProductCandidateResourceDao newProductCandidateResourceDao;

	@Resource
	private NewRoutingService newRoutingService;

	@Resource
	private NewRoutingStepService newRoutingStepService;

	@Resource
	private PhysicalResourceService physicalResourceService;

	@Override
	public BaseResponse<Void> doCreate(NewRoutingStepResourceDTO routingStepResourceDTO) {
		// 0.数据转换
		NewRoutingStepResourceDO routingStepResourceDO = NewRoutingStepResourceConvertor.INSTANCE.dto2Do(routingStepResourceDTO);
		NewRoutingStepResourcePO routingStepResourcePO = NewRoutingStepResourceConvertor.INSTANCE.dto2Po(routingStepResourceDTO);
		// 1.数据校验
		newRoutingStepResourceDomainService.validation(routingStepResourceDO);
		// 2.数据持久化
		BasePOUtils.insertFiller(routingStepResourcePO);
		newRoutingStepResourceDao.insertWithPrimaryKey(routingStepResourcePO);
		return BaseResponse.success(BaseResponse.OP_SUCCESS);
	}

	@Override
	public BaseResponse<Void> doUpdate(NewRoutingStepResourceDTO routingStepResourceDTO) {
		// 0.数据转换
		NewRoutingStepResourceDO routingStepResourceDO = NewRoutingStepResourceConvertor.INSTANCE.dto2Do(routingStepResourceDTO);
		NewRoutingStepResourcePO routingStepResourcePO = NewRoutingStepResourceConvertor.INSTANCE.dto2Po(routingStepResourceDTO);
		// 1.数据校验
		newRoutingStepResourceDomainService.validation(routingStepResourceDO);
		// 2.数据持久化
		BasePOUtils.updateFiller(routingStepResourcePO);
		newRoutingStepResourceDao.update(routingStepResourcePO);
		return BaseResponse.success(BaseResponse.OP_SUCCESS);
	}

	@Override
	public void doCreateBatch(List<NewRoutingStepResourceDTO> list) {
		List<NewRoutingStepResourcePO> newList = NewRoutingStepResourceConvertor.INSTANCE.dto2Pos(list);
		BasePOUtils.insertBatchFiller(newList);
		newRoutingStepResourceDao.insertBatchWithPrimaryKey(newList);
	}

	@Override
	public void doUpdateBatch(List<NewRoutingStepResourceDTO> list) {
		List<NewRoutingStepResourcePO> newList = NewRoutingStepResourceConvertor.INSTANCE.dto2Pos(list);
		BasePOUtils.updateBatchFiller(newList);
		newRoutingStepResourceDao.updateBatch(newList);
	}

	@Override
	public int doDelete(List<String> idList) {
		if (CollectionUtils.isEmpty(idList)) {
			return 0;
		}
		if (idList.size() > 1) {
			return newRoutingStepResourceDao.deleteBatch(idList);
		}
		return newRoutingStepResourceDao.deleteByPrimaryKey(idList.get(0));
	}

	@Override
	public NewRoutingStepResourceVO selectByPrimaryKey(String id) {
		NewRoutingStepResourcePO po = newRoutingStepResourceDao.selectByPrimaryKey(id);
		return NewRoutingStepResourceConvertor.INSTANCE.po2Vo(po);
	}

	@Override
	@Expression(value = "v_mds_rou_routing_step_resource")
	public List<NewRoutingStepResourceVO> selectByPage(Pagination pagination, String sortParam,
													   String queryCriteriaParam) {
		PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
		return this.selectByCondition(sortParam, queryCriteriaParam);
	}

	@Override
	@Expression(value = "v_mds_rou_routing_step_resource")
	public List<NewRoutingStepResourceVO> selectByCondition(String sortParam, String queryCriteriaParam) {
		List<NewRoutingStepResourceVO> dataList = newRoutingStepResourceDao.selectByCondition(sortParam, queryCriteriaParam);
		NewRoutingStepResourceServiceImpl target = SpringBeanUtils.getBean(NewRoutingStepResourceServiceImpl.class);
		return target.invocation(dataList, null, this.getInvocationName());
	}

	@Override
	public List<NewRoutingStepResourceVO> selectByParams(Map<String, Object> params) {
		List<NewRoutingStepResourcePO> list = newRoutingStepResourceDao.selectByParams(params);
		return NewRoutingStepResourceConvertor.INSTANCE.po2Vos(list);
	}

	@Override
	public List<NewRoutingStepResourceVO> selectAll() {
		return this.selectByParams(new HashMap<>(2));
	}

	@Override
	public String getObjectType() {
		return ObjectTypeEnum.NEW_ROUTING_STEP_RESOURCE.getCode();
	}

	@Override
	public List<NewRoutingStepResourceVO> invocation(List<NewRoutingStepResourceVO> dataList,
													 Map<String, Object> params, String invocation) {
		return dataList;
	}

	@Override
	public void doLogicDeleteBatchByRoutingIds(List<String> routingIds) {
		newRoutingStepResourceDao.doLogicDeleteBatchByRoutingIds(routingIds);
	}

	@Override
	public void doLogicDeleteBatchByRoutingStepIds(List<String> routingStepIds) {
		newRoutingStepResourceDao.doLogicDeleteBatchByRoutingStepIds(routingStepIds);
	}

	@Override
	public void doTransitionRoutingStepResource(List<String> routingStepResourceIds,
												List<NewRoutingVO> routingList,
												List<NewRoutingStepVO> newRoutingSteps,
												List<NewStockPointVO> stockPoints,
												String scenario) {

		ExecutorService executor = Executors.newFixedThreadPool(6);
		try {
			StopWatch stopWatch = new StopWatch("候选资源处理");
			// 获取候选资源数据
			stopWatch.start("查询出本次需要转换的候选资源数据");
			List<ProductCandidateResourcePO> productCandidateResourceList;
			if (CollectionUtils.isNotEmpty(routingStepResourceIds)) {
				productCandidateResourceList =
						newProductCandidateResourceDao.selectByPrimaryKeys(routingStepResourceIds);
			} else {
				productCandidateResourceList = newProductCandidateResourceDao.selectByParams(new HashMap<>(2));
			}
			if (CollectionUtils.isEmpty(productCandidateResourceList)) {
				return;
			}
			List<String> productIds = productCandidateResourceList.stream()
					.map(ProductCandidateResourcePO::getProductId).distinct().collect(Collectors.toList());
			stopWatch.stop();

			stopWatch.start("多线程查询资源和库存点数据");
			// 获取路径资源数据
			CompletableFuture<List<NewRoutingStepResourcePO>> routingStepResourceFuture =
					CompletableFuture.supplyAsync(() -> {
				DynamicDataSourceContextHolder.setDataSource(scenario);
				List<String> ids = productCandidateResourceList.stream()
						.map(ProductCandidateResourcePO::getId).collect(Collectors.toList());
				List<NewRoutingStepResourcePO> routingStepResourceList =
						newRoutingStepResourceDao.selectByPrimaryKeys(ids);
				DynamicDataSourceContextHolder.clearDataSource();
				return routingStepResourceList;
			}, executor);
			// 获取物理资源数据
			CompletableFuture<Map<String, PhysicalResourceVO>> physicalResourceMapFuture =
					CompletableFuture.supplyAsync(() -> {
				DynamicDataSourceContextHolder.setDataSource(scenario);
				List<PhysicalResourceVO> physicalResources = physicalResourceService.selectAll().stream()
						.filter(p -> StringUtils.isNotBlank(p.getEnabled())
							&& p.getEnabled().equals(YesOrNoEnum.YES.getCode())).collect(Collectors.toList());
				Map<String, PhysicalResourceVO> physicalResourceMap =
					StreamUtils.mapByColumn(physicalResources, PhysicalResourceVO::getId);
				DynamicDataSourceContextHolder.clearDataSource();
				return physicalResourceMap;
			}, executor);
			// 获取库存点信息
			CompletableFuture<Map<String, String>> stockPointMapFuture =
					CompletableFuture.supplyAsync(() -> stockPoints.stream().collect(Collectors
							.toMap(NewStockPointVO::getId, NewStockPointVO::getStockPointCode,
									(v1, v2) -> v1)), executor);
			CompletableFuture.allOf(routingStepResourceFuture, physicalResourceMapFuture, stockPointMapFuture).join();
			stopWatch.stop();

			// 查询存量已经维护过的候选资源数据
			stopWatch.start("查询存量已经维护过的候选资源数据");
			List<NewRoutingStepResourcePO> oldStepResourceList = routingStepResourceFuture.get();
			Map<String, NewRoutingStepResourcePO> oldStepResourceMap = oldStepResourceList.stream()
					.collect(Collectors.toMap(NewRoutingStepResourcePO::getId, Function.identity(),
							(v1, v2) -> v1));
			stopWatch.stop();

			stopWatch.start("查询路径数据");
			if (CollectionUtils.isEmpty(routingList)) {
				routingList = newRoutingService.selectByParams(ImmutableMap
						.of("productIds", productIds, "enabled", YesOrNoEnum.YES.getCode()));
			}
			if (CollectionUtils.isEmpty(routingList)) {
				return;
			}
			Map<String, NewRoutingVO> routingMap = routingList.stream().collect(Collectors
					.toMap(t -> t.getStockPointId() + "_" + t.getProductId(), Function.identity(),
							(v1, v2) -> v1));
			stopWatch.stop();

			stopWatch.start("获取routing下的所有工艺路径步骤");
			if (CollectionUtils.isEmpty(newRoutingSteps)) {
				// 获取routing下的所有工艺路径步骤
				List<String> routingIds = routingList.stream().map(NewRoutingVO::getId).collect(Collectors.toList());
				newRoutingSteps = newRoutingStepService.selectByParams(ImmutableMap
						.of("routingIds", routingIds, "enabled", YesOrNoEnum.YES.getCode()));
			}
			Map<String, NewRoutingStepVO> newRoutingStepMap = newRoutingSteps.stream().collect(Collectors
					.toMap(t -> t.getRoutingId() + "_" + t.getStandardStepId(),
							Function.identity(), (v1, v2) -> v1));
			stopWatch.stop();

			Map<String, PhysicalResourceVO> physicalResourceMap = physicalResourceMapFuture.get();
			Map<String, String> stockPointMap = stockPointMapFuture.get();
			// 物品工艺路径转换产品工艺路径
			stopWatch.start("核心逻辑处理");
			List<NewRoutingStepResourceDTO> insertBatchResourceList = new ArrayList<>();
			List<NewRoutingStepResourceDTO> updateBatchResourceList = new ArrayList<>();
			for (ProductCandidateResourcePO productCandidateResource : productCandidateResourceList) {
				// 获取对应的库存点
				String stockPointCode = stockPointMap.get(productCandidateResource.getStockPointId());
				if (StringUtils.isEmpty(stockPointCode)) {
					continue;
				}
				// 获取routingId
				NewRoutingVO newRouting = routingMap.get(stockPointCode + "_" + productCandidateResource.getProductId());
				if (newRouting == null) {
					continue;
				}
				// 获取routingStepId
				NewRoutingStepVO newRoutingStep = newRoutingStepMap.get(newRouting.getId()
						+ "_" + productCandidateResource.getStandardStepId());
				if (newRoutingStep == null) {
					continue;
				}
				NewRoutingStepResourceDTO newRoutingStepResource = new NewRoutingStepResourceDTO();
				if (Objects.nonNull(oldStepResourceMap.get(productCandidateResource.getId()))) {
					// 修改
					NewRoutingStepResourcePO oldResourcePO = oldStepResourceMap.get(productCandidateResource.getId());
					newRoutingStepResource = NewRoutingStepResourceConvertor.INSTANCE.po2Dto(oldResourcePO);
					transitionRoutingStepResource(productCandidateResource, newRouting, newRoutingStep, newRoutingStepResource);
					String physicalResourceId = newRoutingStepResource.getPhysicalResourceId();
					if (!physicalResourceMap.containsKey(physicalResourceId)) {
						log.warn("物理资源不存在，不处理工序候选资源，步骤id：{}", newRoutingStepResource.getRoutingStepId());
						continue;
					}
					updateBatchResourceList.add(newRoutingStepResource);
				} else {
					// 新增
					newRoutingStepResource.setId(productCandidateResource.getId());
					transitionRoutingStepResource(productCandidateResource, newRouting, newRoutingStep, newRoutingStepResource);
					String physicalResourceId = newRoutingStepResource.getPhysicalResourceId();
					if (!physicalResourceMap.containsKey(physicalResourceId)) {
						log.warn("物理资源不存在，不处理工序候选资源，步骤id：{}", newRoutingStepResource.getRoutingStepId());
						continue;
					}
					insertBatchResourceList.add(newRoutingStepResource);
				}
			}
			stopWatch.stop();

			stopWatch.start("数据入库");
			if (CollectionUtils.isNotEmpty(insertBatchResourceList)) {
				BulkOperationUtils.bulkUpdateOrCreate(insertBatchResourceList, this::doCreateBatchWithPrimaryKey, 2500);
			}
			if (CollectionUtils.isNotEmpty(updateBatchResourceList)) {
				BulkOperationUtils.bulkUpdateOrCreate(updateBatchResourceList, this::doUpdateBatchSelective, 2500);
			}
			stopWatch.stop();

			// 组装时若存在S2的10工序没有候选资源则默认维护S2XL01资源
			stopWatch.start("组装时若存在S2的10工序没有候选资源则默认维护S2XL01资源");
			newRoutingStepService.doAddDefaultResource();
			stopWatch.stop();
			log.info(stopWatch.prettyPrint(TimeUnit.SECONDS));
		} catch (ExecutionException e) {
			throw new BusinessException(e.getLocalizedMessage());
		} catch (InterruptedException e1) {
			Thread.currentThread().interrupt();
			throw new BusinessException(e1.getLocalizedMessage());
		}finally {
			if (!executor.isShutdown()) {
				try {
					log.info("Shutting down IOExecutor...");
					executor.shutdown();
					if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
						log.warn("IOExecutor did not terminate in 10 seconds, forcing shutdown.");
						executor.shutdownNow();
						if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
							log.error("IOExecutor did not terminate even after shutdownNow.");
						}
					}
					log.info("IOExecutor shut down successfully.");
				} catch (InterruptedException ie) {
					log.error("Shutdown of IOExecutor interrupted.", ie);
					executor.shutdownNow();
					Thread.currentThread().interrupt();
				}
			}
		}
	}

	private void transitionRoutingStepResource(ProductCandidateResourcePO productCandidateResourcePO,
											   NewRoutingVO newRoutingVO, NewRoutingStepVO newRoutingStepVO, NewRoutingStepResourceDTO newResourceDTO) {
		newResourceDTO.setRoutingId(newRoutingVO.getId());
		newResourceDTO.setRoutingStepId(newRoutingStepVO.getId());
		newResourceDTO.setStandardResourceId(productCandidateResourcePO.getStandardResourceId());
		newResourceDTO.setPhysicalResourceId(productCandidateResourcePO.getPhysicalResourceId());
		newResourceDTO.setKid(productCandidateResourcePO.getKid());
		newResourceDTO.setUnitProductionTime(productCandidateResourcePO.getUnitProductionTime());
		newResourceDTO.setFixedWorkHours(productCandidateResourcePO.getFixedWorkHours());
		newResourceDTO.setUnitsPerHour(productCandidateResourcePO.getUnitsPerHour());
		Integer maxLotSize = productCandidateResourcePO.getMaxLotSize();
		if(maxLotSize != null) {
			newResourceDTO.setMaxLotSize(BigDecimal.valueOf(maxLotSize));
		}
		Integer minLotSize = productCandidateResourcePO.getMinLotSize();
		if(minLotSize != null) {
			newResourceDTO.setMinLotSize(BigDecimal.valueOf(minLotSize));
		}
		newResourceDTO.setSetupUnitBatchSize(productCandidateResourcePO.getSetupUnitBatchSize());
		newResourceDTO.setProductionUnitBatchSize(productCandidateResourcePO.getProductionUnitBatchSize());
		newResourceDTO.setCleanupUnitBatchSize(productCandidateResourcePO.getCleanupUnitBatchSize());
		newResourceDTO.setMaxProductionSuspendDuration(productCandidateResourcePO.getMaxProductionSuspendDuration());
		newResourceDTO.setMaxSetupSuspendDuration(productCandidateResourcePO.getMaxSetupSuspendDuration());
		newResourceDTO.setMaxCleanupSuspendDuration(productCandidateResourcePO.getMaxCleanupSuspendDuration());
		newResourceDTO.setPriority(productCandidateResourcePO.getPriority());
		newResourceDTO.setAltToolCode(productCandidateResourcePO.getAltToolCode());
		newResourceDTO.setEffectiveTime(productCandidateResourcePO.getEffectiveTime());
		newResourceDTO.setEffective(productCandidateResourcePO.getEnabled());
		newResourceDTO.setExpiryTime(productCandidateResourcePO.getExpiryTime());
		newResourceDTO.setRemark(productCandidateResourcePO.getRemark());
		newResourceDTO.setEnabled(productCandidateResourcePO.getEnabled());
	}

	@Override
	public void doCreateBatchWithPrimaryKey(List<NewRoutingStepResourceDTO> list) {
		List<NewRoutingStepResourcePO> newList = NewRoutingStepResourceConvertor.INSTANCE.dto2Pos(list);
		BasePOUtils.insertBatchFiller(newList);
		BulkOperationUtils.bulkUpdateOrCreate(newList, poList ->
				newRoutingStepResourceDao.insertBatchWithPrimaryKey(poList), 2500);
	}

	@Override
	public void doUpdateBatchSelective(List<NewRoutingStepResourceDTO> list) {
		List<NewRoutingStepResourcePO> newList = NewRoutingStepResourceConvertor.INSTANCE.dto2Pos(list);
		BasePOUtils.updateBatchFiller(newList);
		BulkOperationUtils.bulkUpdateOrCreate(newList, poList ->
				newRoutingStepResourceDao.updateBatchSelective(poList), 2500);
	}

	@Override
	public List<String> selectAllRoutingStepIds() {
		return newRoutingStepResourceDao.selectAllRoutingStepIds();
	}

	@Override
	public List<NewRoutingStepResourceVO> selectRoutingStepResoueceBase(List<String> routingIds) {
		return newRoutingStepResourceDao.selectRoutingStepResoueceBase(routingIds);
	}

	@Override
	public Map<String, String> selectMainProcessByProductCodes(List<String> productCodes) {
		if (CollectionUtils.isEmpty(productCodes)) {
			return Collections.emptyMap();
		}

		List<String> uniqueProductCodes = productCodes.stream()
				.filter(StringUtils::isNotBlank)
				.distinct()
				.collect(Collectors.toList());

		List<Map<String, Object>> records = newRoutingStepResourceDao.selectMainProcessByProductCodes(uniqueProductCodes);

		Map<String, String> resultMap = new HashMap<>();
		for (Map<String, Object> record : records) {
			String productCode = (String) record.get("product_code");
			String mainProcess = (String) record.get("physical_resource_code");
			if (StringUtils.isNotBlank(productCode) && StringUtils.isNotBlank(mainProcess)) {
				resultMap.put(productCode, mainProcess);
			}
		}

		return resultMap;
	}
}