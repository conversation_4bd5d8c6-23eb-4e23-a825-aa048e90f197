package com.yhl.scp.mds.stock.domain.service;

import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.mds.common.dto.RemoveVersionDTO;
import com.yhl.scp.mds.stock.domain.entity.NewStockPointDO;
import com.yhl.scp.mds.stock.infrastructure.dao.NewStockPointDao;
import com.yhl.scp.mds.stock.infrastructure.po.NewStockPointPO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class NewStockPointDomainService {

    @Resource
    private NewStockPointDao newStockPointDao;

    /**
     * 数据校验
     *
     * @param newStockPointDO 领域对象
     */
    public void validation(NewStockPointDO newStockPointDO) {
        checkNotNull(newStockPointDO);
        checkUniqueCode(newStockPointDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param newStockPointDO 领域对象
     */
    private void checkNotNull(NewStockPointDO newStockPointDO) {
        if (StringUtils.isBlank(newStockPointDO.getStockPointCode())) {
            throw new BusinessException("库存点代码，不能为空");
        }
        if (StringUtils.isBlank(newStockPointDO.getStockPointName())) {
            throw new BusinessException("库存点名称，不能为空");
        }
    }

    /**
     * 唯一性校验
     *
     * @param newStockPointDO 领域对象
     */
    private void checkUniqueCode(NewStockPointDO newStockPointDO) {
        Map<String, Object> params = new HashMap<>(4);
        params.put("stockPointCode", newStockPointDO.getStockPointCode());
        if (StringUtils.isBlank(newStockPointDO.getId())) {
            List<NewStockPointPO> list = newStockPointDao.selectByParams(params);
            if (CollectionUtils.isNotEmpty(list)) {
                throw new BusinessException("新增失败，库存点代码已存在：" + newStockPointDO.getStockPointCode());
            }
        } else {
            NewStockPointPO old = newStockPointDao.selectByPrimaryKey(newStockPointDO.getId());
            if (!newStockPointDO.getStockPointCode().equals(old.getStockPointCode())) {
                List<NewStockPointPO> list = newStockPointDao.selectByParams(params);
                if (CollectionUtils.isNotEmpty(list)) {
                    throw new BusinessException("修改失败，库存点代码已存在：" + newStockPointDO.getStockPointCode());
                }
            }
            if (!newStockPointDO.getVersionValue().equals(old.getVersionValue())) {
                throw new BusinessException("修改失败，数据已被修改，请刷新后重试");
            }
        }
    }

    /**
     * 删除数据校验
     *
     * @param removeVersionDTOS
     */
    public void checkDelete(List<RemoveVersionDTO> removeVersionDTOS) {
        if (CollectionUtils.isEmpty(removeVersionDTOS)) {
            return;
        }
        for (RemoveVersionDTO removeVersionDTO : removeVersionDTOS) {
            NewStockPointPO old = newStockPointDao.selectByPrimaryKey(removeVersionDTO.getId());
            if (Objects.isNull(old)) {
                throw new BusinessException("删除失败，数据不存在：" + removeVersionDTO.getId());
            }
            if (!removeVersionDTO.getVersionValue().equals(old.getVersionValue())) {
                throw new BusinessException("删除失败，数据已被修改，请刷新后重试");
            }
        }
    }
}
