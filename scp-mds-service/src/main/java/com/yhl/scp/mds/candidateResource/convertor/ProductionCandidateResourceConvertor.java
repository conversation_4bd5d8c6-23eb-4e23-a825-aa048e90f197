package com.yhl.scp.mds.candidateResource.convertor;

import com.yhl.scp.mds.candidateResource.domain.entity.ProductionCandidateResourceDO;
import com.yhl.scp.mds.candidateResource.dto.ProductionCandidateResourceDTO;
import com.yhl.scp.mds.candidateResource.infrastructure.po.ProductionCandidateResourcePO;
import com.yhl.scp.mds.candidateResource.vo.ProductionCandidateResourceVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;


@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ProductionCandidateResourceConvertor {

    ProductionCandidateResourceConvertor INSTANCE = Mappers.getMapper(ProductionCandidateResourceConvertor.class);

    ProductionCandidateResourceDO dto2Do(ProductionCandidateResourceDTO obj);

    ProductionCandidateResourceDTO do2Dto(ProductionCandidateResourceDO obj);

    List<ProductionCandidateResourceDO> dto2Dos(List<ProductionCandidateResourceDTO> list);

    List<ProductionCandidateResourceDTO> do2Dtos(List<ProductionCandidateResourceDO> list);

    ProductionCandidateResourceVO do2Vo(ProductionCandidateResourceDO obj);

    ProductionCandidateResourceVO po2Vo(ProductionCandidateResourcePO obj);

    List<ProductionCandidateResourceVO> po2Vos(List<ProductionCandidateResourcePO> list);

    ProductionCandidateResourcePO do2Po(ProductionCandidateResourceDO obj);

    ProductionCandidateResourceDO po2Do(ProductionCandidateResourcePO obj);

    ProductionCandidateResourcePO dto2Po(ProductionCandidateResourceDTO obj);

    List<ProductionCandidateResourcePO> dto2Pos(List<ProductionCandidateResourceDTO> obj);

}
