package com.yhl.scp.mds.routing.service.impl;

import cn.hutool.core.util.ObjUtil;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.platform.component.external.AbstractStrategyContext;
import com.yhl.platform.component.external.StrategyContext;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesMoldToolingGroupDir;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.mold.infrastructure.dao.MoldToolingDao;
import com.yhl.scp.mds.mold.infrastructure.dao.MoldToolingGroupDao;
import com.yhl.scp.mds.mold.infrastructure.po.MoldToolingGroupPO;
import com.yhl.scp.mds.mold.infrastructure.po.MoldToolingPO;
import com.yhl.scp.mds.newproduct.service.NewProductStockPointService;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.productroutestepbase.infrastructure.dao.MdsProductStockPointBaseDao;
import com.yhl.scp.mds.routing.convertor.ProductCandidateResourceConvertor;
import com.yhl.scp.mds.routing.domain.entity.ProductCandidateResourceDO;
import com.yhl.scp.mds.routing.domain.service.ProductCandidateResourceDomainService;
import com.yhl.scp.mds.routing.domain.service.RoutingDomainService;
import com.yhl.scp.mds.routing.dto.ProductCandidateResourceDTO;
import com.yhl.scp.mds.routing.extension.ProductCandidateResourceCalculationStrategy;
import com.yhl.scp.mds.routing.extension.ProductCandidateResourceCalculationWrapper;
import com.yhl.scp.mds.routing.infrastructure.dao.NewProductCandidateResourceDao;
import com.yhl.scp.mds.routing.infrastructure.dao.NewRoutingDao;
import com.yhl.scp.mds.routing.infrastructure.dao.NewRoutingStepResourceDao;
import com.yhl.scp.mds.routing.infrastructure.dao.StandardStepDao;
import com.yhl.scp.mds.routing.infrastructure.po.NewRoutingPO;
import com.yhl.scp.mds.routing.infrastructure.po.NewRoutingStepResourcePO;
import com.yhl.scp.mds.routing.infrastructure.po.ProductCandidateResourcePO;
import com.yhl.scp.mds.routing.service.NewProductCandidateResourceService;
import com.yhl.scp.mds.routing.vo.ProductCandidateResourceVO;
import io.seata.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>ProductCandidateResourceServiceImpl</code>
 * <p>
 * 物品候选资源应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-18 11:45:18
 */
@Slf4j
@Service
public class NewProductCandidateResourceServiceImpl extends AbstractService implements NewProductCandidateResourceService {

    @Resource
    private NewProductCandidateResourceDao newProductCandidateResourceDao;

    @Resource
    private ProductCandidateResourceDomainService productCandidateResourceDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private NewProductStockPointService productStockPointService;

    @Resource
    private NewProductStockPointService newProductStockPointService;

    @Resource
    private RoutingDomainService routingDomainService;

    @Resource
    private NewRoutingStepResourceDao newRoutingStepResourceDao;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private MdsProductStockPointBaseDao mdsProductStockPointBaseDao;

    @Resource
    private MoldToolingGroupDao moldToolingGroupDao;

    @Resource
    private StandardStepDao standardStepDao;

    @Resource
    private MoldToolingDao moldToolingDao;

    @Resource
    private NewRoutingDao newRoutingDao;

    @Override
    public BaseResponse<Void> doCreate(ProductCandidateResourceDTO productCandidateResourceDTO) {
        // 设置生产节拍
        setProductionTakt(Collections.singletonList(productCandidateResourceDTO));
        // 0.数据转换
        ProductCandidateResourceDO productCandidateResourceDO = ProductCandidateResourceConvertor.INSTANCE.dto2Do(productCandidateResourceDTO);
        ProductCandidateResourcePO productCandidateResourcePO = ProductCandidateResourceConvertor.INSTANCE.dto2Po(productCandidateResourceDTO);
        // 1.数据校验
        productCandidateResourceDomainService.validation(productCandidateResourceDO);
        productCandidateResourceDomainService.handleDefault(productCandidateResourceDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(productCandidateResourcePO);
        BigDecimal setupUnitBatchSize = productCandidateResourcePO.getSetupUnitBatchSize();
        if (null == setupUnitBatchSize) {
            productCandidateResourcePO.setSetupUnitBatchSize(BigDecimal.ONE);
        }
        BigDecimal productionUnitBatchSize = productCandidateResourcePO.getProductionUnitBatchSize();
        if (null == productionUnitBatchSize) {
            productCandidateResourcePO.setProductionUnitBatchSize(BigDecimal.ONE);
        }
        BigDecimal cleanupUnitBatchSize = productCandidateResourcePO.getCleanupUnitBatchSize();
        if (null == cleanupUnitBatchSize) {
            productCandidateResourcePO.setCleanupUnitBatchSize(BigDecimal.ONE);
        }
        Integer cleanupDuration = productCandidateResourcePO.getCleanupDuration();
        if (null == cleanupDuration) {
            productCandidateResourcePO.setCleanupDuration(1);
        }
        productCandidateResourcePO.setEnabled(YesOrNoEnum.YES.getCode());

        // 匹配物品
        matchProductInProductOrganize(productCandidateResourceDTO, productCandidateResourcePO);
        newProductCandidateResourceDao.insertWithPrimaryKey(productCandidateResourcePO);

        //        doCreateOrDeleteRoutingStepResource(Collections.singletonList(productCandidateResourcePO), OperationTypeEnum.CREATE);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    private void matchProductInProductOrganize(ProductCandidateResourceDTO productCandidateResourceDTO, ProductCandidateResourcePO productCandidateResourcePO) {
        String productId = productCandidateResourcePO.getProductId();
        String stockPointId = productCandidateResourceDTO.getStockPointId();
        String newProductId = newProductCandidateResourceDao.selectProductOrganizeProductId(productId, stockPointId);
        if (StringUtils.isBlank(newProductId)) {
            throw new BusinessException("物品匹配失败");
        }
        productCandidateResourcePO.setProductId(newProductId);
    }

    @Override
    public BaseResponse<Void> doUpdate(ProductCandidateResourceDTO productCandidateResourceDTO) {
        // 0.数据转换
        ProductCandidateResourceDO productCandidateResourceDO = ProductCandidateResourceConvertor.INSTANCE.dto2Do(productCandidateResourceDTO);
        ProductCandidateResourcePO productCandidateResourcePO = ProductCandidateResourceConvertor.INSTANCE.dto2Po(productCandidateResourceDTO);
        // 1.数据校验
        productCandidateResourceDomainService.validation(productCandidateResourceDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(productCandidateResourcePO);
        newProductCandidateResourceDao.updateSelective(productCandidateResourcePO);
        //修改产品候选资源
        NewRoutingStepResourcePO routingStepResourceUpdate = new NewRoutingStepResourcePO();
        routingStepResourceUpdate.setId(productCandidateResourceDTO.getId());
        routingStepResourceUpdate.setPriority(productCandidateResourceDTO.getPriority());
        routingStepResourceUpdate.setUnitProductionTime(productCandidateResourceDTO.getUnitProductionTime());
        BasePOUtils.updateFiller(routingStepResourceUpdate);
        // 匹配物品
        matchProductInProductOrganize(productCandidateResourceDTO, productCandidateResourcePO);
        newRoutingStepResourceDao.updateSelective(routingStepResourceUpdate);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<ProductCandidateResourceDTO> list) {
        List<ProductCandidateResourcePO> newList = ProductCandidateResourceConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        newProductCandidateResourceDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<ProductCandidateResourceDTO> list) {
        List<ProductCandidateResourcePO> newList = ProductCandidateResourceConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        newProductCandidateResourceDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return newProductCandidateResourceDao.deleteBatch(idList);
        }
        return newProductCandidateResourceDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public ProductCandidateResourceVO selectByPrimaryKey(String id) {
        ProductCandidateResourcePO po = newProductCandidateResourceDao.selectByPrimaryKey(id);
        return ProductCandidateResourceConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mds_rou_product_candidate_resource")
    public List<ProductCandidateResourceVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mds_rou_product_candidate_resource")
    public List<ProductCandidateResourceVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<ProductCandidateResourceVO> dataList = newProductCandidateResourceDao.selectByCondition(sortParam, queryCriteriaParam);
        NewProductCandidateResourceServiceImpl target = springBeanUtils.getBean(NewProductCandidateResourceServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<ProductCandidateResourceVO> selectByParams(Map<String, Object> params) {
        List<ProductCandidateResourcePO> list = newProductCandidateResourceDao.selectByParams(params);
        return ProductCandidateResourceConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<ProductCandidateResourceVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }


    @Override
    public String getObjectType() {
        //    	 return ObjectTypeEnum.PRODUCT_CANDIDATE_RESOURCE.getCode();
        return null;
    }

    @Override
    public List<ProductCandidateResourceVO> invocation(List<ProductCandidateResourceVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

    private void setProductionTakt(List<ProductCandidateResourceDTO> list) {
        ProductCandidateResourceCalculationWrapper productCandidateResourceCalculationWrapper = new ProductCandidateResourceCalculationWrapper(list);
        AbstractStrategyContext<ProductCandidateResourceCalculationStrategy> strategyContext = new StrategyContext<>();
        ProductCandidateResourceCalculationStrategy strategy = strategyContext.getStrategy(ProductCandidateResourceCalculationStrategy.extensionCode);
        if (strategy != null) {
            strategy.setProductionTakt(productCandidateResourceCalculationWrapper);
        }
    }

    //    public void doCreateOrDeleteRoutingStepResource(List<ProductCandidateResourcePO> productCandidateResourcePOS, OperationTypeEnum operationTypeEnum) {
    //        //校验全局参数配置
    ////        Boolean flag = globalConfigsService.checkGlobalConfig("candidate_resource_data_type", CandidateResourceDataTypeEnum.PRODUCT_CANDIDATE_RESOURCE.getCode());
    ////        if (!flag) {
    ////            return;
    ////        }
    //        //查询新增/删除的数据对应的物品
    //        List<NewProductStockPointVO> matchProduct = getMatchProduct(productCandidateResourcePOS);
    //        if (CollectionUtils.isEmpty(matchProduct)) {
    //            return;
    //        }
    //        List<String> stockPointIds = matchProduct.stream().map(NewProductStockPointVO::getStockPointCode).collect(Collectors.toList());
    //        List<String> productIds = matchProduct.stream().map(NewProductStockPointVO::getId).collect(Collectors.toList());
    //        //查询物品对应的工艺路径
    //        List<RoutingDO> routingDOS = routingDomainService.getRoutingDOByParams(ImmutableMap.of("stockPointIds", stockPointIds, "productIds", productIds));
    //        Map<String, List<RoutingDO>> productMainKeyToRoutingMaps = routingDOS.stream().collect(Collectors.groupingBy(k -> k.getStockPointId() + "," + k.getProductId()));
    //
    //        List<String> standardStepIds = productCandidateResourcePOS.stream().map(ProductCandidateResourcePO::getStandardStepId).collect(Collectors.toList());
    //        List<String> productStockPointIds = matchProduct.stream().map(NewProductStockPointVO::getId).collect(Collectors.toList());
    //        ///获取最新的物品在每个步骤上的候选资源
    //        Map<String, Map<String, List<ProductCandidateResourceVO>>> productCandidatePhysicalResource = this.getProductCandidatePhysicalResource(productStockPointIds, standardStepIds);
    //        //查询工艺路径用到的物品候选资源
    //        List<String> oldsCandidateResourceIds = routingDOS.stream().filter(k -> StringUtils.isNotEmpty(k.getSourceCandidateResourceMainKey())).
    //                map(k -> Arrays.asList(k.getSourceCandidateResourceMainKey().split(","))).flatMap(List::stream).collect(Collectors.toList());
    //        List<ProductCandidateResourcePO> oldProductCandidateResources = productCandidateResourceDao.selectByParams(ImmutableMap.of("ids", oldsCandidateResourceIds));
    //        List<RoutingStepResourceDTO> insertResources = new ArrayList<>();
    //        List<String> deleteResourceIds = new ArrayList<>();
    //        List<RoutingDTO> updateRoutingDTOS = new ArrayList<>();
    //        //需要删除的物品候选资源id
    //        List<String> removeCandidateResourceIds = productCandidateResourcePOS.stream().map(ProductCandidateResourcePO::getId).collect(Collectors.toList());
    //        for (ProductStockPointVO productStockPointVO : matchProduct) {
    //            //获取物品对应新增/删除的物品候选资源
    //            List<ProductCandidateResourcePO> productMatchResource = getProductMatchResource(productStockPointVO, productCandidateResourcePOS);
    //            //受影响的步骤有哪些
    //            List<String> reflectStandardStepIds = productMatchResource.stream().map(ProductCandidateResourcePO::getStandardStepId).collect(Collectors.toList());
    //            //获取物品对应的工艺路径
    //            List<RoutingDO> routingDOList = productMainKeyToRoutingMaps.get(productStockPointVO.getStockPointId() + "," + productStockPointVO.getProductId());
    //            if (CollectionUtils.isEmpty(routingDOList)) {
    //                continue;
    //            }
    //            for (RoutingDO routingDO : routingDOList) {
    //                List<RoutingStepDO> routingStepDOList = routingDO.getRoutingStepDOList();
    //                if (CollectionUtils.isEmpty(routingStepDOList)) {
    //                    continue;
    //                }
    //                //只对新增/删除的资源对应的步骤进行二次资源生成
    //                routingStepDOList = routingStepDOList.stream().filter(k -> reflectStandardStepIds.contains(k.getStandardStepId())).collect(Collectors.toList());
    //                List<RoutingStepDTO> routingStepDTOS = RoutingStepConvertor.INSTANCE.do2Dtos(routingStepDOList);
    //                Map<String, ProductStockPointVO> routingStepIdToProductMaps = new HashMap<>();
    //                for (RoutingStepDTO routingStepDTO : routingStepDTOS) {
    //                    routingStepIdToProductMaps.put(routingStepDTO.getId(), productStockPointVO);
    //                }
    //                List<RoutingStepResourceDTO> createRoutingStepResourceDTOS = routingStepResourceDomainService.generateRoutingStepResourceDTOList(routingStepDTOS, routingStepIdToProductMaps, productCandidatePhysicalResource);
    //                insertResources.addAll(createRoutingStepResourceDTOS);
    //                //将之前步骤资源删除，再新增
    //                List<RoutingStepResourceDO> deleteRoutingStepSources = routingStepDOList.stream().filter(k -> CollectionUtils.isNotEmpty(k.getRoutingStepResourceDOList())).map(RoutingStepDO::getRoutingStepResourceDOList).flatMap(List::stream).collect(Collectors.toList());
    //                List<String> deleteIds = deleteRoutingStepSources.stream().map(RoutingStepResourceDO::getId).collect(Collectors.toList());
    //                deleteResourceIds.addAll(deleteIds);
    //
    //                //更新routing的候选资源主键
    //                List<String> candidateResourceIds = createRoutingStepResourceDTOS.stream().map(RoutingStepResourceDTO::getProductCandidateResourceId).collect(Collectors.toList());
    //                String oldSourceCandidateResourceMainKey = routingDO.getSourceCandidateResourceMainKey();
    //                if (StringUtils.isNotEmpty(oldSourceCandidateResourceMainKey)) {
    //                    List<String> oldSourceCandidateResourceMainKeyList = new ArrayList<>(Arrays.asList(oldSourceCandidateResourceMainKey.split(",")));
    //                    if (operationTypeEnum == OperationTypeEnum.DELETE) {
    //                        oldSourceCandidateResourceMainKeyList.removeIf(k -> removeCandidateResourceIds.contains(k));
    //                    }
    //                    candidateResourceIds.addAll(oldSourceCandidateResourceMainKeyList);
    //                    candidateResourceIds = candidateResourceIds.stream().distinct().collect(Collectors.toList());
    //                }
    //                routingDO.setSourceCandidateResourceMainKey(CollectionUtils.isNotEmpty(candidateResourceIds) ? StringUtils.join(candidateResourceIds, ",") : null);
    //                if (StringUtils.isNotEmpty(routingDO.getSourceCandidateResourceMainKey())) {
    //                    routingDO.setSourceCandidateResourceType(CandidateResourceDataTypeEnum.PRODUCT_CANDIDATE_RESOURCE.getCode());
    //                } else {
    //                    routingDO.setSourceCandidateResourceType(null);
    //                }
    //                RoutingDTO routingDTO = RoutingConvertor.INSTANCE.do2Dto(routingDO);
    //                updateRoutingDTOS.add(routingDTO);
    //            }
    //        }
    //        if (CollectionUtils.isNotEmpty(insertResources)) {
    //            routingStepResourceService.doCreateBatch(insertResources);
    //        }
    //        if (CollectionUtils.isNotEmpty(deleteResourceIds)) {
    //            routingStepResourceService.doDelete(deleteResourceIds);
    //        }
    //        if (CollectionUtils.isNotEmpty(updateRoutingDTOS)) {
    //            routingService.doUpdateBatch(updateRoutingDTOS);
    //        }
    //        //路径有效性校验
    //        List<String> routingIds = routingDOS.stream().map(RoutingDO::getId).collect(Collectors.toList());
    //        routingService.txNotSupportedRoutingValidityCheck(routingIds);
    //    }

    /**
     * 获取余物品候选资源相关的库存点物品
     *
     * @param productCandidateResourcePOS
     * @return java.util.List<com.yhl.scp.mds.extension.product.vo.ProductStockPointVO>
     */
    private List<NewProductStockPointVO> getMatchProduct(List<ProductCandidateResourcePO> productCandidateResourcePOS) {
        List<NewProductStockPointVO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(productCandidateResourcePOS)) {
            return result;
        }

        //通过id查询所有的code
        List<String> stockPointIdList = productCandidateResourcePOS.stream().map(ProductCandidateResourcePO::getStockPointId)
                .distinct().collect(Collectors.toList());
        List<NewProductStockPointVO> productStockPointList = newProductStockPointService.selectByPrimaryKeys(stockPointIdList);
        Map<String, NewProductStockPointVO> productStockPointMap = productStockPointList.stream().collect(Collectors.toMap(NewProductStockPointVO::getId, e -> e, (v1, v2) -> v1));
        List<String> stockPointIds = productCandidateResourcePOS.stream().map(ProductCandidateResourcePO::getStockPointId).distinct().collect(Collectors.toList());
        List<NewProductStockPointVO> productStockPointVOS = productStockPointService.selectByParams(ImmutableMap.of("stockPointIds", stockPointIds));
        Map<String, List<NewProductStockPointVO>> stockPointId2ProductMaps = productStockPointVOS.stream()
                .filter(it -> ObjUtil.isNotNull(it.getStockPointCode())).collect(Collectors.groupingBy(NewProductStockPointVO::getStockPointCode));
        for (ProductCandidateResourcePO productCandidateResourcePO : productCandidateResourcePOS) {
            String stockPointId = productCandidateResourcePO.getStockPointId();
            NewProductStockPointVO newProductStockPointVO = productStockPointMap.get(stockPointId);
            List<NewProductStockPointVO> productStockPoints = stockPointId2ProductMaps.get(newProductStockPointVO.getStockPointCode());
            String productType = productCandidateResourcePO.getProductType();
            String productSeriesId = productCandidateResourcePO.getProductSeriesId();
            String productId = productCandidateResourcePO.getProductId();
            if (!"*".equals(productType)) {
                productStockPoints = productStockPoints.stream().filter(k -> k.getProductType().equals(productType)).collect(Collectors.toList());
            }
            //            if (!"*".equals(productSeriesId)) {
            //                productStockPoints = productStockPoints.stream().filter(k -> StringUtils.isEmpty(k.getProductSeriesId()) || k.getProductSeriesId().equals(productSeriesId)).collect(Collectors.toList());
            //            }
            if (!"*".equals(productId)) {
                productStockPoints = productStockPoints.stream().filter(k -> k.getId().equals(productId)).collect(Collectors.toList());
            }
            if (CollectionUtils.isNotEmpty(productStockPoints)) {
                result.addAll(productStockPoints);
            }
        }
        result = result.stream().distinct().collect(Collectors.toList());
        return result;
    }

    @Override
    public BaseResponse<Void> synMoldToolingGroupDir(String tenantId) {
        // 注释：从candidateResource表迁移到fixtureRelation 2025/06/23
//        Map<String, Object> params = MapUtil.newHashMap();
//        newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.MES.getCode(),
//                ApiCategoryEnum.MOLD_TOOLING_GROUP_DIR.getCode(), params);
        return BaseResponse.success("同步成功");
    }

    @Override
    public BaseResponse<Void> handleMoldToolingGroupDir(List<MesMoldToolingGroupDir> list) {
        return BaseResponse.success();
        // 注释：从candidateResource表迁移到fixtureRelation 2025/06/23
//        if (CollectionUtils.isEmpty(list)) {
//            return BaseResponse.success();
//        }
//        List<ProductCandidateResourceDTO> insertDtoS = new ArrayList<>();
//        List<ProductCandidateResourceDTO> updateDtoS = new ArrayList<>();
//
//        Set<String> directoryCodes =
//                list.stream().map(MesMoldToolingGroupDir::getDirectoryCode).collect(Collectors.toSet());
//        HashMap<String, Object> mapProductCode = MapUtil.newHashMap();
//        mapProductCode.put("dirNumList", directoryCodes);
//        List<MdsProductStockPointBasePO> productStockPointBasePOS = mdsProductStockPointBaseDao.selectByParams(mapProductCode);
//        Map<String, MdsProductStockPointBasePO> productStockPointBaseMap = productStockPointBasePOS.stream().collect(
//                Collectors.toMap(MdsProductStockPointBasePO::getProductCode, Function.identity(), (v1, v2) -> v1));
//        Set<String> productCodes =
//                productStockPointBasePOS.stream().map(MdsProductStockPointBasePO::getProductCode).collect(Collectors.toSet());
//        HashMap<String, Object> mapNewRouting = MapUtil.newHashMap();
//        mapNewRouting.put("productCodes", productCodes);
//        List<NewRoutingPO> newRoutingPOS =
//                newRoutingDao.selectByParams(mapNewRouting);
//        List<NewRoutingPO> newRoutingYesPOS = newRoutingPOS.stream()
//                .filter(po -> YesOrNoEnum.YES.getCode().equals(po.getEnabled()))
//                .collect(Collectors.toList());
//        Map<String, NewRoutingPO> newRoutingYesMap = newRoutingYesPOS.stream().collect(
//                Collectors.toMap(NewRoutingPO::getProductCode, Function.identity(), (v1, v2) -> v1));
//        Set<String> groupIds = list.stream().map(MesMoldToolingGroupDir::getToolingGroupId).collect(Collectors.toSet());
//        HashMap<String, Object> mapStandardResource = MapUtil.newHashMap();
//        mapStandardResource.put("kids", groupIds);
//        List<MoldToolingGroupPO> standardResourcePOS = moldToolingGroupDao.selectByParams(mapStandardResource);
//        Map<String, MoldToolingGroupPO> standardResourceMap = standardResourcePOS.stream().collect(
//                Collectors.toMap(MoldToolingGroupPO::getKid, Function.identity(), (v1, v2) -> v1));
//
//        Set<String> standardResourceIds =
//                standardResourcePOS.stream().map(MoldToolingGroupPO::getId).collect(Collectors.toSet());
//        HashMap<String, Object> mapStandard = MapUtil.newHashMap();
//        mapStandard.put("standardResourceIds", standardResourceIds);
//        List<MoldToolingPO> moldToolingGroupPOS = moldToolingDao.selectByParams(mapStandard);
//        Map<String, List<MoldToolingPO>> physicalMap = moldToolingGroupPOS.stream()
//                .collect(Collectors.groupingBy(MoldToolingPO::getStandardResourceId));
//
//        Set<String> kids = list.stream().map(MesMoldToolingGroupDir::getKid).collect(Collectors.toSet());
//        HashMap<String, Object> map = MapUtil.newHashMap();
//        //  map.put("kids", kids);
//        List<ProductCandidateResourcePO> productCandidateResourcePOS = newProductCandidateResourceDao.selectByParams(map);
//        Map<String, ProductCandidateResourcePO> productCandidateResourceMap = productCandidateResourcePOS.stream().collect(
//                Collectors.toMap(po -> po.getProductId() + "_" + po.getPhysicalResourceId(), Function.identity(), (v1, v2) -> v1));
//
//        for (MesMoldToolingGroupDir mesMoldToolingGroupDir : list) {
//
//            if (!productStockPointBaseMap.containsKey(mesMoldToolingGroupDir.getDirectoryCode())) {
//                continue;
//            }
//            MdsProductStockPointBasePO mdsProductStockPointBasePO = productStockPointBaseMap.get(mesMoldToolingGroupDir.getDirectoryCode());
//            String productCode = mdsProductStockPointBasePO.getProductCode();
//            if (!newRoutingYesMap.containsKey(productCode)) {
//                log.info(" productCode:" + mesMoldToolingGroupDir.getDirectoryCode() + "不存在");
//                continue;
//            }
//
//            NewRoutingPO newRoutingPO = newRoutingYesMap.get(productCode);
//            String toolingGroupId = mesMoldToolingGroupDir.getToolingGroupId();
//            if (!standardResourceMap.containsKey(toolingGroupId)) {
//                continue;
//            }
//            String id = standardResourceMap.get(toolingGroupId).getId();
//            if (!physicalMap.containsKey(id)) {
//                continue;
//            }
//            List<MoldToolingPO> moldToolingPOS = physicalMap.get(id);
//            log.info(" 编号目录数量:" + moldToolingPOS.size());
//            for (MoldToolingPO moldToolingPO : moldToolingPOS) {
//
//                ProductCandidateResourceDTO dto = new ProductCandidateResourceDTO();
//                String productCandidateResourceId = newRoutingPO.getProductId() + "_" + moldToolingPO.getId();
//                if (productCandidateResourceMap.containsKey(productCandidateResourceId)) {
//                    ProductCandidateResourcePO oldPo = productCandidateResourceMap.get(productCandidateResourceId);
//                    BeanUtils.copyProperties(oldPo, dto);
//                    generateDto(mesMoldToolingGroupDir, dto, newRoutingPO, standardResourceMap, toolingGroupId, moldToolingPO);
//                    updateDtoS.add(dto);
//                } else {
//                    generateDto(mesMoldToolingGroupDir, dto, newRoutingPO, standardResourceMap, toolingGroupId, moldToolingPO);
//                    dto.setSetupUnitBatchSize(new BigDecimal(1));
//                    dto.setProductionUnitBatchSize(new BigDecimal(1));
//                    dto.setCleanupDuration(1);
//                    dto.setPriority(1);
//                    dto.setKid(mesMoldToolingGroupDir.getKid());
//                    insertDtoS.add(dto);
//                }
//            }
//        }
//        if (CollectionUtils.isNotEmpty(insertDtoS)) {
//            doCreateBatch(insertDtoS);
//        }
//        if (CollectionUtils.isNotEmpty(updateDtoS)) {
//            doUpdateBatch(updateDtoS);
//        }
//        return BaseResponse.success("同步成功");
    }

    private void generateDto(MesMoldToolingGroupDir mesMoldTooling, ProductCandidateResourceDTO dto,
                             NewRoutingPO newRoutingPO, Map<String, MoldToolingGroupPO> standardResourceMap, String toolingGroupId, MoldToolingPO moldToolingPO) {
        dto.setProductId(newRoutingPO.getProductId());
        dto.setStandardResourceId(standardResourceMap.get(toolingGroupId).getId());
        dto.setStockPointId(standardResourceMap.get(toolingGroupId).getOrganizationId());
        dto.setPhysicalResourceId(moldToolingPO.getId());

    }

    @Override
    public List<ProductCandidateResourceVO> selectVOByParams(Map<String, Object> params) {
        return newProductCandidateResourceDao.selectVOByParams(params);
    }

    @Override
    public void doLogicDelete(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        newProductCandidateResourceDao.doLogicDelete(ids);
        //同时删除工艺路径步骤的数据，更新为无效
        newRoutingStepResourceDao.doLogicDelete(ids);
    }
}
