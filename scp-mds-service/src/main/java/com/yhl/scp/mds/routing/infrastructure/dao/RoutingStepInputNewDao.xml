<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mds.routing.infrastructure.dao.RoutingStepInputNewDao">
    <resultMap id="BaseResultMap"
               extends="com.yhl.scp.mds.routing.infrastructure.dao.RoutingStepInputBasicDao.BaseResultMap"
               type="com.yhl.scp.mds.routing.infrastructure.po.RoutingStepInputNewPO">
        <result column="header_id" jdbcType="VARCHAR" property="headerId"/>
        <result column="row_id" jdbcType="VARCHAR" property="rowId"/>
        <result column="last_update_date" jdbcType="TIMESTAMP" property="lastUpdateDate"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,routing_id,root_routing_id,routing_step_id,input_product_id,stock_point_id,main_material,key_material,yield,scrap,input_factor,unit_production_cost,scrap_strategy,percentage_scrap_rate,alt_mode,alt_type,alt_ratio,alt_material_group,match_code,connection_task,connection_type,max_connection_duration,min_connection_duration,global_level,counting_unit_id,effective_time,effective,expire_reason,expiry_time,remark,enabled,creator,create_time,modifier,modify_time,supply_type,header_id,row_id,last_update_date
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.routingId != null and params.routingId != ''">
                and routing_id = #{params.routingId,jdbcType=VARCHAR}
            </if>
            <if test="params.rootRoutingId != null and params.rootRoutingId != ''">
                and root_routing_id = #{params.rootRoutingId,jdbcType=VARCHAR}
            </if>
            <if test="params.routingStepId != null and params.routingStepId != ''">
                and routing_step_id = #{params.routingStepId,jdbcType=VARCHAR}
            </if>
            <if test="params.inputProductId != null and params.inputProductId != ''">
                and input_product_id = #{params.inputProductId,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointId != null and params.stockPointId != ''">
                and stock_point_id = #{params.stockPointId,jdbcType=VARCHAR}
            </if>
            <if test="params.mainMaterial != null and params.mainMaterial != ''">
                and main_material = #{params.mainMaterial,jdbcType=VARCHAR}
            </if>
            <if test="params.keyMaterial != null and params.keyMaterial != ''">
                and key_material = #{params.keyMaterial,jdbcType=VARCHAR}
            </if>
            <if test="params.yield != null">
                and yield = #{params.yield,jdbcType=VARCHAR}
            </if>
            <if test="params.scrap != null">
                and scrap = #{params.scrap,jdbcType=VARCHAR}
            </if>
            <if test="params.inputFactor != null">
                and input_factor = #{params.inputFactor,jdbcType=VARCHAR}
            </if>
            <if test="params.unitProductionCost != null">
                and unit_production_cost = #{params.unitProductionCost,jdbcType=VARCHAR}
            </if>
            <if test="params.scrapStrategy != null and params.scrapStrategy != ''">
                and scrap_strategy = #{params.scrapStrategy,jdbcType=VARCHAR}
            </if>
            <if test="params.percentageScrapRate != null">
                and percentage_scrap_rate = #{params.percentageScrapRate,jdbcType=VARCHAR}
            </if>
            <if test="params.altMode != null and params.altMode != ''">
                and alt_mode = #{params.altMode,jdbcType=VARCHAR}
            </if>
            <if test="params.altType != null and params.altType != ''">
                and alt_type = #{params.altType,jdbcType=VARCHAR}
            </if>
            <if test="params.altRatio != null">
                and alt_ratio = #{params.altRatio,jdbcType=VARCHAR}
            </if>
            <if test="params.altMaterialGroup != null and params.altMaterialGroup != ''">
                and alt_material_group = #{params.altMaterialGroup,jdbcType=VARCHAR}
            </if>
            <if test="params.matchCode != null and params.matchCode != ''">
                and match_code = #{params.matchCode,jdbcType=VARCHAR}
            </if>
            <if test="params.connectionTask != null and params.connectionTask != ''">
                and connection_task = #{params.connectionTask,jdbcType=VARCHAR}
            </if>
            <if test="params.connectionType != null and params.connectionType != ''">
                and connection_type = #{params.connectionType,jdbcType=VARCHAR}
            </if>
            <if test="params.maxConnectionDuration != null">
                and max_connection_duration = #{params.maxConnectionDuration,jdbcType=INTEGER}
            </if>
            <if test="params.minConnectionDuration != null">
                and min_connection_duration = #{params.minConnectionDuration,jdbcType=INTEGER}
            </if>
            <if test="params.globalLevel != null">
                and global_level = #{params.globalLevel,jdbcType=INTEGER}
            </if>
            <if test="params.countingUnitId != null and params.countingUnitId != ''">
                and counting_unit_id = #{params.countingUnitId,jdbcType=VARCHAR}
            </if>
            <if test="params.effectiveTime != null">
                and effective_time = #{params.effectiveTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.effective != null and params.effective != ''">
                and effective = #{params.effective,jdbcType=VARCHAR}
            </if>
            <if test="params.expireReason != null and params.expireReason != ''">
                and expire_reason = #{params.expireReason,jdbcType=VARCHAR}
            </if>
            <if test="params.expiryTime != null">
                and expiry_time = #{params.expiryTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.supplyType != null and params.supplyType != ''">
                and supply_type = #{params.supplyType,jdbcType=VARCHAR}
            </if>
            <if test="params.headerId != null and params.headerId != ''">
                and header_id = #{params.headerId,jdbcType=VARCHAR}
            </if>
            <if test="params.rowId != null and params.rowId != ''">
                and row_id = #{params.rowId,jdbcType=VARCHAR}
            </if>
            <if test="params.lastUpdateDate != null">
                and last_update_date = #{params.lastUpdateDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.inputProductIds != null and params.inputProductIds.size() > 0">
                and input_product_id in
                <foreach collection="params.inputProductIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_rou_routing_step_input
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_rou_routing_step_input
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_rou_routing_step_input
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mds.routing.infrastructure.po.RoutingStepInputNewPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mds_rou_routing_step_input(
        id,
        routing_id,
        root_routing_id,
        routing_step_id,
        input_product_id,
        stock_point_id,
        main_material,
        key_material,
        yield,
        scrap,
        input_factor,
        unit_production_cost,
        scrap_strategy,
        percentage_scrap_rate,
        alt_mode,
        alt_type,
        alt_ratio,
        alt_material_group,
        match_code,
        connection_task,
        connection_type,
        max_connection_duration,
        min_connection_duration,
        global_level,
        counting_unit_id,
        effective_time,
        effective,
        expire_reason,
        expiry_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        supply_type,
        header_id,
        row_id,
        last_update_date)
        values (
        #{id,jdbcType=VARCHAR},
        #{routingId,jdbcType=VARCHAR},
        #{rootRoutingId,jdbcType=VARCHAR},
        #{routingStepId,jdbcType=VARCHAR},
        #{inputProductId,jdbcType=VARCHAR},
        #{stockPointId,jdbcType=VARCHAR},
        #{mainMaterial,jdbcType=VARCHAR},
        #{keyMaterial,jdbcType=VARCHAR},
        #{yield,jdbcType=VARCHAR},
        #{scrap,jdbcType=VARCHAR},
        #{inputFactor,jdbcType=VARCHAR},
        #{unitProductionCost,jdbcType=VARCHAR},
        #{scrapStrategy,jdbcType=VARCHAR},
        #{percentageScrapRate,jdbcType=VARCHAR},
        #{altMode,jdbcType=VARCHAR},
        #{altType,jdbcType=VARCHAR},
        #{altRatio,jdbcType=VARCHAR},
        #{altMaterialGroup,jdbcType=VARCHAR},
        #{matchCode,jdbcType=VARCHAR},
        #{connectionTask,jdbcType=VARCHAR},
        #{connectionType,jdbcType=VARCHAR},
        #{maxConnectionDuration,jdbcType=INTEGER},
        #{minConnectionDuration,jdbcType=INTEGER},
        #{globalLevel,jdbcType=INTEGER},
        #{countingUnitId,jdbcType=VARCHAR},
        #{effectiveTime,jdbcType=TIMESTAMP},
        #{effective,jdbcType=VARCHAR},
        #{expireReason,jdbcType=VARCHAR},
        #{expiryTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{supplyType,jdbcType=VARCHAR},
        #{headerId,jdbcType=VARCHAR},
        #{rowId,jdbcType=VARCHAR},
        #{lastUpdateDate,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mds.routing.infrastructure.po.RoutingStepInputNewPO">
        insert into mds_rou_routing_step_input(id,
                                               routing_id,
                                               root_routing_id,
                                               routing_step_id,
                                               input_product_id,
                                               stock_point_id,
                                               main_material,
                                               key_material,
                                               yield,
                                               scrap,
                                               input_factor,
                                               unit_production_cost,
                                               scrap_strategy,
                                               percentage_scrap_rate,
                                               alt_mode,
                                               alt_type,
                                               alt_ratio,
                                               alt_material_group,
                                               match_code,
                                               connection_task,
                                               connection_type,
                                               max_connection_duration,
                                               min_connection_duration,
                                               global_level,
                                               counting_unit_id,
                                               effective_time,
                                               effective,
                                               expire_reason,
                                               expiry_time,
                                               remark,
                                               enabled,
                                               creator,
                                               create_time,
                                               modifier,
                                               modify_time,
                                               supply_type,
                                               header_id,
                                               row_id,
                                               last_update_date)
        values (#{id,jdbcType=VARCHAR},
                #{routingId,jdbcType=VARCHAR},
                #{rootRoutingId,jdbcType=VARCHAR},
                #{routingStepId,jdbcType=VARCHAR},
                #{inputProductId,jdbcType=VARCHAR},
                #{stockPointId,jdbcType=VARCHAR},
                #{mainMaterial,jdbcType=VARCHAR},
                #{keyMaterial,jdbcType=VARCHAR},
                #{yield,jdbcType=VARCHAR},
                #{scrap,jdbcType=VARCHAR},
                #{inputFactor,jdbcType=VARCHAR},
                #{unitProductionCost,jdbcType=VARCHAR},
                #{scrapStrategy,jdbcType=VARCHAR},
                #{percentageScrapRate,jdbcType=VARCHAR},
                #{altMode,jdbcType=VARCHAR},
                #{altType,jdbcType=VARCHAR},
                #{altRatio,jdbcType=VARCHAR},
                #{altMaterialGroup,jdbcType=VARCHAR},
                #{matchCode,jdbcType=VARCHAR},
                #{connectionTask,jdbcType=VARCHAR},
                #{connectionType,jdbcType=VARCHAR},
                #{maxConnectionDuration,jdbcType=INTEGER},
                #{minConnectionDuration,jdbcType=INTEGER},
                #{globalLevel,jdbcType=INTEGER},
                #{countingUnitId,jdbcType=VARCHAR},
                #{effectiveTime,jdbcType=TIMESTAMP},
                #{effective,jdbcType=VARCHAR},
                #{expireReason,jdbcType=VARCHAR},
                #{expiryTime,jdbcType=TIMESTAMP},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{supplyType,jdbcType=VARCHAR},
                #{headerId,jdbcType=VARCHAR},
                #{rowId,jdbcType=VARCHAR},
                #{lastUpdateDate,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mds_rou_routing_step_input(
        id,
        routing_id,
        root_routing_id,
        routing_step_id,
        input_product_id,
        stock_point_id,
        main_material,
        key_material,
        yield,
        scrap,
        input_factor,
        unit_production_cost,
        scrap_strategy,
        percentage_scrap_rate,
        alt_mode,
        alt_type,
        alt_ratio,
        alt_material_group,
        match_code,
        connection_task,
        connection_type,
        max_connection_duration,
        min_connection_duration,
        global_level,
        counting_unit_id,
        effective_time,
        effective,
        expire_reason,
        expiry_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        supply_type,
        header_id,
        row_id,
        last_update_date)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.routingId,jdbcType=VARCHAR},
            #{entity.rootRoutingId,jdbcType=VARCHAR},
            #{entity.routingStepId,jdbcType=VARCHAR},
            #{entity.inputProductId,jdbcType=VARCHAR},
            #{entity.stockPointId,jdbcType=VARCHAR},
            #{entity.mainMaterial,jdbcType=VARCHAR},
            #{entity.keyMaterial,jdbcType=VARCHAR},
            #{entity.yield,jdbcType=VARCHAR},
            #{entity.scrap,jdbcType=VARCHAR},
            #{entity.inputFactor,jdbcType=VARCHAR},
            #{entity.unitProductionCost,jdbcType=VARCHAR},
            #{entity.scrapStrategy,jdbcType=VARCHAR},
            #{entity.percentageScrapRate,jdbcType=VARCHAR},
            #{entity.altMode,jdbcType=VARCHAR},
            #{entity.altType,jdbcType=VARCHAR},
            #{entity.altRatio,jdbcType=VARCHAR},
            #{entity.altMaterialGroup,jdbcType=VARCHAR},
            #{entity.matchCode,jdbcType=VARCHAR},
            #{entity.connectionTask,jdbcType=VARCHAR},
            #{entity.connectionType,jdbcType=VARCHAR},
            #{entity.maxConnectionDuration,jdbcType=INTEGER},
            #{entity.minConnectionDuration,jdbcType=INTEGER},
            #{entity.globalLevel,jdbcType=INTEGER},
            #{entity.countingUnitId,jdbcType=VARCHAR},
            #{entity.effectiveTime,jdbcType=TIMESTAMP},
            #{entity.effective,jdbcType=VARCHAR},
            #{entity.expireReason,jdbcType=VARCHAR},
            #{entity.expiryTime,jdbcType=TIMESTAMP},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.supplyType,jdbcType=VARCHAR},
            #{entity.headerId,jdbcType=VARCHAR},
            #{entity.rowId,jdbcType=VARCHAR},
            #{entity.lastUpdateDate,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mds_rou_routing_step_input(
        id,
        routing_id,
        root_routing_id,
        routing_step_id,
        input_product_id,
        stock_point_id,
        main_material,
        key_material,
        yield,
        scrap,
        input_factor,
        unit_production_cost,
        scrap_strategy,
        percentage_scrap_rate,
        alt_mode,
        alt_type,
        alt_ratio,
        alt_material_group,
        match_code,
        connection_task,
        connection_type,
        max_connection_duration,
        min_connection_duration,
        global_level,
        counting_unit_id,
        effective_time,
        effective,
        expire_reason,
        expiry_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        supply_type,
        header_id,
        row_id,
        last_update_date)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.routingId,jdbcType=VARCHAR},
            #{entity.rootRoutingId,jdbcType=VARCHAR},
            #{entity.routingStepId,jdbcType=VARCHAR},
            #{entity.inputProductId,jdbcType=VARCHAR},
            #{entity.stockPointId,jdbcType=VARCHAR},
            #{entity.mainMaterial,jdbcType=VARCHAR},
            #{entity.keyMaterial,jdbcType=VARCHAR},
            #{entity.yield,jdbcType=VARCHAR},
            #{entity.scrap,jdbcType=VARCHAR},
            #{entity.inputFactor,jdbcType=VARCHAR},
            #{entity.unitProductionCost,jdbcType=VARCHAR},
            #{entity.scrapStrategy,jdbcType=VARCHAR},
            #{entity.percentageScrapRate,jdbcType=VARCHAR},
            #{entity.altMode,jdbcType=VARCHAR},
            #{entity.altType,jdbcType=VARCHAR},
            #{entity.altRatio,jdbcType=VARCHAR},
            #{entity.altMaterialGroup,jdbcType=VARCHAR},
            #{entity.matchCode,jdbcType=VARCHAR},
            #{entity.connectionTask,jdbcType=VARCHAR},
            #{entity.connectionType,jdbcType=VARCHAR},
            #{entity.maxConnectionDuration,jdbcType=INTEGER},
            #{entity.minConnectionDuration,jdbcType=INTEGER},
            #{entity.globalLevel,jdbcType=INTEGER},
            #{entity.countingUnitId,jdbcType=VARCHAR},
            #{entity.effectiveTime,jdbcType=TIMESTAMP},
            #{entity.effective,jdbcType=VARCHAR},
            #{entity.expireReason,jdbcType=VARCHAR},
            #{entity.expiryTime,jdbcType=TIMESTAMP},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.supplyType,jdbcType=VARCHAR},
            #{entity.headerId,jdbcType=VARCHAR},
            #{entity.rowId,jdbcType=VARCHAR},
            #{entity.lastUpdateDate,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mds.routing.infrastructure.po.RoutingStepInputNewPO">
        update mds_rou_routing_step_input
        set routing_id              = #{routingId,jdbcType=VARCHAR},
            root_routing_id         = #{rootRoutingId,jdbcType=VARCHAR},
            routing_step_id         = #{routingStepId,jdbcType=VARCHAR},
            input_product_id        = #{inputProductId,jdbcType=VARCHAR},
            stock_point_id          = #{stockPointId,jdbcType=VARCHAR},
            main_material           = #{mainMaterial,jdbcType=VARCHAR},
            key_material            = #{keyMaterial,jdbcType=VARCHAR},
            yield                   = #{yield,jdbcType=VARCHAR},
            scrap                   = #{scrap,jdbcType=VARCHAR},
            input_factor            = #{inputFactor,jdbcType=VARCHAR},
            unit_production_cost    = #{unitProductionCost,jdbcType=VARCHAR},
            scrap_strategy          = #{scrapStrategy,jdbcType=VARCHAR},
            percentage_scrap_rate   = #{percentageScrapRate,jdbcType=VARCHAR},
            alt_mode                = #{altMode,jdbcType=VARCHAR},
            alt_type                = #{altType,jdbcType=VARCHAR},
            alt_ratio               = #{altRatio,jdbcType=VARCHAR},
            alt_material_group      = #{altMaterialGroup,jdbcType=VARCHAR},
            match_code              = #{matchCode,jdbcType=VARCHAR},
            connection_task         = #{connectionTask,jdbcType=VARCHAR},
            connection_type         = #{connectionType,jdbcType=VARCHAR},
            max_connection_duration = #{maxConnectionDuration,jdbcType=INTEGER},
            min_connection_duration = #{minConnectionDuration,jdbcType=INTEGER},
            global_level            = #{globalLevel,jdbcType=INTEGER},
            counting_unit_id        = #{countingUnitId,jdbcType=VARCHAR},
            effective_time          = #{effectiveTime,jdbcType=TIMESTAMP},
            effective               = #{effective,jdbcType=VARCHAR},
            expire_reason           = #{expireReason,jdbcType=VARCHAR},
            expiry_time             = #{expiryTime,jdbcType=TIMESTAMP},
            remark                  = #{remark,jdbcType=VARCHAR},
            enabled                 = #{enabled,jdbcType=VARCHAR},
            modifier                = #{modifier,jdbcType=VARCHAR},
            modify_time             = #{modifyTime,jdbcType=TIMESTAMP},
            supply_type             = #{supplyType,jdbcType=VARCHAR},
            header_id               = #{headerId,jdbcType=VARCHAR},
            row_id                  = #{rowId,jdbcType=VARCHAR},
            last_update_date        = #{lastUpdateDate,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mds.routing.infrastructure.po.RoutingStepInputNewPO">
        update mds_rou_routing_step_input
        <set>
            <if test="item.routingId != null and item.routingId != ''">
                routing_id = #{item.routingId,jdbcType=VARCHAR},
            </if>
            <if test="item.rootRoutingId != null and item.rootRoutingId != ''">
                root_routing_id = #{item.rootRoutingId,jdbcType=VARCHAR},
            </if>
            <if test="item.routingStepId != null and item.routingStepId != ''">
                routing_step_id = #{item.routingStepId,jdbcType=VARCHAR},
            </if>
            <if test="item.inputProductId != null and item.inputProductId != ''">
                input_product_id = #{item.inputProductId,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointId != null and item.stockPointId != ''">
                stock_point_id = #{item.stockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.mainMaterial != null and item.mainMaterial != ''">
                main_material = #{item.mainMaterial,jdbcType=VARCHAR},
            </if>
            <if test="item.keyMaterial != null and item.keyMaterial != ''">
                key_material = #{item.keyMaterial,jdbcType=VARCHAR},
            </if>
            <if test="item.yield != null">
                yield = #{item.yield,jdbcType=VARCHAR},
            </if>
            <if test="item.scrap != null">
                scrap = #{item.scrap,jdbcType=VARCHAR},
            </if>
            <if test="item.inputFactor != null">
                input_factor = #{item.inputFactor,jdbcType=VARCHAR},
            </if>
            <if test="item.unitProductionCost != null">
                unit_production_cost = #{item.unitProductionCost,jdbcType=VARCHAR},
            </if>
            <if test="item.scrapStrategy != null and item.scrapStrategy != ''">
                scrap_strategy = #{item.scrapStrategy,jdbcType=VARCHAR},
            </if>
            <if test="item.percentageScrapRate != null">
                percentage_scrap_rate = #{item.percentageScrapRate,jdbcType=VARCHAR},
            </if>
            <if test="item.altMode != null and item.altMode != ''">
                alt_mode = #{item.altMode,jdbcType=VARCHAR},
            </if>
            <if test="item.altType != null and item.altType != ''">
                alt_type = #{item.altType,jdbcType=VARCHAR},
            </if>
            <if test="item.altRatio != null">
                alt_ratio = #{item.altRatio,jdbcType=VARCHAR},
            </if>
            <if test="item.altMaterialGroup != null and item.altMaterialGroup != ''">
                alt_material_group = #{item.altMaterialGroup,jdbcType=VARCHAR},
            </if>
            <if test="item.matchCode != null and item.matchCode != ''">
                match_code = #{item.matchCode,jdbcType=VARCHAR},
            </if>
            <if test="item.connectionTask != null and item.connectionTask != ''">
                connection_task = #{item.connectionTask,jdbcType=VARCHAR},
            </if>
            <if test="item.connectionType != null and item.connectionType != ''">
                connection_type = #{item.connectionType,jdbcType=VARCHAR},
            </if>
            <if test="item.maxConnectionDuration != null">
                max_connection_duration = #{item.maxConnectionDuration,jdbcType=INTEGER},
            </if>
            <if test="item.minConnectionDuration != null">
                min_connection_duration = #{item.minConnectionDuration,jdbcType=INTEGER},
            </if>
            <if test="item.globalLevel != null">
                global_level = #{item.globalLevel,jdbcType=INTEGER},
            </if>
            <if test="item.countingUnitId != null and item.countingUnitId != ''">
                counting_unit_id = #{item.countingUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.effectiveTime != null">
                effective_time = #{item.effectiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.effective != null and item.effective != ''">
                effective = #{item.effective,jdbcType=VARCHAR},
            </if>
            <if test="item.expireReason != null and item.expireReason != ''">
                expire_reason = #{item.expireReason,jdbcType=VARCHAR},
            </if>
            <if test="item.expiryTime != null">
                expiry_time = #{item.expiryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.supplyType != null and item.supplyType != ''">
                supply_type = #{item.supplyType,jdbcType=VARCHAR},
            </if>
            <if test="item.headerId != null and item.headerId != ''">
                header_id = #{item.headerId,jdbcType=VARCHAR},
            </if>
            <if test="item.rowId != null and item.rowId != ''">
                row_id = #{item.rowId,jdbcType=VARCHAR},
            </if>
            <if test="item.lastUpdateDate != null">
                last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mds_rou_routing_step_input
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="routing_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.routingId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="root_routing_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.rootRoutingId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="routing_step_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.routingStepId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="input_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inputProductId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="main_material = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.mainMaterial,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="key_material = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.keyMaterial,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="yield = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.yield,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="scrap = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.scrap,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="input_factor = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inputFactor,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="unit_production_cost = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.unitProductionCost,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="scrap_strategy = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.scrapStrategy,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="percentage_scrap_rate = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.percentageScrapRate,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="alt_mode = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.altMode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="alt_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.altType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="alt_ratio = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.altRatio,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="alt_material_group = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.altMaterialGroup,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="match_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.matchCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="connection_task = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.connectionTask,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="connection_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.connectionType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="max_connection_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.maxConnectionDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="min_connection_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.minConnectionDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="global_level = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.globalLevel,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="counting_unit_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.countingUnitId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="effective_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.effectiveTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="effective = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.effective,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="expire_reason = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.expireReason,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="expiry_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.expiryTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="supply_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplyType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="header_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.headerId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="row_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.rowId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="last_update_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lastUpdateDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mds_rou_routing_step_input
            <set>
                <if test="item.routingId != null and item.routingId != ''">
                    routing_id = #{item.routingId,jdbcType=VARCHAR},
                </if>
                <if test="item.rootRoutingId != null and item.rootRoutingId != ''">
                    root_routing_id = #{item.rootRoutingId,jdbcType=VARCHAR},
                </if>
                <if test="item.routingStepId != null and item.routingStepId != ''">
                    routing_step_id = #{item.routingStepId,jdbcType=VARCHAR},
                </if>
                <if test="item.inputProductId != null and item.inputProductId != ''">
                    input_product_id = #{item.inputProductId,jdbcType=VARCHAR},
                </if>
                <if test="item.stockPointId != null and item.stockPointId != ''">
                    stock_point_id = #{item.stockPointId,jdbcType=VARCHAR},
                </if>
                <if test="item.mainMaterial != null and item.mainMaterial != ''">
                    main_material = #{item.mainMaterial,jdbcType=VARCHAR},
                </if>
                <if test="item.keyMaterial != null and item.keyMaterial != ''">
                    key_material = #{item.keyMaterial,jdbcType=VARCHAR},
                </if>
                <if test="item.yield != null">
                    yield = #{item.yield,jdbcType=VARCHAR},
                </if>
                <if test="item.scrap != null">
                    scrap = #{item.scrap,jdbcType=VARCHAR},
                </if>
                <if test="item.inputFactor != null">
                    input_factor = #{item.inputFactor,jdbcType=VARCHAR},
                </if>
                <if test="item.unitProductionCost != null">
                    unit_production_cost = #{item.unitProductionCost,jdbcType=VARCHAR},
                </if>
                <if test="item.scrapStrategy != null and item.scrapStrategy != ''">
                    scrap_strategy = #{item.scrapStrategy,jdbcType=VARCHAR},
                </if>
                <if test="item.percentageScrapRate != null">
                    percentage_scrap_rate = #{item.percentageScrapRate,jdbcType=VARCHAR},
                </if>
                <if test="item.altMode != null and item.altMode != ''">
                    alt_mode = #{item.altMode,jdbcType=VARCHAR},
                </if>
                <if test="item.altType != null and item.altType != ''">
                    alt_type = #{item.altType,jdbcType=VARCHAR},
                </if>
                <if test="item.altRatio != null">
                    alt_ratio = #{item.altRatio,jdbcType=VARCHAR},
                </if>
                <if test="item.altMaterialGroup != null and item.altMaterialGroup != ''">
                    alt_material_group = #{item.altMaterialGroup,jdbcType=VARCHAR},
                </if>
                <if test="item.matchCode != null and item.matchCode != ''">
                    match_code = #{item.matchCode,jdbcType=VARCHAR},
                </if>
                <if test="item.connectionTask != null and item.connectionTask != ''">
                    connection_task = #{item.connectionTask,jdbcType=VARCHAR},
                </if>
                <if test="item.connectionType != null and item.connectionType != ''">
                    connection_type = #{item.connectionType,jdbcType=VARCHAR},
                </if>
                <if test="item.maxConnectionDuration != null">
                    max_connection_duration = #{item.maxConnectionDuration,jdbcType=INTEGER},
                </if>
                <if test="item.minConnectionDuration != null">
                    min_connection_duration = #{item.minConnectionDuration,jdbcType=INTEGER},
                </if>
                <if test="item.globalLevel != null">
                    global_level = #{item.globalLevel,jdbcType=INTEGER},
                </if>
                <if test="item.countingUnitId != null and item.countingUnitId != ''">
                    counting_unit_id = #{item.countingUnitId,jdbcType=VARCHAR},
                </if>
                <if test="item.effectiveTime != null">
                    effective_time = #{item.effectiveTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.effective != null and item.effective != ''">
                    effective = #{item.effective,jdbcType=VARCHAR},
                </if>
                <if test="item.expireReason != null and item.expireReason != ''">
                    expire_reason = #{item.expireReason,jdbcType=VARCHAR},
                </if>
                <if test="item.expiryTime != null">
                    expiry_time = #{item.expiryTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.supplyType != null and item.supplyType != ''">
                    supply_type = #{item.supplyType,jdbcType=VARCHAR},
                </if>
                <if test="item.headerId != null and item.headerId != ''">
                    header_id = #{item.headerId,jdbcType=VARCHAR},
                </if>
                <if test="item.rowId != null and item.rowId != ''">
                    row_id = #{item.rowId,jdbcType=VARCHAR},
                </if>
                <if test="item.lastUpdateDate != null">
                    last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    
    <select id="selectVOByParams" resultType="com.yhl.scp.mds.extension.routing.vo.RoutingStepInputVO">
        select
        	id,
			routing_id routingId,
			root_routing_id rootRoutingId,
			routing_step_id routingStepId,
			input_product_id inputProductId,
			stock_point_id stockPointId,
			main_material mainMaterial,
			key_material keyMaterial,
			yield,
			scrap,
			input_factor inputFactor,
			unit_production_cost unitProductionCost,
			scrap_strategy scrapStrategy,
			percentage_scrap_rate percentageScrapRate,
			alt_mode altMode,
			alt_type altType,
			alt_ratio altRatio,
			alt_material_group altMaterialGroup,
			match_code matchCode,
			connection_task connectionTask,
			connection_type connectionType,
			max_connection_duration maxConnectionDuration,
			min_connection_duration minConnectionDuration,
			global_level globalLevel,
			counting_unit_id countingUnitId,
			effective_time effectiveTime,
			effective,
			expire_reason expireReason,
			expiry_time expiryTime,
			remark,
			enabled,
			creator,
			create_time createTime,
			modifier,
			modify_time modifyTime,
			supply_type supplyType
        from mds_rou_routing_step_input
        <include refid="Base_Where_Condition"/>
    </select>
</mapper>
