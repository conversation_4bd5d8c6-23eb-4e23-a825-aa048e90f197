<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mds.productBox.infrastructure.dao.ProductBoxRelationDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mds.productBox.infrastructure.po.ProductBoxRelationPO">
        <!--@Table mds_product_box_relation-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="product_stock_point_id" jdbcType="VARCHAR" property="productStockPointId"/>
        <result column="box_type" jdbcType="VARCHAR" property="boxType"/>
        <result column="priority" jdbcType="INTEGER" property="priority"/>
        <result column="box_id" jdbcType="VARCHAR" property="boxId"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="kid" jdbcType="VARCHAR" property="kid"/>
        <result column="standard_load" jdbcType="INTEGER" property="standardLoad"/>
        <result column="plan_area" jdbcType="VARCHAR" property="planArea"/>
        <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mds.productBox.vo.ProductBoxRelationVO">
        <result column="box_code" jdbcType="VARCHAR" property="boxCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="stock_point_name" jdbcType="VARCHAR" property="stockPointName"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,product_stock_point_id,box_type,priority,standard_load,box_id,remark,enabled,creator,create_time,modifier,modify_time,version_value,kid,last_update_time,plan_area
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>,box_code,product_code,product_name,stock_point_code,stock_point_name
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.productStockPointId != null and params.productStockPointId != ''">
                and product_stock_point_id = #{params.productStockPointId,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productName != null and params.productName != ''">
                and product_name = #{params.productName,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointName != null and params.stockPointName != ''">
                and stock_point_name = #{params.stockPointName,jdbcType=VARCHAR}
            </if>
            <if test="params.boxType != null and params.boxType != ''">
                and box_type = #{params.boxType,jdbcType=VARCHAR}
            </if>
            <if test="params.priority != null">
                and priority = #{params.priority,jdbcType=INTEGER}
            </if>
            <if test="params.standardLoad != null">
                and standard_load = #{params.standardLoad,jdbcType=INTEGER}
            </if>
            <if test="params.boxId != null and params.boxId != ''">
                and box_id = #{params.boxId,jdbcType=VARCHAR}
            </if>
            <if test="params.boxCode != null and params.boxCode != ''">
                and box_code = #{params.boxCode,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.kid != null and params.kid != ''">
                and kid = #{params.kid,jdbcType=VARCHAR}
            </if>
            <if test="params.lastUpdateTime != null">
                and last_update_time = #{params.lastUpdateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.planArea != null and params.planArea != ''">
                and plan_area = #{params.planArea,jdbcType=VARCHAR}
            </if>
            <if test="params.productStockPointIdList != null and params.productStockPointIdList.size() > 0">
                and product_stock_point_id in
                <foreach collection="params.productStockPointIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.kids != null and params.kids != ''">
                and kid in
                <foreach collection="params.kids" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_product_box_relation
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- 详情查询VO -->
    <select id="selectVODetailById" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_mds_product_box_relation
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_product_box_relation
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mds_product_box_relation
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_product_box_relation
        <include refid="Base_Where_Condition"/>
    </select>

    <select id="selectVOByProductCodeList" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_mds_product_box_relation
        where product_code in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="selectProBoxRelationByProStockPointId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_product_box_relation
        where product_stock_point_id in
        <foreach collection="productStockPointIdList" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mds.productBox.infrastructure.po.ProductBoxRelationPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mds_product_box_relation(
        id,
        product_stock_point_id,
        box_type,
        priority,
                                             standard_load,
        box_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        kid,
        last_update_time,
        plan_area)
        values (
        #{id,jdbcType=VARCHAR},
        #{productStockPointId,jdbcType=VARCHAR},
        #{boxType,jdbcType=VARCHAR},
        #{priority,jdbcType=INTEGER},
        #{standardLoad,jdbcType=INTEGER},
        #{boxId,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{kid,jdbcType=VARCHAR},
        #{lastUpdateTime,jdbcType=TIMESTAMP},
        #{planArea,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mds.productBox.infrastructure.po.ProductBoxRelationPO">
        insert into mds_product_box_relation(id,
                                             product_stock_point_id,
                                             box_type,
                                             priority,
                                             standard_load,
                                             box_id,
                                             remark,
                                             enabled,
                                             creator,
                                             create_time,
                                             modifier,
                                             modify_time,
                                             version_value,
                                             kid,
                                             last_update_time,
                                             plan_area)
        values (#{id,jdbcType=VARCHAR},
                #{productStockPointId,jdbcType=VARCHAR},
                #{boxType,jdbcType=VARCHAR},
                #{priority,jdbcType=INTEGER},
                #{standardLoad,jdbcType=INTEGER},
                #{boxId,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER},
                #{kid,jdbcType=VARCHAR},
                #{lastUpdateTime,jdbcType=TIMESTAMP},
                #{planArea,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mds_product_box_relation(
        id,
        product_stock_point_id,
        box_type,
        priority,
                                             standard_load,
        box_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        kid,
        last_update_time,
        plan_area)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.productStockPointId,jdbcType=VARCHAR},
            #{entity.boxType,jdbcType=VARCHAR},
            #{entity.priority,jdbcType=INTEGER},
            #{entity.standardLoad,jdbcType=INTEGER},
            #{entity.boxId,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER},
            #{entity.kid,jdbcType=VARCHAR},
            #{entity.lastUpdateTime,jdbcType=TIMESTAMP},
            #{entity.planArea,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mds_product_box_relation(
        id,
        product_stock_point_id,
        box_type,
        priority,
        standard_load,
        box_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        kid,
        last_update_time,
        plan_area)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.productStockPointId,jdbcType=VARCHAR},
            #{entity.boxType,jdbcType=VARCHAR},
            #{entity.priority,jdbcType=INTEGER},
            #{entity.standardLoad,jdbcType=INTEGER},
            #{entity.boxId,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER},
            #{entity.kid,jdbcType=VARCHAR},
            #{entity.lastUpdateTime,jdbcType=TIMESTAMP},
            #{entity.planArea,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mds.productBox.infrastructure.po.ProductBoxRelationPO">
        update mds_product_box_relation
        set product_stock_point_id = #{productStockPointId,jdbcType=VARCHAR},
            box_type               = #{boxType,jdbcType=VARCHAR},
            priority               = #{priority,jdbcType=INTEGER},
            standard_load          = #{standardLoad,jdbcType=INTEGER},
            box_id                 = #{boxId,jdbcType=VARCHAR},
            remark                 = #{remark,jdbcType=VARCHAR},
            enabled                = #{enabled,jdbcType=VARCHAR},
            modifier               = #{modifier,jdbcType=VARCHAR},
            modify_time            = #{modifyTime,jdbcType=TIMESTAMP},
            kid                   = #{kid,jdbcType=VARCHAR},
            plan_area              = #{planArea,jdbcType=VARCHAR},
            last_update_time       = #{lastUpdateTime,jdbcType=TIMESTAMP},
            version_value          = version_value + 1
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mds.productBox.infrastructure.po.ProductBoxRelationPO">
        update mds_product_box_relation
        <set>
            <if test="item.productStockPointId != null and item.productStockPointId != ''">
                product_stock_point_id = #{item.productStockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.boxType != null and item.boxType != ''">
                box_type = #{item.boxType,jdbcType=VARCHAR},
            </if>
            <if test="item.priority != null">
                priority = #{item.priority,jdbcType=INTEGER},
            </if>
            <if test="item.standardLoad != null">
                standard_load = #{item.standardLoad,jdbcType=INTEGER},
            </if>
            <if test="item.boxId != null and item.boxId != ''">
                box_id = #{item.boxId,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.kid != null and item.kid != ''">
                kid = #{item.kid,jdbcType=VARCHAR},
            </if>
            <if test="item.lastUpdateTime != null">
                last_update_time = #{item.lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.planArea != null and item.planArea != ''">
                plan_area = #{item.planArea,jdbcType=VARCHAR},
            </if>
            version_value = version_value + 1,
        </set>
        where id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mds_product_box_relation
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="product_stock_point_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.productStockPointId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="box_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.boxType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="priority = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.priority,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="standard_load = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.standardLoad,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="box_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.boxId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then version_value + 1
                </foreach>
            </trim>
            <trim prefix="kid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.kid,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="last_update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lastUpdateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        <trim prefix="plan_area = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
            when id = #{item.id,jdbcType=VARCHAR} then #{item.planArea,jdbcType=VARCHAR}
        </foreach>
        </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mds_product_box_relation
            <set>
                <if test="item.productStockPointId != null and item.productStockPointId != ''">
                    product_stock_point_id = #{item.productStockPointId,jdbcType=VARCHAR},
                </if>
                <if test="item.boxType != null and item.boxType != ''">
                    box_type = #{item.boxType,jdbcType=VARCHAR},
                </if>
                <if test="item.priority != null">
                    priority = #{item.priority,jdbcType=INTEGER},
                </if>
                <if test="item.standardLoad != null">
                    standard_load = #{item.standardLoad,jdbcType=INTEGER},
                </if>
                <if test="item.boxId != null and item.boxId != ''">
                    box_id = #{item.boxId,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
                <if test="item.kid != null and item.kid != ''">
                    kid = #{item.kid,jdbcType=VARCHAR},
                </if>
                <if test="item.lastUpdateTime != null">
                    last_update_time = #{item.lastUpdateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.planArea != null and item.planArea != ''">
                    plan_area = #{item.planArea,jdbcType=VARCHAR},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mds_product_box_relation
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mds_product_box_relation where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <delete id="deleteBatchVersion" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            delete from mds_product_box_relation where
            id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </delete>
</mapper>
