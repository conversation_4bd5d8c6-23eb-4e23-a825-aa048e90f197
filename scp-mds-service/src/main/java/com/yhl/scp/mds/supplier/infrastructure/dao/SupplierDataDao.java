package com.yhl.scp.mds.supplier.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpSupplier;
import com.yhl.scp.mds.extension.supplier.infrastructure.po.SupplierPO;
import com.yhl.scp.mds.extension.supplier.vo.SupplierVO;
import com.yhl.scp.mds.newproduct.infrastructure.po.NewProductStockPointPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>SupplierDao</code>
 * <p>
 * 供应商DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-08-09 16:19:48
 */
public interface SupplierDataDao extends BaseDao<SupplierPO, SupplierVO> {
    @Override
    SupplierPO selectByPrimaryKey(@Param("id") String id);;
    @Override
    List<SupplierPO> selectByPrimaryKeys(@Param("ids") List<String> ids);


    List<SupplierVO> selectByCondition(@Param("sortParam") String sortParam,@Param("queryCriteriaParam") String queryCriteriaParam);

    List<SupplierPO> selectByParams(@Param("params") Map<String, Object> params);
    @Override
    int insert(SupplierPO obj);
    @Override
    int insertBatch(@Param("list") List<SupplierPO> list);
    @Override
    int update(SupplierPO obj);
    @Override
    int updateSelective(@Param("item") SupplierPO obj);
    @Override
    int updateBatch(@Param("list") List<SupplierPO> list);
    @Override
    int updateBatchSelective(@Param("list") List<SupplierPO> list);

    List<SupplierPO> selectBySupplierIds(@Param("list") List<ErpSupplier> list);

	List<SupplierVO> selectSeaTransportation();

    List<SupplierPO> selectSupplierLike(String supplierName);
}
