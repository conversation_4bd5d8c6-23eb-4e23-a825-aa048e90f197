package com.yhl.scp.mds.mold.convertor;

import com.yhl.scp.mds.mold.domain.entity.MoldToolingGroupDO;
import com.yhl.scp.mds.mold.dto.MoldToolingGroupDTO;
import com.yhl.scp.mds.mold.infrastructure.po.MoldToolingGroupPO;
import com.yhl.scp.mds.mold.vo.MoldToolingGroupVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>MoldToolingGroupConvertor</code>
 * <p>
 * 模具工装族转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-11 10:26:14
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MoldToolingGroupConvertor {

    MoldToolingGroupConvertor INSTANCE = Mappers.getMapper(MoldToolingGroupConvertor.class);

    MoldToolingGroupDO dto2Do(MoldToolingGroupDTO obj);

    MoldToolingGroupDTO do2Dto(MoldToolingGroupDO obj);

    List<MoldToolingGroupDO> dto2Dos(List<MoldToolingGroupDTO> list);

    List<MoldToolingGroupDTO> do2Dtos(List<MoldToolingGroupDO> list);

    MoldToolingGroupVO do2Vo(MoldToolingGroupDO obj);

    MoldToolingGroupVO po2Vo(MoldToolingGroupPO obj);

    List<MoldToolingGroupVO> po2Vos(List<MoldToolingGroupPO> list);

    MoldToolingGroupPO do2Po(MoldToolingGroupDO obj);

    MoldToolingGroupDO po2Do(MoldToolingGroupPO obj);

    MoldToolingGroupPO dto2Po(MoldToolingGroupDTO obj);

    List<MoldToolingGroupPO> dto2Pos(List<MoldToolingGroupDTO> obj);

}
