<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mds.routing.infrastructure.dao.BomTreeNewDao">
    <select id="selectTopBomVos" resultType="com.yhl.scp.mds.routing.vo.BomTreeNewVO">
        SELECT DISTINCT
        p.id AS procuctId,
        p.product_code AS productCode,
        p.product_name AS productName,
        p.product_type AS productType,
        sp.id AS stockPointId,
        sp.stock_point_code AS stockPointCode,
        br.id AS routingId,
        br.routing_code AS routingCode,
        mpsp.switch_mode AS switchStrategy,
        mpsp.switch_date AS switchTime,
        p1.product_code AS switchProduct
        FROM
        mds_rou_routing br
        LEFT JOIN mds_stock_point sp ON sp.stock_point_code = br.stock_point_id
        LEFT JOIN mds_product_stock_point p ON p.id = br.product_id
        LEFT JOIN mds_pro_switchover_product mpsp ON br.product_id = mpsp.before_product_id AND sp.id =
        mpsp.stock_point_id
        AND mpsp.switch_date &lt;= SYSDATE()
        LEFT JOIN mds_product_stock_point p1 ON p1.id = mpsp.after_product_id
        WHERE
        br.enabled = 'YES'
         AND p.product_type = 'FG'
        <if test="productCode != null and productCode != ''">
            AND p.product_code like concat('%', #{productCode,jdbcType=VARCHAR}, '%')
        </if>
    </select>
    <select id="selectProductChildren" resultType="com.yhl.scp.mds.extension.routing.vo.BomRoutingStepInputVO">
        SELECT p.product_code AS productCode,
        p.product_name AS productName,
        p.product_type AS productType,
        sp.stock_point_code AS stockingPointCode,
        sp.id AS stockPointId,
        sp.stock_point_name AS stockPointName,
        br.id AS routingId,
        br.routing_code AS routingCode,
        br.effective_time AS effectiveTime,
        br.expiry_time AS expiryTime,
        rsi.yield,
        rsi.scrap,
        rsi.max_connection_duration AS maxConnectionDuration,
        rsi.min_connection_duration AS minConnectionDuration,
        rsi.connection_task AS connectionTask,
        rsi.connection_type AS connectionType,
        rsi.input_factor AS inputFactor,
        rsi.alt_material_group AS altMaterialGroup,
        rsi.match_code AS matchCode,
        rsi.main_material AS mainMaterial,
        rsi.alt_type AS altType,
        rsi.alt_ratio AS altRatio,
        rsi.routing_step_id AS routingStepId,
        rsi.input_product_id AS inputProductId,
        rsi.id as routingStepInputId,
        rsi.alt_material_group AS altMaterialGroup,
        unit.unit_desc AS countingUnit,
        mpsp.switch_mode AS switchStrategy,
        mpsp.switch_date AS switchTime,
        p1.product_code AS switchProduct
        FROM mds_rou_routing_step_input rsi
        LEFT JOIN mds_rou_routing_step rs ON rs.id = rsi.routing_step_id
        LEFT JOIN mds_rou_routing br ON rsi.routing_id = br.id
        JOIN mds_stock_point sp ON sp.stock_point_code = rsi.stock_point_id
        JOIN mds_product_stock_point p ON p.id = rsi.input_product_id
        LEFT JOIN mds_uni_unit unit ON unit.id = rsi.counting_unit_id
        LEFT JOIN mds_pro_switchover_product mpsp
        ON rsi.input_product_id = mpsp.before_product_id AND rsi.stock_point_id = mpsp.stock_point_id
        AND mpsp.switch_date &lt;= SYSDATE()
        LEFT JOIN mds_product_stock_point p1 ON p1.id = mpsp.after_product_id
        WHERE br.enabled = 'YES'
        AND br.product_id = #{productId}
        <if test="routingId != null and routingId != ''">
            AND br.id = #{routingId}
        </if>
    </select>
    <select id="selectByCondition" resultType="com.yhl.scp.mds.routing.vo.BomTreeNewVO">
        SELECT
        p.product_code AS productCode,
        p.product_name AS productName,
        p.product_type AS productType,
        sp.stock_point_code AS stockPointCode,
        sp.stock_point_name AS stockPointName,
        br.stock_point_id AS stockPointId,
        br.product_id AS productId,
        br.id AS routingId,
        br.routing_code AS routingCode,
        br.effective_time AS effectiveTime,
        br.expiry_time AS expiryTime,
        br.priority,
        p.enabled as productStatus,
        p.enabled as materialStatus,
        rst.step_id AS routingStepId,
        rst.standard_step_code AS routingStepCode,
        mpsp.switch_mode AS switchStrategy,
        mpsp.switch_date AS switchTime,
        p1.product_code AS switchProduct
        FROM
        mds_rou_routing br
        LEFT JOIN mds_product_stock_point p ON br.product_id = p.id
        LEFT JOIN mds_stock_point sp ON sp.stock_point_code = br.stock_point_id
        LEFT JOIN (
        SELECT
        a.id AS step_id,
        a.routing_id,
        sor.standard_step_code,
        a.connection_type,
        a.connection_task
        FROM
        mds_rou_routing_step a
        INNER JOIN ( SELECT routing_id, min( sequence_no ) AS min_nr FROM mds_rou_routing_step GROUP BY routing_id ) b
        ON a.routing_id =
        b.routing_id
        AND a.sequence_no = b.min_nr
        LEFT JOIN mds_rou_standard_step sor ON sor.id = a.standard_step_id
        ) rst ON br.id = rst.routing_id
        LEFT JOIN mds_pro_switchover_product mpsp ON br.product_id = mpsp.before_product_id AND sp.id =
        mpsp.stock_point_id
        AND mpsp.switch_date &lt;= SYSDATE()
        LEFT JOIN mds_product_stock_point p1 ON p1.id = mpsp.after_product_id
        WHERE
        br.enabled = 'YES'
        AND p.product_type = 'FG'
        <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
            ${queryCriteriaParam}
        </if>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <select id="selectProductByProductIds" resultType="java.lang.String">
        SELECT DISTINCT p.id
        FROM mds_product_stock_point p
        LEFT JOIN mds_rou_routing br ON br.product_id = p.id
        LEFT JOIN mds_rou_routing_step_input rsi ON rsi.routing_id = br.id
        WHERE
        rsi.input_product_id IN
        <foreach item="item" index="index" collection="productIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND p.product_type = #{type}
    </select>
    <select id="selectRoutingStepInputByParams" resultType="com.yhl.scp.mds.extension.routing.vo.BomRoutingStepInputVO">
        SELECT rsi.input_factor AS inputFactor,
        rsi.routing_id AS routingId,
        rsi.input_product_id AS inputProductId,
        rsi.stock_point_id AS stockPointId,
        rsi.percentage_scrap_rate AS percentageScrapRate,
        rsi.scrap AS scrap,
        rsi.scrap_strategy AS scrapStrategy,
        rsi.yield AS yield,
        rsi.id as routingStepInputId,
        rsi.routing_step_id as routingStepId,
        rsi.main_material as mainMaterial,
        rsi.match_code as matchCode,
        rsi.alt_mode as altMode,
        rsi.alt_ratio as altRatio,
        rsi.alt_material_group AS altMaterialGroup,
        rsi.connection_task AS connectionTask,
        rsi.connection_type AS connectionType,
        p.product_code AS productCode,
        p.product_name AS productName,
        p.product_type AS productType,
        sp.stock_point_code AS stockPointCode,
        sp.stock_point_name AS stockPointName,
        r.effective_time AS effectiveTime,
        r.expiry_time AS expiryTime,
        r.priority,
        unit.unit_desc AS countingUnit,
        mpsp.switch_mode AS switchStrategy,
        mpsp.switch_date AS switchTime,
        p1.product_code AS switchProduct,
        r.product_code AS sourceProductCode
        FROM mds_rou_routing_step_input rsi
        LEFT JOIN mds_rou_routing r ON r.id = rsi.routing_id
        JOIN mds_product_stock_point p ON p.id = rsi.input_product_id
        JOIN mds_stock_point sp ON sp.id = rsi.stock_point_id
        LEFT JOIN mds_uni_unit unit ON unit.id = rsi.counting_unit_id
        LEFT JOIN mds_pro_switchover_product mpsp ON rsi.input_product_id = mpsp.before_product_id AND
        rsi.stock_point_id = mpsp.stock_point_id
        AND mpsp.switch_date &lt;= SYSDATE()
        LEFT JOIN mds_product_stock_point p1 ON p1.id = mpsp.after_product_id
        WHERE rsi.enabled = 'YES'
          and r.enabled = 'YES'
        <if test="params.routingId != null and params.routingId != ''">
            and rsi.routing_id = #{params.routingId,jdbcType=VARCHAR}
        </if>
        <if test="params.routingStepId != null and params.routingStepId != ''">
            and rsi.routing_step_id = #{params.routingStepId,jdbcType=VARCHAR}
        </if>
        <if test="params.routingStepIds != null and params.routingStepIds.size() > 0">
            and rsi.routing_step_id in
            <foreach collection="params.routingStepIds" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="params.inputProductId != null and params.inputProductId != ''">
            and rsi.input_product_id = #{params.inputProductId,jdbcType=VARCHAR}
        </if>
        <if test="params.stockPointId != null and params.stockPointId != ''">
            and rsi.stock_point_id = #{params.stockPointId,jdbcType=VARCHAR}
        </if>
        <if test="params.productType != null and params.productType != ''">
            and p.product_type = #{params.productType}
        </if>
        <if test="params.productCode != null and params.productCode != ''">
            and r.product_code = #{params.productCode}
        </if>
        <if test="params.productCodeList != null and params.productCodeList.size() > 0">
            and r.product_code in
            <foreach collection="params.productCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="selectBomTreeByParams" resultType="com.yhl.scp.mds.routing.vo.BomTreeNewVO">
        SELECT
        p.product_code AS productCode,
        p.product_name AS productName,
        p.product_type AS productType,
        sp.stock_point_code AS stockPointCode,
        sp.stock_point_name AS stockPointName,
        br.stock_point_id AS stockPointId,
        br.product_id AS productId,
        br.id AS routingId,
        br.routing_code AS routingCode,
        br.effective_time AS effectiveTime,
        br.expiry_time AS expiryTime,
        br.priority,
        rst.step_id AS routingStepId,
        rst.standard_step_code AS routingStepCode,
        mpsp.switch_mode AS switchStrategy,
        mpsp.switch_date AS switchTime,
        p1.product_code AS switchProduct
        FROM
        mds_rou_routing br
        LEFT JOIN mds_product_stock_point p ON br.product_id = p.id
        LEFT JOIN mds_stock_point sp ON sp.stock_point_code = br.stock_point_id
        LEFT JOIN (
        SELECT
        a.id AS step_id,
        a.routing_id,
        sor.standard_step_code,
        a.connection_type,
        a.connection_task
        FROM
        mds_rou_routing_step a
        INNER JOIN ( SELECT routing_id, min( sequence_no ) AS min_nr FROM mds_rou_routing_step GROUP BY routing_id ) b
        ON a.routing_id =
        b.routing_id
        AND a.sequence_no = b.min_nr
        LEFT JOIN mds_rou_standard_step sor ON sor.id = a.standard_step_id
        ) rst ON br.id = rst.routing_id
        LEFT JOIN mds_pro_switchover_product mpsp ON br.product_id = mpsp.before_product_id AND sp.id =
        mpsp.stock_point_id
        AND mpsp.switch_date &lt;= SYSDATE()
        LEFT JOIN mds_product_stock_point p1 ON p1.id = mpsp.after_product_id
        WHERE
        br.enabled = 'YES'
        AND p.product_type = 'FG'
        <if test="params.productIds != null and params.productIds.size() > 0">
            AND br.product_id IN
            <foreach item="item" index="index" collection="params.productIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.routingIds != null and params.routingIds.size() > 0">
            AND br.id IN
            <foreach item="item" index="index" collection="params.routingIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.productId != null and params.productId != ''">
            and br.product_id = #{params.productId}
        </if>
        <if test="params.stockPointId != null and params.stockPointId != ''">
            and sp.id = #{params.stockPointId}
        </if>
        <if test="params.routingId != null and params.routingId != ''">
            and br.id = #{params.routingId}
        </if>
        <if test="params.routingStepId != null and params.routingStepId != ''">
            and rst.step_id = #{params.routingStepId}
        </if>
        <if test="params.productType != null and params.productType != ''">
            and p.product_type = #{params.productType}
        </if>
    </select>
    <select id="selectRoutingProductByProductIds" resultType="com.yhl.scp.mds.extension.routing.vo.RoutingProductVO">
        SELECT DISTINCT p.id AS productId,br.id AS routingId
        FROM mds_product_stock_point p
        LEFT JOIN mds_rou_routing br ON br.product_id = p.id
        LEFT JOIN mds_rou_routing_step_input rsi ON rsi.routing_id = br.id
        WHERE
        rsi.input_product_id IN
        <foreach item="item" index="index" collection="productIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectRoutingByInputProductIds" resultType="com.yhl.scp.mds.routing.vo.BomTreeNewVO">
        SELECT
        p.id as productId,
        p.product_code as productCode,
        p.product_name as productName,
        p.product_type as productType,
        p.vehicle_model_code as vehicleModelCode,
        rsi.input_product_id as inputProductId
        FROM mds_product_stock_point p
        LEFT JOIN mds_rou_routing br ON br.product_id = p.id
        LEFT JOIN mds_rou_routing_step_input rsi ON rsi.routing_id = br.id
        WHERE
        rsi.input_product_id IN
        <foreach item="item" index="index" collection="productIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND p.product_type = #{type}
    </select>
</mapper>
