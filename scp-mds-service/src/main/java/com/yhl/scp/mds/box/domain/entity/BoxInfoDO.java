package com.yhl.scp.mds.box.domain.entity;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>BoxInfoDO</code>
 * <p>
 * 箱体信息DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:46:36
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class BoxInfoDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = -31605287717934895L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 箱子类型：铁箱/纸箱
     */
    private String boxType;
    /**
     * 箱子编码
     */
    private String boxCode;
    /**
     * 单箱片数
     */
    private Integer piecePerBox;
    /**
     * 单排箱数
     */
    private Integer boxPerRow;
    /**
     * 单垛数量
     */
    private Integer perStackQuantity;
    /**
     * 箱数
     */
    private Integer boxQuantity;
    /**
     * 箱长
     */
    private BigDecimal boxLength;
    /**
     * 箱宽
     */
    private BigDecimal boxWidth;
    /**
     * 箱高
     */
    private BigDecimal boxHeight;
    /**
     * 库存点ID
     */
    private String stockPointId;
    /**
     * kid
     */
    private String kid;
    /**
     * 最后修改时间
     */
    private Date lastUpdateTime;
    /**
     * 计划区域
     */
    private String planArea;

}
