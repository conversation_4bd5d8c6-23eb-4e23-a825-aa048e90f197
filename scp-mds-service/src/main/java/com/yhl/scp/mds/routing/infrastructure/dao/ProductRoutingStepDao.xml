<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mds.routing.infrastructure.dao.ProductRoutingStepDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mds.routing.infrastructure.po.ProductRoutingStepPO">
        <!--@Table mds_rou_product_routing_step-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="routing_id" jdbcType="VARCHAR" property="routingId"/>
        <result column="operation_sequence_id" jdbcType="VARCHAR" property="operationSequenceId"/>
        <result column="sequence_no" jdbcType="INTEGER" property="sequenceNo"/>
        <result column="pre_routing_step_sequence_no" jdbcType="VARCHAR" property="preRoutingStepSequenceNo"/>
        <result column="next_routing_step_sequence_no" jdbcType="VARCHAR" property="nextRoutingStepSequenceNo"/>
        <result column="yield" jdbcType="VARCHAR" property="yield"/>
        <result column="scrap_strategy" jdbcType="VARCHAR" property="scrapStrategy"/>
        <result column="percentage_scrap_rate" jdbcType="VARCHAR" property="percentageScrapRate"/>
        <result column="scrap" jdbcType="VARCHAR" property="scrap"/>
        <result column="pre_process_ratio" jdbcType="VARCHAR" property="preProcessRatio"/>
        <result column="processing_type" jdbcType="VARCHAR" property="processingType"/>
        <result column="connection_task" jdbcType="VARCHAR" property="connectionTask"/>
        <result column="connection_type" jdbcType="VARCHAR" property="connectionType"/>
        <result column="max_connection_duration" jdbcType="INTEGER" property="maxConnectionDuration"/>
        <result column="min_connection_duration" jdbcType="INTEGER" property="minConnectionDuration"/>
        <result column="standard_step_id" jdbcType="VARCHAR" property="standardStepId"/>
        <result column="counting_unit_id" jdbcType="VARCHAR" property="countingUnitId"/>
        <result column="effective" jdbcType="VARCHAR" property="effective"/>
        <result column="expire_reason" jdbcType="VARCHAR" property="expireReason"/>
        <result column="last_update_date" jdbcType="TIMESTAMP" property="lastUpdateDate"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="start_date" jdbcType="TIMESTAMP" property="startDate"/>
        <result column="disable_date" jdbcType="TIMESTAMP" property="disableDate"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mds.routing.vo.ProductRoutingStepVO">
    	<result column="routing_code" jdbcType="VARCHAR" property="routingCode"/>
    	<result column="standard_step_code" jdbcType="VARCHAR" property="standardStepCode"/>
    	<result column="standard_step_name" jdbcType="VARCHAR" property="standardStepName"/>
        <result column="flag" jdbcType="VARCHAR" property="flag"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
		routing_id,
		operation_sequence_id,
		sequence_no,
		pre_routing_step_sequence_no,
		next_routing_step_sequence_no,
		yield,
		scrap_strategy,
		percentage_scrap_rate,
		scrap,
		pre_process_ratio,
		processing_type,
		connection_task,
		connection_type,
		max_connection_duration,
		min_connection_duration,
		standard_step_id,
		counting_unit_id,
		effective,
		expire_reason,
		last_update_date,
		version_value,
		remark,
		enabled,
		creator,
		create_time,
		modifier,
		modify_time,start_date,disable_date
    </sql>
    <sql id="VO_Column_List">
        <include refid="Base_Column_List"/>,
        routing_code,
        standard_step_code,
        standard_step_name,
        flag
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.routingId != null and params.routingId != ''">
                and routing_id = #{params.routingId,jdbcType=VARCHAR}
            </if>
            <if test="params.operationSequenceId != null and params.operationSequenceId != ''">
                and operation_sequence_id = #{params.operationSequenceId,jdbcType=VARCHAR}
            </if>
            <if test="params.operationSequenceIds != null and params.operationSequenceIds.size()>0">
                and operation_sequence_id in
                <foreach collection="params.operationSequenceIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>

            <if test="params.sequenceNo != null">
                and sequence_no = #{params.sequenceNo,jdbcType=INTEGER}
            </if>
            <if test="params.preRoutingStepSequenceNo != null and params.preRoutingStepSequenceNo != ''">
                and pre_routing_step_sequence_no = #{params.preRoutingStepSequenceNo,jdbcType=VARCHAR}
            </if>
            <if test="params.nextRoutingStepSequenceNo != null and params.nextRoutingStepSequenceNo != ''">
                and next_routing_step_sequence_no = #{params.nextRoutingStepSequenceNo,jdbcType=VARCHAR}
            </if>
            <if test="params.yield != null">
                and yield = #{params.yield,jdbcType=VARCHAR}
            </if>
            <if test="params.scrapStrategy != null and params.scrapStrategy != ''">
                and scrap_strategy = #{params.scrapStrategy,jdbcType=VARCHAR}
            </if>
            <if test="params.percentageScrapRate != null">
                and percentage_scrap_rate = #{params.percentageScrapRate,jdbcType=VARCHAR}
            </if>
            <if test="params.scrap != null">
                and scrap = #{params.scrap,jdbcType=VARCHAR}
            </if>
            <if test="params.preProcessRatio != null">
                and pre_process_ratio = #{params.preProcessRatio,jdbcType=VARCHAR}
            </if>
            <if test="params.processingType != null and params.processingType != ''">
                and processing_type = #{params.processingType,jdbcType=VARCHAR}
            </if>
            <if test="params.connectionTask != null and params.connectionTask != ''">
                and connection_task = #{params.connectionTask,jdbcType=VARCHAR}
            </if>
            <if test="params.connectionType != null and params.connectionType != ''">
                and connection_type = #{params.connectionType,jdbcType=VARCHAR}
            </if>
            <if test="params.maxConnectionDuration != null">
                and max_connection_duration = #{params.maxConnectionDuration,jdbcType=INTEGER}
            </if>
            <if test="params.minConnectionDuration != null">
                and min_connection_duration = #{params.minConnectionDuration,jdbcType=INTEGER}
            </if>
            <if test="params.standardStepId != null and params.standardStepId != ''">
                and standard_step_id = #{params.standardStepId,jdbcType=VARCHAR}
            </if>
            <if test="params.countingUnitId != null and params.countingUnitId != ''">
                and counting_unit_id = #{params.countingUnitId,jdbcType=VARCHAR}
            </if>
            <if test="params.effective != null and params.effective != ''">
                and effective = #{params.effective,jdbcType=VARCHAR}
            </if>
            <if test="params.expireReason != null and params.expireReason != ''">
                and expire_reason = #{params.expireReason,jdbcType=VARCHAR}
            </if>
            <if test="params.lastUpdateDate != null">
                and last_update_date = #{params.lastUpdateDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.startDate != null">
                and start_date = #{params.startDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.disableDate != null">
                and disable_date = #{params.disableDate,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_rou_product_routing_step
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_rou_product_routing_step
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mds_rou_product_routing_step
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_rou_product_routing_step
        <include refid="Base_Where_Condition"/>
    </select>
    <select id="selectByOperationSequenceIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_rou_product_routing_step
        where enabled='YES'
        and operation_sequence_id in
        <foreach collection="operationSequenceIds" item="operationSequenceId" index="index" open="(" separator="," close=")">
            #{operationSequenceId,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="selectByRoutingIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_rou_product_routing_step
        where  routing_id in
        <foreach collection="routingIds" item="routingId" index="index" open="(" separator="," close=")">
            #{routingId,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mds.routing.infrastructure.po.ProductRoutingStepPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mds_rou_product_routing_step(
        id,
        routing_id,
        operation_sequence_id,
        sequence_no,
        pre_routing_step_sequence_no,
        next_routing_step_sequence_no,
        yield,
        scrap_strategy,
        percentage_scrap_rate,
        scrap,
        pre_process_ratio,
        processing_type,
        connection_task,
        connection_type,
        max_connection_duration,
        min_connection_duration,
        standard_step_id,
        counting_unit_id,
        effective,
        expire_reason,
        last_update_date,
        version_value,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        start_date,
        disable_date)
        values (
        #{id,jdbcType=VARCHAR},
        #{routingId,jdbcType=VARCHAR},
        #{operationSequenceId,jdbcType=VARCHAR},
        #{sequenceNo,jdbcType=INTEGER},
        #{preRoutingStepSequenceNo,jdbcType=VARCHAR},
        #{nextRoutingStepSequenceNo,jdbcType=VARCHAR},
        #{yield,jdbcType=VARCHAR},
        #{scrapStrategy,jdbcType=VARCHAR},
        #{percentageScrapRate,jdbcType=VARCHAR},
        #{scrap,jdbcType=VARCHAR},
        #{preProcessRatio,jdbcType=VARCHAR},
        #{processingType,jdbcType=VARCHAR},
        #{connectionTask,jdbcType=VARCHAR},
        #{connectionType,jdbcType=VARCHAR},
        #{maxConnectionDuration,jdbcType=INTEGER},
        #{minConnectionDuration,jdbcType=INTEGER},
        #{standardStepId,jdbcType=VARCHAR},
        #{countingUnitId,jdbcType=VARCHAR},
        #{effective,jdbcType=VARCHAR},
        #{expireReason,jdbcType=VARCHAR},
        #{lastUpdateDate,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{startDate,jdbcType=TIMESTAMP},
        #{disableDate,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mds.routing.infrastructure.po.ProductRoutingStepPO">
        insert into mds_rou_product_routing_step(id,
                                                 routing_id,
                                                 operation_sequence_id,
                                                 sequence_no,
                                                 pre_routing_step_sequence_no,
                                                 next_routing_step_sequence_no,
                                                 yield,
                                                 scrap_strategy,
                                                 percentage_scrap_rate,
                                                 scrap,
                                                 pre_process_ratio,
                                                 processing_type,
                                                 connection_task,
                                                 connection_type,
                                                 max_connection_duration,
                                                 min_connection_duration,
                                                 standard_step_id,
                                                 counting_unit_id,
                                                 effective,
                                                 expire_reason,
                                                 last_update_date,
                                                 version_value,
                                                 remark,
                                                 enabled,
                                                 creator,
                                                 create_time,
                                                 modifier,
                                                 modify_time,
                                                 start_date,
                                                 disable_date)
        values (#{id,jdbcType=VARCHAR},
                #{routingId,jdbcType=VARCHAR},
                #{operationSequenceId,jdbcType=VARCHAR},
                #{sequenceNo,jdbcType=INTEGER},
                #{preRoutingStepSequenceNo,jdbcType=VARCHAR},
                #{nextRoutingStepSequenceNo,jdbcType=VARCHAR},
                #{yield,jdbcType=VARCHAR},
                #{scrapStrategy,jdbcType=VARCHAR},
                #{percentageScrapRate,jdbcType=VARCHAR},
                #{scrap,jdbcType=VARCHAR},
                #{preProcessRatio,jdbcType=VARCHAR},
                #{processingType,jdbcType=VARCHAR},
                #{connectionTask,jdbcType=VARCHAR},
                #{connectionType,jdbcType=VARCHAR},
                #{maxConnectionDuration,jdbcType=INTEGER},
                #{minConnectionDuration,jdbcType=INTEGER},
                #{standardStepId,jdbcType=VARCHAR},
                #{countingUnitId,jdbcType=VARCHAR},
                #{effective,jdbcType=VARCHAR},
                #{expireReason,jdbcType=VARCHAR},
                #{lastUpdateDate,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{startDate,jdbcType=TIMESTAMP},
                #{disableDate,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mds_rou_product_routing_step(
        id,
        routing_id,
        operation_sequence_id,
        sequence_no,
        pre_routing_step_sequence_no,
        next_routing_step_sequence_no,
        yield,
        scrap_strategy,
        percentage_scrap_rate,
        scrap,
        pre_process_ratio,
        processing_type,
        connection_task,
        connection_type,
        max_connection_duration,
        min_connection_duration,
        standard_step_id,
        counting_unit_id,
        effective,
        expire_reason,
        last_update_date,
        version_value,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        start_date,
        disable_date)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.routingId,jdbcType=VARCHAR},
            #{entity.operationSequenceId,jdbcType=VARCHAR},
            #{entity.sequenceNo,jdbcType=INTEGER},
            #{entity.preRoutingStepSequenceNo,jdbcType=VARCHAR},
            #{entity.nextRoutingStepSequenceNo,jdbcType=VARCHAR},
            #{entity.yield,jdbcType=VARCHAR},
            #{entity.scrapStrategy,jdbcType=VARCHAR},
            #{entity.percentageScrapRate,jdbcType=VARCHAR},
            #{entity.scrap,jdbcType=VARCHAR},
            #{entity.preProcessRatio,jdbcType=VARCHAR},
            #{entity.processingType,jdbcType=VARCHAR},
            #{entity.connectionTask,jdbcType=VARCHAR},
            #{entity.connectionType,jdbcType=VARCHAR},
            #{entity.maxConnectionDuration,jdbcType=INTEGER},
            #{entity.minConnectionDuration,jdbcType=INTEGER},
            #{entity.standardStepId,jdbcType=VARCHAR},
            #{entity.countingUnitId,jdbcType=VARCHAR},
            #{entity.effective,jdbcType=VARCHAR},
            #{entity.expireReason,jdbcType=VARCHAR},
            #{entity.lastUpdateDate,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.startDate,jdbcType=TIMESTAMP},
            #{entity.disableDate,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mds_rou_product_routing_step(
        id,
        routing_id,
        operation_sequence_id,
        sequence_no,
        pre_routing_step_sequence_no,
        next_routing_step_sequence_no,
        yield,
        scrap_strategy,
        percentage_scrap_rate,
        scrap,
        pre_process_ratio,
        processing_type,
        connection_task,
        connection_type,
        max_connection_duration,
        min_connection_duration,
        standard_step_id,
        counting_unit_id,
        effective,
        expire_reason,
        last_update_date,
        version_value,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        start_date,
        disable_date)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.routingId,jdbcType=VARCHAR},
            #{entity.operationSequenceId,jdbcType=VARCHAR},
            #{entity.sequenceNo,jdbcType=INTEGER},
            #{entity.preRoutingStepSequenceNo,jdbcType=VARCHAR},
            #{entity.nextRoutingStepSequenceNo,jdbcType=VARCHAR},
            #{entity.yield,jdbcType=VARCHAR},
            #{entity.scrapStrategy,jdbcType=VARCHAR},
            #{entity.percentageScrapRate,jdbcType=VARCHAR},
            #{entity.scrap,jdbcType=VARCHAR},
            #{entity.preProcessRatio,jdbcType=VARCHAR},
            #{entity.processingType,jdbcType=VARCHAR},
            #{entity.connectionTask,jdbcType=VARCHAR},
            #{entity.connectionType,jdbcType=VARCHAR},
            #{entity.maxConnectionDuration,jdbcType=INTEGER},
            #{entity.minConnectionDuration,jdbcType=INTEGER},
            #{entity.standardStepId,jdbcType=VARCHAR},
            #{entity.countingUnitId,jdbcType=VARCHAR},
            #{entity.effective,jdbcType=VARCHAR},
            #{entity.expireReason,jdbcType=VARCHAR},
            #{entity.lastUpdateDate,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.startDate,jdbcType=TIMESTAMP},
            #{entity.disableDate,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mds.routing.infrastructure.po.ProductRoutingStepPO">
        update mds_rou_product_routing_step
        set routing_id                    = #{routingId,jdbcType=VARCHAR},
            operation_sequence_id         = #{operationSequenceId,jdbcType=VARCHAR},
            sequence_no                   = #{sequenceNo,jdbcType=INTEGER},
            pre_routing_step_sequence_no  = #{preRoutingStepSequenceNo,jdbcType=VARCHAR},
            next_routing_step_sequence_no = #{nextRoutingStepSequenceNo,jdbcType=VARCHAR},
            yield                         = #{yield,jdbcType=VARCHAR},
            scrap_strategy                = #{scrapStrategy,jdbcType=VARCHAR},
            percentage_scrap_rate         = #{percentageScrapRate,jdbcType=VARCHAR},
            scrap                         = #{scrap,jdbcType=VARCHAR},
            pre_process_ratio             = #{preProcessRatio,jdbcType=VARCHAR},
            processing_type               = #{processingType,jdbcType=VARCHAR},
            connection_task               = #{connectionTask,jdbcType=VARCHAR},
            connection_type               = #{connectionType,jdbcType=VARCHAR},
            max_connection_duration       = #{maxConnectionDuration,jdbcType=INTEGER},
            min_connection_duration       = #{minConnectionDuration,jdbcType=INTEGER},
            standard_step_id              = #{standardStepId,jdbcType=VARCHAR},
            counting_unit_id              = #{countingUnitId,jdbcType=VARCHAR},
            effective                     = #{effective,jdbcType=VARCHAR},
            expire_reason                 = #{expireReason,jdbcType=VARCHAR},
            last_update_date              = #{lastUpdateDate,jdbcType=TIMESTAMP},
            version_value                 = #{versionValue,jdbcType=INTEGER},
            remark                        = #{remark,jdbcType=VARCHAR},
            enabled                       = #{enabled,jdbcType=VARCHAR},
            modifier                      = #{modifier,jdbcType=VARCHAR},
            modify_time                   = #{modifyTime,jdbcType=TIMESTAMP},
            start_date                    = #{startDate,jdbcType=TIMESTAMP},
            disable_date                  = #{disableDate,jdbcType=TIMESTAMP},
            version_value = version_value + 1
        where id = #{id,jdbcType=VARCHAR} and version_value = #{versionValue,jdbcType=INTEGER}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mds.routing.infrastructure.po.ProductRoutingStepPO">
        update mds_rou_product_routing_step
        <set>
            <if test="item.routingId != null and item.routingId != ''">
                routing_id = #{item.routingId,jdbcType=VARCHAR},
            </if>
            <if test="item.operationSequenceId != null and item.operationSequenceId != ''">
                operation_sequence_id = #{item.operationSequenceId,jdbcType=VARCHAR},
            </if>
            <if test="item.sequenceNo != null">
                sequence_no = #{item.sequenceNo,jdbcType=INTEGER},
            </if>
            <if test="item.preRoutingStepSequenceNo != null and item.preRoutingStepSequenceNo != ''">
                pre_routing_step_sequence_no = #{item.preRoutingStepSequenceNo,jdbcType=VARCHAR},
            </if>
            <if test="item.nextRoutingStepSequenceNo != null and item.nextRoutingStepSequenceNo != ''">
                next_routing_step_sequence_no = #{item.nextRoutingStepSequenceNo,jdbcType=VARCHAR},
            </if>
            <if test="item.yield != null">
                yield = #{item.yield,jdbcType=VARCHAR},
            </if>
            <if test="item.scrapStrategy != null and item.scrapStrategy != ''">
                scrap_strategy = #{item.scrapStrategy,jdbcType=VARCHAR},
            </if>
            <if test="item.percentageScrapRate != null">
                percentage_scrap_rate = #{item.percentageScrapRate,jdbcType=VARCHAR},
            </if>
            <if test="item.scrap != null">
                scrap = #{item.scrap,jdbcType=VARCHAR},
            </if>
            <if test="item.preProcessRatio != null">
                pre_process_ratio = #{item.preProcessRatio,jdbcType=VARCHAR},
            </if>
            <if test="item.processingType != null and item.processingType != ''">
                processing_type = #{item.processingType,jdbcType=VARCHAR},
            </if>
            <if test="item.connectionTask != null and item.connectionTask != ''">
                connection_task = #{item.connectionTask,jdbcType=VARCHAR},
            </if>
            <if test="item.connectionType != null and item.connectionType != ''">
                connection_type = #{item.connectionType,jdbcType=VARCHAR},
            </if>
            <if test="item.maxConnectionDuration != null">
                max_connection_duration = #{item.maxConnectionDuration,jdbcType=INTEGER},
            </if>
            <if test="item.minConnectionDuration != null">
                min_connection_duration = #{item.minConnectionDuration,jdbcType=INTEGER},
            </if>
            <if test="item.standardStepId != null and item.standardStepId != ''">
                standard_step_id = #{item.standardStepId,jdbcType=VARCHAR},
            </if>
            <if test="item.countingUnitId != null and item.countingUnitId != ''">
                counting_unit_id = #{item.countingUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.effective != null and item.effective != ''">
                effective = #{item.effective,jdbcType=VARCHAR},
            </if>
            <if test="item.expireReason != null and item.expireReason != ''">
                expire_reason = #{item.expireReason,jdbcType=VARCHAR},
            </if>
            <if test="item.lastUpdateDate != null">
                last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.startDate != null">
                start_date = #{item.startDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.disableDate != null">
                disable_date = #{item.disableDate,jdbcType=TIMESTAMP},
            </if>
            version_value = version_value + 1,

        </set>
        where id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mds_rou_product_routing_step
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="routing_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.routingId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="operation_sequence_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.operationSequenceId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sequence_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sequenceNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="pre_routing_step_sequence_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.preRoutingStepSequenceNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="next_routing_step_sequence_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.nextRoutingStepSequenceNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="yield = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.yield,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="scrap_strategy = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.scrapStrategy,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="percentage_scrap_rate = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.percentageScrapRate,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="scrap = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.scrap,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="pre_process_ratio = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.preProcessRatio,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="processing_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.processingType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="connection_task = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.connectionTask,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="connection_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.connectionType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="max_connection_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.maxConnectionDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="min_connection_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.minConnectionDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="standard_step_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.standardStepId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="counting_unit_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.countingUnitId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="effective = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.effective,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="expire_reason = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.expireReason,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="last_update_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lastUpdateDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="start_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.startDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="disable_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.disableDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mds_rou_product_routing_step
            <set>
                <if test="item.routingId != null and item.routingId != ''">
                    routing_id = #{item.routingId,jdbcType=VARCHAR},
                </if>
                <if test="item.operationSequenceId != null and item.operationSequenceId != ''">
                    operation_sequence_id = #{item.operationSequenceId,jdbcType=VARCHAR},
                </if>
                <if test="item.sequenceNo != null">
                    sequence_no = #{item.sequenceNo,jdbcType=INTEGER},
                </if>
                <if test="item.preRoutingStepSequenceNo != null and item.preRoutingStepSequenceNo != ''">
                    pre_routing_step_sequence_no = #{item.preRoutingStepSequenceNo,jdbcType=VARCHAR},
                </if>
                <if test="item.nextRoutingStepSequenceNo != null and item.nextRoutingStepSequenceNo != ''">
                    next_routing_step_sequence_no = #{item.nextRoutingStepSequenceNo,jdbcType=VARCHAR},
                </if>
                <if test="item.yield != null">
                    yield = #{item.yield,jdbcType=VARCHAR},
                </if>
                <if test="item.scrapStrategy != null and item.scrapStrategy != ''">
                    scrap_strategy = #{item.scrapStrategy,jdbcType=VARCHAR},
                </if>
                <if test="item.percentageScrapRate != null">
                    percentage_scrap_rate = #{item.percentageScrapRate,jdbcType=VARCHAR},
                </if>
                <if test="item.scrap != null">
                    scrap = #{item.scrap,jdbcType=VARCHAR},
                </if>
                <if test="item.preProcessRatio != null">
                    pre_process_ratio = #{item.preProcessRatio,jdbcType=VARCHAR},
                </if>
                <if test="item.processingType != null and item.processingType != ''">
                    processing_type = #{item.processingType,jdbcType=VARCHAR},
                </if>
                <if test="item.connectionTask != null and item.connectionTask != ''">
                    connection_task = #{item.connectionTask,jdbcType=VARCHAR},
                </if>
                <if test="item.connectionType != null and item.connectionType != ''">
                    connection_type = #{item.connectionType,jdbcType=VARCHAR},
                </if>
                <if test="item.maxConnectionDuration != null">
                    max_connection_duration = #{item.maxConnectionDuration,jdbcType=INTEGER},
                </if>
                <if test="item.minConnectionDuration != null">
                    min_connection_duration = #{item.minConnectionDuration,jdbcType=INTEGER},
                </if>
                <if test="item.standardStepId != null and item.standardStepId != ''">
                    standard_step_id = #{item.standardStepId,jdbcType=VARCHAR},
                </if>
                <if test="item.countingUnitId != null and item.countingUnitId != ''">
                    counting_unit_id = #{item.countingUnitId,jdbcType=VARCHAR},
                </if>
                <if test="item.effective != null and item.effective != ''">
                    effective = #{item.effective,jdbcType=VARCHAR},
                </if>
                <if test="item.expireReason != null and item.expireReason != ''">
                    expire_reason = #{item.expireReason,jdbcType=VARCHAR},
                </if>
                <if test="item.lastUpdateDate != null">
                    last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.startDate != null">
                    start_date = #{item.startDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.disableDate != null">
                    disable_date = #{item.disableDate,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <update id="doLogicDeleteBatch">
        <foreach collection="deleteProductRoutingStepVersionList" index="index" item="item" separator=";">
            update mds_rou_product_routing_step
            set
            enabled = 'NO',
            effective='NO',
            version_value = version_value + 1
            where id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mds_rou_product_routing_step
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mds_rou_product_routing_step where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
