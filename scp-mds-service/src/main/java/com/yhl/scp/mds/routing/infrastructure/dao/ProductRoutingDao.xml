<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mds.routing.infrastructure.dao.ProductRoutingDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mds.routing.infrastructure.po.ProductRoutingPO">
        <!--@Table mds_rou_product_routing-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="routing_sequence_id" jdbcType="VARCHAR" property="routingSequenceId"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="stock_point_id" jdbcType="VARCHAR" property="stockPointId"/>
        <result column="routing_code" jdbcType="VARCHAR" property="routingCode"/>
        <result column="routing_name" jdbcType="VARCHAR" property="routingName"/>
        <result column="priority" jdbcType="INTEGER" property="priority"/>
        <result column="lead_time" jdbcType="INTEGER" property="leadTime"/>
        <result column="production_version" jdbcType="VARCHAR" property="productionVersion"/>
        <result column="production_cost" jdbcType="VARCHAR" property="productionCost"/>
        <result column="max_quantity" jdbcType="VARCHAR" property="maxQuantity"/>
        <result column="min_quantity" jdbcType="VARCHAR" property="minQuantity"/>
        <result column="lot_size" jdbcType="VARCHAR" property="lotSize"/>
        <result column="outsourcing_work_hours" jdbcType="INTEGER" property="outsourcingWorkHours"/>
        <result column="effective_time" jdbcType="TIMESTAMP" property="effectiveTime"/>
        <result column="expiry_time" jdbcType="TIMESTAMP" property="expiryTime"/>
        <result column="currency_unit_id" jdbcType="VARCHAR" property="currencyUnitId"/>
        <result column="counting_unit_id" jdbcType="VARCHAR" property="countingUnitId"/>
        <result column="effective" jdbcType="VARCHAR" property="effective"/>
        <result column="expire_reason" jdbcType="VARCHAR" property="expireReason"/>
        <result column="last_update_date" jdbcType="TIMESTAMP" property="lastUpdateDate"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mds.routing.vo.ProductRoutingVO">
    	<result column="product_code" jdbcType="VARCHAR" property="productCode"/>
    	<result column="product_name" jdbcType="VARCHAR" property="productName"/>
    	<result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
    	<result column="stock_point_name" jdbcType="VARCHAR" property="stockPointName"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
		routing_sequence_id,
		product_id,
		stock_point_id,
		routing_code,
		routing_name,
		priority,
		lead_time,
		production_version,
		production_cost,
		max_quantity,
		min_quantity,
		lot_size,
		outsourcing_work_hours,
		effective_time,
		expiry_time,
		currency_unit_id,
		counting_unit_id,
		effective,
		expire_reason,
		last_update_date,
		version_value,
		remark,
		enabled,
		creator,
		create_time,
		modifier,
		modify_time
    </sql>
    <sql id="VO_Column_List">
        <include refid="Base_Column_List"/>,
        product_code,
        product_name,
        stock_point_code,
        stock_point_name
    </sql>

    <sql id="VO_Base_Where_Condition">
        <include refid="Base_Where_Condition" />
        <if test="params.productCode != null and params.productCode != ''">
            and product_code = #{params.productCode,jdbcType=VARCHAR}
        </if>
        <if test="params.productName != null and params.productName != ''">
            and product_name = #{params.productName,jdbcType=VARCHAR}
        </if>
        <if test="params.stockPointCode != null and params.stockPointCode != ''">
            and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
        </if>
        <if test="params.stockPointName != null and params.stockPointName != ''">
            and stock_point_name = #{params.stockPointName,jdbcType=VARCHAR}
        </if>
    </sql>

    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.routingSequenceId != null and params.routingSequenceId != ''">
                and routing_sequence_id = #{params.routingSequenceId,jdbcType=VARCHAR}
            </if>
            <if test="params.productId != null and params.productId != ''">
                and product_id = #{params.productId,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointId != null and params.stockPointId != ''">
                and stock_point_id = #{params.stockPointId,jdbcType=VARCHAR}
            </if>
            <if test="params.routingCode != null and params.routingCode != ''">
                and routing_code = #{params.routingCode,jdbcType=VARCHAR}
            </if>
            <if test="params.routingName != null and params.routingName != ''">
                and routing_name = #{params.routingName,jdbcType=VARCHAR}
            </if>
            <if test="params.priority != null">
                and priority = #{params.priority,jdbcType=INTEGER}
            </if>
            <if test="params.leadTime != null">
                and lead_time = #{params.leadTime,jdbcType=INTEGER}
            </if>
            <if test="params.productionVersion != null and params.productionVersion != ''">
                and production_version = #{params.productionVersion,jdbcType=VARCHAR}
            </if>
            <if test="params.productionCost != null">
                and production_cost = #{params.productionCost,jdbcType=VARCHAR}
            </if>
            <if test="params.maxQuantity != null">
                and max_quantity = #{params.maxQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.minQuantity != null">
                and min_quantity = #{params.minQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.lotSize != null">
                and lot_size = #{params.lotSize,jdbcType=VARCHAR}
            </if>
            <if test="params.outsourcingWorkHours != null">
                and outsourcing_work_hours = #{params.outsourcingWorkHours,jdbcType=INTEGER}
            </if>
            <if test="params.effectiveTime != null">
                and effective_time = #{params.effectiveTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.expiryTime != null">
                and expiry_time = #{params.expiryTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.currencyUnitId != null and params.currencyUnitId != ''">
                and currency_unit_id = #{params.currencyUnitId,jdbcType=VARCHAR}
            </if>
            <if test="params.countingUnitId != null and params.countingUnitId != ''">
                and counting_unit_id = #{params.countingUnitId,jdbcType=VARCHAR}
            </if>
            <if test="params.effective != null and params.effective != ''">
                and effective = #{params.effective,jdbcType=VARCHAR}
            </if>
            <if test="params.expireReason != null and params.expireReason != ''">
                and expire_reason = #{params.expireReason,jdbcType=VARCHAR}
            </if>
            <if test="params.lastUpdateDate != null">
                and last_update_date = #{params.lastUpdateDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.routingSequenceIds != null and params.routingSequenceIds.size() > 0">
                and routing_sequence_id in
                <foreach collection="params.routingSequenceIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_rou_product_routing
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_rou_product_routing
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_mds_rou_product_routing
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_rou_product_routing
        <include refid="Base_Where_Condition"/>
    </select>
    <select id="selectByRoutingSequenceIds" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_mds_rou_product_routing
        where  routing_sequence_id in
        <foreach collection="routingSequenceIds" item="routingSequenceId" index="index" open="(" separator="," close=")">
            #{routingSequenceId,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="selectByProductIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_rou_product_routing
        where enabled='YES' and product_id in
        <foreach collection="productIds" item="productId" index="index" open="(" separator="," close=")">
            #{productId,jdbcType=VARCHAR}
        </foreach>
    </select>    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mds.routing.infrastructure.po.ProductRoutingPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mds_rou_product_routing(
        id,
        routing_sequence_id,
        product_id,
        stock_point_id,
        routing_code,
        routing_name,
        priority,
        lead_time,
        production_version,
        production_cost,
        max_quantity,
        min_quantity,
        lot_size,
        outsourcing_work_hours,
        effective_time,
        expiry_time,
        currency_unit_id,
        counting_unit_id,
        effective,
        expire_reason,
        last_update_date,
        version_value,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{routingSequenceId,jdbcType=VARCHAR},
        #{productId,jdbcType=VARCHAR},
        #{stockPointId,jdbcType=VARCHAR},
        #{routingCode,jdbcType=VARCHAR},
        #{routingName,jdbcType=VARCHAR},
        #{priority,jdbcType=INTEGER},
        #{leadTime,jdbcType=INTEGER},
        #{productionVersion,jdbcType=VARCHAR},
        #{productionCost,jdbcType=VARCHAR},
        #{maxQuantity,jdbcType=VARCHAR},
        #{minQuantity,jdbcType=VARCHAR},
        #{lotSize,jdbcType=VARCHAR},
        #{outsourcingWorkHours,jdbcType=INTEGER},
        #{effectiveTime,jdbcType=TIMESTAMP},
        #{expiryTime,jdbcType=TIMESTAMP},
        #{currencyUnitId,jdbcType=VARCHAR},
        #{countingUnitId,jdbcType=VARCHAR},
        #{effective,jdbcType=VARCHAR},
        #{expireReason,jdbcType=VARCHAR},
        #{lastUpdateDate,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mds.routing.infrastructure.po.ProductRoutingPO">
        insert into mds_rou_product_routing(id,
                                            routing_sequence_id,
                                            product_id,
                                            stock_point_id,
                                            routing_code,
                                            routing_name,
                                            priority,
                                            lead_time,
                                            production_version,
                                            production_cost,
                                            max_quantity,
                                            min_quantity,
                                            lot_size,
                                            outsourcing_work_hours,
                                            effective_time,
                                            expiry_time,
                                            currency_unit_id,
                                            counting_unit_id,
                                            effective,
                                            expire_reason,
                                            last_update_date,
                                            version_value,
                                            remark,
                                            enabled,
                                            creator,
                                            create_time,
                                            modifier,
                                            modify_time)
        values (#{id,jdbcType=VARCHAR},
                #{routingSequenceId,jdbcType=VARCHAR},
                #{productId,jdbcType=VARCHAR},
                #{stockPointId,jdbcType=VARCHAR},
                #{routingCode,jdbcType=VARCHAR},
                #{routingName,jdbcType=VARCHAR},
                #{priority,jdbcType=INTEGER},
                #{leadTime,jdbcType=INTEGER},
                #{productionVersion,jdbcType=VARCHAR},
                #{productionCost,jdbcType=VARCHAR},
                #{maxQuantity,jdbcType=VARCHAR},
                #{minQuantity,jdbcType=VARCHAR},
                #{lotSize,jdbcType=VARCHAR},
                #{outsourcingWorkHours,jdbcType=INTEGER},
                #{effectiveTime,jdbcType=TIMESTAMP},
                #{expiryTime,jdbcType=TIMESTAMP},
                #{currencyUnitId,jdbcType=VARCHAR},
                #{countingUnitId,jdbcType=VARCHAR},
                #{effective,jdbcType=VARCHAR},
                #{expireReason,jdbcType=VARCHAR},
                #{lastUpdateDate,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mds_rou_product_routing(
        id,
        routing_sequence_id,
        product_id,
        stock_point_id,
        routing_code,
        routing_name,
        priority,
        lead_time,
        production_version,
        production_cost,
        max_quantity,
        min_quantity,
        lot_size,
        outsourcing_work_hours,
        effective_time,
        expiry_time,
        currency_unit_id,
        counting_unit_id,
        effective,
        expire_reason,
        last_update_date,
        version_value,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.routingSequenceId,jdbcType=VARCHAR},
            #{entity.productId,jdbcType=VARCHAR},
            #{entity.stockPointId,jdbcType=VARCHAR},
            #{entity.routingCode,jdbcType=VARCHAR},
            #{entity.routingName,jdbcType=VARCHAR},
            #{entity.priority,jdbcType=INTEGER},
            #{entity.leadTime,jdbcType=INTEGER},
            #{entity.productionVersion,jdbcType=VARCHAR},
            #{entity.productionCost,jdbcType=VARCHAR},
            #{entity.maxQuantity,jdbcType=VARCHAR},
            #{entity.minQuantity,jdbcType=VARCHAR},
            #{entity.lotSize,jdbcType=VARCHAR},
            #{entity.outsourcingWorkHours,jdbcType=INTEGER},
            #{entity.effectiveTime,jdbcType=TIMESTAMP},
            #{entity.expiryTime,jdbcType=TIMESTAMP},
            #{entity.currencyUnitId,jdbcType=VARCHAR},
            #{entity.countingUnitId,jdbcType=VARCHAR},
            #{entity.effective,jdbcType=VARCHAR},
            #{entity.expireReason,jdbcType=VARCHAR},
            #{entity.lastUpdateDate,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mds_rou_product_routing(
        id,
        routing_sequence_id,
        product_id,
        stock_point_id,
        routing_code,
        routing_name,
        priority,
        lead_time,
        production_version,
        production_cost,
        max_quantity,
        min_quantity,
        lot_size,
        outsourcing_work_hours,
        effective_time,
        expiry_time,
        currency_unit_id,
        counting_unit_id,
        effective,
        expire_reason,
        last_update_date,
        version_value,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.routingSequenceId,jdbcType=VARCHAR},
            #{entity.productId,jdbcType=VARCHAR},
            #{entity.stockPointId,jdbcType=VARCHAR},
            #{entity.routingCode,jdbcType=VARCHAR},
            #{entity.routingName,jdbcType=VARCHAR},
            #{entity.priority,jdbcType=INTEGER},
            #{entity.leadTime,jdbcType=INTEGER},
            #{entity.productionVersion,jdbcType=VARCHAR},
            #{entity.productionCost,jdbcType=VARCHAR},
            #{entity.maxQuantity,jdbcType=VARCHAR},
            #{entity.minQuantity,jdbcType=VARCHAR},
            #{entity.lotSize,jdbcType=VARCHAR},
            #{entity.outsourcingWorkHours,jdbcType=INTEGER},
            #{entity.effectiveTime,jdbcType=TIMESTAMP},
            #{entity.expiryTime,jdbcType=TIMESTAMP},
            #{entity.currencyUnitId,jdbcType=VARCHAR},
            #{entity.countingUnitId,jdbcType=VARCHAR},
            #{entity.effective,jdbcType=VARCHAR},
            #{entity.expireReason,jdbcType=VARCHAR},
            #{entity.lastUpdateDate,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mds.routing.infrastructure.po.ProductRoutingPO">
        update mds_rou_product_routing
        set routing_sequence_id    = #{routingSequenceId,jdbcType=VARCHAR},
            product_id             = #{productId,jdbcType=VARCHAR},
            stock_point_id         = #{stockPointId,jdbcType=VARCHAR},
            routing_code           = #{routingCode,jdbcType=VARCHAR},
            routing_name           = #{routingName,jdbcType=VARCHAR},
            priority               = #{priority,jdbcType=INTEGER},
            lead_time              = #{leadTime,jdbcType=INTEGER},
            production_version     = #{productionVersion,jdbcType=VARCHAR},
            production_cost        = #{productionCost,jdbcType=VARCHAR},
            max_quantity           = #{maxQuantity,jdbcType=VARCHAR},
            min_quantity           = #{minQuantity,jdbcType=VARCHAR},
            lot_size               = #{lotSize,jdbcType=VARCHAR},
            outsourcing_work_hours = #{outsourcingWorkHours,jdbcType=INTEGER},
            effective_time         = #{effectiveTime,jdbcType=TIMESTAMP},
            expiry_time            = #{expiryTime,jdbcType=TIMESTAMP},
            currency_unit_id       = #{currencyUnitId,jdbcType=VARCHAR},
            counting_unit_id       = #{countingUnitId,jdbcType=VARCHAR},
            effective              = #{effective,jdbcType=VARCHAR},
            expire_reason          = #{expireReason,jdbcType=VARCHAR},
            last_update_date       = #{lastUpdateDate,jdbcType=TIMESTAMP},
            version_value          = #{versionValue,jdbcType=INTEGER},
            remark                 = #{remark,jdbcType=VARCHAR},
            enabled                = #{enabled,jdbcType=VARCHAR},
            modifier               = #{modifier,jdbcType=VARCHAR},
            modify_time            = #{modifyTime,jdbcType=TIMESTAMP},
            version_value = version_value + 1
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mds.routing.infrastructure.po.ProductRoutingPO">
        update mds_rou_product_routing
        <set>
            <if test="item.routingSequenceId != null and item.routingSequenceId != ''">
                routing_sequence_id = #{item.routingSequenceId,jdbcType=VARCHAR},
            </if>
            <if test="item.productId != null and item.productId != ''">
                product_id = #{item.productId,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointId != null and item.stockPointId != ''">
                stock_point_id = #{item.stockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.routingCode != null and item.routingCode != ''">
                routing_code = #{item.routingCode,jdbcType=VARCHAR},
            </if>
            <if test="item.routingName != null and item.routingName != ''">
                routing_name = #{item.routingName,jdbcType=VARCHAR},
            </if>
            <if test="item.priority != null">
                priority = #{item.priority,jdbcType=INTEGER},
            </if>
            <if test="item.leadTime != null">
                lead_time = #{item.leadTime,jdbcType=INTEGER},
            </if>
            <if test="item.productionVersion != null and item.productionVersion != ''">
                production_version = #{item.productionVersion,jdbcType=VARCHAR},
            </if>
            <if test="item.productionCost != null">
                production_cost = #{item.productionCost,jdbcType=VARCHAR},
            </if>
            <if test="item.maxQuantity != null">
                max_quantity = #{item.maxQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.minQuantity != null">
                min_quantity = #{item.minQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.lotSize != null">
                lot_size = #{item.lotSize,jdbcType=VARCHAR},
            </if>
            <if test="item.outsourcingWorkHours != null">
                outsourcing_work_hours = #{item.outsourcingWorkHours,jdbcType=INTEGER},
            </if>
            <if test="item.effectiveTime != null">
                effective_time = #{item.effectiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.expiryTime != null">
                expiry_time = #{item.expiryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.currencyUnitId != null and item.currencyUnitId != ''">
                currency_unit_id = #{item.currencyUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.countingUnitId != null and item.countingUnitId != ''">
                counting_unit_id = #{item.countingUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.effective != null and item.effective != ''">
                effective = #{item.effective,jdbcType=VARCHAR},
            </if>
            <if test="item.expireReason != null and item.expireReason != ''">
                expire_reason = #{item.expireReason,jdbcType=VARCHAR},
            </if>
            <if test="item.lastUpdateDate != null">
                last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            version_value = version_value + 1,
        </set>
        where id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mds_rou_product_routing
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="routing_sequence_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.routingSequenceId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="routing_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.routingCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="routing_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.routingName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="priority = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.priority,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="lead_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.leadTime,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="production_version = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productionVersion,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="production_cost = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productionCost,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="max_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.maxQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="min_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.minQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="lot_size = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lotSize,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="outsourcing_work_hours = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.outsourcingWorkHours,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="effective_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.effectiveTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="expiry_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.expiryTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="currency_unit_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.currencyUnitId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="counting_unit_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.countingUnitId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="effective = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.effective,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="expire_reason = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.expireReason,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="last_update_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lastUpdateDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mds_rou_product_routing
            <set>
                <if test="item.routingSequenceId != null and item.routingSequenceId != ''">
                    routing_sequence_id = #{item.routingSequenceId,jdbcType=VARCHAR},
                </if>
                <if test="item.productId != null and item.productId != ''">
                    product_id = #{item.productId,jdbcType=VARCHAR},
                </if>
                <if test="item.stockPointId != null and item.stockPointId != ''">
                    stock_point_id = #{item.stockPointId,jdbcType=VARCHAR},
                </if>
                <if test="item.routingCode != null and item.routingCode != ''">
                    routing_code = #{item.routingCode,jdbcType=VARCHAR},
                </if>
                <if test="item.routingName != null and item.routingName != ''">
                    routing_name = #{item.routingName,jdbcType=VARCHAR},
                </if>
                <if test="item.priority != null">
                    priority = #{item.priority,jdbcType=INTEGER},
                </if>
                <if test="item.leadTime != null">
                    lead_time = #{item.leadTime,jdbcType=INTEGER},
                </if>
                <if test="item.productionVersion != null and item.productionVersion != ''">
                    production_version = #{item.productionVersion,jdbcType=VARCHAR},
                </if>
                <if test="item.productionCost != null">
                    production_cost = #{item.productionCost,jdbcType=VARCHAR},
                </if>
                <if test="item.maxQuantity != null">
                    max_quantity = #{item.maxQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.minQuantity != null">
                    min_quantity = #{item.minQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.lotSize != null">
                    lot_size = #{item.lotSize,jdbcType=VARCHAR},
                </if>
                <if test="item.outsourcingWorkHours != null">
                    outsourcing_work_hours = #{item.outsourcingWorkHours,jdbcType=INTEGER},
                </if>
                <if test="item.effectiveTime != null">
                    effective_time = #{item.effectiveTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.expiryTime != null">
                    expiry_time = #{item.expiryTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.currencyUnitId != null and item.currencyUnitId != ''">
                    currency_unit_id = #{item.currencyUnitId,jdbcType=VARCHAR},
                </if>
                <if test="item.countingUnitId != null and item.countingUnitId != ''">
                    counting_unit_id = #{item.countingUnitId,jdbcType=VARCHAR},
                </if>
                <if test="item.effective != null and item.effective != ''">
                    effective = #{item.effective,jdbcType=VARCHAR},
                </if>
                <if test="item.expireReason != null and item.expireReason != ''">
                    expire_reason = #{item.expireReason,jdbcType=VARCHAR},
                </if>
                <if test="item.lastUpdateDate != null">
                    last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <update id="doLogicDeleteBatch">
        <foreach collection="deleteProductRoutingVersionList" index="index" item="item" separator=";">
            update mds_rou_product_routing
            set
            enabled = 'NO',
            effective='NO',
            version_value = version_value + 1
            where id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mds_rou_product_routing
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mds_rou_product_routing where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <!-- 条件查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_mds_rou_product_routing
        <include refid="VO_Base_Where_Condition"/>
    </select>

    <select id="selectByRoutingSequenceIdNotNull" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_mds_rou_product_routing
        where routing_sequence_id is not null
    </select>

</mapper>
