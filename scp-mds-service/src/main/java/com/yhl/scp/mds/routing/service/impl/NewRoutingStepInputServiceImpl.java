package com.yhl.scp.mds.routing.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.date.StopWatch;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.mds.enums.ProductTypeEnum;
import com.yhl.scp.mds.extension.routing.infrastructure.po.RoutingStepInputPO;
import com.yhl.scp.mds.extension.routing.vo.RoutingVO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.newproduct.service.NewProductStockPointService;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.routing.infrastructure.dao.RoutingStepInputDao;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.common.utils.BulkOperationUtils;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.bom.service.MdsProductBomService;
import com.yhl.scp.mds.bom.service.MdsProductBomVersionService;
import com.yhl.scp.mds.bom.vo.ProductBomVO;
import com.yhl.scp.mds.bom.vo.ProductBomVersionVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepInputVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepVO;
import com.yhl.scp.mds.product.vo.ProductDemandSupplyCalculateVO;
import com.yhl.scp.mds.routing.convertor.NewRoutingStepInputConvertor;
import com.yhl.scp.mds.routing.domain.entity.NewRoutingStepInputDO;
import com.yhl.scp.mds.routing.domain.service.NewRoutingStepInputDomainService;
import com.yhl.scp.mds.routing.dto.NewRoutingStepInputDTO;
import com.yhl.scp.mds.routing.infrastructure.dao.NewRoutingStepInputDao;
import com.yhl.scp.mds.routing.infrastructure.po.NewRoutingStepInputPO;
import com.yhl.scp.mds.routing.service.NewRoutingService;
import com.yhl.scp.mds.routing.service.NewRoutingStepInputService;
import com.yhl.scp.mds.routing.service.NewRoutingStepService;
import com.yhl.scp.mds.routing.service.RoutingService;
import com.yhl.scp.mds.routing.service.RoutingStepInputService;
import com.yhl.scp.mds.routing.service.RoutingStepService;
import com.yhl.scp.mds.routing.vo.NewRoutingStepInputVO;
import com.yhl.scp.mds.routing.vo.NewRoutingStepVO;
import com.yhl.scp.mds.routing.vo.NewRoutingVO;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.service.NewStockPointService;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>NewRoutingStepInputServiceImpl</code>
 * <p>
 * 新-生产路径步骤输入物品应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-20 14:57:56
 */
@Slf4j
@Service
public class NewRoutingStepInputServiceImpl extends AbstractService implements NewRoutingStepInputService {

    @Resource
    private NewRoutingStepInputDao newRoutingStepInputDao;

    @Resource
    private NewRoutingStepInputDomainService newRoutingStepInputDomainService;

    @Resource
    private MdsProductBomService mdsProductBomService;

    @Resource
    private MdsProductBomVersionService mdsProductBomVersionService;

    @Resource
    private NewRoutingService newRoutingService;

    @Resource
    private NewRoutingStepService newRoutingStepService;

    @Resource
    private NewProductStockPointService newProductStockPointService;

    @Resource
    private RoutingService routingService;

    @Resource
    private RoutingStepInputDao routingStepInputDao;

    @Resource
    private RoutingStepService routingStepService;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private NewStockPointService newStockPointService;

    @Resource
    private RoutingStepInputService routingStepInputService;

    @Override
    public BaseResponse<Void> doCreate(NewRoutingStepInputDTO routingStepInputDTO) {
        // 0.数据转换
        NewRoutingStepInputDO routingStepInputDO = NewRoutingStepInputConvertor.INSTANCE.dto2Do(routingStepInputDTO);
        NewRoutingStepInputPO routingStepInputPO = NewRoutingStepInputConvertor.INSTANCE.dto2Po(routingStepInputDTO);
        // 1.数据校验
        newRoutingStepInputDomainService.validation(routingStepInputDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(routingStepInputPO);
        newRoutingStepInputDao.insertWithPrimaryKey(routingStepInputPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(NewRoutingStepInputDTO routingStepInputDTO) {
        // 0.数据转换
        NewRoutingStepInputDO routingStepInputDO = NewRoutingStepInputConvertor.INSTANCE.dto2Do(routingStepInputDTO);
        NewRoutingStepInputPO routingStepInputPO = NewRoutingStepInputConvertor.INSTANCE.dto2Po(routingStepInputDTO);
        // 1.数据校验
        newRoutingStepInputDomainService.validation(routingStepInputDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(routingStepInputPO);
        newRoutingStepInputDao.update(routingStepInputPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<NewRoutingStepInputDTO> list) {
        List<NewRoutingStepInputPO> newList = NewRoutingStepInputConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        BulkOperationUtils.bulkUpdateOrCreate(newList, poList ->
                newRoutingStepInputDao.insertBatchWithPrimaryKey(poList), 2500);
    }

    @Override
    public void doUpdateBatch(List<NewRoutingStepInputDTO> list) {
        List<NewRoutingStepInputPO> newList = NewRoutingStepInputConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        BulkOperationUtils.bulkUpdateOrCreate(newList, poList ->
                newRoutingStepInputDao.updateBatch(poList), 2500);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return newRoutingStepInputDao.deleteBatch(idList);
        }
        return newRoutingStepInputDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public NewRoutingStepInputVO selectByPrimaryKey(String id) {
        NewRoutingStepInputPO po = newRoutingStepInputDao.selectByPrimaryKey(id);
        return NewRoutingStepInputConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mds_rou_routing_step_input")
    public List<NewRoutingStepInputVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mds_rou_routing_step_input")
    public List<NewRoutingStepInputVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<NewRoutingStepInputVO> dataList = newRoutingStepInputDao.selectByCondition(sortParam, queryCriteriaParam);
        NewRoutingStepInputServiceImpl target = SpringBeanUtils.getBean(NewRoutingStepInputServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<NewRoutingStepInputVO> selectByParams(Map<String, Object> params) {
        List<NewRoutingStepInputPO> list = newRoutingStepInputDao.selectByParams(params);
        return NewRoutingStepInputConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<NewRoutingStepInputVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.NEW_ROUTING_STEP_INPUT.getCode();
    }

    @Override
    public List<NewRoutingStepInputVO> invocation(List<NewRoutingStepInputVO> dataList,
                                                  Map<String, Object> params, String invocation) {
        return dataList;
    }

    @Override
    public void doLogicDeleteBatchByRoutingIds(List<String> routingIds) {
        newRoutingStepInputDao.doLogicDeleteBatchByRoutingIds(routingIds);
    }

    @Override
    public void doLogicDeleteBatchByRoutingStepIds(List<String> routingStepIds) {
        newRoutingStepInputDao.doLogicDeleteBatchByRoutingStepIds(routingStepIds);
    }

    @Override
    public void doLogicDeleteBatchByIds(List<String> ids) {
        newRoutingStepInputDao.doLogicDeleteBatchByIds(ids);
    }

    @Override
    public void doTransitionRoutingStepInput(List<String> componentSequenceIdList, List<NewRoutingVO> routingList,
                                             List<NewRoutingStepVO> newRoutingSteps,
                                             List<NewStockPointVO> stockPoints, String scenario) {
        StopWatch stopWatch = new StopWatch("输入物品处理");
        // 先将需要转换的Bom数据查询出来
        stopWatch.start("查询转化BOM");
        List<ProductBomVO> oldBomList;
        if (CollectionUtils.isNotEmpty(componentSequenceIdList)) {
            oldBomList = mdsProductBomService.selectByParams(ImmutableMap
                    .of("componentSequenceIds", componentSequenceIdList));
        } else {
            oldBomList = mdsProductBomService.selectComponentSequenceNotNull();
        }
        stopWatch.stop();

        stopWatch.start("查询BOM版本数据");
        List<String> bomVersionIds = oldBomList.stream().map(ProductBomVO::getBomVersionId)
                .distinct().collect(Collectors.toList());
        // 查出对应的Bom版本数据
        List<ProductBomVersionVO> productBomVersions = mdsProductBomVersionService.selectByPrimaryKeys(bomVersionIds);
        Map<String, ProductBomVersionVO> productBomVersionMap = productBomVersions.stream().collect(Collectors
                .toMap(ProductBomVersionVO::getId, Function.identity(), (v1, v2) -> v1));
        List<String> productIds = productBomVersions.stream().map(ProductBomVersionVO::getProductId)
                .distinct().collect(Collectors.toList());
        stopWatch.stop();

        stopWatch.start("查询工艺路径数据");
        if (CollectionUtils.isEmpty(routingList)) {
            routingList = newRoutingService.selectByParams(ImmutableMap.of("productIds", productIds,
                    "enabled", YesOrNoEnum.YES.getCode()));
        }
        if (CollectionUtils.isEmpty(routingList)) {
            return;
        }
        Map<String, NewRoutingVO> routingMap = routingList.stream().collect(Collectors
                .toMap(item -> item.getStockPointId() + "_" + item.getProductId(),
                        Function.identity(), (v1, v2) -> v1));
        List<String> routingIds = routingList.stream().map(NewRoutingVO::getId).collect(Collectors.toList());
        stopWatch.stop();

        // 获取routing下的所有工艺路径步骤
        stopWatch.start("查询路径步骤数据");
        if (CollectionUtils.isEmpty(newRoutingSteps)) {
            newRoutingSteps = newRoutingStepService.selectByParams(ImmutableMap
                    .of("routingIds", routingIds, "enabled", YesOrNoEnum.YES.getCode()));
        }
        Map<String, NewRoutingStepVO> newRoutingStepMap = newRoutingSteps.stream().collect(Collectors
                .toMap(item -> item.getRoutingId() + "_" + item.getStandardStepId(),
                        Function.identity(), (v1, v2) -> v1));
        stopWatch.stop();

        stopWatch.start("再次查询工艺路径数据");
        List<String> ioProductIds = oldBomList.stream().map(ProductBomVO::getIoProductId)
                .distinct().collect(Collectors.toList());
        List<NewRoutingVO> ioRoutingList = newRoutingService.selectByParams(ImmutableMap
                .of("productIds", ioProductIds, "enabled", YesOrNoEnum.YES.getCode()));
        ioProductIds = ioRoutingList.stream().map(NewRoutingVO::getProductId).distinct().collect(Collectors.toList());
        stopWatch.stop();

        // 查询存量的输入物品数据
        stopWatch.start("查询存量的输入物品数据");
        List<String> routingStepInputIds = oldBomList.stream().map(ProductBomVO::getId).collect(Collectors.toList());
        List<NewRoutingStepInputPO> oldInputList = newRoutingStepInputDao.selectByPrimaryKeys(routingStepInputIds);
        Map<String, NewRoutingStepInputPO> oldInputMap = oldInputList.stream().collect(Collectors
                .toMap(NewRoutingStepInputPO::getId, Function.identity(), (v1, v2) -> v1));
        stopWatch.stop();

        stopWatch.start("处理核心逻辑");
        // 获取库存点信息
        Map<String, String> stockPointMap = stockPoints.stream().collect(Collectors
                .toMap(NewStockPointVO::getId, NewStockPointVO::getStockPointCode, (v1, v2) -> v1));
        List<NewRoutingStepInputDTO> insertBatchInputList = new ArrayList<>();
        List<NewRoutingStepInputDTO> updateBatchInputList = new ArrayList<>();
        List<String> finalIoProductIds = ioProductIds;
        oldBomList.forEach(productBomVO -> {
            // 获取routingId
            ProductBomVersionVO productBomVersionVO = productBomVersionMap.get(productBomVO.getBomVersionId());
            if (productBomVersionVO == null) {
                return;
            }
            // 获取对应的库存点
            String stockPointCode = stockPointMap.get(productBomVersionVO.getStockPointId());
            if (StringUtils.isEmpty(stockPointCode)) {
                return;
            }
            NewRoutingVO newRoutingVO = routingMap.get(stockPointCode + "_" + productBomVersionVO.getProductId());
            if (newRoutingVO == null) {
                return;
            }
            // 获取routingStepId
            NewRoutingStepVO newRoutingStepVO = newRoutingStepMap.get(newRoutingVO.getId()
                    + "_" + productBomVO.getStandardStepId());
            if (newRoutingStepVO == null) {
                return;
            }
            String keyMaterial = YesOrNoEnum.NO.getCode();
            if (finalIoProductIds.contains(productBomVO.getIoProductId())) {
                keyMaterial = YesOrNoEnum.YES.getCode();
            }
            NewRoutingStepInputDTO newRoutingStepInputDTO = new NewRoutingStepInputDTO();
            if (Objects.nonNull(oldInputMap.get(productBomVO.getId()))) {
                // 修改
                NewRoutingStepInputPO oldInputPO = oldInputMap.get(productBomVO.getId());
                newRoutingStepInputDTO = NewRoutingStepInputConvertor.INSTANCE.po2Dto(oldInputPO);

                transitionRoutingStepInput(productBomVO, newRoutingVO, newRoutingStepVO, keyMaterial, newRoutingStepInputDTO);
                updateBatchInputList.add(newRoutingStepInputDTO);
            } else {
                // 新增
                newRoutingStepInputDTO.setId(productBomVO.getId());
                transitionRoutingStepInput(productBomVO, newRoutingVO, newRoutingStepVO, keyMaterial, newRoutingStepInputDTO);
                insertBatchInputList.add(newRoutingStepInputDTO);
            }
        });
        stopWatch.stop();

        stopWatch.start("数据入库");
        if (CollectionUtils.isNotEmpty(insertBatchInputList)) {
            this.doCreateBatchWithPrimaryKey(insertBatchInputList);
        }
        if (CollectionUtils.isNotEmpty(updateBatchInputList)) {
            this.doUpdateBatchSelective(updateBatchInputList);
        }
        stopWatch.stop();

        // 将时间时间小于等于当前时间的输入物品设置为无效
        stopWatch.start("将时间时间小于等于当前时间的输入物品设置为无效");
        newRoutingStepInputDao.updateEnableForExpiryTime();
        stopWatch.stop();

        // 后处理修改制造属性无工艺路径的输入物品
        stopWatch.start("后处理修改制造属性无工艺路径的输入物品");
        afterSetInputProduct(stockPoints, scenario);
        stopWatch.stop();

        log.info(stopWatch.prettyPrint(TimeUnit.SECONDS));
    }

    @Override
    public void afterSetInputProduct(List<NewStockPointVO> stockPoints, String scenario) {
        // 1. 初始化库存点映射
        Map<String, NewStockPointVO> stockPointMap = StreamUtils.mapByColumn(stockPoints,
                NewStockPointVO::getStockPointCode);
        ExecutorService executor = Executors.newFixedThreadPool(6);
        try {
//            // 2. 异步加载数据源
//            CompletableFuture<Map<String, RoutingVO>> routingMapFuture = CompletableFuture.supplyAsync(() -> {
//                DynamicDataSourceContextHolder.setDataSource(scenario);
//                List<RoutingVO> routingList = routingService.selectAll().stream().filter(r ->
//                        YesOrNoEnum.YES.getCode().equals(r.getEnabled())).collect(Collectors.toList());
//                DynamicDataSourceContextHolder.clearDataSource();
//                return StreamUtils.mapByColumn(routingList, RoutingVO::getProductId);
//            }, executor);
//            CompletableFuture<List<RoutingStepInputPO>> inputFuture = CompletableFuture.supplyAsync(() -> {
//                DynamicDataSourceContextHolder.setDataSource(scenario);
//                List<RoutingStepInputPO> inputs = routingStepInputDao.selectByParams(new HashMap<>()).stream()
//                        .filter(i -> YesOrNoEnum.YES.getCode().equals(i.getEnabled()))
//                        .collect(Collectors.toList());
//                DynamicDataSourceContextHolder.clearDataSource();
//                return inputs;
//            }, executor);

            CompletableFuture<List<NewProductStockPointVO>> productStockPointFuture = CompletableFuture.supplyAsync(() -> {
                DynamicDataSourceContextHolder.setDataSource(scenario);
                List<String> dynamicHeader = Arrays.asList("id", "stock_point_code", "product_code", "product_type");
                List<NewProductStockPointVO> products = newProductStockPointService
                        .selectProductListByParamOnDynamicColumns(dynamicHeader, new HashMap<>());
                DynamicDataSourceContextHolder.clearDataSource();
                return products;
            }, executor);

            List<RoutingVO> routingList = routingService.selectAll().stream().filter(r ->
                    YesOrNoEnum.YES.getCode().equals(r.getEnabled())).collect(Collectors.toList());
            Map<String, RoutingVO> routingMap = StreamUtils.mapByColumn(routingList, RoutingVO::getProductId);

            List<RoutingStepInputPO> routingStepInputs = routingStepInputDao.selectByParams(new HashMap<>()).stream()
                    .filter(i -> YesOrNoEnum.YES.getCode().equals(i.getEnabled()))
                    .collect(Collectors.toList());

            // 等待所有数据加载完成
            CompletableFuture<Void> allDone = CompletableFuture.allOf(
                    productStockPointFuture);
            allDone.join();
            // 获取异步结果
//            Map<String, RoutingVO> routingMap = routingMapFuture.get();
//            List<RoutingStepInputPO> routingStepInputs = inputFuture.get();
            List<NewProductStockPointVO> productStockPoints = productStockPointFuture.get();
            // 3. 构建基础映射关系
            Map<String, NewProductStockPointVO> productStockPointMap = StreamUtils.mapByColumn(productStockPoints,
                    NewProductStockPointVO::getId);
            Map<String, List<NewProductStockPointVO>> productCodeMap = StreamUtils.mapListByColumn(productStockPoints,
                    NewProductStockPointVO::getProductCode);
            // 4. 构建替代映射：productCode -> 可用的 productId
            Map<String, String> availableProductMap = new HashMap<>();
            Map<String, String> stockPointIdMap = new HashMap<>();
            for (NewProductStockPointVO vo : productStockPoints) {
                String productCode = vo.getProductCode();
                if (!availableProductMap.containsKey(productCode) && routingMap.containsKey(vo.getId())) {
                    availableProductMap.put(productCode, vo.getId());
                    stockPointIdMap.put(productCode, stockPointMap.get(vo.getStockPointCode()).getId());
                }
            }

            // 5. 收集需要更新的数据
            List<RoutingStepInputPO> updateList = Collections.synchronizedList(new ArrayList<>());
            routingStepInputs.parallelStream().forEach(inputVO -> {
                String inputProductId = inputVO.getInputProductId();
                if (!productStockPointMap.containsKey(inputProductId)) {
                    return;
                }
                NewProductStockPointVO productVO = productStockPointMap.get(inputProductId);
                if (!ProductTypeEnum.SA.getCode().equals(productVO.getProductType())) {
                    return;
                }
                if (routingMap.containsKey(inputProductId)) {
                    return;
                }
                String productCode = productVO.getProductCode();
                if (!productCodeMap.containsKey(productCode)) {
                    return;
                }
                String replacementId = availableProductMap.get(productCode);
                String stockPointId = stockPointIdMap.get(productCode);

                if (replacementId == null || stockPointId == null) {
                    return;
                }
                inputVO.setInputProductId(replacementId);
                inputVO.setStockPointId(stockPointId);
                inputVO.setKeyMaterial(YesOrNoEnum.YES.getCode());
                updateList.add(inputVO);
            });
            log.info("修改路径不存在输入物品数量：{}", updateList.size());
            // 6. 批量更新
            if (CollectionUtils.isNotEmpty(updateList)) {
                BasePOUtils.insertBatchFiller(updateList);
                BulkOperationUtils.bulkUpdateOrCreate(updateList, list ->
                        routingStepInputDao.updateBatch(list), 2500);
            }
        } catch (ExecutionException e) {
            throw new BusinessException(e.getLocalizedMessage());
        } catch (InterruptedException e1) {
            Thread.currentThread().interrupt();
            throw new BusinessException(e1.getLocalizedMessage());
        } finally {
            if (!executor.isShutdown()) {
                try {
                    log.info("Shutting down IOExecutor...");
                    executor.shutdown();
                    if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                        log.warn("IOExecutor did not terminate in 10 seconds, forcing shutdown.");
                        executor.shutdownNow();
                        if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                            log.error("IOExecutor did not terminate even after shutdownNow.");
                        }
                    }
                    log.info("IOExecutor shut down successfully.");
                } catch (InterruptedException ie) {
                    log.error("Shutdown of IOExecutor interrupted.", ie);
                    executor.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }
        }
    }


    private void transitionRoutingStepInput(ProductBomVO productBomVO, NewRoutingVO newRoutingVO,
                                            NewRoutingStepVO newRoutingStepVO, String keyMaterial,
                                            NewRoutingStepInputDTO newRoutingStepInputDTO) {
        newRoutingStepInputDTO.setRoutingId(newRoutingVO.getId());
        newRoutingStepInputDTO.setRoutingStepId(newRoutingStepVO.getId());
        newRoutingStepInputDTO.setInputProductId(productBomVO.getIoProductId());
        newRoutingStepInputDTO.setStockPointId(productBomVO.getIoStockPointId());
        newRoutingStepInputDTO.setMainMaterial(productBomVO.getMainMaterial());
        newRoutingStepInputDTO.setKeyMaterial(keyMaterial);
        newRoutingStepInputDTO.setYield(productBomVO.getYield());
        newRoutingStepInputDTO.setScrap(productBomVO.getScrap());
        newRoutingStepInputDTO.setInputFactor(productBomVO.getIoFactor());
        newRoutingStepInputDTO.setUnitProductionCost(productBomVO.getUnitProductionCost());
        newRoutingStepInputDTO.setScrapStrategy(productBomVO.getScrapStrategy());
        newRoutingStepInputDTO.setPercentageScrapRate(productBomVO.getPercentageScrapRate());
        newRoutingStepInputDTO.setAltMode(productBomVO.getAltMode());
        newRoutingStepInputDTO.setAltRatio(productBomVO.getAltRatio());
        newRoutingStepInputDTO.setAltMaterialGroup(productBomVO.getAltMaterialGroup());
        newRoutingStepInputDTO.setMatchCode(productBomVO.getMatchCode());
        newRoutingStepInputDTO.setConnectionTask(productBomVO.getConnectionTask());
        newRoutingStepInputDTO.setConnectionType(productBomVO.getConnectionType());
        newRoutingStepInputDTO.setMaxConnectionDuration(productBomVO.getMaxConnectionDuration());
        newRoutingStepInputDTO.setMinConnectionDuration(productBomVO.getMinConnectionDuration());
        newRoutingStepInputDTO.setCountingUnitId(productBomVO.getCountingUnitId());
        newRoutingStepInputDTO.setEffective(productBomVO.getEnabled());
        newRoutingStepInputDTO.setEnabled(productBomVO.getEnabled());
        newRoutingStepInputDTO.setSupplyType(productBomVO.getWipSupplyType());
        newRoutingStepInputDTO.setEffectiveTime(productBomVO.getStartTime());
        newRoutingStepInputDTO.setExpiryTime(productBomVO.getEndTime());
        if (newRoutingStepInputDTO.getExpiryTime() != null
                && newRoutingStepInputDTO.getExpiryTime().before(new Date())) {
            newRoutingStepInputDTO.setEnabled(YesOrNoEnum.NO.getCode());
            newRoutingStepInputDTO.setEffective(YesOrNoEnum.NO.getCode());
        }
    }

    @Override
    public void doCreateBatchWithPrimaryKey(List<NewRoutingStepInputDTO> list) {
        List<NewRoutingStepInputPO> newList = NewRoutingStepInputConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        BulkOperationUtils.bulkUpdateOrCreate(newList, poList ->
                newRoutingStepInputDao.insertBatchWithPrimaryKey(poList), 2500);
    }

    @Override
    public void doUpdateBatchSelective(List<NewRoutingStepInputDTO> list) {
        List<NewRoutingStepInputPO> newList = NewRoutingStepInputConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        BulkOperationUtils.bulkUpdateOrCreate(newList, poList ->
                newRoutingStepInputDao.updateBatchSelective(poList), 2500);
    }

    @Override
    public List<NewRoutingStepInputVO> selectMbplByStepIds(List<String> routingStepIds) {
        List<NewRoutingStepInputPO> list = newRoutingStepInputDao.selectMbplByStepIds(routingStepIds);
        return NewRoutingStepInputConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public int updateEnableForExpiryTime() {
        return newRoutingStepInputDao.updateEnableForExpiryTime();
    }

	@Override
	public List<String> selectInputProductIdsByRoutingId(String routingId) {
		return newRoutingStepInputDao.selectInputProductIdsByRoutingId(routingId);
	}

    @Override
    public List<NewRoutingStepInputVO> selectVOByParams(Map<String, Object> params) {
        return newRoutingStepInputDao.selectVOByParams(params);
    }

    @Override
    public List<NewRoutingStepInputVO> selectFormingProcess(List<String> inputProductIds) {
        return newRoutingStepInputDao.selectFormingProcess(inputProductIds);
    }

    @Override
    public List<StandardStepVO> selectDirectFormingProcess(String inputProductId) {
        return newRoutingStepInputDao.selectDirectFormingProcess(inputProductId);
    }

	@Override
	public ProductDemandSupplyCalculateVO selectRoutingStepInputForSupplyCalculate(String scenario, String productCode,
			String productId, List<Integer> sequenceNos) {
		ProductDemandSupplyCalculateVO returnVo = new ProductDemandSupplyCalculateVO();
		List<RoutingStepInputVO> resultList = new ArrayList<>();
		List<String> otherProductIds = new ArrayList<>();
		BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(defaultScenario.getData(),
                StockPointOrganizeTypeEnum.PRODUCT_ORGANIZATION.getCode(),
                "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        List<String> stockPointCodes = Arrays.asList(rangeData.split(","));
        String firstStockPointCode = stockPointCodes.get(0);
		String secondStockPointCode = stockPointCodes.get(1);
		if (StringUtils.isNotEmpty(productCode)) {
			queryProductForSupplyCalculate(productCode, null, sequenceNos, resultList, otherProductIds, returnVo,
					firstStockPointCode, secondStockPointCode);
		} else {
			queryProductForSupplyCalculate(null, productId, sequenceNos, resultList, otherProductIds, returnVo,
					firstStockPointCode, secondStockPointCode);
		}
		//递归查询
		while (CollUtil.isNotEmpty(otherProductIds)) {
			List<String> subOtherProductIds = new ArrayList<>();
			for (String subProductId : otherProductIds) {
				queryProductForSupplyCalculate(null, subProductId, null, resultList, subOtherProductIds, returnVo,
						firstStockPointCode, secondStockPointCode);
			}
			otherProductIds = subOtherProductIds;
		}
		if (CollUtil.isNotEmpty(resultList) && StringUtils.isNotEmpty(productCode)) {
			//获计划区域为FYSH的库存点
			String planArea = scenario.split("_")[1].toUpperCase();
			List<NewStockPointVO> stockPointVOs = newStockPointService.selectByParams(MapUtil.of("planArea", planArea));
			returnVo.setStockPointVOs(stockPointVOs);
		}
		returnVo.setRoutingStepInputVOs(resultList);
		return returnVo;
	}

	private void queryProductForSupplyCalculate(String productCode, String productId, List<Integer> sequenceNos,
            List<RoutingStepInputVO> resultList, List<String> otherProductIds,
            ProductDemandSupplyCalculateVO returnVo, String firstStockPointCode, String secondStockPointCode) {
		List<String> productIds = new ArrayList<>();
        if (StringUtils.isNotEmpty(productCode)) {
            List<NewProductStockPointVO> productList = newProductStockPointService.selectByProductCode(Arrays.asList(productCode));
            if (CollUtil.isEmpty(productList)) {
                return;
            }
            productIds = productList.stream().map(NewProductStockPointVO::getId).collect(Collectors.toList());
        } else {
            productIds.add(productId);
        }
        //获取对应的工艺
        List<RoutingVO> routingList = routingService.getRoutingByProductIdList(productIds);
        if (CollUtil.isEmpty(routingList)) {
            return;
        }
        //如果只查到一个，默认这个就是当前工艺，差到多个则优先找库存点为firstStockPointCode
        RoutingVO routingVO = null;
        Map<String, RoutingVO> routingMap = routingList.stream().collect(Collectors.toMap(RoutingVO::getStockPointId, Function.identity()));
        if (routingMap.containsKey(firstStockPointCode)) {
            routingVO = routingMap.get(firstStockPointCode);
        } else if (routingMap.containsKey(secondStockPointCode)) {
            routingVO = routingMap.get(secondStockPointCode);
        }
        if (Objects.isNull(routingVO)) {
            return;
        }
        //获取工艺下所有的工艺路径及路径下的供料
        List<RoutingStepVO> routingStepList = routingStepService.selectByRoutingIds(Arrays.asList(routingVO.getId()));
        Map<String, Integer> sequenceNoMap = new HashMap<>();
        if (CollUtil.isNotEmpty(sequenceNos)) {
            //递归循环不匹配工序号
            routingStepList = routingStepList.stream().filter(e -> sequenceNos.contains(e.getSequenceNo())).collect(Collectors.toList());
            sequenceNoMap = routingStepList.stream().collect(Collectors.toMap(RoutingStepVO::getId, RoutingStepVO::getSequenceNo));
        }
        if (Objects.isNull(routingStepList)) {
            return;
        }
        //获取路径下对应的输入物品
        List<String> routingStepIds = routingStepList.stream().map(RoutingStepVO::getId).collect(Collectors.toList());
        List<RoutingStepInputVO> routingStepInputList = routingStepInputService.selectByRoutingStepIds(routingStepIds);
        if (CollUtil.isEmpty(routingStepInputList)) {
            return;
        }
        List<String> queryProductIds = routingStepInputList.stream().map(RoutingStepInputVO::getInputProductId).collect(Collectors.toList());
        List<NewProductStockPointVO> productInfoList = newProductStockPointService.selectByPrimaryKeys(queryProductIds);
        Map<String, NewProductStockPointVO> productMap = productInfoList.stream().collect(Collectors.toMap(NewProductStockPointVO::getId, Function.identity()));
        List<Integer> sourceSequenceNos = new ArrayList<>();
        for (RoutingStepInputVO rsiVO : routingStepInputList) {
            NewProductStockPointVO psp = productMap.get(rsiVO.getInputProductId());
            if (!Objects.isNull(psp)) {
                if ("P".equals(psp.getProductType())) {
                    rsiVO.setProductCode(psp.getProductCode());
                    rsiVO.setProductName(psp.getProductName());
                    resultList.add(rsiVO);
                } else {
                    otherProductIds.add(psp.getId());
                }
            }
            Integer sequenceNo = sequenceNoMap.get(rsiVO.getRoutingStepId());
            if (sequenceNo != null && !sourceSequenceNos.contains(sequenceNo)) {
                sourceSequenceNos.add(sequenceNo);
            }
        }
        returnVo.setSequenceNos(sourceSequenceNos);
	}

}