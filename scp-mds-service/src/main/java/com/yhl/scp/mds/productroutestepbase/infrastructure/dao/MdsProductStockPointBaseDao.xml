<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mds.productroutestepbase.infrastructure.dao.MdsProductStockPointBaseDao">
    <resultMap id="BaseResultMap"
               type="com.yhl.scp.mds.productroutestepbase.infrastructure.po.MdsProductStockPointBasePO">
        <!--@Table mds_product_stock_point_base-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="stock_point_id" jdbcType="VARCHAR" property="stockPointId"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="stock_point_name" jdbcType="VARCHAR" property="stockPointName"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="product_length" jdbcType="VARCHAR" property="productLength"/>
        <result column="product_width" jdbcType="VARCHAR" property="productWidth"/>
        <result column="product_thickness" jdbcType="VARCHAR" property="productThickness"/>
        <result column="load_position" jdbcType="VARCHAR" property="loadPosition"/>
        <result column="product_color" jdbcType="VARCHAR" property="productColor"/>
        <result column="glass_color" jdbcType="VARCHAR" property="glassColor"/>
        <result column="product_type" jdbcType="VARCHAR" property="productType"/>
        <result column="difficulty_level" jdbcType="VARCHAR" property="difficultyLevel"/>
        <result column="grid_type" jdbcType="VARCHAR" property="gridType"/>
        <result column="production_model" jdbcType="VARCHAR" property="productionModel"/>
        <result column="toughening_type" jdbcType="VARCHAR" property="tougheningType"/>
        <result column="membrane_system" jdbcType="VARCHAR" property="membraneSystem"/>
        <result column="hud" jdbcType="VARCHAR" property="hud"/>
        <result column="clamp_type" jdbcType="VARCHAR" property="clampType"/>
        <result column="seal_edge" jdbcType="VARCHAR" property="sealEdge"/>
        <result column="product_area" jdbcType="VARCHAR" property="productArea"/>
        <result column="curvature" jdbcType="VARCHAR" property="curvature"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="dir_num" jdbcType="VARCHAR" property="dirNum"/>
        <result column="item_type" jdbcType="VARCHAR" property="itemType"/>
        <result column="attr1" jdbcType="VARCHAR" property="attr1"/>
        <result column="item_flag" jdbcType="VARCHAR" property="itemFlag"/>
        <result column="line_group" jdbcType="VARCHAR" property="lineGroup"/>
        <result column="standard_resource_id" jdbcType="VARCHAR" property="standardResourceId"/>
        <result column="part_num" jdbcType="VARCHAR" property="partNum"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO">
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="resource_code" jdbcType="VARCHAR" property="resourceCode"/>
        <result column="resource_name" jdbcType="VARCHAR" property="resourceName"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        stock_point_id,
        stock_point_code,
        stock_point_name,
        product_code,
        product_name,
        product_length,
        product_width,
        product_thickness,
        load_position,
        product_color,
        glass_color,
        product_type,
        difficulty_level,
        grid_type,
        production_model,
        toughening_type,
        membrane_system,
        hud,
        clamp_type,
        seal_edge,
        product_area,
        curvature,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        dir_num,
        item_type,
        attr1,
        item_flag,
        line_group,
        standard_resource_id,
        part_num
    </sql>
    <sql id="VO_Column_List">
        <include refid="Base_Column_List"/>,
        resource_code,
        resource_name
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointId != null and params.stockPointId != ''">
                and stock_point_id = #{params.stockPointId,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointName != null and params.stockPointName != ''">
                and stock_point_name = #{params.stockPointName,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productName != null and params.productName != ''">
                and product_name = #{params.productName,jdbcType=VARCHAR}
            </if>
            <if test="params.productLength != null">
                and product_length = #{params.productLength,jdbcType=VARCHAR}
            </if>
            <if test="params.productWidth != null">
                and product_width = #{params.productWidth,jdbcType=VARCHAR}
            </if>
            <if test="params.productThickness != null">
                and product_thickness = #{params.productThickness,jdbcType=VARCHAR}
            </if>
            <if test="params.loadPosition != null and params.loadPosition != ''">
                and load_position = #{params.loadPosition,jdbcType=VARCHAR}
            </if>
            <if test="params.productColor != null and params.productColor != ''">
                and product_color = #{params.productColor,jdbcType=VARCHAR}
            </if>
            <if test="params.glassColor != null and params.glassColor != ''">
                and glass_color = #{params.glassColor,jdbcType=VARCHAR}
            </if>
            <if test="params.productType != null and params.productType != ''">
                and product_type = #{params.productType,jdbcType=VARCHAR}
            </if>
            <if test="params.difficultyLevel != null and params.difficultyLevel != ''">
                and difficulty_level = #{params.difficultyLevel,jdbcType=VARCHAR}
            </if>
            <if test="params.gridType != null and params.gridType != ''">
                and grid_type = #{params.gridType,jdbcType=VARCHAR}
            </if>
            <if test="params.productionModel != null and params.productionModel != ''">
                and production_model = #{params.productionModel,jdbcType=VARCHAR}
            </if>
            <if test="params.tougheningType != null and params.tougheningType != ''">
                and toughening_type = #{params.tougheningType,jdbcType=VARCHAR}
            </if>
            <if test="params.membraneSystem != null and params.membraneSystem != ''">
                and membrane_system = #{params.membraneSystem,jdbcType=VARCHAR}
            </if>
            <if test="params.hud != null and params.hud != ''">
                and hud = #{params.hud,jdbcType=VARCHAR}
            </if>
            <if test="params.clampType != null and params.clampType != ''">
                and clamp_type = #{params.clampType,jdbcType=VARCHAR}
            </if>
            <if test="params.sealEdge != null and params.sealEdge != ''">
                and seal_edge = #{params.sealEdge,jdbcType=VARCHAR}
            </if>
            <if test="params.productArea != null">
                and product_area = #{params.productArea,jdbcType=VARCHAR}
            </if>
            <if test="params.curvature != null">
                and curvature = #{params.curvature,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.dirNum != null and params.dirNum != ''">
                and dir_num = #{params.dirNum,jdbcType=VARCHAR}
            </if>
            <if test="params.itemType != null and params.itemType != ''">
                and item_type = #{params.itemType,jdbcType=VARCHAR}
            </if>
            <if test="params.attr1 != null and params.attr1 != ''">
                and attr1 = #{params.attr1,jdbcType=VARCHAR}
            </if>
            <if test="params.itemFlag != null and params.itemFlag != ''">
                and item_flag = #{params.itemFlag,jdbcType=VARCHAR}
            </if>
            <if test="params.lineGroup != null and params.lineGroup != ''">
                and line_group = #{params.lineGroup,jdbcType=VARCHAR}
            </if>
            <if test="params.standardResourceId != null and params.standardResourceId != ''">
                and standard_resource_id = #{params.standardResourceId,jdbcType=VARCHAR}
            </if>
            <if test="params.partNum != null and params.partNum != ''">
                and part_num = #{params.partNum,jdbcType=VARCHAR}
            </if>
            <if test="params.lineGroupList != null and params.lineGroupList != ''">
                and line_group in
                <foreach collection="params.lineGroupList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.productCodeList != null and params.productCodeList != ''">
                and product_code in
                <foreach collection="params.productCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.dirNumList != null and params.dirNumList != ''">
                and dir_num in
                <foreach collection="params.dirNumList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_product_stock_point_base
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_product_stock_point_base
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_mds_product_stock_point_base
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_product_stock_point_base
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 条件查询 -->
    <select id="selectByProduct" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_product_stock_point_base
        where product_code in
        <foreach collection="productCodes" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectAllProduct" resultType="java.lang.String">
        select product_code
        from mds_product_stock_point_base
        where product_code is not null
        group by product_code
    </select>
    <!-- 新增 -->
    <insert id="insert"
            parameterType="com.yhl.scp.mds.productroutestepbase.infrastructure.po.MdsProductStockPointBasePO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid())
            from dual
        </selectKey>
        insert into mds_product_stock_point_base(id,
        stock_point_id,
        stock_point_code,
        stock_point_name,
        product_code,
        product_name,
        product_length,
        product_width,
        product_thickness,
        load_position,
        product_color,
        glass_color,
        product_type,
        difficulty_level,
        grid_type,
        production_model,
        toughening_type,
        membrane_system,
        hud,
        clamp_type,
        seal_edge,
        product_area,
        curvature,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        dir_num,
        item_type,
        attr1,
        item_flag,
        line_group,
        standard_resource_id,
        part_num)
        values (#{id,jdbcType=VARCHAR},
        #{stockPointId,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{stockPointName,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{productName,jdbcType=VARCHAR},
        #{productLength,jdbcType=VARCHAR},
        #{productWidth,jdbcType=VARCHAR},
        #{productThickness,jdbcType=VARCHAR},
        #{loadPosition,jdbcType=VARCHAR},
        #{productColor,jdbcType=VARCHAR},
        #{glassColor,jdbcType=VARCHAR},
        #{productType,jdbcType=VARCHAR},
        #{difficultyLevel,jdbcType=VARCHAR},
        #{gridType,jdbcType=VARCHAR},
        #{productionModel,jdbcType=VARCHAR},
        #{tougheningType,jdbcType=VARCHAR},
        #{membraneSystem,jdbcType=VARCHAR},
        #{hud,jdbcType=VARCHAR},
        #{clampType,jdbcType=VARCHAR},
        #{sealEdge,jdbcType=VARCHAR},
        #{productArea,jdbcType=VARCHAR},
        #{curvature,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{dirNum,jdbcType=VARCHAR},
        #{itemType,jdbcType=VARCHAR},
        #{attr1,jdbcType=VARCHAR},
        #{itemFlag,jdbcType=VARCHAR},
        #{lineGroup,jdbcType=VARCHAR},
        #{standardResourceId,jdbcType=VARCHAR},
        #{partNum,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mds.productroutestepbase.infrastructure.po.MdsProductStockPointBasePO">
        insert into mds_product_stock_point_base(id,
        stock_point_id,
        stock_point_code,
        stock_point_name,
        product_code,
        product_name,
        product_length,
        product_width,
        product_thickness,
        load_position,
        product_color,
        glass_color,
        product_type,
        difficulty_level,
        grid_type,
        production_model,
        toughening_type,
        membrane_system,
        hud,
        clamp_type,
        seal_edge,
        product_area,
        curvature,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        dir_num,
        item_type,
        attr1,
        item_flag,
        line_group,
        standard_resource_id,
        part_num)
        values (#{id,jdbcType=VARCHAR},
        #{stockPointId,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{stockPointName,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{productName,jdbcType=VARCHAR},
        #{productLength,jdbcType=VARCHAR},
        #{productWidth,jdbcType=VARCHAR},
        #{productThickness,jdbcType=VARCHAR},
        #{loadPosition,jdbcType=VARCHAR},
        #{productColor,jdbcType=VARCHAR},
        #{glassColor,jdbcType=VARCHAR},
        #{productType,jdbcType=VARCHAR},
        #{difficultyLevel,jdbcType=VARCHAR},
        #{gridType,jdbcType=VARCHAR},
        #{productionModel,jdbcType=VARCHAR},
        #{tougheningType,jdbcType=VARCHAR},
        #{membraneSystem,jdbcType=VARCHAR},
        #{hud,jdbcType=VARCHAR},
        #{clampType,jdbcType=VARCHAR},
        #{sealEdge,jdbcType=VARCHAR},
        #{productArea,jdbcType=VARCHAR},
        #{curvature,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{dirNum,jdbcType=VARCHAR},
        #{itemType,jdbcType=VARCHAR},
        #{attr1,jdbcType=VARCHAR},
        #{itemFlag,jdbcType=VARCHAR},
        #{lineGroup,jdbcType=VARCHAR},
        #{standardResourceId,jdbcType=VARCHAR},
        #{partNum,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mds_product_stock_point_base(id,
        stock_point_id,
        stock_point_code,
        stock_point_name,
        product_code,
        product_name,
        product_length,
        product_width,
        product_thickness,
        load_position,
        product_color,
        glass_color,
        product_type,
        difficulty_level,
        grid_type,
        production_model,
        toughening_type,
        membrane_system,
        hud,
        clamp_type,
        seal_edge,
        product_area,
        curvature,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        dir_num,
        item_type,
        attr1,
        item_flag,
        line_group,
        standard_resource_id,
        part_num)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.id,jdbcType=VARCHAR},
             #{entity.stockPointId,jdbcType=VARCHAR},
             #{entity.stockPointCode,jdbcType=VARCHAR},
             #{entity.stockPointName,jdbcType=VARCHAR},
             #{entity.productCode,jdbcType=VARCHAR},
             #{entity.productName,jdbcType=VARCHAR},
             #{entity.productLength,jdbcType=VARCHAR},
             #{entity.productWidth,jdbcType=VARCHAR},
             #{entity.productThickness,jdbcType=VARCHAR},
             #{entity.loadPosition,jdbcType=VARCHAR},
             #{entity.productColor,jdbcType=VARCHAR},
             #{entity.glassColor,jdbcType=VARCHAR},
             #{entity.productType,jdbcType=VARCHAR},
             #{entity.difficultyLevel,jdbcType=VARCHAR},
             #{entity.gridType,jdbcType=VARCHAR},
             #{entity.productionModel,jdbcType=VARCHAR},
             #{entity.tougheningType,jdbcType=VARCHAR},
             #{entity.membraneSystem,jdbcType=VARCHAR},
             #{entity.hud,jdbcType=VARCHAR},
             #{entity.clampType,jdbcType=VARCHAR},
             #{entity.sealEdge,jdbcType=VARCHAR},
             #{entity.productArea,jdbcType=VARCHAR},
             #{entity.curvature,jdbcType=VARCHAR},
             #{entity.remark,jdbcType=VARCHAR},
             #{entity.enabled,jdbcType=VARCHAR},
             #{entity.creator,jdbcType=VARCHAR},
             #{entity.createTime,jdbcType=TIMESTAMP},
             #{entity.modifier,jdbcType=VARCHAR},
             #{entity.modifyTime,jdbcType=TIMESTAMP},
             #{entity.dirNum,jdbcType=VARCHAR},
             #{entity.itemType,jdbcType=VARCHAR},
             #{entity.attr1,jdbcType=VARCHAR},
             #{entity.itemFlag,jdbcType=VARCHAR},
             #{entity.lineGroup,jdbcType=VARCHAR},
             #{entity.standardResourceId,jdbcType=VARCHAR},
             #{entity.partNum,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mds_product_stock_point_base(id,
        stock_point_id,
        stock_point_code,
        stock_point_name,
        product_code,
        product_name,
        product_length,
        product_width,
        product_thickness,
        load_position,
        product_color,
        glass_color,
        product_type,
        difficulty_level,
        grid_type,
        production_model,
        toughening_type,
        membrane_system,
        hud,
        clamp_type,
        seal_edge,
        product_area,
        curvature,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        dir_num,
        item_type,
        attr1,
        item_flag,
        line_group,
        standard_resource_id,
        part_num)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.id,jdbcType=VARCHAR},
            #{entity.stockPointId,jdbcType=VARCHAR},
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.stockPointName,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.productName,jdbcType=VARCHAR},
            #{entity.productLength,jdbcType=VARCHAR},
            #{entity.productWidth,jdbcType=VARCHAR},
            #{entity.productThickness,jdbcType=VARCHAR},
            #{entity.loadPosition,jdbcType=VARCHAR},
            #{entity.productColor,jdbcType=VARCHAR},
            #{entity.glassColor,jdbcType=VARCHAR},
            #{entity.productType,jdbcType=VARCHAR},
            #{entity.difficultyLevel,jdbcType=VARCHAR},
            #{entity.gridType,jdbcType=VARCHAR},
            #{entity.productionModel,jdbcType=VARCHAR},
            #{entity.tougheningType,jdbcType=VARCHAR},
            #{entity.membraneSystem,jdbcType=VARCHAR},
            #{entity.hud,jdbcType=VARCHAR},
            #{entity.clampType,jdbcType=VARCHAR},
            #{entity.sealEdge,jdbcType=VARCHAR},
            #{entity.productArea,jdbcType=VARCHAR},
            #{entity.curvature,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.dirNum,jdbcType=VARCHAR},
            #{entity.itemType,jdbcType=VARCHAR},
            #{entity.attr1,jdbcType=VARCHAR},
            #{entity.itemFlag,jdbcType=VARCHAR},
            #{entity.lineGroup,jdbcType=VARCHAR},
            #{entity.standardResourceId,jdbcType=VARCHAR},
            #{entity.partNum,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update"
            parameterType="com.yhl.scp.mds.productroutestepbase.infrastructure.po.MdsProductStockPointBasePO">
        update mds_product_stock_point_base
        set stock_point_id       = #{stockPointId,jdbcType=VARCHAR},
            stock_point_code     = #{stockPointCode,jdbcType=VARCHAR},
            stock_point_name     = #{stockPointName,jdbcType=VARCHAR},
            product_code         = #{productCode,jdbcType=VARCHAR},
            product_name         = #{productName,jdbcType=VARCHAR},
            product_length       = #{productLength,jdbcType=VARCHAR},
            product_width        = #{productWidth,jdbcType=VARCHAR},
            product_thickness    = #{productThickness,jdbcType=VARCHAR},
            load_position        = #{loadPosition,jdbcType=VARCHAR},
            product_color        = #{productColor,jdbcType=VARCHAR},
            glass_color          = #{glassColor,jdbcType=VARCHAR},
            product_type         = #{productType,jdbcType=VARCHAR},
            difficulty_level     = #{difficultyLevel,jdbcType=VARCHAR},
            grid_type            = #{gridType,jdbcType=VARCHAR},
            production_model     = #{productionModel,jdbcType=VARCHAR},
            toughening_type      = #{tougheningType,jdbcType=VARCHAR},
            membrane_system      = #{membraneSystem,jdbcType=VARCHAR},
            hud                  = #{hud,jdbcType=VARCHAR},
            clamp_type           = #{clampType,jdbcType=VARCHAR},
            seal_edge            = #{sealEdge,jdbcType=VARCHAR},
            product_area         = #{productArea,jdbcType=VARCHAR},
            curvature            = #{curvature,jdbcType=VARCHAR},
            remark               = #{remark,jdbcType=VARCHAR},
            enabled              = #{enabled,jdbcType=VARCHAR},
            modifier             = #{modifier,jdbcType=VARCHAR},
            modify_time          = #{modifyTime,jdbcType=TIMESTAMP},
            dir_num              = #{dirNum,jdbcType=VARCHAR},
            item_type            = #{itemType,jdbcType=VARCHAR},
            attr1                = #{attr1,jdbcType=VARCHAR},
            item_flag            = #{itemFlag,jdbcType=VARCHAR},
            line_group           = #{lineGroup,jdbcType=VARCHAR},
            standard_resource_id = #{standardResourceId,jdbcType=VARCHAR},
            part_num             = #{partNum,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mds.productroutestepbase.infrastructure.po.MdsProductStockPointBasePO">
        update mds_product_stock_point_base
        <set>
            <if test="item.stockPointId != null and item.stockPointId != ''">
                stock_point_id = #{item.stockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointName != null and item.stockPointName != ''">
                stock_point_name = #{item.stockPointName,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productName != null and item.productName != ''">
                product_name = #{item.productName,jdbcType=VARCHAR},
            </if>
            <if test="item.productLength != null">
                product_length = #{item.productLength,jdbcType=VARCHAR},
            </if>
            <if test="item.productWidth != null">
                product_width = #{item.productWidth,jdbcType=VARCHAR},
            </if>
            <if test="item.productThickness != null">
                product_thickness = #{item.productThickness,jdbcType=VARCHAR},
            </if>
            <if test="item.loadPosition != null and item.loadPosition != ''">
                load_position = #{item.loadPosition,jdbcType=VARCHAR},
            </if>
            <if test="item.productColor != null and item.productColor != ''">
                product_color = #{item.productColor,jdbcType=VARCHAR},
            </if>
            <if test="item.glassColor != null and item.glassColor != ''">
                glass_color = #{item.glassColor,jdbcType=VARCHAR},
            </if>
            <if test="item.productType != null and item.productType != ''">
                product_type = #{item.productType,jdbcType=VARCHAR},
            </if>
            <if test="item.difficultyLevel != null and item.difficultyLevel != ''">
                difficulty_level = #{item.difficultyLevel,jdbcType=VARCHAR},
            </if>
            <if test="item.gridType != null and item.gridType != ''">
                grid_type = #{item.gridType,jdbcType=VARCHAR},
            </if>
            <if test="item.productionModel != null and item.productionModel != ''">
                production_model = #{item.productionModel,jdbcType=VARCHAR},
            </if>
            <if test="item.tougheningType != null and item.tougheningType != ''">
                toughening_type = #{item.tougheningType,jdbcType=VARCHAR},
            </if>
            <if test="item.membraneSystem != null and item.membraneSystem != ''">
                membrane_system = #{item.membraneSystem,jdbcType=VARCHAR},
            </if>
            <if test="item.hud != null and item.hud != ''">
                hud = #{item.hud,jdbcType=VARCHAR},
            </if>
            <if test="item.clampType != null and item.clampType != ''">
                clamp_type = #{item.clampType,jdbcType=VARCHAR},
            </if>
            <if test="item.sealEdge != null and item.sealEdge != ''">
                seal_edge = #{item.sealEdge,jdbcType=VARCHAR},
            </if>
            <if test="item.productArea != null">
                product_area = #{item.productArea,jdbcType=VARCHAR},
            </if>
            <if test="item.curvature != null">
                curvature = #{item.curvature,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.dirNum != null and item.dirNum != ''">
                dir_num = #{item.dirNum,jdbcType=VARCHAR},
            </if>
            <if test="item.itemType != null and item.itemType != ''">
                item_type = #{item.itemType,jdbcType=VARCHAR},
            </if>
            <if test="item.attr1 != null and item.attr1 != ''">
                attr1 = #{item.attr1,jdbcType=VARCHAR},
            </if>
            <if test="item.itemFlag != null and item.itemFlag != ''">
                item_flag = #{item.itemFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.lineGroup != null and item.lineGroup != ''">
                line_group = #{item.lineGroup,jdbcType=VARCHAR},
            </if>
            <if test="item.standardResourceId != null and item.standardResourceId != ''">
                standard_resource_id = #{item.standardResourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.partNum != null and item.partNum != ''">
                part_num = #{item.partNum,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mds_product_stock_point_base
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="stock_point_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_length = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productLength,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_width = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productWidth,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_thickness = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productThickness,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="load_position = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.loadPosition,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_color = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productColor,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="glass_color = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.glassColor,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="difficulty_level = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.difficultyLevel,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="grid_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.gridType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="production_model = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productionModel,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="toughening_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.tougheningType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="membrane_system = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.membraneSystem,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="hud = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.hud,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="clamp_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.clampType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="seal_edge = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sealEdge,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_area = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productArea,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="curvature = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.curvature,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="dir_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.dirNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="item_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.itemType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="attr1 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.attr1,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="item_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.itemFlag,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="line_group = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lineGroup,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="standard_resource_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.standardResourceId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="part_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.partNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mds_product_stock_point_base
            <set>
                <if test="item.stockPointId != null and item.stockPointId != ''">
                    stock_point_id = #{item.stockPointId,jdbcType=VARCHAR},
                </if>
                <if test="item.stockPointCode != null and item.stockPointCode != ''">
                    stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
                </if>
                <if test="item.stockPointName != null and item.stockPointName != ''">
                    stock_point_name = #{item.stockPointName,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productName != null and item.productName != ''">
                    product_name = #{item.productName,jdbcType=VARCHAR},
                </if>
                <if test="item.productLength != null">
                    product_length = #{item.productLength,jdbcType=VARCHAR},
                </if>
                <if test="item.productWidth != null">
                    product_width = #{item.productWidth,jdbcType=VARCHAR},
                </if>
                <if test="item.productThickness != null">
                    product_thickness = #{item.productThickness,jdbcType=VARCHAR},
                </if>
                <if test="item.loadPosition != null and item.loadPosition != ''">
                    load_position = #{item.loadPosition,jdbcType=VARCHAR},
                </if>
                <if test="item.productColor != null and item.productColor != ''">
                    product_color = #{item.productColor,jdbcType=VARCHAR},
                </if>
                <if test="item.glassColor != null and item.glassColor != ''">
                    glass_color = #{item.glassColor,jdbcType=VARCHAR},
                </if>
                <if test="item.productType != null and item.productType != ''">
                    product_type = #{item.productType,jdbcType=VARCHAR},
                </if>
                <if test="item.difficultyLevel != null and item.difficultyLevel != ''">
                    difficulty_level = #{item.difficultyLevel,jdbcType=VARCHAR},
                </if>
                <if test="item.gridType != null and item.gridType != ''">
                    grid_type = #{item.gridType,jdbcType=VARCHAR},
                </if>
                <if test="item.productionModel != null and item.productionModel != ''">
                    production_model = #{item.productionModel,jdbcType=VARCHAR},
                </if>
                <if test="item.tougheningType != null and item.tougheningType != ''">
                    toughening_type = #{item.tougheningType,jdbcType=VARCHAR},
                </if>
                <if test="item.membraneSystem != null and item.membraneSystem != ''">
                    membrane_system = #{item.membraneSystem,jdbcType=VARCHAR},
                </if>
                <if test="item.hud != null and item.hud != ''">
                    hud = #{item.hud,jdbcType=VARCHAR},
                </if>
                <if test="item.clampType != null and item.clampType != ''">
                    clamp_type = #{item.clampType,jdbcType=VARCHAR},
                </if>
                <if test="item.sealEdge != null and item.sealEdge != ''">
                    seal_edge = #{item.sealEdge,jdbcType=VARCHAR},
                </if>
                <if test="item.productArea != null">
                    product_area = #{item.productArea,jdbcType=VARCHAR},
                </if>
                <if test="item.curvature != null">
                    curvature = #{item.curvature,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.dirNum != null and item.dirNum != ''">
                    dir_num = #{item.dirNum,jdbcType=VARCHAR},
                </if>
                <if test="item.itemType != null and item.itemType != ''">
                    item_type = #{item.itemType,jdbcType=VARCHAR},
                </if>
                <if test="item.attr1 != null and item.attr1 != ''">
                    attr1 = #{item.attr1,jdbcType=VARCHAR},
                </if>
                <if test="item.itemFlag != null and item.itemFlag != ''">
                    item_flag = #{item.itemFlag,jdbcType=VARCHAR},
                </if>
                <if test="item.lineGroup != null and item.lineGroup != ''">
                    line_group = #{item.lineGroup,jdbcType=VARCHAR},
                </if>
                <if test="item.standardResourceId != null and item.standardResourceId != ''">
                    standard_resource_id = #{item.standardResourceId,jdbcType=VARCHAR},
                </if>
                <if test="item.partNum != null and item.partNum != ''">
                    part_num = #{item.partNum,jdbcType=VARCHAR},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mds_product_stock_point_base
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete
        from mds_product_stock_point_base where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <update id="updateItemFlagByProductCode" parameterType="map">
        UPDATE mds_product_stock_point_base
        SET item_flag = #{newItemFlag}
        WHERE product_code = #{productCode}
    </update>
    <update id="updateLineGroupByProductCode" parameterType="map">
        UPDATE mds_product_stock_point_base
        SET line_group = #{value}
        WHERE product_code = #{key}
    </update>
    <update id="updateStandardResourceIdByProductCode" parameterType="map">
        UPDATE mds_product_stock_point_base
        SET standard_resource_id = #{value}
        WHERE product_code = #{key}
    </update>

    <select id="selectByLineGroup" resultMap="VOResultMap">
        SELECT pspb.line_group,
               psp.id product_id
        FROM mds_product_stock_point_base pspb
                 LEFT JOIN mds_product_stock_point psp ON pspb.product_code = psp.product_code
        WHERE 1 = 1
          AND pspb.line_group IS NOT NULL
          AND pspb.enabled = 'YES'
          and pspb.line_group in
        <foreach collection="lineGroupList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and psp.stock_point_code in
        <foreach collection="stockPointCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectProductCodesByModifyTime" resultType="java.lang.String">
        SELECT product_code
        FROM mds_product_stock_point_base
        WHERE modify_time >= #{modifyTime}
    </select>

    <select id="selectPermissionsByLineGroup" resultType="java.lang.String">
        select DISTINCT line_group
        from mds_product_stock_point_base
        where line_group in (select standard_resource_code
                             from mds_res_standard_resource
                             where find_in_set(#{userId,jdbcType=VARCHAR}, production_planner) > 0)
    </select>

    <select id="selectPermissionsByLineGroupList" resultType="java.lang.String">
        select DISTINCT product_code
        from mds_product_stock_point_base
        where line_group in
        <foreach collection="lineGroups" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectPermissionsProductCodeByLineGroup" resultType="java.lang.String">
        select DISTINCT product_code
        from mds_product_stock_point_base
        where line_group in (select standard_resource_code
                             from mds_res_standard_resource
                             where find_in_set(#{userId,jdbcType=VARCHAR}, production_planner) > 0)
    </select>

    <select id="getPlannerProductIdList" resultType="java.lang.String">
        select id
        from mds_product_stock_point
        where product_code IN
              (select DISTINCT product_code
               from mds_product_stock_point_base
               where line_group in (select standard_resource_code
                                    from mds_res_standard_resource
                                    where find_in_set(#{userId,jdbcType=VARCHAR}, production_planner) > 0))
    </select>

    <select id="getPlannerProductIdByLineGroup" resultType="java.lang.String">
        select id
        from mds_product_stock_point
        where product_code IN
        (select DISTINCT product_code
         from mds_product_stock_point_base
        where line_group in
        <foreach collection="lineGroupList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
    </select>

    <select id="selectProductCodesWithEmptyLineGroup" resultType="String">
        SELECT product_code
        FROM mds_product_stock_point_base
        WHERE line_group IS NULL OR line_group = ''
    </select>
    <select id="selectFg2SaList" parameterType="java.util.List" resultType="java.util.Map">
        select distinct
            pp.product_code  as fgCode,
            psp.product_code as saCode
        from mds_rou_routing_step_input rsi
                 left join mds_rou_routing r on rsi.routing_id=r.id
                 left join mds_product_stock_point psp on rsi.input_product_id=psp.id
                 left join mds_product_stock_point pp on r.product_id=pp.id
        where psp.product_type='SA'
          and psp.supply_type='制造'
          and r.product_id in (
            SELECT psp1.id FROM mds_rou_routing r1
            left join mds_product_stock_point psp1
            on r1.product_id=psp1.id WHERE psp1.product_code in
            <foreach collection="fgCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        )
    </select>
    <select id="selectByProductCodes" parameterType="list" resultType="com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO">
        SELECT
        product_code,
        membrane_system,
        clamp_type,
        item_flag
        FROM
        mds_product_stock_point_base
        WHERE
        product_code IN
        <foreach collection="productCodes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>
    <select id="selectPartNumByProductCodes" resultType="java.util.Map">
        SELECT product_code, part_num, product_name
        FROM mds_product_stock_point_base
        WHERE part_num IS NOT NULL AND part_num != ''
        AND product_code IN
        <foreach item="code" collection="productCodes" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>
</mapper>
