<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mds.routing.infrastructure.dao.NewRoutingStepResourceDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mds.routing.infrastructure.po.NewRoutingStepResourcePO">
        <!--@Table mds_rou_routing_step_resource-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="routing_id" jdbcType="VARCHAR" property="routingId"/>
        <result column="routing_step_id" jdbcType="VARCHAR" property="routingStepId"/>
        <result column="standard_resource_id" jdbcType="VARCHAR" property="standardResourceId"/>
        <result column="physical_resource_id" jdbcType="VARCHAR" property="physicalResourceId"/>
        <result column="kid" jdbcType="VARCHAR" property="kid"/>
        <result column="resource_type" jdbcType="VARCHAR" property="resourceType"/>
        <result column="unit_production_time" jdbcType="VARCHAR" property="unitProductionTime"/>
        <result column="unit_production_cost" jdbcType="VARCHAR" property="unitProductionCost"/>
        <result column="fixed_work_hours" jdbcType="INTEGER" property="fixedWorkHours"/>
        <result column="units_per_hour" jdbcType="VARCHAR" property="unitsPerHour"/>
        <result column="input_frequency" jdbcType="VARCHAR" property="inputFrequency"/>
        <result column="lot_size" jdbcType="VARCHAR" property="lotSize"/>
        <result column="max_lot_size" jdbcType="VARCHAR" property="maxLotSize"/>
        <result column="min_lot_size" jdbcType="VARCHAR" property="minLotSize"/>
        <result column="setup_unit_batch_size" jdbcType="VARCHAR" property="setupUnitBatchSize"/>
        <result column="production_unit_batch_size" jdbcType="VARCHAR" property="productionUnitBatchSize"/>
        <result column="cleanup_unit_batch_size" jdbcType="VARCHAR" property="cleanupUnitBatchSize"/>
        <result column="max_quantity" jdbcType="VARCHAR" property="maxQuantity"/>
        <result column="min_quantity" jdbcType="VARCHAR" property="minQuantity"/>
        <result column="lead_time" jdbcType="INTEGER" property="leadTime"/>
        <result column="lead_time_stdev" jdbcType="INTEGER" property="leadTimeStdev"/>
        <result column="scheduling_space" jdbcType="INTEGER" property="schedulingSpace"/>
        <result column="setup_duration" jdbcType="VARCHAR" property="setupDuration"/>
        <result column="production_duration" jdbcType="VARCHAR" property="productionDuration"/>
        <result column="cleanup_duration" jdbcType="VARCHAR" property="cleanupDuration"/>
        <result column="max_production_suspend_duration" jdbcType="INTEGER" property="maxProductionSuspendDuration"/>
        <result column="max_setup_suspend_duration" jdbcType="INTEGER" property="maxSetupSuspendDuration"/>
        <result column="max_cleanup_suspend_duration" jdbcType="INTEGER" property="maxCleanupSuspendDuration"/>
        <result column="priority" jdbcType="INTEGER" property="priority"/>
        <result column="alt_tool_code" jdbcType="VARCHAR" property="altToolCode"/>
        <result column="match_code" jdbcType="VARCHAR" property="matchCode"/>
        <result column="production_line" jdbcType="VARCHAR" property="productionLine"/>
        <result column="strict_production_line_constraints" jdbcType="VARCHAR" property="strictProductionLineConstraints"/>
        <result column="split_allowed" jdbcType="VARCHAR" property="splitAllowed"/>
        <result column="counting_unit_id" jdbcType="VARCHAR" property="countingUnitId"/>
        <result column="resource_category" jdbcType="VARCHAR" property="resourceCategory"/>
        <result column="effective_time" jdbcType="TIMESTAMP" property="effectiveTime"/>
        <result column="effective" jdbcType="VARCHAR" property="effective"/>
        <result column="expire_reason" jdbcType="VARCHAR" property="expireReason"/>
        <result column="expiry_time" jdbcType="TIMESTAMP" property="expiryTime"/>
        <result column="currency_unit_id" jdbcType="VARCHAR" property="currencyUnitId"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mds.routing.vo.NewRoutingStepResourceVO">
    	<result column="standard_resource_code" jdbcType="VARCHAR" property="standardResourceCode"/>
    	<result column="standard_resource_name" jdbcType="VARCHAR" property="standardResourceName"/>
    	<result column="physical_resource_code" jdbcType="VARCHAR" property="physicalResourceCode"/>
    	<result column="physical_resource_name" jdbcType="VARCHAR" property="physicalResourceName"/>
    	<result column="routing_code" jdbcType="VARCHAR" property="routingCode"/>
    	<result column="routing_name" jdbcType="VARCHAR" property="routingName"/>
    	<result column="sequence_no" jdbcType="VARCHAR" property="sequenceNo"/>
    	<result column="standard_step_code" jdbcType="VARCHAR" property="standardStepCode"/>
    	<result column="standard_step_name" jdbcType="VARCHAR" property="standardStepName"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,routing_id,routing_step_id,standard_resource_id,physical_resource_id,kid,resource_type,unit_production_time,unit_production_cost,fixed_work_hours,units_per_hour,input_frequency,lot_size,max_lot_size,min_lot_size,setup_unit_batch_size,production_unit_batch_size,cleanup_unit_batch_size,max_quantity,min_quantity,lead_time,lead_time_stdev,scheduling_space,setup_duration,production_duration,cleanup_duration,max_production_suspend_duration,max_setup_suspend_duration,max_cleanup_suspend_duration,priority,alt_tool_code,match_code,production_line,strict_production_line_constraints,split_allowed,counting_unit_id,resource_category,effective_time,effective,expire_reason,expiry_time,currency_unit_id,remark,enabled,creator,create_time,modifier,modify_time
    </sql>
    <sql id="VO_Column_List">
		id,
		routing_id,
		routing_step_id,
		standard_resource_id,
		standard_resource_code,
		standard_resource_name,
		physical_resource_id,
		physical_resource_code,
		physical_resource_name,
		priority,
		routing_code,
		routing_name,
		sequence_no,
		standard_step_code,
		standard_step_name,
		enabled,
		unit_production_time
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.routingId != null and params.routingId != ''">
                and routing_id = #{params.routingId,jdbcType=VARCHAR}
            </if>
            <if test="params.routingStepId != null and params.routingStepId != ''">
                and routing_step_id = #{params.routingStepId,jdbcType=VARCHAR}
            </if>
            <if test="params.standardResourceId != null and params.standardResourceId != ''">
                and standard_resource_id = #{params.standardResourceId,jdbcType=VARCHAR}
            </if>
            <if test="params.physicalResourceId != null and params.physicalResourceId != ''">
                and physical_resource_id = #{params.physicalResourceId,jdbcType=VARCHAR}
            </if>
            <if test="params.kid != null and params.kid != ''">
                and kid = #{params.kid,jdbcType=VARCHAR}
            </if>
            <if test="params.resourceType != null and params.resourceType != ''">
                and resource_type = #{params.resourceType,jdbcType=VARCHAR}
            </if>
            <if test="params.unitProductionTime != null">
                and unit_production_time = #{params.unitProductionTime,jdbcType=VARCHAR}
            </if>
            <if test="params.unitProductionCost != null">
                and unit_production_cost = #{params.unitProductionCost,jdbcType=VARCHAR}
            </if>
            <if test="params.fixedWorkHours != null">
                and fixed_work_hours = #{params.fixedWorkHours,jdbcType=INTEGER}
            </if>
            <if test="params.unitsPerHour != null">
                and units_per_hour = #{params.unitsPerHour,jdbcType=VARCHAR}
            </if>
            <if test="params.inputFrequency != null and params.inputFrequency != ''">
                and input_frequency = #{params.inputFrequency,jdbcType=VARCHAR}
            </if>
            <if test="params.lotSize != null">
                and lot_size = #{params.lotSize,jdbcType=VARCHAR}
            </if>
            <if test="params.maxLotSize != null">
                and max_lot_size = #{params.maxLotSize,jdbcType=VARCHAR}
            </if>
            <if test="params.minLotSize != null">
                and min_lot_size = #{params.minLotSize,jdbcType=VARCHAR}
            </if>
            <if test="params.setupUnitBatchSize != null">
                and setup_unit_batch_size = #{params.setupUnitBatchSize,jdbcType=VARCHAR}
            </if>
            <if test="params.productionUnitBatchSize != null">
                and production_unit_batch_size = #{params.productionUnitBatchSize,jdbcType=VARCHAR}
            </if>
            <if test="params.cleanupUnitBatchSize != null">
                and cleanup_unit_batch_size = #{params.cleanupUnitBatchSize,jdbcType=VARCHAR}
            </if>
            <if test="params.maxQuantity != null">
                and max_quantity = #{params.maxQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.minQuantity != null">
                and min_quantity = #{params.minQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.leadTime != null">
                and lead_time = #{params.leadTime,jdbcType=INTEGER}
            </if>
            <if test="params.leadTimeStdev != null">
                and lead_time_stdev = #{params.leadTimeStdev,jdbcType=INTEGER}
            </if>
            <if test="params.schedulingSpace != null">
                and scheduling_space = #{params.schedulingSpace,jdbcType=INTEGER}
            </if>
            <if test="params.setupDuration != null and params.setupDuration != ''">
                and setup_duration = #{params.setupDuration,jdbcType=VARCHAR}
            </if>
            <if test="params.productionDuration != null and params.productionDuration != ''">
                and production_duration = #{params.productionDuration,jdbcType=VARCHAR}
            </if>
            <if test="params.cleanupDuration != null and params.cleanupDuration != ''">
                and cleanup_duration = #{params.cleanupDuration,jdbcType=VARCHAR}
            </if>
            <if test="params.maxProductionSuspendDuration != null">
                and max_production_suspend_duration = #{params.maxProductionSuspendDuration,jdbcType=INTEGER}
            </if>
            <if test="params.maxSetupSuspendDuration != null">
                and max_setup_suspend_duration = #{params.maxSetupSuspendDuration,jdbcType=INTEGER}
            </if>
            <if test="params.maxCleanupSuspendDuration != null">
                and max_cleanup_suspend_duration = #{params.maxCleanupSuspendDuration,jdbcType=INTEGER}
            </if>
            <if test="params.priority != null">
                and priority = #{params.priority,jdbcType=INTEGER}
            </if>
            <if test="params.altToolCode != null and params.altToolCode != ''">
                and alt_tool_code = #{params.altToolCode,jdbcType=VARCHAR}
            </if>
            <if test="params.matchCode != null and params.matchCode != ''">
                and match_code = #{params.matchCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productionLine != null and params.productionLine != ''">
                and production_line = #{params.productionLine,jdbcType=VARCHAR}
            </if>
            <if test="params.strictProductionLineConstraints != null and params.strictProductionLineConstraints != ''">
                and strict_production_line_constraints = #{params.strictProductionLineConstraints,jdbcType=VARCHAR}
            </if>
            <if test="params.splitAllowed != null and params.splitAllowed != ''">
                and split_allowed = #{params.splitAllowed,jdbcType=VARCHAR}
            </if>
            <if test="params.countingUnitId != null and params.countingUnitId != ''">
                and counting_unit_id = #{params.countingUnitId,jdbcType=VARCHAR}
            </if>
            <if test="params.resourceCategory != null and params.resourceCategory != ''">
                and resource_category = #{params.resourceCategory,jdbcType=VARCHAR}
            </if>
            <if test="params.effectiveTime != null">
                and effective_time = #{params.effectiveTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.effective != null and params.effective != ''">
                and effective = #{params.effective,jdbcType=VARCHAR}
            </if>
            <if test="params.expireReason != null and params.expireReason != ''">
                and expire_reason = #{params.expireReason,jdbcType=VARCHAR}
            </if>
            <if test="params.expiryTime != null">
                and expiry_time = #{params.expiryTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.currencyUnitId != null and params.currencyUnitId != ''">
                and currency_unit_id = #{params.currencyUnitId,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.routingStepIds != null and params.routingStepIds.size() > 0">
                and routing_step_id in
                <foreach collection="params.routingStepIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_rou_routing_step_resource
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_rou_routing_step_resource
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_new_mds_rou_routing_step_resource
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_rou_routing_step_resource
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_mds_rou_routing_step_resource
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mds.routing.infrastructure.po.NewRoutingStepResourcePO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mds_rou_routing_step_resource(
        id,
        routing_id,
        routing_step_id,
        standard_resource_id,
        physical_resource_id,
        kid,
        resource_type,
        unit_production_time,
        unit_production_cost,
        fixed_work_hours,
        units_per_hour,
        input_frequency,
        lot_size,
        max_lot_size,
        min_lot_size,
        setup_unit_batch_size,
        production_unit_batch_size,
        cleanup_unit_batch_size,
        max_quantity,
        min_quantity,
        lead_time,
        lead_time_stdev,
        scheduling_space,
        setup_duration,
        production_duration,
        cleanup_duration,
        max_production_suspend_duration,
        max_setup_suspend_duration,
        max_cleanup_suspend_duration,
        priority,
        alt_tool_code,
        match_code,
        production_line,
        strict_production_line_constraints,
        split_allowed,
        counting_unit_id,
        resource_category,
        effective_time,
        effective,
        expire_reason,
        expiry_time,
        currency_unit_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{routingId,jdbcType=VARCHAR},
        #{routingStepId,jdbcType=VARCHAR},
        #{standardResourceId,jdbcType=VARCHAR},
        #{physicalResourceId,jdbcType=VARCHAR},
        #{kid,jdbcType=VARCHAR},
        #{resourceType,jdbcType=VARCHAR},
        #{unitProductionTime,jdbcType=VARCHAR},
        #{unitProductionCost,jdbcType=VARCHAR},
        #{fixedWorkHours,jdbcType=INTEGER},
        #{unitsPerHour,jdbcType=VARCHAR},
        #{inputFrequency,jdbcType=VARCHAR},
        #{lotSize,jdbcType=VARCHAR},
        #{maxLotSize,jdbcType=VARCHAR},
        #{minLotSize,jdbcType=VARCHAR},
        #{setupUnitBatchSize,jdbcType=VARCHAR},
        #{productionUnitBatchSize,jdbcType=VARCHAR},
        #{cleanupUnitBatchSize,jdbcType=VARCHAR},
        #{maxQuantity,jdbcType=VARCHAR},
        #{minQuantity,jdbcType=VARCHAR},
        #{leadTime,jdbcType=INTEGER},
        #{leadTimeStdev,jdbcType=INTEGER},
        #{schedulingSpace,jdbcType=INTEGER},
        #{setupDuration,jdbcType=VARCHAR},
        #{productionDuration,jdbcType=VARCHAR},
        #{cleanupDuration,jdbcType=VARCHAR},
        #{maxProductionSuspendDuration,jdbcType=INTEGER},
        #{maxSetupSuspendDuration,jdbcType=INTEGER},
        #{maxCleanupSuspendDuration,jdbcType=INTEGER},
        #{priority,jdbcType=INTEGER},
        #{altToolCode,jdbcType=VARCHAR},
        #{matchCode,jdbcType=VARCHAR},
        #{productionLine,jdbcType=VARCHAR},
        #{strictProductionLineConstraints,jdbcType=VARCHAR},
        #{splitAllowed,jdbcType=VARCHAR},
        #{countingUnitId,jdbcType=VARCHAR},
        #{resourceCategory,jdbcType=VARCHAR},
        #{effectiveTime,jdbcType=TIMESTAMP},
        #{effective,jdbcType=VARCHAR},
        #{expireReason,jdbcType=VARCHAR},
        #{expiryTime,jdbcType=TIMESTAMP},
        #{currencyUnitId,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mds.routing.infrastructure.po.NewRoutingStepResourcePO">
        insert into mds_rou_routing_step_resource(
        id,
        routing_id,
        routing_step_id,
        standard_resource_id,
        physical_resource_id,
        kid,
        resource_type,
        unit_production_time,
        unit_production_cost,
        fixed_work_hours,
        units_per_hour,
        input_frequency,
        lot_size,
        max_lot_size,
        min_lot_size,
        setup_unit_batch_size,
        production_unit_batch_size,
        cleanup_unit_batch_size,
        max_quantity,
        min_quantity,
        lead_time,
        lead_time_stdev,
        scheduling_space,
        setup_duration,
        production_duration,
        cleanup_duration,
        max_production_suspend_duration,
        max_setup_suspend_duration,
        max_cleanup_suspend_duration,
        priority,
        alt_tool_code,
        match_code,
        production_line,
        strict_production_line_constraints,
        split_allowed,
        counting_unit_id,
        resource_category,
        effective_time,
        effective,
        expire_reason,
        expiry_time,
        currency_unit_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{routingId,jdbcType=VARCHAR},
        #{routingStepId,jdbcType=VARCHAR},
        #{standardResourceId,jdbcType=VARCHAR},
        #{physicalResourceId,jdbcType=VARCHAR},
        #{kid,jdbcType=VARCHAR},
        #{resourceType,jdbcType=VARCHAR},
        #{unitProductionTime,jdbcType=VARCHAR},
        #{unitProductionCost,jdbcType=VARCHAR},
        #{fixedWorkHours,jdbcType=INTEGER},
        #{unitsPerHour,jdbcType=VARCHAR},
        #{inputFrequency,jdbcType=VARCHAR},
        #{lotSize,jdbcType=VARCHAR},
        #{maxLotSize,jdbcType=VARCHAR},
        #{minLotSize,jdbcType=VARCHAR},
        #{setupUnitBatchSize,jdbcType=VARCHAR},
        #{productionUnitBatchSize,jdbcType=VARCHAR},
        #{cleanupUnitBatchSize,jdbcType=VARCHAR},
        #{maxQuantity,jdbcType=VARCHAR},
        #{minQuantity,jdbcType=VARCHAR},
        #{leadTime,jdbcType=INTEGER},
        #{leadTimeStdev,jdbcType=INTEGER},
        #{schedulingSpace,jdbcType=INTEGER},
        #{setupDuration,jdbcType=VARCHAR},
        #{productionDuration,jdbcType=VARCHAR},
        #{cleanupDuration,jdbcType=VARCHAR},
        #{maxProductionSuspendDuration,jdbcType=INTEGER},
        #{maxSetupSuspendDuration,jdbcType=INTEGER},
        #{maxCleanupSuspendDuration,jdbcType=INTEGER},
        #{priority,jdbcType=INTEGER},
        #{altToolCode,jdbcType=VARCHAR},
        #{matchCode,jdbcType=VARCHAR},
        #{productionLine,jdbcType=VARCHAR},
        #{strictProductionLineConstraints,jdbcType=VARCHAR},
        #{splitAllowed,jdbcType=VARCHAR},
        #{countingUnitId,jdbcType=VARCHAR},
        #{resourceCategory,jdbcType=VARCHAR},
        #{effectiveTime,jdbcType=TIMESTAMP},
        #{effective,jdbcType=VARCHAR},
        #{expireReason,jdbcType=VARCHAR},
        #{expiryTime,jdbcType=TIMESTAMP},
        #{currencyUnitId,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mds_rou_routing_step_resource(
        id,
        routing_id,
        routing_step_id,
        standard_resource_id,
        physical_resource_id,
        kid,
        resource_type,
        unit_production_time,
        unit_production_cost,
        fixed_work_hours,
        units_per_hour,
        input_frequency,
        lot_size,
        max_lot_size,
        min_lot_size,
        setup_unit_batch_size,
        production_unit_batch_size,
        cleanup_unit_batch_size,
        max_quantity,
        min_quantity,
        lead_time,
        lead_time_stdev,
        scheduling_space,
        setup_duration,
        production_duration,
        cleanup_duration,
        max_production_suspend_duration,
        max_setup_suspend_duration,
        max_cleanup_suspend_duration,
        priority,
        alt_tool_code,
        match_code,
        production_line,
        strict_production_line_constraints,
        split_allowed,
        counting_unit_id,
        resource_category,
        effective_time,
        effective,
        expire_reason,
        expiry_time,
        currency_unit_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.routingId,jdbcType=VARCHAR},
        #{entity.routingStepId,jdbcType=VARCHAR},
        #{entity.standardResourceId,jdbcType=VARCHAR},
        #{entity.physicalResourceId,jdbcType=VARCHAR},
        #{entity.kid,jdbcType=VARCHAR},
        #{entity.resourceType,jdbcType=VARCHAR},
        #{entity.unitProductionTime,jdbcType=VARCHAR},
        #{entity.unitProductionCost,jdbcType=VARCHAR},
        #{entity.fixedWorkHours,jdbcType=INTEGER},
        #{entity.unitsPerHour,jdbcType=VARCHAR},
        #{entity.inputFrequency,jdbcType=VARCHAR},
        #{entity.lotSize,jdbcType=VARCHAR},
        #{entity.maxLotSize,jdbcType=VARCHAR},
        #{entity.minLotSize,jdbcType=VARCHAR},
        #{entity.setupUnitBatchSize,jdbcType=VARCHAR},
        #{entity.productionUnitBatchSize,jdbcType=VARCHAR},
        #{entity.cleanupUnitBatchSize,jdbcType=VARCHAR},
        #{entity.maxQuantity,jdbcType=VARCHAR},
        #{entity.minQuantity,jdbcType=VARCHAR},
        #{entity.leadTime,jdbcType=INTEGER},
        #{entity.leadTimeStdev,jdbcType=INTEGER},
        #{entity.schedulingSpace,jdbcType=INTEGER},
        #{entity.setupDuration,jdbcType=VARCHAR},
        #{entity.productionDuration,jdbcType=VARCHAR},
        #{entity.cleanupDuration,jdbcType=VARCHAR},
        #{entity.maxProductionSuspendDuration,jdbcType=INTEGER},
        #{entity.maxSetupSuspendDuration,jdbcType=INTEGER},
        #{entity.maxCleanupSuspendDuration,jdbcType=INTEGER},
        #{entity.priority,jdbcType=INTEGER},
        #{entity.altToolCode,jdbcType=VARCHAR},
        #{entity.matchCode,jdbcType=VARCHAR},
        #{entity.productionLine,jdbcType=VARCHAR},
        #{entity.strictProductionLineConstraints,jdbcType=VARCHAR},
        #{entity.splitAllowed,jdbcType=VARCHAR},
        #{entity.countingUnitId,jdbcType=VARCHAR},
        #{entity.resourceCategory,jdbcType=VARCHAR},
        #{entity.effectiveTime,jdbcType=TIMESTAMP},
        #{entity.effective,jdbcType=VARCHAR},
        #{entity.expireReason,jdbcType=VARCHAR},
        #{entity.expiryTime,jdbcType=TIMESTAMP},
        #{entity.currencyUnitId,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mds_rou_routing_step_resource(
        id,
        routing_id,
        routing_step_id,
        standard_resource_id,
        physical_resource_id,
        kid,
        resource_type,
        unit_production_time,
        unit_production_cost,
        fixed_work_hours,
        units_per_hour,
        input_frequency,
        lot_size,
        max_lot_size,
        min_lot_size,
        setup_unit_batch_size,
        production_unit_batch_size,
        cleanup_unit_batch_size,
        max_quantity,
        min_quantity,
        lead_time,
        lead_time_stdev,
        scheduling_space,
        setup_duration,
        production_duration,
        cleanup_duration,
        max_production_suspend_duration,
        max_setup_suspend_duration,
        max_cleanup_suspend_duration,
        priority,
        alt_tool_code,
        match_code,
        production_line,
        strict_production_line_constraints,
        split_allowed,
        counting_unit_id,
        resource_category,
        effective_time,
        effective,
        expire_reason,
        expiry_time,
        currency_unit_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.routingId,jdbcType=VARCHAR},
        #{entity.routingStepId,jdbcType=VARCHAR},
        #{entity.standardResourceId,jdbcType=VARCHAR},
        #{entity.physicalResourceId,jdbcType=VARCHAR},
        #{entity.kid,jdbcType=VARCHAR},
        #{entity.resourceType,jdbcType=VARCHAR},
        #{entity.unitProductionTime,jdbcType=VARCHAR},
        #{entity.unitProductionCost,jdbcType=VARCHAR},
        #{entity.fixedWorkHours,jdbcType=INTEGER},
        #{entity.unitsPerHour,jdbcType=VARCHAR},
        #{entity.inputFrequency,jdbcType=VARCHAR},
        #{entity.lotSize,jdbcType=VARCHAR},
        #{entity.maxLotSize,jdbcType=VARCHAR},
        #{entity.minLotSize,jdbcType=VARCHAR},
        #{entity.setupUnitBatchSize,jdbcType=VARCHAR},
        #{entity.productionUnitBatchSize,jdbcType=VARCHAR},
        #{entity.cleanupUnitBatchSize,jdbcType=VARCHAR},
        #{entity.maxQuantity,jdbcType=VARCHAR},
        #{entity.minQuantity,jdbcType=VARCHAR},
        #{entity.leadTime,jdbcType=INTEGER},
        #{entity.leadTimeStdev,jdbcType=INTEGER},
        #{entity.schedulingSpace,jdbcType=INTEGER},
        #{entity.setupDuration,jdbcType=VARCHAR},
        #{entity.productionDuration,jdbcType=VARCHAR},
        #{entity.cleanupDuration,jdbcType=VARCHAR},
        #{entity.maxProductionSuspendDuration,jdbcType=INTEGER},
        #{entity.maxSetupSuspendDuration,jdbcType=INTEGER},
        #{entity.maxCleanupSuspendDuration,jdbcType=INTEGER},
        #{entity.priority,jdbcType=INTEGER},
        #{entity.altToolCode,jdbcType=VARCHAR},
        #{entity.matchCode,jdbcType=VARCHAR},
        #{entity.productionLine,jdbcType=VARCHAR},
        #{entity.strictProductionLineConstraints,jdbcType=VARCHAR},
        #{entity.splitAllowed,jdbcType=VARCHAR},
        #{entity.countingUnitId,jdbcType=VARCHAR},
        #{entity.resourceCategory,jdbcType=VARCHAR},
        #{entity.effectiveTime,jdbcType=TIMESTAMP},
        #{entity.effective,jdbcType=VARCHAR},
        #{entity.expireReason,jdbcType=VARCHAR},
        #{entity.expiryTime,jdbcType=TIMESTAMP},
        #{entity.currencyUnitId,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mds.routing.infrastructure.po.NewRoutingStepResourcePO">
        update mds_rou_routing_step_resource set
        routing_id = #{routingId,jdbcType=VARCHAR},
        routing_step_id = #{routingStepId,jdbcType=VARCHAR},
        standard_resource_id = #{standardResourceId,jdbcType=VARCHAR},
        physical_resource_id = #{physicalResourceId,jdbcType=VARCHAR},
        kid = #{kid,jdbcType=VARCHAR},
        resource_type = #{resourceType,jdbcType=VARCHAR},
        unit_production_time = #{unitProductionTime,jdbcType=VARCHAR},
        unit_production_cost = #{unitProductionCost,jdbcType=VARCHAR},
        fixed_work_hours = #{fixedWorkHours,jdbcType=INTEGER},
        units_per_hour = #{unitsPerHour,jdbcType=VARCHAR},
        input_frequency = #{inputFrequency,jdbcType=VARCHAR},
        lot_size = #{lotSize,jdbcType=VARCHAR},
        max_lot_size = #{maxLotSize,jdbcType=VARCHAR},
        min_lot_size = #{minLotSize,jdbcType=VARCHAR},
        setup_unit_batch_size = #{setupUnitBatchSize,jdbcType=VARCHAR},
        production_unit_batch_size = #{productionUnitBatchSize,jdbcType=VARCHAR},
        cleanup_unit_batch_size = #{cleanupUnitBatchSize,jdbcType=VARCHAR},
        max_quantity = #{maxQuantity,jdbcType=VARCHAR},
        min_quantity = #{minQuantity,jdbcType=VARCHAR},
        lead_time = #{leadTime,jdbcType=INTEGER},
        lead_time_stdev = #{leadTimeStdev,jdbcType=INTEGER},
        scheduling_space = #{schedulingSpace,jdbcType=INTEGER},
        setup_duration = #{setupDuration,jdbcType=VARCHAR},
        production_duration = #{productionDuration,jdbcType=VARCHAR},
        cleanup_duration = #{cleanupDuration,jdbcType=VARCHAR},
        max_production_suspend_duration = #{maxProductionSuspendDuration,jdbcType=INTEGER},
        max_setup_suspend_duration = #{maxSetupSuspendDuration,jdbcType=INTEGER},
        max_cleanup_suspend_duration = #{maxCleanupSuspendDuration,jdbcType=INTEGER},
        priority = #{priority,jdbcType=INTEGER},
        alt_tool_code = #{altToolCode,jdbcType=VARCHAR},
        match_code = #{matchCode,jdbcType=VARCHAR},
        production_line = #{productionLine,jdbcType=VARCHAR},
        strict_production_line_constraints = #{strictProductionLineConstraints,jdbcType=VARCHAR},
        split_allowed = #{splitAllowed,jdbcType=VARCHAR},
        counting_unit_id = #{countingUnitId,jdbcType=VARCHAR},
        resource_category = #{resourceCategory,jdbcType=VARCHAR},
        effective_time = #{effectiveTime,jdbcType=TIMESTAMP},
        effective = #{effective,jdbcType=VARCHAR},
        expire_reason = #{expireReason,jdbcType=VARCHAR},
        expiry_time = #{expiryTime,jdbcType=TIMESTAMP},
        currency_unit_id = #{currencyUnitId,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mds.routing.infrastructure.po.NewRoutingStepResourcePO">
        update mds_rou_routing_step_resource
        <set>
            <if test="item.routingId != null and item.routingId != ''">
                routing_id = #{item.routingId,jdbcType=VARCHAR},
            </if>
            <if test="item.routingStepId != null and item.routingStepId != ''">
                routing_step_id = #{item.routingStepId,jdbcType=VARCHAR},
            </if>
            <if test="item.standardResourceId != null and item.standardResourceId != ''">
                standard_resource_id = #{item.standardResourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.physicalResourceId != null and item.physicalResourceId != ''">
                physical_resource_id = #{item.physicalResourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.kid != null and item.kid != ''">
                kid = #{item.kid,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceType != null and item.resourceType != ''">
                resource_type = #{item.resourceType,jdbcType=VARCHAR},
            </if>
            <if test="item.unitProductionTime != null">
                unit_production_time = #{item.unitProductionTime,jdbcType=VARCHAR},
            </if>
            <if test="item.unitProductionCost != null">
                unit_production_cost = #{item.unitProductionCost,jdbcType=VARCHAR},
            </if>
            <if test="item.fixedWorkHours != null">
                fixed_work_hours = #{item.fixedWorkHours,jdbcType=INTEGER},
            </if>
            <if test="item.unitsPerHour != null">
                units_per_hour = #{item.unitsPerHour,jdbcType=VARCHAR},
            </if>
            <if test="item.inputFrequency != null and item.inputFrequency != ''">
                input_frequency = #{item.inputFrequency,jdbcType=VARCHAR},
            </if>
            <if test="item.lotSize != null">
                lot_size = #{item.lotSize,jdbcType=VARCHAR},
            </if>
            <if test="item.maxLotSize != null">
                max_lot_size = #{item.maxLotSize,jdbcType=VARCHAR},
            </if>
            <if test="item.minLotSize != null">
                min_lot_size = #{item.minLotSize,jdbcType=VARCHAR},
            </if>
            <if test="item.setupUnitBatchSize != null">
                setup_unit_batch_size = #{item.setupUnitBatchSize,jdbcType=VARCHAR},
            </if>
            <if test="item.productionUnitBatchSize != null">
                production_unit_batch_size = #{item.productionUnitBatchSize,jdbcType=VARCHAR},
            </if>
            <if test="item.cleanupUnitBatchSize != null">
                cleanup_unit_batch_size = #{item.cleanupUnitBatchSize,jdbcType=VARCHAR},
            </if>
            <if test="item.maxQuantity != null">
                max_quantity = #{item.maxQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.minQuantity != null">
                min_quantity = #{item.minQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.leadTime != null">
                lead_time = #{item.leadTime,jdbcType=INTEGER},
            </if>
            <if test="item.leadTimeStdev != null">
                lead_time_stdev = #{item.leadTimeStdev,jdbcType=INTEGER},
            </if>
            <if test="item.schedulingSpace != null">
                scheduling_space = #{item.schedulingSpace,jdbcType=INTEGER},
            </if>
            <if test="item.setupDuration != null and item.setupDuration != ''">
                setup_duration = #{item.setupDuration,jdbcType=VARCHAR},
            </if>
            <if test="item.productionDuration != null and item.productionDuration != ''">
                production_duration = #{item.productionDuration,jdbcType=VARCHAR},
            </if>
            <if test="item.cleanupDuration != null and item.cleanupDuration != ''">
                cleanup_duration = #{item.cleanupDuration,jdbcType=VARCHAR},
            </if>
            <if test="item.maxProductionSuspendDuration != null">
                max_production_suspend_duration = #{item.maxProductionSuspendDuration,jdbcType=INTEGER},
            </if>
            <if test="item.maxSetupSuspendDuration != null">
                max_setup_suspend_duration = #{item.maxSetupSuspendDuration,jdbcType=INTEGER},
            </if>
            <if test="item.maxCleanupSuspendDuration != null">
                max_cleanup_suspend_duration = #{item.maxCleanupSuspendDuration,jdbcType=INTEGER},
            </if>
            <if test="item.priority != null">
                priority = #{item.priority,jdbcType=INTEGER},
            </if>
            <if test="item.altToolCode != null and item.altToolCode != ''">
                alt_tool_code = #{item.altToolCode,jdbcType=VARCHAR},
            </if>
            <if test="item.matchCode != null and item.matchCode != ''">
                match_code = #{item.matchCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productionLine != null and item.productionLine != ''">
                production_line = #{item.productionLine,jdbcType=VARCHAR},
            </if>
            <if test="item.strictProductionLineConstraints != null and item.strictProductionLineConstraints != ''">
                strict_production_line_constraints = #{item.strictProductionLineConstraints,jdbcType=VARCHAR},
            </if>
            <if test="item.splitAllowed != null and item.splitAllowed != ''">
                split_allowed = #{item.splitAllowed,jdbcType=VARCHAR},
            </if>
            <if test="item.countingUnitId != null and item.countingUnitId != ''">
                counting_unit_id = #{item.countingUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceCategory != null and item.resourceCategory != ''">
                resource_category = #{item.resourceCategory,jdbcType=VARCHAR},
            </if>
            <if test="item.effectiveTime != null">
                effective_time = #{item.effectiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.effective != null and item.effective != ''">
                effective = #{item.effective,jdbcType=VARCHAR},
            </if>
            <if test="item.expireReason != null and item.expireReason != ''">
                expire_reason = #{item.expireReason,jdbcType=VARCHAR},
            </if>
            <if test="item.expiryTime != null">
                expiry_time = #{item.expiryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.currencyUnitId != null and item.currencyUnitId != ''">
                currency_unit_id = #{item.currencyUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mds_rou_routing_step_resource
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="routing_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.routingId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="routing_step_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.routingStepId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="standard_resource_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.standardResourceId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="physical_resource_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.physicalResourceId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="kid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.kid,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="resource_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.resourceType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="unit_production_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.unitProductionTime,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="unit_production_cost = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.unitProductionCost,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="fixed_work_hours = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.fixedWorkHours,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="units_per_hour = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.unitsPerHour,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="input_frequency = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inputFrequency,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="lot_size = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lotSize,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="max_lot_size = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.maxLotSize,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="min_lot_size = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.minLotSize,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="setup_unit_batch_size = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.setupUnitBatchSize,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="production_unit_batch_size = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productionUnitBatchSize,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cleanup_unit_batch_size = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.cleanupUnitBatchSize,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="max_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.maxQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="min_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.minQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="lead_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.leadTime,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="lead_time_stdev = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.leadTimeStdev,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="scheduling_space = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.schedulingSpace,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="setup_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.setupDuration,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="production_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productionDuration,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cleanup_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.cleanupDuration,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="max_production_suspend_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.maxProductionSuspendDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="max_setup_suspend_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.maxSetupSuspendDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="max_cleanup_suspend_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.maxCleanupSuspendDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="priority = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.priority,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="alt_tool_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.altToolCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="match_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.matchCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="production_line = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productionLine,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="strict_production_line_constraints = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.strictProductionLineConstraints,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="split_allowed = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.splitAllowed,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="counting_unit_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.countingUnitId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="resource_category = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.resourceCategory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="effective_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.effectiveTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="effective = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.effective,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="expire_reason = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.expireReason,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="expiry_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.expiryTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="currency_unit_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.currencyUnitId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update mds_rou_routing_step_resource 
        <set>
            <if test="item.routingId != null and item.routingId != ''">
                routing_id = #{item.routingId,jdbcType=VARCHAR},
            </if>
            <if test="item.routingStepId != null and item.routingStepId != ''">
                routing_step_id = #{item.routingStepId,jdbcType=VARCHAR},
            </if>
            <if test="item.standardResourceId != null and item.standardResourceId != ''">
                standard_resource_id = #{item.standardResourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.physicalResourceId != null and item.physicalResourceId != ''">
                physical_resource_id = #{item.physicalResourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.kid != null and item.kid != ''">
                kid = #{item.kid,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceType != null and item.resourceType != ''">
                resource_type = #{item.resourceType,jdbcType=VARCHAR},
            </if>
            <if test="item.unitProductionTime != null">
                unit_production_time = #{item.unitProductionTime,jdbcType=VARCHAR},
            </if>
            <if test="item.unitProductionCost != null">
                unit_production_cost = #{item.unitProductionCost,jdbcType=VARCHAR},
            </if>
            <if test="item.fixedWorkHours != null">
                fixed_work_hours = #{item.fixedWorkHours,jdbcType=INTEGER},
            </if>
            <if test="item.unitsPerHour != null">
                units_per_hour = #{item.unitsPerHour,jdbcType=VARCHAR},
            </if>
            <if test="item.inputFrequency != null and item.inputFrequency != ''">
                input_frequency = #{item.inputFrequency,jdbcType=VARCHAR},
            </if>
            <if test="item.lotSize != null">
                lot_size = #{item.lotSize,jdbcType=VARCHAR},
            </if>
            <if test="item.maxLotSize != null">
                max_lot_size = #{item.maxLotSize,jdbcType=VARCHAR},
            </if>
            <if test="item.minLotSize != null">
                min_lot_size = #{item.minLotSize,jdbcType=VARCHAR},
            </if>
            <if test="item.setupUnitBatchSize != null">
                setup_unit_batch_size = #{item.setupUnitBatchSize,jdbcType=VARCHAR},
            </if>
            <if test="item.productionUnitBatchSize != null">
                production_unit_batch_size = #{item.productionUnitBatchSize,jdbcType=VARCHAR},
            </if>
            <if test="item.cleanupUnitBatchSize != null">
                cleanup_unit_batch_size = #{item.cleanupUnitBatchSize,jdbcType=VARCHAR},
            </if>
            <if test="item.maxQuantity != null">
                max_quantity = #{item.maxQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.minQuantity != null">
                min_quantity = #{item.minQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.leadTime != null">
                lead_time = #{item.leadTime,jdbcType=INTEGER},
            </if>
            <if test="item.leadTimeStdev != null">
                lead_time_stdev = #{item.leadTimeStdev,jdbcType=INTEGER},
            </if>
            <if test="item.schedulingSpace != null">
                scheduling_space = #{item.schedulingSpace,jdbcType=INTEGER},
            </if>
            <if test="item.setupDuration != null and item.setupDuration != ''">
                setup_duration = #{item.setupDuration,jdbcType=VARCHAR},
            </if>
            <if test="item.productionDuration != null and item.productionDuration != ''">
                production_duration = #{item.productionDuration,jdbcType=VARCHAR},
            </if>
            <if test="item.cleanupDuration != null and item.cleanupDuration != ''">
                cleanup_duration = #{item.cleanupDuration,jdbcType=VARCHAR},
            </if>
            <if test="item.maxProductionSuspendDuration != null">
                max_production_suspend_duration = #{item.maxProductionSuspendDuration,jdbcType=INTEGER},
            </if>
            <if test="item.maxSetupSuspendDuration != null">
                max_setup_suspend_duration = #{item.maxSetupSuspendDuration,jdbcType=INTEGER},
            </if>
            <if test="item.maxCleanupSuspendDuration != null">
                max_cleanup_suspend_duration = #{item.maxCleanupSuspendDuration,jdbcType=INTEGER},
            </if>
            <if test="item.priority != null">
                priority = #{item.priority,jdbcType=INTEGER},
            </if>
            <if test="item.altToolCode != null and item.altToolCode != ''">
                alt_tool_code = #{item.altToolCode,jdbcType=VARCHAR},
            </if>
            <if test="item.matchCode != null and item.matchCode != ''">
                match_code = #{item.matchCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productionLine != null and item.productionLine != ''">
                production_line = #{item.productionLine,jdbcType=VARCHAR},
            </if>
            <if test="item.strictProductionLineConstraints != null and item.strictProductionLineConstraints != ''">
                strict_production_line_constraints = #{item.strictProductionLineConstraints,jdbcType=VARCHAR},
            </if>
            <if test="item.splitAllowed != null and item.splitAllowed != ''">
                split_allowed = #{item.splitAllowed,jdbcType=VARCHAR},
            </if>
            <if test="item.countingUnitId != null and item.countingUnitId != ''">
                counting_unit_id = #{item.countingUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceCategory != null and item.resourceCategory != ''">
                resource_category = #{item.resourceCategory,jdbcType=VARCHAR},
            </if>
            <if test="item.effectiveTime != null">
                effective_time = #{item.effectiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.effective != null and item.effective != ''">
                effective = #{item.effective,jdbcType=VARCHAR},
            </if>
            <if test="item.expireReason != null and item.expireReason != ''">
                expire_reason = #{item.expireReason,jdbcType=VARCHAR},
            </if>
            <if test="item.expiryTime != null">
                expiry_time = #{item.expiryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.currencyUnitId != null and item.currencyUnitId != ''">
                currency_unit_id = #{item.currencyUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from mds_rou_routing_step_resource where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mds_rou_routing_step_resource where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    
     <update id="doLogicDeleteBatchByRoutingIds">
        update 
        	mds_rou_routing_step_resource
        set
        	enabled = 'NO',
        	effective = 'NO'
        where 
        	routing_id in
        	<foreach collection="routingIds" item="item" open="(" separator="," close=")">
            	#{item,jdbcType=VARCHAR}
        	</foreach>
    </update>
    
    <update id="doLogicDeleteBatchByRoutingStepIds">
        update 
        	mds_rou_routing_step_resource
        set
        	enabled = 'NO',
        	effective = 'NO'
        where 
        	routing_step_id in
        	<foreach collection="routingStepIds" item="item" open="(" separator="," close=")">
            	#{item,jdbcType=VARCHAR}
        	</foreach>
    </update>
    
    <select id="selectAllRoutingStepIds" resultType="java.lang.String">
        SELECT DISTINCT
			routing_step_id 
		FROM
			mds_rou_routing_step_resource 
		WHERE
			enabled = 'YES'
    </select>
    
     <delete id="deleteByCreator">
        delete from mds_rou_routing_step_resource where creator = #{creator,jdbcType=VARCHAR}
    </delete>
    
    <update id="doLogicDelete" parameterType="java.util.List">
        update mds_rou_routing_step_resource 
        set enabled = 'NO', effective = 'NO'
        where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="selectRoutingStepResoueceBase" resultType="com.yhl.scp.mds.routing.vo.NewRoutingStepResourceVO">
        SELECT
			t1.id routingId,
			t1.stock_point_id stockPointCode,
			t2.sequence_no sequenceNo,
			t3.physical_resource_id physicalResourceId
		FROM
			mds_rou_routing t1,
			mds_rou_routing_step t2,
			mds_rou_routing_step_resource t3
		WHERE
			t1.id = t2.routing_id
			AND t2.id = t3.routing_step_id
			AND t1.enabled = 'YES'
			AND t2.enabled = 'YES'
			AND t3.enabled = 'YES'
			AND t1.id in
        <foreach item="code" collection="routingIds" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>

    <select id="selectMainProcessByProductCodes" resultType="map">
        SELECT
        t.inner_product_code AS product_code,
        t.inner_physical_resource_code AS physical_resource_code
        FROM (
        <trim>
            SELECT
            rt.`product_code` AS inner_product_code,
            pr.`physical_resource_code` AS inner_physical_resource_code,
            ROW_NUMBER() OVER (PARTITION BY rt.`product_code` ORDER BY rs.priority ASC) AS rn
            FROM mds_rou_routing_step_resource rs
            LEFT JOIN mds_res_physical_resource pr ON rs.physical_resource_id = pr.id
            LEFT JOIN mds_rou_routing_step ms ON rs.routing_step_id = ms.id
            LEFT JOIN mds_rou_standard_step mr ON ms.standard_step_id = mr.id
            LEFT JOIN mds_rou_routing rt ON ms.routing_id = rt.id
            WHERE rs.enabled = 'YES'
            AND pr.enabled = 'YES'
            AND mr.standard_step_type = 'FORMING_PROCESS'
            AND rt.`product_code` IN
            <foreach collection="productCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </trim>
        ) t
        WHERE t.rn = 1
    </select>
</mapper>
