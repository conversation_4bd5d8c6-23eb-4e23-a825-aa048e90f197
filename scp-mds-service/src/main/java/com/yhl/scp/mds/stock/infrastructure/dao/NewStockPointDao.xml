<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mds.stock.infrastructure.dao.NewStockPointDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mds.stock.infrastructure.po.NewStockPointPO">
        <!--@Table mds_stock_point-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="stock_point_name" jdbcType="VARCHAR" property="stockPointName"/>
        <result column="stock_point_type" jdbcType="VARCHAR" property="stockPointType"/>
        <result column="organize_id" jdbcType="VARCHAR" property="organizeId"/>
        <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="plan_area" jdbcType="VARCHAR" property="planArea"/>
        <result column="organize_type" jdbcType="VARCHAR" property="organizeType"/>
        <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
        <result column="edi_mode" jdbcType="VARCHAR" property="ediMode"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="interface_flag" jdbcType="VARCHAR" property="interfaceFlag"/>
        <result column="customer_code" jdbcType="VARCHAR" property="customerCode"/>
        <result column="supplier_id" jdbcType="VARCHAR" property="supplierId"/>
        <result column="interface_source" jdbcType="VARCHAR" property="interfaceSource"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mds.stock.vo.NewStockPointVO">
    	<result column="supplier_code" jdbcType="VARCHAR" property="supplierCode"/>
    </resultMap>
    <sql id="Base_Column_List">
		id,
		stock_point_code,
		stock_point_name,
		stock_point_type,
		organize_id,
		last_update_time,
		plan_area,
		organize_type,
		company_name,
		remark,
		enabled,
		creator,
		create_time,
		modifier,
		modify_time,
		version_value,
		edi_mode,
		interface_flag,
		customer_code,
		supplier_id,
		interface_source
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />,
        supplier_code
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCodes != null and params.stockPointCodes.size > 0">
                <choose>
                    <when test="params.stockPointCodes.size == 1">
                        and stock_point_code = #{params.stockPointCodes[0],jdbcType=VARCHAR}
                    </when>
                    <otherwise>
                        and stock_point_code in
                        <foreach collection="params.stockPointCodes" item="item" index="index" open="(" separator=","
                                 close=")">
                            #{item,jdbcType=VARCHAR}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="params.stockPointName != null and params.stockPointName != ''">
                and stock_point_name = #{params.stockPointName,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointType != null and params.stockPointType != ''">
                and stock_point_type = #{params.stockPointType,jdbcType=VARCHAR}
            </if>
            <if test="params.organizeId != null and params.organizeId != ''">
                and organize_id = #{params.organizeId,jdbcType=VARCHAR}
            </if>
            <if test="params.lastUpdateTime != null">
                and last_update_time = #{params.lastUpdateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.planArea != null and params.planArea != ''">
                and plan_area = #{params.planArea,jdbcType=VARCHAR}
            </if>
            <if test="params.organizeType != null and params.organizeType != ''">
                and organize_type = #{params.organizeType,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.ediMode != null and params.ediMode != ''">
                and edi_mode = #{params.ediMode,jdbcType=VARCHAR}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.interfaceFlag != null and params.interfaceFlag != ''">
                and interface_flag = #{params.interfaceFlag,jdbcType=VARCHAR}
            </if>
            <if test="params.customerCode != null and params.customerCode != ''">
                and customer_code = #{params.customerCode,jdbcType=VARCHAR}
            </if>
            <if test="params.interfaceSource != null and params.interfaceSource != ''">
                and interface_source = #{params.interfaceSource,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointTypeList != null and params.stockPointTypeList.size()>0">
                and stock_point_type in
                <foreach collection="params.stockPointTypeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.notEqualsStockPointTypeList != null and params.notEqualsStockPointTypeList.size()>0">
                and stock_point_type not in
                <foreach collection="params.notEqualsStockPointTypeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.organizeTypeList != null and params.organizeTypeList.size()>0">
                and organize_type in
                <foreach collection="params.organizeTypeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.supplierId != null and params.supplierId != ''">
                and supplier_id = #{params.supplierId,jdbcType=VARCHAR}
            </if>
            <if test="params.organizeIds != null and params.organizeIds.size > 0">
                <choose>
                    <when test="params.organizeIds.size == 1">
                        and organize_id = #{params.organizeIds[0],jdbcType=VARCHAR}
                    </when>
                    <otherwise>
                        and organize_id in
                        <foreach collection="params.organizeIds" item="item" index="index" open="(" separator=","
                                 close=")">
                            #{item,jdbcType=VARCHAR}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_stock_point
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_stock_point
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_mds_stock_point
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_stock_point
        <include refid="Base_Where_Condition" />
    </select>

    <select id="selectSaleOrgaByOrganizeType" resultMap="VOResultMap">
            select
            <include refid="Base_Column_List" />
            from mds_stock_point
            where organize_type = 'SALE_ORGANIZATION'
    </select>

    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mds.stock.infrastructure.po.NewStockPointPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mds_stock_point(
        id,
        customer_code,
        stock_point_code,
        stock_point_name,
        stock_point_type,
        organize_id,
        last_update_time,
        plan_area,
        organize_type,
        company_name,
        edi_mode,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        interface_flag,
        supplier_id,
        interface_source)
        values (
        #{id,jdbcType=VARCHAR},
        #{customerCode,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{stockPointName,jdbcType=VARCHAR},
        #{stockPointType,jdbcType=VARCHAR},
        #{organizeId,jdbcType=VARCHAR},
        #{lastUpdateTime,jdbcType=TIMESTAMP},
        #{planArea,jdbcType=VARCHAR},
        #{organizeType,jdbcType=VARCHAR},
        #{companyName,jdbcType=VARCHAR},
        #{ediMode,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{interfaceFlag,jdbcType=VARCHAR},
        #{supplierId,jdbcType=VARCHAR},
        #{interfaceSource,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mds.stock.infrastructure.po.NewStockPointPO">
        insert into mds_stock_point(
        id,
        customer_code,
        stock_point_code,
        stock_point_name,
        stock_point_type,
        organize_id,
        last_update_time,
        plan_area,
        organize_type,
        company_name,
        edi_mode,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        interface_flag,
        supplier_id,
        interface_source)
        values (
        #{id,jdbcType=VARCHAR},
        #{customerCode,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{stockPointName,jdbcType=VARCHAR},
        #{stockPointType,jdbcType=VARCHAR},
        #{organizeId,jdbcType=VARCHAR},
        #{lastUpdateTime,jdbcType=TIMESTAMP},
        #{planArea,jdbcType=VARCHAR},
        #{organizeType,jdbcType=VARCHAR},
        #{companyName,jdbcType=VARCHAR},
        #{ediMode,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{interfaceFlag,jdbcType=VARCHAR},
        #{supplierId,jdbcType=VARCHAR},
        #{interfaceSource,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mds_stock_point(
        id,
        customer_code,
        stock_point_code,
        stock_point_name,
        stock_point_type,
        organize_id,
        last_update_time,
        plan_area,
        organize_type,
        company_name,
        edi_mode,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        interface_flag,
        supplier_id,
        interface_source)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.customerCode,jdbcType=VARCHAR},
        #{entity.stockPointCode,jdbcType=VARCHAR},
        #{entity.stockPointName,jdbcType=VARCHAR},
        #{entity.stockPointType,jdbcType=VARCHAR},
        #{entity.organizeId,jdbcType=VARCHAR},
        #{entity.lastUpdateTime,jdbcType=TIMESTAMP},
        #{entity.planArea,jdbcType=VARCHAR},
        #{entity.organizeType,jdbcType=VARCHAR},
        #{entity.companyName,jdbcType=VARCHAR},
        #{entity.ediMode,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.interfaceFlag,jdbcType=VARCHAR},
        #{entity.supplierId,jdbcType=VARCHAR},
        #{entity.interfaceSource,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mds_stock_point(
        id,
        customer_code,
        stock_point_code,
        stock_point_name,
        stock_point_type,
        organize_id,
        last_update_time,
        plan_area,
        organize_type,
        company_name,
        edi_mode,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        interface_flag,
        supplier_id,
        interface_source)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.customerCode,jdbcType=VARCHAR},
        #{entity.stockPointCode,jdbcType=VARCHAR},
        #{entity.stockPointName,jdbcType=VARCHAR},
        #{entity.stockPointType,jdbcType=VARCHAR},
        #{entity.organizeId,jdbcType=VARCHAR},
        #{entity.lastUpdateTime,jdbcType=TIMESTAMP},
        #{entity.planArea,jdbcType=VARCHAR},
        #{entity.organizeType,jdbcType=VARCHAR},
        #{entity.companyName,jdbcType=VARCHAR},
        #{entity.ediMode,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.interfaceFlag,jdbcType=VARCHAR},
        #{entity.supplierId,jdbcType=VARCHAR},
        #{entity.interfaceSource,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mds.stock.infrastructure.po.NewStockPointPO">
        update mds_stock_point set
        customer_code = #{customerCode,jdbcType=VARCHAR},
        stock_point_code = #{stockPointCode,jdbcType=VARCHAR},
        stock_point_name = #{stockPointName,jdbcType=VARCHAR},
        stock_point_type = #{stockPointType,jdbcType=VARCHAR},
        organize_id = #{organizeId,jdbcType=VARCHAR},
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
        plan_area = #{planArea,jdbcType=VARCHAR},
        organize_type = #{organizeType,jdbcType=VARCHAR},
        company_name = #{companyName,jdbcType=VARCHAR},
        edi_mode = #{ediMode,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        version_value = #{versionValue,jdbcType=INTEGER},
        interface_flag = #{interfaceFlag,jdbcType=VARCHAR},
        supplier_id = #{supplierId,jdbcType=VARCHAR},
        interface_source = #{interfaceSource,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mds.stock.infrastructure.po.NewStockPointPO">
        update mds_stock_point
        <set>
            <if test="item.customerCode != null and item.customerCode != ''">
                customer_code = #{item.customerCode,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointName != null and item.stockPointName != ''">
                stock_point_name = #{item.stockPointName,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointType != null and item.stockPointType != ''">
                stock_point_type = #{item.stockPointType,jdbcType=VARCHAR},
            </if>
            <if test="item.organizeId != null and item.organizeId != ''">
                organize_id = #{item.organizeId,jdbcType=VARCHAR},
            </if>
            <if test="item.lastUpdateTime != null">
                last_update_time = #{item.lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.planArea != null and item.planArea != ''">
                plan_area = #{item.planArea,jdbcType=VARCHAR},
            </if>
            <if test="item.organizeType != null and item.organizeType != ''">
                organize_type = #{item.organizeType,jdbcType=VARCHAR},
            </if>
            <if test="item.companyName != null and item.companyName != ''">
                company_name = #{item.companyName,jdbcType=VARCHAR},
            </if>
            <if test="item.ediMode != null and item.ediMode != ''">
                edi_mode = #{item.ediMode,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.interfaceFlag != null and item.interfaceFlag != ''">
                interface_flag = #{item.interfaceFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierId != null and item.supplierId != ''">
                supplier_id = #{item.supplierId,jdbcType=VARCHAR},
            </if>
            <if test="item.interfaceSource != null and item.interfaceSource != ''">
                interface_source = #{item.interfaceSource,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mds_stock_point
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="customer_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.customerCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="organize_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.organizeId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="last_update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lastUpdateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="plan_area = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.planArea,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="organize_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.organizeType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="company_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.companyName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="edi_mode = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when id = #{item.id,jdbcType=VARCHAR} then #{item.ediMode,jdbcType=VARCHAR}
            </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    version_value + 1
                </foreach>
            </trim>
            <trim prefix="interface_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.interfaceFlag,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supplier_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="interface_source = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.interfaceSource,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update mds_stock_point 
        <set>
            <if test="item.customerCode != null and item.customerCode != ''">
                customer_code = #{item.customerCode,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointName != null and item.stockPointName != ''">
                stock_point_name = #{item.stockPointName,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointType != null and item.stockPointType != ''">
                stock_point_type = #{item.stockPointType,jdbcType=VARCHAR},
            </if>
            <if test="item.organizeId != null and item.organizeId != ''">
                organize_id = #{item.organizeId,jdbcType=VARCHAR},
            </if>
            <if test="item.lastUpdateTime != null">
                last_update_time = #{item.lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.planArea != null and item.planArea != ''">
                plan_area = #{item.planArea,jdbcType=VARCHAR},
            </if>
            <if test="item.organizeType != null and item.organizeType != ''">
                organize_type = #{item.organizeType,jdbcType=VARCHAR},
            </if>
            <if test="item.companyName != null and item.companyName != ''">
                company_name = #{item.companyName,jdbcType=VARCHAR},
            </if>
            <if test="item.ediMode != null and item.ediMode != ''">
                edi_mode = #{item.ediMode,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.interfaceFlag != null and item.interfaceFlag != ''">
                interface_flag = #{item.interfaceFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierId != null and item.supplierId != ''">
                supplier_id = #{item.supplierId,jdbcType=VARCHAR},
            </if>
            <if test="item.interfaceSource != null and item.interfaceSource != ''">
                interface_source = #{item.interfaceSource,jdbcType=VARCHAR},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from mds_stock_point where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mds_stock_point where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <!-- 根据版本删除 -->
    <delete id="deleteBatchVersion" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            delete from mds_stock_point where
            id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </delete>
    
    <select id="selectAllVehicleModel" resultType="java.lang.String">
        SELECT 
        	DISTINCT vehicle_model_code 
		FROM
			mds_product_stock_point 
		WHERE
			enabled = 'YES' 
			AND vehicle_model_code IS NOT NULL
    </select>
</mapper>
