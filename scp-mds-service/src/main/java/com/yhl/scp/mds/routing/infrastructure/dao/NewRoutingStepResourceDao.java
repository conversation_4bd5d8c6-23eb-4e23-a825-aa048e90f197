package com.yhl.scp.mds.routing.infrastructure.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.mds.routing.infrastructure.po.NewRoutingStepResourcePO;
import com.yhl.scp.mds.routing.vo.NewRoutingStepResourceVO;

/**
 * <code>NewRoutingStepResourceDao</code>
 * <p>
 * 新-生产路径步骤资源DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-20 14:58:54
 */
public interface NewRoutingStepResourceDao extends BaseDao<NewRoutingStepResourcePO, NewRoutingStepResourceVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link NewRoutingStepResourceVO}
     */
    List<NewRoutingStepResourceVO> selectVOByParams(@Param("params") Map<String, Object> params);

	void doLogicDeleteBatchByRoutingIds(@Param("routingIds") List<String> routingIds);

	void doLogicDeleteBatchByRoutingStepIds(@Param("routingStepIds") List<String> routingStepIds);

	List<String> selectAllRoutingStepIds();

	void deleteByCreator(@Param("creator") String creator);

	void doLogicDelete(@Param("ids") List<String> ids);

	List<NewRoutingStepResourceVO> selectRoutingStepResoueceBase(@Param("routingIds") List<String> routingIds);

	@MapKey("product_code")
	List<Map<String, Object>> selectMainProcessByProductCodes(@Param("productCodes") List<String> productCodes);
}
