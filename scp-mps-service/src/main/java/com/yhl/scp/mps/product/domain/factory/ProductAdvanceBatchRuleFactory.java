package com.yhl.scp.mps.product.domain.factory;

import com.yhl.scp.mps.product.domain.entity.ProductAdvanceBatchRuleDO;
import com.yhl.scp.mps.product.dto.ProductAdvanceBatchRuleDTO;
import com.yhl.scp.mps.product.infrastructure.dao.ProductAdvanceBatchRuleDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>ProductAdvanceBatchRuleFactory</code>
 * <p>
 * 提前生产批次规则领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-22 18:59:27
 */
@Component
public class ProductAdvanceBatchRuleFactory {

    @Resource
    private ProductAdvanceBatchRuleDao productAdvanceBatchRuleDao;

    ProductAdvanceBatchRuleDO create(ProductAdvanceBatchRuleDTO dto) {
        // TODO
        return null;
    }

}
