package com.yhl.scp.mps.operationPublished.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mps.operationPublished.dto.OperationResourcePublishedDTO;
import com.yhl.scp.mps.operationPublished.service.OperationResourcePublishedService;
import com.yhl.scp.mps.operationPublished.vo.OperationResourcePublishedVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>OperationResourcePublishedController</code>
 * <p>
 * 工序资源发布信息表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-20 09:38:45
 */
@Slf4j
@Api(tags = "工序资源发布信息表控制器")
@RestController
@RequestMapping("operationResourcePublished")
public class OperationResourcePublishedController extends BaseController {

    @Resource
    private OperationResourcePublishedService operationResourcePublishedService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<OperationResourcePublishedVO>> page() {
        List<OperationResourcePublishedVO> operationResourcePublishedList = operationResourcePublishedService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<OperationResourcePublishedVO> pageInfo = new PageInfo<>(operationResourcePublishedList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody OperationResourcePublishedDTO operationResourcePublishedDTO) {
        return operationResourcePublishedService.doCreate(operationResourcePublishedDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody OperationResourcePublishedDTO operationResourcePublishedDTO) {
        return operationResourcePublishedService.doUpdate(operationResourcePublishedDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        operationResourcePublishedService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<OperationResourcePublishedVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, operationResourcePublishedService.selectByPrimaryKey(id));
    }

}
