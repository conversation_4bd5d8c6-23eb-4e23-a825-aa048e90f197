package com.yhl.scp.mps.coating.domain.service;

import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.mps.coating.constants.AbnormalConstants;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.mps.coating.domain.entity.MpsCoatingChangeTimeDO;
import com.yhl.scp.mps.coating.infrastructure.dao.MpsCoatingChangeTimeDao;
import com.yhl.scp.mps.coating.infrastructure.po.MpsCoatingChangeTimePO;
import com.yhl.scp.mps.productionLimit.infrastructure.po.ProductionLimitPO;import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <code>MpsCoatingChangeTimeDomainService</code>
 * <p>
 * 镀膜切换时间领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-28 20:38:08
 */
@Service
public class MpsCoatingChangeTimeDomainService {

    @Resource
    private MpsCoatingChangeTimeDao mpsCoatingChangeTimeDao;

    /**
     * 数据校验
     *
     * @param mpsCoatingChangeTimeDO 领域对象
     */
    public void validation(MpsCoatingChangeTimeDO mpsCoatingChangeTimeDO) {
        checkNotNull(mpsCoatingChangeTimeDO);
        checkUniqueCode(mpsCoatingChangeTimeDO);

        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param mpsCoatingChangeTimeDO 领域对象
     */
    private void checkNotNull(MpsCoatingChangeTimeDO mpsCoatingChangeTimeDO) {
        if (StringUtils.isBlank(mpsCoatingChangeTimeDO.getCompanyCode())) {
            throw new BusinessException(AbnormalConstants.COMPANY_CODE_NOT_ALLOW_NULL);
        }
        if (StringUtils.isBlank(mpsCoatingChangeTimeDO.getOrgCode())) {
            throw new BusinessException(AbnormalConstants.ORG_CODE_NOT_ALLOW_NULL);
        }
        if (StringUtils.isBlank(mpsCoatingChangeTimeDO.getOrgName())) {
            throw new BusinessException(AbnormalConstants.ORG_NAME_NOT_ALLOW_NULL);
        }
        if (StringUtils.isBlank(mpsCoatingChangeTimeDO.getChangeFilmBefore())) {
            throw new BusinessException(AbnormalConstants.CHANGE_FILM_BEFORE_NOT_ALLOW_NULL);
        }
        if (StringUtils.isBlank(mpsCoatingChangeTimeDO.getChangeFilmNext())) {
            throw new BusinessException(AbnormalConstants.CHANGE_FILM_NEXT_NOT_ALLOW_NULL);
        }
        if (StringUtils.isBlank(mpsCoatingChangeTimeDO.getEnabled())) {
            throw new BusinessException(AbnormalConstants.ENABLED_NOT_ALLOW_NULL);
        }
    }

    /**
     * 唯一性校验
     *
     * @param mpsCoatingChangeTimeDO 领域对象
     */
    private void checkUniqueCode(MpsCoatingChangeTimeDO mpsCoatingChangeTimeDO) {
        Map<String, Object> params = new HashMap<>(4);
        params.put("companyCode", mpsCoatingChangeTimeDO.getCompanyCode());
        params.put("orgCode", mpsCoatingChangeTimeDO.getOrgCode());
        params.put("changeFilmBefore", mpsCoatingChangeTimeDO.getChangeFilmBefore());
        params.put("changeFilmNext", mpsCoatingChangeTimeDO.getChangeFilmNext());
        if (StringUtils.isBlank(mpsCoatingChangeTimeDO.getId())) {
            List<MpsCoatingChangeTimePO> list = mpsCoatingChangeTimeDao.selectByParams(params);
            if (CollectionUtils.isNotEmpty(list)) {
                throw new BusinessException(AbnormalConstants.ADDFAILED_COMPANYCODE_ORGCODE_CHANGE_FILM_BEFORE_NEXT_EXISTS);
            }
        } else {
            MpsCoatingChangeTimePO old = mpsCoatingChangeTimeDao.selectByPrimaryKey(mpsCoatingChangeTimeDO.getId());
            if (!(mpsCoatingChangeTimeDO.getCompanyCode().equals(old.getCompanyCode()) &&
                    mpsCoatingChangeTimeDO.getOrgCode().equals(old.getOrgCode())  &&
                    mpsCoatingChangeTimeDO.getChangeFilmBefore().equals(old.getChangeFilmBefore()) &&
                    mpsCoatingChangeTimeDO.getChangeFilmNext().equals(old.getChangeFilmNext()))) {
                List<MpsCoatingChangeTimePO> list = mpsCoatingChangeTimeDao.selectByParams(params);
                if (CollectionUtils.isNotEmpty(list)) {
                    throw new BusinessException(AbnormalConstants.EDITFAILED_COMPANYCODE_ORGCODE_CHANGE_FILM_BEFORE_NEXT_EXISTS);
                }
            }
        }
    }

    /**
     * 删除校验
     *
     * @param versionDTOList 领域对象
     */
    public void checkDelete(List<RemoveVersionDTO> versionDTOList) {
        for (RemoveVersionDTO removeVersionDTO : versionDTOList) {
            MpsCoatingChangeTimePO old = mpsCoatingChangeTimeDao.selectByPrimaryKey(removeVersionDTO.getId());
            if (Objects.isNull(old)) {
                throw new BusinessException(AbnormalConstants.DELETE_EXCEPTION_MSG + removeVersionDTO.getId());
            }
            if (!removeVersionDTO.getVersionValue().equals(old.getVersionValue())) {
                throw new BusinessException(AbnormalConstants.DELETE_VERSION_EXCEPTION_MSG);
            }
        }
    }

    /**
     * 更新校验
     *
     * @param mpsCoatingChangeTimeDO 领域对象
     */
    public void checkUpdate(MpsCoatingChangeTimeDO mpsCoatingChangeTimeDO) {
        MpsCoatingChangeTimePO old = mpsCoatingChangeTimeDao.selectByPrimaryKey(mpsCoatingChangeTimeDO.getId());
        if (Objects.isNull(old)) {
            throw new BusinessException(AbnormalConstants.UPDATE_VERSION_EXCEPTION_DELETED_MSG);
        }
        if (!mpsCoatingChangeTimeDO.getVersionValue().equals(old.getVersionValue())) {
            throw new BusinessException(AbnormalConstants.UPDATE_VERSION_EXCEPTION_MSG);
        }
    }


}
