package com.yhl.scp.mps.operationPublished.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mps.operationPublished.dto.OperationTaskPublishedDTO;
import com.yhl.scp.mps.operationPublished.service.OperationTaskPublishedService;
import com.yhl.scp.mps.operationPublished.vo.OperationTaskPublishedVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>OperationTaskPublishedController</code>
 * <p>
 * 工序任务发布信息表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-20 09:35:51
 */
@Slf4j
@Api(tags = "工序任务发布信息表控制器")
@RestController
@RequestMapping("operationTaskPublished")
public class OperationTaskPublishedController extends BaseController {

    @Resource
    private OperationTaskPublishedService operationTaskPublishedService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<OperationTaskPublishedVO>> page() {
        List<OperationTaskPublishedVO> operationTaskPublishedList = operationTaskPublishedService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<OperationTaskPublishedVO> pageInfo = new PageInfo<>(operationTaskPublishedList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody OperationTaskPublishedDTO operationTaskPublishedDTO) {
        return operationTaskPublishedService.doCreate(operationTaskPublishedDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody OperationTaskPublishedDTO operationTaskPublishedDTO) {
        return operationTaskPublishedService.doUpdate(operationTaskPublishedDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        operationTaskPublishedService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<OperationTaskPublishedVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, operationTaskPublishedService.selectByPrimaryKey(id));
    }

}
