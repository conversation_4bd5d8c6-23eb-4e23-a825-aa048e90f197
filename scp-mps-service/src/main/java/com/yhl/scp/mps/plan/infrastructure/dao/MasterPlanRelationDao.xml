<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mps.plan.infrastructure.dao.MasterPlanRelationDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mps.plan.infrastructure.po.MasterPlanRelationPO">
        <!--@Table mps_master_plan_relation-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="org_id" jdbcType="VARCHAR" property="orgId"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="plan_no" jdbcType="VARCHAR" property="planNo"/>
        <result column="plan_status" jdbcType="VARCHAR" property="planStatus"/>
        <result column="erp_order_no" jdbcType="VARCHAR" property="erpOrderNo"/>
        <result column="operation_step_code" jdbcType="VARCHAR" property="operationStepCode"/>
        <result column="line_no" jdbcType="VARCHAR" property="lineNo"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="log_is_sync" jdbcType="VARCHAR" property="logIsSync"/>
        <result column="order_quantity" jdbcType="DECIMAL" property="orderQuantity"/>
        <result column="delivery_quantity" jdbcType="DECIMAL" property="deliveryQuantity"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mps.plan.vo.MasterPlanRelationVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
id,org_id,order_no,plan_no,plan_status,erp_order_no,operation_step_code,line_no,remark,enabled,creator,create_time,
  modifier,modify_time,version_value,log_is_sync,order_quantity,delivery_quantity,stock_point_code,product_code
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.idList != null and params.idList.size() > 0">
                and id in
                <foreach collection="params.idList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.orgId != null and params.orgId != ''">
                and org_id = #{params.orgId,jdbcType=VARCHAR}
            </if>
            <if test="params.orgIdIsNotNull != null and params.orgIdIsNotNull != ''">
                and org_id is not null
            </if>
            <if test="params.orderNo != null and params.orderNo != ''">
                and order_no = #{params.orderNo,jdbcType=VARCHAR}
            </if>
            <if test="params.orderNos != null and params.orderNos.size() > 0">
                and order_no in
                <foreach collection="params.orderNos" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.planNo != null and params.planNo != ''">
                and plan_no = #{params.planNo,jdbcType=VARCHAR}
            </if>
            <if test="params.planNos != null and params.planNos.size() > 0">
                and plan_no in
                <foreach collection="params.planNos" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.planStatus != null and params.planStatus != ''">
                and plan_status = #{params.planStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.planStatusNotEqual != null and params.planStatusNotEqual != ''">
                and plan_status != #{params.planStatusNotEqual,jdbcType=VARCHAR}
            </if>
            <if test="params.planStatusNotIn != null and params.planStatusNotIn.size() > 0">
                and plan_status not in
                <foreach collection="params.planStatusNotIn" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.erpOrderNo != null and params.erpOrderNo != ''">
                and erp_order_no = #{params.erpOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="params.erpOrderNoList != null and params.erpOrderNoList.size() > 0">
                and erp_order_no in
                <foreach collection="params.erpOrderNoList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.operation_step_code != null and params.operation_step_code != ''">
                and operation_step_code = #{params.operationStepCode,jdbcType=VARCHAR}
            </if>
            <if test="params.operationStepCodes != null and params.operationStepCodes.size() > 0">
                and operation_step_code in
                <foreach collection="params.operationStepCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.lineNo != null and params.lineNo != ''">
                and line_no = #{params.lineNo,jdbcType=VARCHAR}
            </if>
            <if test="params.erpOrderNOIsNull != null and params.erpOrderNOIsNull != ''">
                and erp_order_no is null
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.createTimeIsNotNull != null">
                and create_time is not null
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.logIsSync != null and params.logIsSync != ''">
                and log_is_sync = #{params.logIsSync,jdbcType=VARCHAR}
            </if>
            <if test="params.logIsSyncIsNull != null and params.logIsSyncIsNull != ''">
                and log_is_sync is null or log_is_sync != 'YES'
            </if>
            <if test="params.orderQuantity != null">
                and order_quantity = #{params.orderQuantity,jdbcType=DECIMAL}
            </if>
            <if test="params.deliveryQuantity != null">
                and delivery_quantity = #{params.deliveryQuantity,jdbcType=DECIMAL}
            </if>
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCodes != null and params.productCodes.size() > 0">
                and product_code in
                <foreach collection="params.productCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mps_master_plan_relation
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mps_master_plan_relation
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from mps_master_plan_relation
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mps_master_plan_relation
        <include refid="Base_Where_Condition" />
    </select>
    <select id="selectByTwoParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mps_master_plan_relation
        <where>
            (plan_no,line_no) in
            <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
                (#{item.reqNumber},#{item.lineNum})
            </foreach>
        </where>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mps.plan.infrastructure.po.MasterPlanRelationPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mps_master_plan_relation(
        id,
        org_id,
        order_no,
        plan_no,
        plan_status,
        erp_order_no,
        operation_step_code,
        line_no,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        log_is_sync,
        order_quantity,
        delivery_quantity,
        stock_point_code,
        product_code)
        values (
        #{id,jdbcType=VARCHAR},
        #{orgId,jdbcType=VARCHAR},
        #{orderNo,jdbcType=VARCHAR},
        #{planNo,jdbcType=VARCHAR},
        #{planStatus,jdbcType=VARCHAR},
        #{erpOrderNo,jdbcType=VARCHAR},
        #{operationStepCode,jdbcType=VARCHAR},
        #{lineNo,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{logIsSync,jdbcType=VARCHAR},
        #{orderQuantity,jdbcType=DECIMAL},
        #{deliveryQuantity,jdbcType=DECIMAL},
        #{stockPointCode,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mps.plan.infrastructure.po.MasterPlanRelationPO">
        insert into mps_master_plan_relation(
        id,
        org_id,
        order_no,
        plan_no,
        plan_status,
        erp_order_no,
        operation_step_code,
        line_no,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        log_is_sync,
        order_quantity,
        delivery_quantity,
        stock_point_code,
        product_code)
        values (
        #{id,jdbcType=VARCHAR},
        #{orgId,jdbcType=VARCHAR},
        #{orderNo,jdbcType=VARCHAR},
        #{planNo,jdbcType=VARCHAR},
        #{planStatus,jdbcType=VARCHAR},
        #{erpOrderNo,jdbcType=VARCHAR},
        #{operationStepCode,jdbcType=VARCHAR},
        #{lineNo,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{logIsSync,jdbcType=VARCHAR},
        #{orderQuantity,jdbcType=DECIMAL},
        #{deliveryQuantity,jdbcType=DECIMAL},
        #{stockPointCode,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mps_master_plan_relation(
        id,
        org_id,
        order_no,
        plan_no,
        plan_status,
        erp_order_no,
        operation_step_code,
        line_no,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        log_is_sync,
        order_quantity,
        delivery_quantity,
        stock_point_code,
        product_code)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.orgId,jdbcType=VARCHAR},
        #{entity.orderNo,jdbcType=VARCHAR},
        #{entity.planNo,jdbcType=VARCHAR},
        #{entity.planStatus,jdbcType=VARCHAR},
        #{entity.erpOrderNo,jdbcType=VARCHAR},
        #{entity.operationStepCode,jdbcType=VARCHAR},
        #{entity.lineNo,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.logIsSync,jdbcType=VARCHAR},
        #{entity.orderQuantity,jdbcType=DECIMAL},
        #{entity.deliveryQuantity,jdbcType=DECIMAL},
        #{entity.stockPointCode,jdbcType=VARCHAR},
        #{entity.productCode,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mps_master_plan_relation(
        id,
        org_id,
        order_no,
        plan_no,
        plan_status,
        erp_order_no,
        operation_step_code,
        line_no,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        log_is_sync,
        order_quantity,
        delivery_quantity,
        stock_point_code,
        product_code)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.orgId,jdbcType=VARCHAR},
        #{entity.orderNo,jdbcType=VARCHAR},
        #{entity.planNo,jdbcType=VARCHAR},
        #{entity.planStatus,jdbcType=VARCHAR},
        #{entity.erpOrderNo,jdbcType=VARCHAR},
        #{entity.operationStepCode,jdbcType=VARCHAR},
        #{entity.lineNo,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.logIsSync,jdbcType=VARCHAR},
        #{entity.orderQuantity,jdbcType=DECIMAL},
        #{entity.deliveryQuantity,jdbcType=DECIMAL},
        #{entity.stockPointCode,jdbcType=VARCHAR},
        #{entity.productCode,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mps.plan.infrastructure.po.MasterPlanRelationPO">
        update mps_master_plan_relation set
        org_id = #{orgId,jdbcType=VARCHAR},
        order_no = #{orderNo,jdbcType=VARCHAR},
        plan_no = #{planNo,jdbcType=VARCHAR},
        plan_status = #{planStatus,jdbcType=VARCHAR},
        erp_order_no = #{erpOrderNo,jdbcType=VARCHAR},
        operation_step_code = #{operationStepCode,jdbcType=VARCHAR},
        line_no = #{lineNo,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        version_value = #{versionValue,jdbcType=INTEGER},
        log_is_sync = #{logIsSync,jdbcType=VARCHAR},
        order_quantity = #{orderQuantity,jdbcType=DECIMAL},
        delivery_quantity = #{deliveryQuantity,jdbcType=DECIMAL},
        stock_point_code = #{stockPointCode,jdbcType=VARCHAR},
        product_code = #{productCode,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mps.plan.infrastructure.po.MasterPlanRelationPO">
        update mps_master_plan_relation
        <set>
            <if test="item.orgId != null and item.orgId != ''">
                org_id = #{item.orgId,jdbcType=VARCHAR},
            </if>
            <if test="item.orderNo != null and item.orderNo != ''">
                order_no = #{item.orderNo,jdbcType=VARCHAR},
            </if>
            <if test="item.planNo != null and item.planNo != ''">
                plan_no = #{item.planNo,jdbcType=VARCHAR},
            </if>
            <if test="item.planStatus != null and item.planStatus != ''">
                plan_status = #{item.planStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.erpOrderNo != null and item.erpOrderNo != ''">
                erp_order_no = #{item.erpOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="item.operationStepCode != null and item.operationStepCode != ''">
                operation_step_code = #{item.operationStepCode,jdbcType=VARCHAR},
            </if>
            <if test="item.lineNo != null and item.lineNo != ''">
                line_no = #{item.lineNo,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.logIsSync != null and item.logIsSync != ''">
                log_is_sync = #{item.logIsSync,jdbcType=VARCHAR},
            </if>
            <if test="item.orderQuantity != null">
                order_quantity = #{item.orderQuantity,jdbcType=DECIMAL},
            </if>
            <if test="item.deliveryQuantity != null">
                delivery_quantity = #{item.deliveryQuantity,jdbcType=DECIMAL},
            </if>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mps_master_plan_relation
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="org_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.orgId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="order_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.orderNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="plan_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.planNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="plan_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.planStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="erp_order_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.erpOrderNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="operation_step_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.operationStepCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="line_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lineNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="log_is_sync = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.logIsSync,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="order_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.orderQuantity,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="delivery_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.deliveryQuantity,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="stock_point_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update mps_master_plan_relation
        <set>
            <if test="item.orgId != null and item.orgId != ''">
                org_id = #{item.orgId,jdbcType=VARCHAR},
            </if>
            <if test="item.orderNo != null and item.orderNo != ''">
                order_no = #{item.orderNo,jdbcType=VARCHAR},
            </if>
            <if test="item.planNo != null and item.planNo != ''">
                plan_no = #{item.planNo,jdbcType=VARCHAR},
            </if>
            <if test="item.planStatus != null and item.planStatus != ''">
                plan_status = #{item.planStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.erpOrderNo != null and item.erpOrderNo != ''">
                erp_order_no = #{item.erpOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="item.operationStepCode != null and item.operationStepCode != ''">
                operation_step_code = #{item.operationStepCode,jdbcType=VARCHAR},
            </if>
            <if test="item.lineNo != null and item.lineNo != ''">
                line_no = #{item.lineNo,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.logIsSync != null and item.logIsSync != ''">
                log_is_sync = #{item.logIsSync,jdbcType=VARCHAR},
            </if>
            <if test="item.orderQuantity != null">
                order_quantity = #{item.orderQuantity,jdbcType=DECIMAL},
            </if>
            <if test="item.deliveryQuantity != null">
                delivery_quantity = #{item.deliveryQuantity,jdbcType=DECIMAL},
            </if>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from mps_master_plan_relation where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mps_master_plan_relation where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
