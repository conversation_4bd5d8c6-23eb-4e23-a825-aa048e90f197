<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mps.fixtureRelation.infrastructure.dao.ProductFixtureRelationDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mps.fixtureRelation.infrastructure.po.ProductFixtureRelationPO">
        <!--@Table mps_product_fixture_relation-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
        <result column="standard_step_code" jdbcType="VARCHAR" property="standardStepCode"/>
        <result column="tool_standard_resource" jdbcType="VARCHAR" property="toolStandardResource"/>
        <result column="tool_physical_resource" jdbcType="VARCHAR" property="toolPhysicalResource"/>
        <result column="standard_resource_id" jdbcType="VARCHAR" property="standardResourceId"/>
        <result column="physical_resource_id" jdbcType="VARCHAR" property="physicalResourceId"/>
        <result column="alt_tool_code" jdbcType="VARCHAR" property="altToolCode"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.mps.fixtureRelation.vo.ProductFixtureRelationVO">
        <!-- TODO -->
        <result column="stock_point_name" jdbcType="VARCHAR" property="stockPointName"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="standard_step_name" jdbcType="VARCHAR" property="standardStepName"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,stock_point_code,product_code,vehicle_model_code,standard_step_code,tool_standard_resource,tool_physical_resource,
         standard_resource_id,physical_resource_id,alt_tool_code,remark,enabled,creator,create_time,modifier,modify_time
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>,stock_point_name,product_name,standard_step_name
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCodes != null and params.productCodes.size() > 0">
                and product_code in
                <foreach item="item" index="index" collection="params.productCodes"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.vehicleModelCode != null and params.vehicleModelCode != ''">
                and vehicle_model_code = #{params.vehicleModelCode,jdbcType=VARCHAR}
            </if>
            <if test="params.standardStepCode != null and params.standardStepCode != ''">
                and standard_step_code = #{params.standardStepCode,jdbcType=VARCHAR}
            </if>
            <if test="params.toolStandardResource != null and params.toolStandardResource != ''">
                and tool_standard_resource = #{params.toolStandardResource,jdbcType=VARCHAR}
            </if>
            <if test="params.toolPhysicalResource != null and params.toolPhysicalResource != ''">
                and tool_physical_resource = #{params.toolPhysicalResource,jdbcType=VARCHAR}
            </if>
            <if test="params.standardResourceId != null and params.standardResourceId != ''">
                and standard_resource_id = #{params.standardResourceId,jdbcType=VARCHAR}
            </if>
            <if test="params.physicalResourceId != null and params.physicalResourceId != ''">
                and physical_resource_id = #{params.physicalResourceId,jdbcType=VARCHAR}
            </if>
            <if test="params.altToolCode != null and params.altToolCode != ''">
                and alt_tool_code = #{params.altToolCode,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mps_product_fixture_relation
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mps_product_fixture_relation
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mps_product_fixture_relation
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mps_product_fixture_relation
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mps.fixtureRelation.infrastructure.po.ProductFixtureRelationPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mps_product_fixture_relation(
        id,
        stock_point_code,
        product_code,
        vehicle_model_code,
        standard_step_code,
        tool_standard_resource,
        tool_physical_resource,
        standard_resource_id,
        physical_resource_id,
        alt_tool_code,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{vehicleModelCode,jdbcType=VARCHAR},
        #{standardStepCode,jdbcType=VARCHAR},
        #{toolStandardResource,jdbcType=VARCHAR},
        #{toolPhysicalResource,jdbcType=VARCHAR},
        #{standardResourceId,jdbcType=VARCHAR},
        #{physicalResourceId,jdbcType=VARCHAR},
        #{altToolCode,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mps.fixtureRelation.infrastructure.po.ProductFixtureRelationPO">
        insert into mps_product_fixture_relation(id,
                                                 stock_point_code,
                                                 product_code,
                                                 vehicle_model_code,
                                                 standard_step_code,
                                                 tool_standard_resource,
                                                 tool_physical_resource,
                                                 standard_resource_id,
                                                 physical_resource_id,
                                                 alt_tool_code,
                                                 remark,
                                                 enabled,
                                                 creator,
                                                 create_time,
                                                 modifier,
                                                 modify_time)
        values (#{id,jdbcType=VARCHAR},
                #{stockPointCode,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{vehicleModelCode,jdbcType=VARCHAR},
                #{standardStepCode,jdbcType=VARCHAR},
                #{toolStandardResource,jdbcType=VARCHAR},
                #{toolPhysicalResource,jdbcType=VARCHAR},
                #{standardResourceId,jdbcType=VARCHAR},
                #{physicalResourceId,jdbcType=VARCHAR},
                #{altToolCode,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mps_product_fixture_relation(
        id,
        stock_point_code,
        product_code,
        vehicle_model_code,
        standard_step_code,
        tool_standard_resource,
        tool_physical_resource,
        standard_resource_id,
        physical_resource_id,
        alt_tool_code,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.vehicleModelCode,jdbcType=VARCHAR},
            #{entity.standardStepCode,jdbcType=VARCHAR},
            #{entity.toolStandardResource,jdbcType=VARCHAR},
            #{entity.toolPhysicalResource,jdbcType=VARCHAR},
            #{entity.standardResourceId,jdbcType=VARCHAR},
            #{entity.physicalResourceId,jdbcType=VARCHAR},
            #{entity.altToolCode,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mps_product_fixture_relation(
        id,
        stock_point_code,
        product_code,
        vehicle_model_code,
        standard_step_code,
        tool_standard_resource,
        tool_physical_resource,
        standard_resource_id,
        physical_resource_id,
        alt_tool_code,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.vehicleModelCode,jdbcType=VARCHAR},
            #{entity.standardStepCode,jdbcType=VARCHAR},
            #{entity.toolStandardResource,jdbcType=VARCHAR},
            #{entity.toolPhysicalResource,jdbcType=VARCHAR},
            #{entity.standardResourceId,jdbcType=VARCHAR},
            #{entity.physicalResourceId,jdbcType=VARCHAR},
            #{entity.altToolCode,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mps.fixtureRelation.infrastructure.po.ProductFixtureRelationPO">
        update mps_product_fixture_relation
        set stock_point_code       = #{stockPointCode,jdbcType=VARCHAR},
            product_code           = #{productCode,jdbcType=VARCHAR},
            vehicle_model_code     = #{vehicleModelCode,jdbcType=VARCHAR},
            standard_step_code     = #{standardStepCode,jdbcType=VARCHAR},
            tool_standard_resource = #{toolStandardResource,jdbcType=VARCHAR},
            tool_physical_resource = #{toolPhysicalResource,jdbcType=VARCHAR},
            standard_resource_id   = #{standardResourceId,jdbcType=VARCHAR},
            physical_resource_id   = #{physicalResourceId,jdbcType=VARCHAR},
            alt_tool_code          = #{altToolCode,jdbcType=VARCHAR},
            remark                 = #{remark,jdbcType=VARCHAR},
            enabled                = #{enabled,jdbcType=VARCHAR},
            modifier               = #{modifier,jdbcType=VARCHAR},
            modify_time            = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mps.fixtureRelation.infrastructure.po.ProductFixtureRelationPO">
        update mps_product_fixture_relation
        <set>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
            </if>
            <if test="item.standardStepCode != null and item.standardStepCode != ''">
                standard_step_code = #{item.standardStepCode,jdbcType=VARCHAR},
            </if>
            <if test="item.toolStandardResource != null and item.toolStandardResource != ''">
                tool_standard_resource = #{item.toolStandardResource,jdbcType=VARCHAR},
            </if>
            <if test="item.toolPhysicalResource != null and item.toolPhysicalResource != ''">
                tool_physical_resource = #{item.toolPhysicalResource,jdbcType=VARCHAR},
            </if>
            <if test="item.standardResourceId != null and item.standardResourceId != ''">
                standard_resource_id = #{item.standardResourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.physicalResourceId != null and item.physicalResourceId != ''">
                physical_resource_id = #{item.physicalResourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.altToolCode != null and item.altToolCode != ''">
                alt_tool_code = #{item.altToolCode,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mps_product_fixture_relation
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="stock_point_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="vehicle_model_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.vehicleModelCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="standard_step_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.standardStepCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="tool_standard_resource = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.toolStandardResource,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="tool_physical_resource = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.toolPhysicalResource,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="standard_resource_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.standardResourceId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="physical_resource_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.physicalResourceId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="alt_tool_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.altToolCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mps_product_fixture_relation
            <set>
                <if test="item.stockPointCode != null and item.stockPointCode != ''">
                    stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                    vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
                </if>
                <if test="item.standardStepCode != null and item.standardStepCode != ''">
                    standard_step_code = #{item.standardStepCode,jdbcType=VARCHAR},
                </if>
                <if test="item.toolStandardResource != null and item.toolStandardResource != ''">
                    tool_standard_resource = #{item.toolStandardResource,jdbcType=VARCHAR},
                </if>
                <if test="item.toolPhysicalResource != null and item.toolPhysicalResource != ''">
                    tool_physical_resource = #{item.toolPhysicalResource,jdbcType=VARCHAR},
                </if>
                <if test="item.standardResourceId != null and item.standardResourceId != ''">
                    standard_resource_id = #{item.standardResourceId,jdbcType=VARCHAR},
                </if>
                <if test="item.physicalResourceId != null and item.physicalResourceId != ''">
                    physical_resource_id = #{item.physicalResourceId,jdbcType=VARCHAR},
                </if>
                <if test="item.altToolCode != null and item.altToolCode != ''">
                    alt_tool_code = #{item.altToolCode,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mps_product_fixture_relation
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mps_product_fixture_relation where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
