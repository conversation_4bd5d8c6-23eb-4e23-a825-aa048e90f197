package com.yhl.scp.mps.capacityBalance.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.mps.capacityBalance.dto.CapacitySupplyRelationshipDTO;
import com.yhl.scp.mps.capacityBalance.dto.CapacitySupplyRelationshipEditorDTO;
import com.yhl.scp.mps.capacityBalance.dto.CapacitySupplyRelationshipLockDTO;
import com.yhl.scp.mps.capacityBalance.dto.OutsourcingAdjustmentDTO;
import com.yhl.scp.mps.capacityBalance.service.CapacitySupplyRelationshipService;
import com.yhl.scp.mps.capacityBalance.vo.CapacitySupplyRelationshipVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>CapacitySupplyRelationshipController</code>
 * <p>
 * 产能供应关系控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-16 15:41:29
 */
@Slf4j
@Api(tags = "产能供应关系控制器")
@RestController
@RequestMapping("capacitySupplyRelationship")
public class CapacitySupplyRelationshipController extends BaseController {

    @Resource
    private CapacitySupplyRelationshipService capacitySupplyRelationshipService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<CapacitySupplyRelationshipVO>> page() {
        List<CapacitySupplyRelationshipVO> capacitySupplyRelationshipList = capacitySupplyRelationshipService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<CapacitySupplyRelationshipVO> pageInfo = new PageInfo<>(capacitySupplyRelationshipList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody CapacitySupplyRelationshipDTO capacitySupplyRelationshipDTO) {
        return capacitySupplyRelationshipService.doCreate(capacitySupplyRelationshipDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody CapacitySupplyRelationshipDTO capacitySupplyRelationshipDTO) {
        return capacitySupplyRelationshipService.doUpdate(capacitySupplyRelationshipDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        capacitySupplyRelationshipService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<CapacitySupplyRelationshipVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, capacitySupplyRelationshipService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "委外调整")
    @PostMapping(value = "outsourcingAdjustment")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> outsourcingAdjustment(@RequestBody OutsourcingAdjustmentDTO outsourceTransferSummaryDTO) {
        capacitySupplyRelationshipService.doOutsourcingAdjustment(outsourceTransferSummaryDTO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "批量锁定/解锁")
    @PostMapping(value = "lockStatusBatch")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> lockStatusBatch(@RequestBody CapacitySupplyRelationshipLockDTO dto) {
        capacitySupplyRelationshipService.lockStatusBatchNew(dto.getVoList(), dto.getLockStatus());
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "编辑")
    @PostMapping(value = "lineEdit")
    @BusinessMonitorLog(businessCode = "月度产能均衡", moduleCode = "MPS", businessFrequency = "MONTH")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> lockStatusBatch(@RequestBody CapacitySupplyRelationshipEditorDTO dto) {
        capacitySupplyRelationshipService.doLineEditNew(dto);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "批量编辑")
    @PostMapping(value = "batchEdit")
    @BusinessMonitorLog(businessCode = "月度产能均衡", moduleCode = "MPS", businessFrequency = "MONTH")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> batchEdit(@RequestBody List<CapacitySupplyRelationshipEditorDTO> dto) {
        capacitySupplyRelationshipService.doBatchEdit(dto);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "产能平衡明细-编辑-资源下拉-新")
    @PostMapping(value = "resourceDropDown")
    public BaseResponse<List<LabelValue<String>>> resourceDropDown(@RequestBody List<String> routingStepIds) {
        return BaseResponse.success(capacitySupplyRelationshipService.resourceDropDown(routingStepIds));
    }

    @ApiOperation(value = "按月汇总查询")
    @PostMapping(value = "selectCollect")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<CapacitySupplyRelationshipVO>> selectCollect(@RequestBody CapacitySupplyRelationshipEditorDTO dto) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, capacitySupplyRelationshipService.selectCollect(dto));
    }

    @ApiOperation(value = "按版本号-月汇总查询")
    @PostMapping(value = "selectCollectByVersion")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<CapacitySupplyRelationshipVO>> selectCollectByVersion(@RequestBody CapacitySupplyRelationshipEditorDTO dto) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, capacitySupplyRelationshipService.selectCollectByVersion(dto));
    }

}
