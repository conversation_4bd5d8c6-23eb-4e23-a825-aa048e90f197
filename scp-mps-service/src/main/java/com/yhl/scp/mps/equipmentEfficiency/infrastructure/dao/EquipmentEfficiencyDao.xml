<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mps.equipmentEfficiency.infrastructure.dao.EquipmentEfficiencyDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mps.equipmentEfficiency.infrastructure.po.EquipmentEfficiencyPO">
        <!--@Table mps_special_technology_equipment_efficiency-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="company_code" jdbcType="VARCHAR" property="companyCode"/>
        <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
        <result column="special_technology" jdbcType="VARCHAR" property="specialTechnology"/>
        <result column="technology_type" jdbcType="VARCHAR" property="technologyType"/>
        <result column="equipment_number" jdbcType="INTEGER" property="equipmentNumber"/>
        <result column="equipment_oee" jdbcType="VARCHAR" property="equipmentOee"/>
        <result column="equipment_not_use_day" jdbcType="VARCHAR" property="equipmentNotUseDay"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mps.equipmentEfficiency.vo.EquipmentEfficiencyVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id,company_code,company_name,special_technology,technology_type,equipment_number,equipment_oee,equipment_not_use_day,remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.companyCode != null and params.companyCode != ''">
                and company_code = #{params.companyCode,jdbcType=VARCHAR}
            </if>
            <if test="params.companyName != null and params.companyName != ''">
                and company_name = #{params.companyName,jdbcType=VARCHAR}
            </if>
            <if test="params.specialTechnology != null and params.specialTechnology != ''">
                and special_technology = #{params.specialTechnology,jdbcType=VARCHAR}
            </if>
            <if test="params.technologyType != null and params.technologyType != ''">
                and technology_type = #{params.technologyType,jdbcType=VARCHAR}
            </if>
            <if test="params.equipmentNumber != null">
                and equipment_number = #{params.equipmentNumber,jdbcType=INTEGER}
            </if>
            <if test="params.equipmentOee != null">
                and equipment_oee = #{params.equipmentOee,jdbcType=VARCHAR}
            </if>
            <if test="params.equipmentNotUseDay != null">
                and equipment_not_use_day = #{params.equipmentNotUseDay,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mps_special_technology_equipment_efficiency
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mps_special_technology_equipment_efficiency
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from mps_special_technology_equipment_efficiency
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mps_special_technology_equipment_efficiency
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mps.equipmentEfficiency.infrastructure.po.EquipmentEfficiencyPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mps_special_technology_equipment_efficiency(
        id,
        company_code,
        company_name,
        special_technology,
        technology_type,
        equipment_number,
        equipment_oee,
        equipment_not_use_day,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{companyCode,jdbcType=VARCHAR},
        #{companyName,jdbcType=VARCHAR},
        #{specialTechnology,jdbcType=VARCHAR},
        #{technologyType,jdbcType=VARCHAR},
        #{equipmentNumber,jdbcType=INTEGER},
        #{equipmentOee,jdbcType=VARCHAR},
        #{equipmentNotUseDay,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mps.equipmentEfficiency.infrastructure.po.EquipmentEfficiencyPO">
        insert into mps_special_technology_equipment_efficiency(
        id,
        company_code,
        company_name,
        special_technology,
        technology_type,
        equipment_number,
        equipment_oee,
        equipment_not_use_day,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{companyCode,jdbcType=VARCHAR},
        #{companyName,jdbcType=VARCHAR},
        #{specialTechnology,jdbcType=VARCHAR},
        #{technologyType,jdbcType=VARCHAR},
        #{equipmentNumber,jdbcType=INTEGER},
        #{equipmentOee,jdbcType=VARCHAR},
        #{equipmentNotUseDay,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mps_special_technology_equipment_efficiency(
        id,
        company_code,
        company_name,
        special_technology,
        technology_type,
        equipment_number,
        equipment_oee,
        equipment_not_use_day,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.companyCode,jdbcType=VARCHAR},
        #{entity.companyName,jdbcType=VARCHAR},
        #{entity.specialTechnology,jdbcType=VARCHAR},
        #{entity.technologyType,jdbcType=VARCHAR},
        #{entity.equipmentNumber,jdbcType=INTEGER},
        #{entity.equipmentOee,jdbcType=VARCHAR},
        #{entity.equipmentNotUseDay,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mps_special_technology_equipment_efficiency(
        id,
        company_code,
        company_name,
        special_technology,
        technology_type,
        equipment_number,
        equipment_oee,
        equipment_not_use_day,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.companyCode,jdbcType=VARCHAR},
        #{entity.companyName,jdbcType=VARCHAR},
        #{entity.specialTechnology,jdbcType=VARCHAR},
        #{entity.technologyType,jdbcType=VARCHAR},
        #{entity.equipmentNumber,jdbcType=INTEGER},
        #{entity.equipmentOee,jdbcType=VARCHAR},
        #{entity.equipmentNotUseDay,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mps.equipmentEfficiency.infrastructure.po.EquipmentEfficiencyPO">
        update mps_special_technology_equipment_efficiency
        set company_code          = #{companyCode,jdbcType=VARCHAR},
            company_name          = #{companyName,jdbcType=VARCHAR},
            special_technology    = #{specialTechnology,jdbcType=VARCHAR},
            technology_type       = #{technologyType,jdbcType=VARCHAR},
            equipment_number      = #{equipmentNumber,jdbcType=INTEGER},
            equipment_oee         = #{equipmentOee,jdbcType=VARCHAR},
            equipment_not_use_day = #{equipmentNotUseDay,jdbcType=VARCHAR},
            remark                = #{remark,jdbcType=VARCHAR},
            enabled               = #{enabled,jdbcType=VARCHAR},
            modifier              = #{modifier,jdbcType=VARCHAR},
            modify_time           = #{modifyTime,jdbcType=TIMESTAMP},
            version_value         = version_value + 1
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mps.equipmentEfficiency.infrastructure.po.EquipmentEfficiencyPO">
        update mps_special_technology_equipment_efficiency
        <set>
            <if test="item.companyCode != null and item.companyCode != ''">
                company_code = #{item.companyCode,jdbcType=VARCHAR},
            </if>
            <if test="item.companyName != null and item.companyName != ''">
                company_name = #{item.companyName,jdbcType=VARCHAR},
            </if>
            <if test="item.specialTechnology != null and item.specialTechnology != ''">
                special_technology = #{item.specialTechnology,jdbcType=VARCHAR},
            </if>
            <if test="item.technologyType != null and item.technologyType != ''">
                technology_type = #{item.technologyType,jdbcType=VARCHAR},
            </if>
            <if test="item.equipmentNumber != null">
                equipment_number = #{item.equipmentNumber,jdbcType=INTEGER},
            </if>
            <if test="item.equipmentOee != null">
                equipment_oee = #{item.equipmentOee,jdbcType=VARCHAR},
            </if>
            <if test="item.equipmentNotUseDay != null">
                equipment_not_use_day = #{item.equipmentNotUseDay,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>

            version_value = version_value + 1,
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
        and version_value = #{item.versionValue,jdbcType=INTEGER}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mps_special_technology_equipment_efficiency
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="company_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.companyCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="company_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.companyName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="special_technology = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.specialTechnology,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="technology_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.technologyType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="equipment_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.equipmentNumber,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="equipment_oee = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.equipmentOee,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="equipment_not_use_day = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.equipmentNotUseDay,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    version_value + 1
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update mps_special_technology_equipment_efficiency 
        <set>
            <if test="item.companyCode != null and item.companyCode != ''">
                company_code = #{item.companyCode,jdbcType=VARCHAR},
            </if>
            <if test="item.companyName != null and item.companyName != ''">
                company_name = #{item.companyName,jdbcType=VARCHAR},
            </if>
            <if test="item.specialTechnology != null and item.specialTechnology != ''">
                special_technology = #{item.specialTechnology,jdbcType=VARCHAR},
            </if>
            <if test="item.technologyType != null and item.technologyType != ''">
                technology_type = #{item.technologyType,jdbcType=VARCHAR},
            </if>
            <if test="item.equipmentNumber != null">
                equipment_number = #{item.equipmentNumber,jdbcType=INTEGER},
            </if>
            <if test="item.equipmentOee != null">
                equipment_oee = #{item.equipmentOee,jdbcType=VARCHAR},
            </if>
            <if test="item.equipmentNotUseDay != null">
                equipment_not_use_day = #{item.equipmentNotUseDay,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            version_value = version_value + 1
        </set>
           where id = #{item.id,jdbcType=VARCHAR}
           and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from mps_special_technology_equipment_efficiency where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mps_special_technology_equipment_efficiency where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <!-- 根据版本号删除 -->
    <delete id="deleteBatchVersion" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            delete from mps_special_technology_equipment_efficiency where
            id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </delete>
</mapper>
