package com.yhl.scp.mps.domain.workbench.impl;

import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.ips.enums.MessageTypeEnum;
import com.yhl.scp.ips.rbac.vo.UserMessageVO;
import com.yhl.scp.ips.rbac.vo.WorkBenchResourceVO;
import com.yhl.scp.mps.capacityBalance.vo.CapacityLoadVO;
import com.yhl.scp.mps.domain.workbench.model.WorkBenchContext;
import com.yhl.scp.mps.domain.workbench.model.res.WorkBenchRes;
import com.yhl.scp.mps.domain.workbench.process.AbstractWorkBenchProcess;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>DefaultWorkBenchImpl</code>
 * <p>
 * default 默认流程实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-10 13:36:57
 */
@Slf4j
@Service
public class DefaultWorkBenchImpl extends AbstractWorkBenchProcess {

    @Override
    protected void completedStatistics(WorkBenchContext workBenchContext, WorkBenchRes workBenchRes) {
        DynamicDataSourceContextHolder.setDataSource(workBenchContext.getScenario());

        DynamicDataSourceContextHolder.clearDataSource();
    }

    @Override
    protected void orderPlanOperation(WorkBenchContext workBenchContext, WorkBenchRes workBenchRes) {
        DynamicDataSourceContextHolder.setDataSource(workBenchContext.getScenario());
        String userId = workBenchContext.getUserId();
        List<WorkBenchResourceVO> workBenchResourceVOS = ipsFeign.selectWorkBenchResource(userId);
        Map<String, List<WorkBenchResourceVO>> resourceMap = new HashMap<>();
        Map<String, List<WorkBenchResourceVO>> workBenchResourceMap = StreamUtils.mapListByColumn(workBenchResourceVOS, WorkBenchResourceVO::getModule);
        for (Map.Entry<String, List<WorkBenchResourceVO>> entry : workBenchResourceMap.entrySet()) {
            String key = entry.getKey();
            // 去重操作，一个人有多个角色则合并权限资源信息
            List<WorkBenchResourceVO> uniqueValue = entry.getValue().stream()
                    .filter(resource -> resource.getResourceId() != null)
                    .collect(Collectors.groupingBy(
                            WorkBenchResourceVO::getResourceId,
                            Collectors.reducing((existing, replacement) -> existing)
                    )).values().stream()
                    .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
            resourceMap.put(key, uniqueValue);
        }
        workBenchRes.setResourceMap(resourceMap);
        DynamicDataSourceContextHolder.clearDataSource();
    }

    @Override
    protected void deliveryStatus(WorkBenchContext workBenchContext, WorkBenchRes workBenchRes) {
        DynamicDataSourceContextHolder.setDataSource(workBenchContext.getScenario());
        List<String> permissionCodes = workBenchContext.getPermissionPhysicalResourceCodes();
        List<CapacityLoadVO> capacityLoadVOS = capacityLoadDao.selectCapacityWeekLoad(permissionCodes);
        if (CollectionUtils.isEmpty(capacityLoadVOS)) {
            return;
        }
        List<Map<String, Object>> result = capacityLoadVOS.stream()
                .filter(Objects::nonNull)
                .map(loadVO -> {
                    String resourceName = loadVO.getResourceName();
                    BigDecimal utilization = Optional.ofNullable(loadVO.getCapacityUtilization())
                            .map(util -> util.multiply(BigDecimal.valueOf(100))
                                    .setScale(0, RoundingMode.HALF_UP))
                            .orElse(BigDecimal.ZERO);
                    String color = utilization.compareTo(BigDecimal.valueOf(100)) >= 0 ? "red" : "yellow";
                    Map<String, Object> map = new HashMap<>();
                    map.put("msg", String.format("%s产线周产能负荷超额", resourceName));
                    map.put("schedule", utilization + "%");
                    map.put("color", color);
                    return map;
                }).collect(Collectors.toList());
        workBenchRes.setDeliveryStatus(result);
        DynamicDataSourceContextHolder.clearDataSource();
    }


    @Override
    protected void toDoItem(WorkBenchContext workBenchContext, WorkBenchRes workBenchRes) {
        DynamicDataSourceContextHolder.setDataSource(workBenchContext.getScenario());

        DynamicDataSourceContextHolder.clearDataSource();
    }

    @Override
    protected void message(WorkBenchContext workBenchContext, WorkBenchRes workBenchRes) {
        DynamicDataSourceContextHolder.setDataSource(workBenchContext.getScenario());
        Map<String, List<UserMessageVO>> messageTypeMap = workBenchContext.getMessageTypeMap();
        if (messageTypeMap == null || !messageTypeMap.containsKey(MessageTypeEnum.MY_MESSAGE.getCode())) {
            return;
        }
        List<UserMessageVO> userMessageVOS = new ArrayList<>(messageTypeMap.get(MessageTypeEnum.MY_MESSAGE.getCode()));
        userMessageVOS.sort(Comparator.comparing(UserMessageVO::getCreateTime));
        workBenchRes.setUserMessageVOS(userMessageVOS);
        DynamicDataSourceContextHolder.clearDataSource();
    }


}
