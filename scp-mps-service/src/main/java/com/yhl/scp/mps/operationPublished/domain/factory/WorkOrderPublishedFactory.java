package com.yhl.scp.mps.operationPublished.domain.factory;

import com.yhl.scp.mps.operationPublished.domain.entity.WorkOrderPublishedDO;
import com.yhl.scp.mps.operationPublished.dto.WorkOrderPublishedDTO;
import com.yhl.scp.mps.operationPublished.infrastructure.dao.WorkOrderPublishedDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>WorkOrderPublishedFactory</code>
 * <p>
 * 制造订单发布信息表领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-20 09:29:45
 */
@Component
public class WorkOrderPublishedFactory {

    @Resource
    private WorkOrderPublishedDao workOrderPublishedDao;

    WorkOrderPublishedDO create(WorkOrderPublishedDTO dto) {
        // TODO
        return null;
    }

}
