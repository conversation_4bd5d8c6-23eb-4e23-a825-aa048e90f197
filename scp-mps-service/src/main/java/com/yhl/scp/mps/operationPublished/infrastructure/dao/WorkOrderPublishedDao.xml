<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mps.operationPublished.infrastructure.dao.WorkOrderPublishedDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mps.operationPublished.infrastructure.po.WorkOrderPublishedPO">
        <!--@Table sds_ord_work_order_published-->
        <id column="new_id" jdbcType="VARCHAR" property="newId"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="product_stock_point_id" jdbcType="VARCHAR" property="productStockPointId"/>
        <result column="stock_point_id" jdbcType="VARCHAR" property="stockPointId"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="organization_id" jdbcType="VARCHAR" property="organizationId"/>
        <result column="parent_id" jdbcType="VARCHAR" property="parentId"/>
        <result column="bom_version_id" jdbcType="VARCHAR" property="bomVersionId"/>
        <result column="upper_order_id" jdbcType="VARCHAR" property="upperOrderId"/>
        <result column="top_order_id" jdbcType="VARCHAR" property="topOrderId"/>
        <result column="lower_order_id" jdbcType="VARCHAR" property="lowerOrderId"/>
        <result column="bottom_order_id" jdbcType="VARCHAR" property="bottomOrderId"/>
        <result column="batch_split" jdbcType="VARCHAR" property="batchSplit"/>
        <result column="quantity" jdbcType="VARCHAR" property="quantity"/>
        <result column="amount" jdbcType="VARCHAR" property="amount"/>
        <result column="order_time" jdbcType="TIMESTAMP" property="orderTime"/>
        <result column="due_date" jdbcType="TIMESTAMP" property="dueDate"/>
        <result column="delay_penalty" jdbcType="VARCHAR" property="delayPenalty"/>
        <result column="order_status" jdbcType="VARCHAR" property="orderStatus"/>
        <result column="priority" jdbcType="INTEGER" property="priority"/>
        <result column="delay_status" jdbcType="VARCHAR" property="delayStatus"/>
        <result column="fixed" jdbcType="VARCHAR" property="fixed"/>
        <result column="feedback_status" jdbcType="VARCHAR" property="feedbackStatus"/>
        <result column="over_production" jdbcType="VARCHAR" property="overProduction"/>
        <result column="resync_allowed" jdbcType="VARCHAR" property="resyncAllowed"/>
        <result column="plan_status" jdbcType="VARCHAR" property="planStatus"/>
        <result column="kit_status" jdbcType="VARCHAR" property="kitStatus"/>
        <result column="sync_status" jdbcType="VARCHAR" property="syncStatus"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="earliest_start_time" jdbcType="TIMESTAMP" property="earliestStartTime"/>
        <result column="calc_earliest_start_time" jdbcType="TIMESTAMP" property="calcEarliestStartTime"/>
        <result column="calc_latest_end_time" jdbcType="TIMESTAMP" property="calcLatestEndTime"/>
        <result column="plannable" jdbcType="VARCHAR" property="plannable"/>
        <result column="appoint_plannable" jdbcType="VARCHAR" property="appointPlannable"/>
        <result column="routing_id" jdbcType="VARCHAR" property="routingId"/>
        <result column="appoint_routing_id" jdbcType="VARCHAR" property="appointRoutingId"/>
        <result column="routing_type" jdbcType="VARCHAR" property="routingType"/>
        <result column="lead_time" jdbcType="INTEGER" property="leadTime"/>
        <result column="appoint_lead_time" jdbcType="INTEGER" property="appointLeadTime"/>
        <result column="appoint_customer_id" jdbcType="VARCHAR" property="appointCustomerId"/>
        <result column="appoint_demand_type" jdbcType="VARCHAR" property="appointDemandType"/>
        <result column="appoint_demand_order_id" jdbcType="VARCHAR" property="appointDemandOrderId"/>
        <result column="appoint_customer_order_id" jdbcType="VARCHAR" property="appointCustomerOrderId"/>
        <result column="appoint_parent_work_order_id" jdbcType="VARCHAR" property="appointParentWorkOrderId"/>
        <result column="sync_failure_reason" jdbcType="VARCHAR" property="syncFailureReason"/>
        <result column="stocking_quantity" jdbcType="VARCHAR" property="stockingQuantity"/>
        <result column="planned_production_date" jdbcType="TIMESTAMP" property="plannedProductionDate"/>
        <result column="planned_output_date" jdbcType="TIMESTAMP" property="plannedOutputDate"/>
        <result column="fulfilled_quantity" jdbcType="VARCHAR" property="fulfilledQuantity"/>
        <result column="fulfillment_info" jdbcType="VARCHAR" property="fulfillmentInfo"/>
        <result column="unfulfilled_quantity" jdbcType="VARCHAR" property="unfulfilledQuantity"/>
        <result column="fulfillment_status" jdbcType="VARCHAR" property="fulfillmentStatus"/>
        <result column="counting_unit_id" jdbcType="VARCHAR" property="countingUnitId"/>
        <result column="currency_unit_id" jdbcType="VARCHAR" property="currencyUnitId"/>
        <result column="participate_scheduling" jdbcType="VARCHAR" property="participateScheduling"/>
        <result column="fixed_quantity_status" jdbcType="VARCHAR" property="fixedQuantityStatus"/>
        <result column="quantity_consistent" jdbcType="VARCHAR" property="quantityConsistent"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="operation_conflict" jdbcType="VARCHAR" property="operationConflict"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="bom_type" jdbcType="VARCHAR" property="bomType"/>
        <result column="planned_production_place_Id" jdbcType="VARCHAR" property="plannedProductionPlaceId"/>
        <result column="planned_time" jdbcType="TIMESTAMP" property="plannedTime"/>
        <result column="dispatch_place_id" jdbcType="VARCHAR" property="dispatchPlaceId"/>
        <result column="ending_inventory_min_safe_diff" jdbcType="INTEGER" property="endingInventoryMinSafeDiff"/>
        <result column="demand_category" jdbcType="VARCHAR" property="demandCategory"/>
        <result column="published_log_id" jdbcType="VARCHAR" property="publishedLogId"/>
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="order_type" jdbcType="VARCHAR" property="orderType"/>
        <result column="test_order_number" jdbcType="VARCHAR" property="testOrderNumber"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mps.operationPublished.vo.WorkOrderPublishedVO">
        <id column="new_id" jdbcType="VARCHAR" property="newId"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="product_stock_point_id" jdbcType="VARCHAR" property="productStockPointId"/>
        <result column="stock_point_id" jdbcType="VARCHAR" property="stockPointId"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="organization_id" jdbcType="VARCHAR" property="organizationId"/>
        <result column="parent_id" jdbcType="VARCHAR" property="parentId"/>
        <result column="bom_version_id" jdbcType="VARCHAR" property="bomVersionId"/>
        <result column="upper_order_id" jdbcType="VARCHAR" property="upperOrderId"/>
        <result column="top_order_id" jdbcType="VARCHAR" property="topOrderId"/>
        <result column="lower_order_id" jdbcType="VARCHAR" property="lowerOrderId"/>
        <result column="bottom_order_id" jdbcType="VARCHAR" property="bottomOrderId"/>
        <result column="batch_split" jdbcType="VARCHAR" property="batchSplit"/>
        <result column="quantity" jdbcType="VARCHAR" property="quantity"/>
        <result column="amount" jdbcType="VARCHAR" property="amount"/>
        <result column="order_time" jdbcType="TIMESTAMP" property="orderTime"/>
        <result column="due_date" jdbcType="TIMESTAMP" property="dueDate"/>
        <result column="delay_penalty" jdbcType="VARCHAR" property="delayPenalty"/>
        <result column="order_status" jdbcType="VARCHAR" property="orderStatus"/>
        <result column="priority" jdbcType="INTEGER" property="priority"/>
        <result column="delay_status" jdbcType="VARCHAR" property="delayStatus"/>
        <result column="fixed" jdbcType="VARCHAR" property="fixed"/>
        <result column="feedback_status" jdbcType="VARCHAR" property="feedbackStatus"/>
        <result column="over_production" jdbcType="VARCHAR" property="overProduction"/>
        <result column="resync_allowed" jdbcType="VARCHAR" property="resyncAllowed"/>
        <result column="plan_status" jdbcType="VARCHAR" property="planStatus"/>
        <result column="kit_status" jdbcType="VARCHAR" property="kitStatus"/>
        <result column="sync_status" jdbcType="VARCHAR" property="syncStatus"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="earliest_start_time" jdbcType="TIMESTAMP" property="earliestStartTime"/>
        <result column="calc_earliest_start_time" jdbcType="TIMESTAMP" property="calcEarliestStartTime"/>
        <result column="calc_latest_end_time" jdbcType="TIMESTAMP" property="calcLatestEndTime"/>
        <result column="plannable" jdbcType="VARCHAR" property="plannable"/>
        <result column="appoint_plannable" jdbcType="VARCHAR" property="appointPlannable"/>
        <result column="routing_id" jdbcType="VARCHAR" property="routingId"/>
        <result column="appoint_routing_id" jdbcType="VARCHAR" property="appointRoutingId"/>
        <result column="routing_type" jdbcType="VARCHAR" property="routingType"/>
        <result column="lead_time" jdbcType="INTEGER" property="leadTime"/>
        <result column="appoint_lead_time" jdbcType="INTEGER" property="appointLeadTime"/>
        <result column="appoint_customer_id" jdbcType="VARCHAR" property="appointCustomerId"/>
        <result column="appoint_demand_type" jdbcType="VARCHAR" property="appointDemandType"/>
        <result column="appoint_demand_order_id" jdbcType="VARCHAR" property="appointDemandOrderId"/>
        <result column="appoint_customer_order_id" jdbcType="VARCHAR" property="appointCustomerOrderId"/>
        <result column="appoint_parent_work_order_id" jdbcType="VARCHAR" property="appointParentWorkOrderId"/>
        <result column="sync_failure_reason" jdbcType="VARCHAR" property="syncFailureReason"/>
        <result column="stocking_quantity" jdbcType="VARCHAR" property="stockingQuantity"/>
        <result column="planned_production_date" jdbcType="TIMESTAMP" property="plannedProductionDate"/>
        <result column="planned_output_date" jdbcType="TIMESTAMP" property="plannedOutputDate"/>
        <result column="fulfilled_quantity" jdbcType="VARCHAR" property="fulfilledQuantity"/>
        <result column="fulfillment_info" jdbcType="VARCHAR" property="fulfillmentInfo"/>
        <result column="unfulfilled_quantity" jdbcType="VARCHAR" property="unfulfilledQuantity"/>
        <result column="fulfillment_status" jdbcType="VARCHAR" property="fulfillmentStatus"/>
        <result column="counting_unit_id" jdbcType="VARCHAR" property="countingUnitId"/>
        <result column="currency_unit_id" jdbcType="VARCHAR" property="currencyUnitId"/>
        <result column="participate_scheduling" jdbcType="VARCHAR" property="participateScheduling"/>
        <result column="fixed_quantity_status" jdbcType="VARCHAR" property="fixedQuantityStatus"/>
        <result column="quantity_consistent" jdbcType="VARCHAR" property="quantityConsistent"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="operation_conflict" jdbcType="VARCHAR" property="operationConflict"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="bom_type" jdbcType="VARCHAR" property="bomType"/>
        <result column="planned_production_place_Id" jdbcType="VARCHAR" property="plannedProductionPlaceId"/>
        <result column="planned_time" jdbcType="TIMESTAMP" property="plannedTime"/>
        <result column="dispatch_place_id" jdbcType="VARCHAR" property="dispatchPlaceId"/>
        <result column="ending_inventory_min_safe_diff" jdbcType="INTEGER" property="endingInventoryMinSafeDiff"/>
        <result column="demand_category" jdbcType="VARCHAR" property="demandCategory"/>
        <result column="published_log_id" jdbcType="VARCHAR" property="publishedLogId"/>
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="order_type" jdbcType="VARCHAR" property="orderType"/>
        <result column="test_order_number" jdbcType="VARCHAR" property="testOrderNumber"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="inventory_item_id" jdbcType="VARCHAR" property="inventoryItemId"/>
        <result column="plan_no" jdbcType="VARCHAR" property="planNo"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
    </resultMap>
    <sql id="Base_Column_List">
        new_id,order_no,product_stock_point_id,stock_point_id,product_id,organization_id,parent_id,bom_version_id,upper_order_id,top_order_id,
        lower_order_id,bottom_order_id,batch_split,quantity,amount,order_time,due_date,delay_penalty,order_status,priority,delay_status,
        fixed,feedback_status,over_production,resync_allowed,plan_status,kit_status,sync_status,
        start_time,end_time,earliest_start_time,calc_earliest_start_time,calc_latest_end_time,
        plannable,appoint_plannable,routing_id,appoint_routing_id,routing_type,lead_time,
        appoint_lead_time,appoint_customer_id,appoint_demand_type,appoint_demand_order_id,appoint_customer_order_id,
        appoint_parent_work_order_id,sync_failure_reason,stocking_quantity,planned_production_date,
        planned_output_date,fulfilled_quantity,fulfillment_info,unfulfilled_quantity,
        fulfillment_status,counting_unit_id,currency_unit_id,participate_scheduling,
        fixed_quantity_status,quantity_consistent,remark,enabled,operation_conflict,creator,create_time,
        modifier,modify_time,bom_type,planned_production_place_Id,planned_time,dispatch_place_id,
        ending_inventory_min_safe_diff,demand_category,id,published_log_id,order_type,test_order_number
    </sql>

    <resultMap id="POResultMap" extends="BaseResultMap" type="com.yhl.scp.mps.operationPublished.infrastructure.po.WorkOrderPublishedPO">
        <!-- TODO -->
    </resultMap>

    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />,product_code,inventory_item_id,plan_no,stock_point_code
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.newId != null and params.newId != ''">
                and new_id = #{params.newId,jdbcType=VARCHAR}
            </if>
            <if test="params.orderNo != null and params.orderNo != ''">
                and order_no = #{params.orderNo,jdbcType=VARCHAR}
            </if>
            <if test="params.orderNos != null and params.orderNos.size()>0">
                and order_no in
                <foreach collection="params.orderNos" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productStockPointId != null and params.productStockPointId != ''">
                and product_stock_point_id = #{params.productStockPointId,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointId != null and params.stockPointId != ''">
                and stock_point_id = #{params.stockPointId,jdbcType=VARCHAR}
            </if>
            <if test="params.productId != null and params.productId != ''">
                and product_id = #{params.productId,jdbcType=VARCHAR}
            </if>
            <if test="params.organizationId != null and params.organizationId != ''">
                and organization_id = #{params.organizationId,jdbcType=VARCHAR}
            </if>
            <if test="params.parentId != null and params.parentId != ''">
                and parent_id = #{params.parentId,jdbcType=VARCHAR}
            </if>
            <if test="params.parentIds != null and params.parentIds.size()>0">
                and parent_id in
                <foreach collection="params.parentIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.parentIdNotNull != null and params.parentIdNotNull != ''">
                and parent_id is not null
            </if>
            <if test="params.parentIdIsNull != null and params.parentIdIsNull != ''">
                and parent_id is null
            </if>
            <if test="params.bomVersionId != null and params.bomVersionId != ''">
                and bom_version_id = #{params.bomVersionId,jdbcType=VARCHAR}
            </if>
            <if test="params.upperOrderId != null and params.upperOrderId != ''">
                and upper_order_id = #{params.upperOrderId,jdbcType=VARCHAR}
            </if>
            <if test="params.topOrderId != null and params.topOrderId != ''">
                and top_order_id = #{params.topOrderId,jdbcType=VARCHAR}
            </if>
            <if test="params.lowerOrderId != null and params.lowerOrderId != ''">
                and lower_order_id = #{params.lowerOrderId,jdbcType=VARCHAR}
            </if>
            <if test="params.bottomOrderId != null and params.bottomOrderId != ''">
                and bottom_order_id = #{params.bottomOrderId,jdbcType=VARCHAR}
            </if>
            <if test="params.batchSplit != null and params.batchSplit != ''">
                and batch_split = #{params.batchSplit,jdbcType=VARCHAR}
            </if>
            <if test="params.quantity != null">
                and quantity = #{params.quantity,jdbcType=VARCHAR}
            </if>
            <if test="params.amount != null">
                and amount = #{params.amount,jdbcType=VARCHAR}
            </if>
            <if test="params.orderTime != null">
                and order_time = #{params.orderTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.dueDate != null">
                and due_date = #{params.dueDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.delayPenalty != null">
                and delay_penalty = #{params.delayPenalty,jdbcType=VARCHAR}
            </if>
            <if test="params.orderStatus != null and params.orderStatus != ''">
                and order_status = #{params.orderStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.priority != null">
                and priority = #{params.priority,jdbcType=INTEGER}
            </if>
            <if test="params.delayStatus != null and params.delayStatus != ''">
                and delay_status = #{params.delayStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.fixed != null and params.fixed != ''">
                and fixed = #{params.fixed,jdbcType=VARCHAR}
            </if>
            <if test="params.feedbackStatus != null and params.feedbackStatus != ''">
                and feedback_status = #{params.feedbackStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.overProduction != null and params.overProduction != ''">
                and over_production = #{params.overProduction,jdbcType=VARCHAR}
            </if>
            <if test="params.resyncAllowed != null and params.resyncAllowed != ''">
                and resync_allowed = #{params.resyncAllowed,jdbcType=VARCHAR}
            </if>
            <if test="params.planStatus != null and params.planStatus != ''">
                and plan_status = #{params.planStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.planStatusList != null and params.planStatusList.size > 0">
                and plan_status in
                <foreach collection="params.planStatusList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.kitStatus != null and params.kitStatus != ''">
                and kit_status = #{params.kitStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.syncStatus != null and params.syncStatus != ''">
                and sync_status = #{params.syncStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.startTime != null">
                and start_time = #{params.startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.endTime != null">
                and end_time = #{params.endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.earliestStartTime != null">
                and earliest_start_time = #{params.earliestStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.calcEarliestStartTime != null">
                and calc_earliest_start_time = #{params.calcEarliestStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.calcLatestEndTime != null">
                and calc_latest_end_time = #{params.calcLatestEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.plannable != null and params.plannable != ''">
                and plannable = #{params.plannable,jdbcType=VARCHAR}
            </if>
            <if test="params.appointPlannable != null and params.appointPlannable != ''">
                and appoint_plannable = #{params.appointPlannable,jdbcType=VARCHAR}
            </if>
            <if test="params.routingId != null and params.routingId != ''">
                and routing_id = #{params.routingId,jdbcType=VARCHAR}
            </if>
            <if test="params.appointRoutingId != null and params.appointRoutingId != ''">
                and appoint_routing_id = #{params.appointRoutingId,jdbcType=VARCHAR}
            </if>
            <if test="params.routingType != null and params.routingType != ''">
                and routing_type = #{params.routingType,jdbcType=VARCHAR}
            </if>
            <if test="params.leadTime != null">
                and lead_time = #{params.leadTime,jdbcType=INTEGER}
            </if>
            <if test="params.appointLeadTime != null">
                and appoint_lead_time = #{params.appointLeadTime,jdbcType=INTEGER}
            </if>
            <if test="params.appointCustomerId != null and params.appointCustomerId != ''">
                and appoint_customer_id = #{params.appointCustomerId,jdbcType=VARCHAR}
            </if>
            <if test="params.appointDemandType != null and params.appointDemandType != ''">
                and appoint_demand_type = #{params.appointDemandType,jdbcType=VARCHAR}
            </if>
            <if test="params.appointDemandOrderId != null and params.appointDemandOrderId != ''">
                and appoint_demand_order_id = #{params.appointDemandOrderId,jdbcType=VARCHAR}
            </if>
            <if test="params.appointCustomerOrderId != null and params.appointCustomerOrderId != ''">
                and appoint_customer_order_id = #{params.appointCustomerOrderId,jdbcType=VARCHAR}
            </if>
            <if test="params.appointParentWorkOrderId != null and params.appointParentWorkOrderId != ''">
                and appoint_parent_work_order_id = #{params.appointParentWorkOrderId,jdbcType=VARCHAR}
            </if>
            <if test="params.syncFailureReason != null and params.syncFailureReason != ''">
                and sync_failure_reason = #{params.syncFailureReason,jdbcType=VARCHAR}
            </if>
            <if test="params.stockingQuantity != null">
                and stocking_quantity = #{params.stockingQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.plannedProductionDate != null">
                and planned_production_date = #{params.plannedProductionDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.plannedOutputDate != null">
                and planned_output_date = #{params.plannedOutputDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.fulfilledQuantity != null">
                and fulfilled_quantity = #{params.fulfilledQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.fulfillmentInfo != null and params.fulfillmentInfo != ''">
                and fulfillment_info = #{params.fulfillmentInfo,jdbcType=VARCHAR}
            </if>
            <if test="params.unfulfilledQuantity != null">
                and unfulfilled_quantity = #{params.unfulfilledQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.fulfillmentStatus != null and params.fulfillmentStatus != ''">
                and fulfillment_status = #{params.fulfillmentStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.countingUnitId != null and params.countingUnitId != ''">
                and counting_unit_id = #{params.countingUnitId,jdbcType=VARCHAR}
            </if>
            <if test="params.currencyUnitId != null and params.currencyUnitId != ''">
                and currency_unit_id = #{params.currencyUnitId,jdbcType=VARCHAR}
            </if>
            <if test="params.participateScheduling != null and params.participateScheduling != ''">
                and participate_scheduling = #{params.participateScheduling,jdbcType=VARCHAR}
            </if>
            <if test="params.fixedQuantityStatus != null and params.fixedQuantityStatus != ''">
                and fixed_quantity_status = #{params.fixedQuantityStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.quantityConsistent != null and params.quantityConsistent != ''">
                and quantity_consistent = #{params.quantityConsistent,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.operationConflict != null and params.operationConflict != ''">
                and operation_conflict = #{params.operationConflict,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.bomType != null and params.bomType != ''">
                and bom_type = #{params.bomType,jdbcType=VARCHAR}
            </if>
            <if test="params.plannedProductionPlaceId != null and params.plannedProductionPlaceId != ''">
                and planned_production_place_Id = #{params.plannedProductionPlaceId,jdbcType=VARCHAR}
            </if>
            <if test="params.plannedTime != null">
                and planned_time = #{params.plannedTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.dispatchPlaceId != null and params.dispatchPlaceId != ''">
                and dispatch_place_id = #{params.dispatchPlaceId,jdbcType=VARCHAR}
            </if>
            <if test="params.endingInventoryMinSafeDiff != null">
                and ending_inventory_min_safe_diff = #{params.endingInventoryMinSafeDiff,jdbcType=INTEGER}
            </if>
            <if test="params.demandCategory != null and params.demandCategory != ''">
                and demand_category = #{params.demandCategory,jdbcType=VARCHAR}
            </if>
            <if test="params.publishedLogId != null and params.publishedLogId != ''">
                and published_log_id = #{params.publishedLogId,jdbcType=VARCHAR}
            </if>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.ids != null and params.ids.size > 0">
                and id in
                <foreach collection="params.ids" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.orderType != null and params.orderType != ''">
                and order_type = #{params.orderType,jdbcType=VARCHAR}
            </if>
            <if test="params.testOrderNumber != null and params.testOrderNumber != ''">
                and test_order_number = #{params.testOrderNumber,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.inventoryItemId != null and params.inventoryItemId != ''">
                and inventory_item_id = #{params.inventoryItemId,jdbcType=VARCHAR}
            </if>
            <if test="params.planNo != null and params.planNo != ''">
                and plan_no = #{params.planNo,jdbcType=VARCHAR}
            </if>
            <if test="params.planNoIsNull != null and params.planNoIsNull != ''">
                and plan_no is null
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sds_ord_work_order_published
        where new_id = #{newId,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sds_ord_work_order_published
        where new_id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from sds_ord_work_order_published
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sds_ord_work_order_published
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        SELECT soop.order_no                       AS order_no,
               soop.product_stock_point_id         AS product_stock_point_id,
               soop.stock_point_id                 AS stock_point_id,
               soop.product_id                     AS product_id,
               soop.parent_id                      AS parent_id,
               soop.upper_order_id                 AS upper_order_id,
               soop.top_order_id                   AS top_order_id,
               soop.lower_order_id                 AS lower_order_id,
               soop.quantity                       AS quantity,
               soop.due_date                       AS due_date,
               soop.priority                       AS priority,
               soop.plan_status                    AS plan_status,
               soop.kit_status                     AS kit_status,
               soop.sync_status                    AS sync_status,
               soop.start_time                     AS start_time,
               soop.end_time                       AS end_time,
               soop.earliest_start_time            AS earliest_start_time,
               soop.calc_earliest_start_time       AS calc_earliest_start_time,
               soop.calc_latest_end_time           AS calc_latest_end_time,
               soop.appoint_plannable              AS appoint_plannable,
               soop.routing_id                     AS routing_id,
               soop.appoint_routing_id             AS appoint_routing_id,
               soop.routing_type                   AS routing_type,
               soop.lead_time                      AS lead_time,
               soop.appoint_lead_time              AS appoint_lead_time,
               soop.appoint_customer_id            AS appoint_customer_id,
               soop.appoint_demand_type            AS appoint_demand_type,
               soop.appoint_demand_order_id        AS appoint_demand_order_id,
               soop.appoint_customer_order_id      AS appoint_customer_order_id,
               soop.appoint_parent_work_order_id   AS appoint_parent_work_order_id,
               soop.sync_failure_reason            AS sync_failure_reason,
               soop.stocking_quantity              AS stocking_quantity,
               soop.planned_production_date        AS planned_production_date,
               soop.planned_output_date            AS planned_output_date,
               soop.fulfilled_quantity             AS fulfilled_quantity,
               soop.fulfillment_info               AS fulfillment_info,
               soop.unfulfilled_quantity           AS unfulfilled_quantity,
               soop.fulfillment_status             AS fulfillment_status,
               soop.counting_unit_id               AS counting_unit_id,
               soop.currency_unit_id               AS currency_unit_id,
               soop.participate_scheduling         AS participate_scheduling,
               soop.fixed_quantity_status          AS fixed_quantity_status,
               soop.quantity_consistent            AS quantity_consistent,
               soop.remark                         AS remark,
               soop.enabled                        AS enabled,
               soop.operation_conflict             AS operation_conflict,
               soop.creator                        AS creator,
               soop.create_time                    AS create_time,
               soop.modifier                       AS modifier,
               soop.modify_time                    AS modify_time,
               soop.bom_type                       AS bom_type,
               soop.planned_production_place_id    AS planned_production_place_id,
               soop.planned_time                   AS planned_time,
               soop.dispatch_place_id              AS dispatch_place_id,
               soop.ending_inventory_min_safe_diff AS ending_inventory_min_safe_diff,
               soop.demand_category                AS demand_category,
               soop.id                             AS id,
               soop.order_type                     AS order_type,
               soop.test_order_number              AS test_order_number,
               mpsp.inventory_item_id              AS inventory_item_id,
               mpsp.product_code                   AS product_code,
               mpsp.stock_point_code               AS stock_point_code,
               mmpr.plan_no                        AS plan_no
        FROM sds_ord_work_order soop
                 LEFT JOIN
             mds_product_stock_point mpsp ON soop.product_id = mpsp.id
                 LEFT JOIN
             mps_master_plan_relation mmpr ON soop.order_no = mmpr.order_no
        where 1 = 1
        <if test="params.parentIdNotNull != null and params.parentIdNotNull != ''">
            and soop.parent_id is not null
        </if>
        <if test="params.parentIdIsNull != null and params.parentIdIsNull != ''">
            and soop.parent_id is null
        </if>
        <if test="params.ids != null and params.ids.size > 0">
            and soop.id in
            <foreach collection="params.ids" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mps.operationPublished.infrastructure.po.WorkOrderPublishedPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into sds_ord_work_order_published(
        new_id,
        order_no,
        product_stock_point_id,
        stock_point_id,
        product_id,
        organization_id,
        parent_id,
        bom_version_id,
        upper_order_id,
        top_order_id,
        lower_order_id,
        bottom_order_id,
        batch_split,
        quantity,
        amount,
        order_time,
        due_date,
        delay_penalty,
        order_status,
        priority,
        delay_status,
        fixed,
        feedback_status,
        over_production,
        resync_allowed,
        plan_status,
        kit_status,
        sync_status,
        start_time,
        end_time,
        earliest_start_time,
        calc_earliest_start_time,
        calc_latest_end_time,
        plannable,
        appoint_plannable,
        routing_id,
        appoint_routing_id,
        routing_type,
        lead_time,
        appoint_lead_time,
        appoint_customer_id,
        appoint_demand_type,
        appoint_demand_order_id,
        appoint_customer_order_id,
        appoint_parent_work_order_id,
        sync_failure_reason,
        stocking_quantity,
        planned_production_date,
        planned_output_date,
        fulfilled_quantity,
        fulfillment_info,
        unfulfilled_quantity,
        fulfillment_status,
        counting_unit_id,
        currency_unit_id,
        participate_scheduling,
        fixed_quantity_status,
        quantity_consistent,
        remark,
        enabled,
        operation_conflict,
        creator,
        create_time,
        modifier,
        modify_time,
        bom_type,
        planned_production_place_Id,
        planned_time,
        dispatch_place_id,
        ending_inventory_min_safe_diff,
        demand_category,
        id,
        published_log_id,
        order_type,
        test_order_number)
        values (
        #{newId,jdbcType=VARCHAR},
        #{orderNo,jdbcType=VARCHAR},
        #{productStockPointId,jdbcType=VARCHAR},
        #{stockPointId,jdbcType=VARCHAR},
        #{productId,jdbcType=VARCHAR},
        #{organizationId,jdbcType=VARCHAR},
        #{parentId,jdbcType=VARCHAR},
        #{bomVersionId,jdbcType=VARCHAR},
        #{upperOrderId,jdbcType=VARCHAR},
        #{topOrderId,jdbcType=VARCHAR},
        #{lowerOrderId,jdbcType=VARCHAR},
        #{bottomOrderId,jdbcType=VARCHAR},
        #{batchSplit,jdbcType=VARCHAR},
        #{quantity,jdbcType=VARCHAR},
        #{amount,jdbcType=VARCHAR},
        #{orderTime,jdbcType=TIMESTAMP},
        #{dueDate,jdbcType=TIMESTAMP},
        #{delayPenalty,jdbcType=VARCHAR},
        #{orderStatus,jdbcType=VARCHAR},
        #{priority,jdbcType=INTEGER},
        #{delayStatus,jdbcType=VARCHAR},
        #{fixed,jdbcType=VARCHAR},
        #{feedbackStatus,jdbcType=VARCHAR},
        #{overProduction,jdbcType=VARCHAR},
        #{resyncAllowed,jdbcType=VARCHAR},
        #{planStatus,jdbcType=VARCHAR},
        #{kitStatus,jdbcType=VARCHAR},
        #{syncStatus,jdbcType=VARCHAR},
        #{startTime,jdbcType=TIMESTAMP},
        #{endTime,jdbcType=TIMESTAMP},
        #{earliestStartTime,jdbcType=TIMESTAMP},
        #{calcEarliestStartTime,jdbcType=TIMESTAMP},
        #{calcLatestEndTime,jdbcType=TIMESTAMP},
        #{plannable,jdbcType=VARCHAR},
        #{appointPlannable,jdbcType=VARCHAR},
        #{routingId,jdbcType=VARCHAR},
        #{appointRoutingId,jdbcType=VARCHAR},
        #{routingType,jdbcType=VARCHAR},
        #{leadTime,jdbcType=INTEGER},
        #{appointLeadTime,jdbcType=INTEGER},
        #{appointCustomerId,jdbcType=VARCHAR},
        #{appointDemandType,jdbcType=VARCHAR},
        #{appointDemandOrderId,jdbcType=VARCHAR},
        #{appointCustomerOrderId,jdbcType=VARCHAR},
        #{appointParentWorkOrderId,jdbcType=VARCHAR},
        #{syncFailureReason,jdbcType=VARCHAR},
        #{stockingQuantity,jdbcType=VARCHAR},
        #{plannedProductionDate,jdbcType=TIMESTAMP},
        #{plannedOutputDate,jdbcType=TIMESTAMP},
        #{fulfilledQuantity,jdbcType=VARCHAR},
        #{fulfillmentInfo,jdbcType=VARCHAR},
        #{unfulfilledQuantity,jdbcType=VARCHAR},
        #{fulfillmentStatus,jdbcType=VARCHAR},
        #{countingUnitId,jdbcType=VARCHAR},
        #{currencyUnitId,jdbcType=VARCHAR},
        #{participateScheduling,jdbcType=VARCHAR},
        #{fixedQuantityStatus,jdbcType=VARCHAR},
        #{quantityConsistent,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{operationConflict,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{bomType,jdbcType=VARCHAR},
        #{plannedProductionPlaceId,jdbcType=VARCHAR},
        #{plannedTime,jdbcType=TIMESTAMP},
        #{dispatchPlaceId,jdbcType=VARCHAR},
        #{endingInventoryMinSafeDiff,jdbcType=INTEGER},
        #{demandCategory,jdbcType=VARCHAR},
     	#{id,jdbcType=VARCHAR},
        #{publishedLogId,jdbcType=VARCHAR},
        #{orderType,jdbcType=VARCHAR},
        #{testOrderNumber,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mps.operationPublished.infrastructure.po.WorkOrderPublishedPO">
        insert into sds_ord_work_order_published(
        new_id,
        order_no,
        product_stock_point_id,
        stock_point_id,
        product_id,
        organization_id,
        parent_id,
        bom_version_id,
        upper_order_id,
        top_order_id,
        lower_order_id,
        bottom_order_id,
        batch_split,
        quantity,
        amount,
        order_time,
        due_date,
        delay_penalty,
        order_status,
        priority,
        delay_status,
        fixed,
        feedback_status,
        over_production,
        resync_allowed,
        plan_status,
        kit_status,
        sync_status,
        start_time,
        end_time,
        earliest_start_time,
        calc_earliest_start_time,
        calc_latest_end_time,
        plannable,
        appoint_plannable,
        routing_id,
        appoint_routing_id,
        routing_type,
        lead_time,
        appoint_lead_time,
        appoint_customer_id,
        appoint_demand_type,
        appoint_demand_order_id,
        appoint_customer_order_id,
        appoint_parent_work_order_id,
        sync_failure_reason,
        stocking_quantity,
        planned_production_date,
        planned_output_date,
        fulfilled_quantity,
        fulfillment_info,
        unfulfilled_quantity,
        fulfillment_status,
        counting_unit_id,
        currency_unit_id,
        participate_scheduling,
        fixed_quantity_status,
        quantity_consistent,
        remark,
        enabled,
        operation_conflict,
        creator,
        create_time,
        modifier,
        modify_time,
        bom_type,
        planned_production_place_Id,
        planned_time,
        dispatch_place_id,
        ending_inventory_min_safe_diff,
        demand_category,
        id,
        published_log_id,
        order_type,
        test_order_number)
        values (
        #{newId,jdbcType=VARCHAR},
        #{orderNo,jdbcType=VARCHAR},
        #{productStockPointId,jdbcType=VARCHAR},
        #{stockPointId,jdbcType=VARCHAR},
        #{productId,jdbcType=VARCHAR},
        #{organizationId,jdbcType=VARCHAR},
        #{parentId,jdbcType=VARCHAR},
        #{bomVersionId,jdbcType=VARCHAR},
        #{upperOrderId,jdbcType=VARCHAR},
        #{topOrderId,jdbcType=VARCHAR},
        #{lowerOrderId,jdbcType=VARCHAR},
        #{bottomOrderId,jdbcType=VARCHAR},
        #{batchSplit,jdbcType=VARCHAR},
        #{quantity,jdbcType=VARCHAR},
        #{amount,jdbcType=VARCHAR},
        #{orderTime,jdbcType=TIMESTAMP},
        #{dueDate,jdbcType=TIMESTAMP},
        #{delayPenalty,jdbcType=VARCHAR},
        #{orderStatus,jdbcType=VARCHAR},
        #{priority,jdbcType=INTEGER},
        #{delayStatus,jdbcType=VARCHAR},
        #{fixed,jdbcType=VARCHAR},
        #{feedbackStatus,jdbcType=VARCHAR},
        #{overProduction,jdbcType=VARCHAR},
        #{resyncAllowed,jdbcType=VARCHAR},
        #{planStatus,jdbcType=VARCHAR},
        #{kitStatus,jdbcType=VARCHAR},
        #{syncStatus,jdbcType=VARCHAR},
        #{startTime,jdbcType=TIMESTAMP},
        #{endTime,jdbcType=TIMESTAMP},
        #{earliestStartTime,jdbcType=TIMESTAMP},
        #{calcEarliestStartTime,jdbcType=TIMESTAMP},
        #{calcLatestEndTime,jdbcType=TIMESTAMP},
        #{plannable,jdbcType=VARCHAR},
        #{appointPlannable,jdbcType=VARCHAR},
        #{routingId,jdbcType=VARCHAR},
        #{appointRoutingId,jdbcType=VARCHAR},
        #{routingType,jdbcType=VARCHAR},
        #{leadTime,jdbcType=INTEGER},
        #{appointLeadTime,jdbcType=INTEGER},
        #{appointCustomerId,jdbcType=VARCHAR},
        #{appointDemandType,jdbcType=VARCHAR},
        #{appointDemandOrderId,jdbcType=VARCHAR},
        #{appointCustomerOrderId,jdbcType=VARCHAR},
        #{appointParentWorkOrderId,jdbcType=VARCHAR},
        #{syncFailureReason,jdbcType=VARCHAR},
        #{stockingQuantity,jdbcType=VARCHAR},
        #{plannedProductionDate,jdbcType=TIMESTAMP},
        #{plannedOutputDate,jdbcType=TIMESTAMP},
        #{fulfilledQuantity,jdbcType=VARCHAR},
        #{fulfillmentInfo,jdbcType=VARCHAR},
        #{unfulfilledQuantity,jdbcType=VARCHAR},
        #{fulfillmentStatus,jdbcType=VARCHAR},
        #{countingUnitId,jdbcType=VARCHAR},
        #{currencyUnitId,jdbcType=VARCHAR},
        #{participateScheduling,jdbcType=VARCHAR},
        #{fixedQuantityStatus,jdbcType=VARCHAR},
        #{quantityConsistent,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{operationConflict,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{bomType,jdbcType=VARCHAR},
        #{plannedProductionPlaceId,jdbcType=VARCHAR},
        #{plannedTime,jdbcType=TIMESTAMP},
        #{dispatchPlaceId,jdbcType=VARCHAR},
        #{endingInventoryMinSafeDiff,jdbcType=INTEGER},
        #{demandCategory,jdbcType=VARCHAR},
     	#{id,jdbcType=VARCHAR},
        #{publishedLogId,jdbcType=VARCHAR},
        #{orderType,jdbcType=VARCHAR},
        #{testOrderNumber,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into sds_ord_work_order_published(
        new_id,
        order_no,
        product_stock_point_id,
        stock_point_id,
        product_id,
        organization_id,
        parent_id,
        bom_version_id,
        upper_order_id,
        top_order_id,
        lower_order_id,
        bottom_order_id,
        batch_split,
        quantity,
        amount,
        order_time,
        due_date,
        delay_penalty,
        order_status,
        priority,
        delay_status,
        fixed,
        feedback_status,
        over_production,
        resync_allowed,
        plan_status,
        kit_status,
        sync_status,
        start_time,
        end_time,
        earliest_start_time,
        calc_earliest_start_time,
        calc_latest_end_time,
        plannable,
        appoint_plannable,
        routing_id,
        appoint_routing_id,
        routing_type,
        lead_time,
        appoint_lead_time,
        appoint_customer_id,
        appoint_demand_type,
        appoint_demand_order_id,
        appoint_customer_order_id,
        appoint_parent_work_order_id,
        sync_failure_reason,
        stocking_quantity,
        planned_production_date,
        planned_output_date,
        fulfilled_quantity,
        fulfillment_info,
        unfulfilled_quantity,
        fulfillment_status,
        counting_unit_id,
        currency_unit_id,
        participate_scheduling,
        fixed_quantity_status,
        quantity_consistent,
        remark,
        enabled,
        operation_conflict,
        creator,
        create_time,
        modifier,
        modify_time,
        bom_type,
        planned_production_place_Id,
        planned_time,
        dispatch_place_id,
        ending_inventory_min_safe_diff,
        demand_category,
        id,
        published_log_id,
        order_type,
        test_order_number)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.orderNo,jdbcType=VARCHAR},
        #{entity.productStockPointId,jdbcType=VARCHAR},
        #{entity.stockPointId,jdbcType=VARCHAR},
        #{entity.productId,jdbcType=VARCHAR},
        #{entity.organizationId,jdbcType=VARCHAR},
        #{entity.parentId,jdbcType=VARCHAR},
        #{entity.bomVersionId,jdbcType=VARCHAR},
        #{entity.upperOrderId,jdbcType=VARCHAR},
        #{entity.topOrderId,jdbcType=VARCHAR},
        #{entity.lowerOrderId,jdbcType=VARCHAR},
        #{entity.bottomOrderId,jdbcType=VARCHAR},
        #{entity.batchSplit,jdbcType=VARCHAR},
        #{entity.quantity,jdbcType=VARCHAR},
        #{entity.amount,jdbcType=VARCHAR},
        #{entity.orderTime,jdbcType=TIMESTAMP},
        #{entity.dueDate,jdbcType=TIMESTAMP},
        #{entity.delayPenalty,jdbcType=VARCHAR},
        #{entity.orderStatus,jdbcType=VARCHAR},
        #{entity.priority,jdbcType=INTEGER},
        #{entity.delayStatus,jdbcType=VARCHAR},
        #{entity.fixed,jdbcType=VARCHAR},
        #{entity.feedbackStatus,jdbcType=VARCHAR},
        #{entity.overProduction,jdbcType=VARCHAR},
        #{entity.resyncAllowed,jdbcType=VARCHAR},
        #{entity.planStatus,jdbcType=VARCHAR},
        #{entity.kitStatus,jdbcType=VARCHAR},
        #{entity.syncStatus,jdbcType=VARCHAR},
        #{entity.startTime,jdbcType=TIMESTAMP},
        #{entity.endTime,jdbcType=TIMESTAMP},
        #{entity.earliestStartTime,jdbcType=TIMESTAMP},
        #{entity.calcEarliestStartTime,jdbcType=TIMESTAMP},
        #{entity.calcLatestEndTime,jdbcType=TIMESTAMP},
        #{entity.plannable,jdbcType=VARCHAR},
        #{entity.appointPlannable,jdbcType=VARCHAR},
        #{entity.routingId,jdbcType=VARCHAR},
        #{entity.appointRoutingId,jdbcType=VARCHAR},
        #{entity.routingType,jdbcType=VARCHAR},
        #{entity.leadTime,jdbcType=INTEGER},
        #{entity.appointLeadTime,jdbcType=INTEGER},
        #{entity.appointCustomerId,jdbcType=VARCHAR},
        #{entity.appointDemandType,jdbcType=VARCHAR},
        #{entity.appointDemandOrderId,jdbcType=VARCHAR},
        #{entity.appointCustomerOrderId,jdbcType=VARCHAR},
        #{entity.appointParentWorkOrderId,jdbcType=VARCHAR},
        #{entity.syncFailureReason,jdbcType=VARCHAR},
        #{entity.stockingQuantity,jdbcType=VARCHAR},
        #{entity.plannedProductionDate,jdbcType=TIMESTAMP},
        #{entity.plannedOutputDate,jdbcType=TIMESTAMP},
        #{entity.fulfilledQuantity,jdbcType=VARCHAR},
        #{entity.fulfillmentInfo,jdbcType=VARCHAR},
        #{entity.unfulfilledQuantity,jdbcType=VARCHAR},
        #{entity.fulfillmentStatus,jdbcType=VARCHAR},
        #{entity.countingUnitId,jdbcType=VARCHAR},
        #{entity.currencyUnitId,jdbcType=VARCHAR},
        #{entity.participateScheduling,jdbcType=VARCHAR},
        #{entity.fixedQuantityStatus,jdbcType=VARCHAR},
        #{entity.quantityConsistent,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.operationConflict,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.bomType,jdbcType=VARCHAR},
        #{entity.plannedProductionPlaceId,jdbcType=VARCHAR},
        #{entity.plannedTime,jdbcType=TIMESTAMP},
        #{entity.dispatchPlaceId,jdbcType=VARCHAR},
        #{entity.endingInventoryMinSafeDiff,jdbcType=INTEGER},
        #{entity.demandCategory,jdbcType=VARCHAR},
        #{entity.id,jdbcType=VARCHAR},
        #{entity.publishedLogId,jdbcType=VARCHAR},
        #{entity.orderType,jdbcType=VARCHAR},
        #{entity.testOrderNumber,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into sds_ord_work_order_published(
        new_id,
        order_no,
        product_stock_point_id,
        stock_point_id,
        product_id,
        organization_id,
        parent_id,
        bom_version_id,
        upper_order_id,
        top_order_id,
        lower_order_id,
        bottom_order_id,
        batch_split,
        quantity,
        amount,
        order_time,
        due_date,
        delay_penalty,
        order_status,
        priority,
        delay_status,
        fixed,
        feedback_status,
        over_production,
        resync_allowed,
        plan_status,
        kit_status,
        sync_status,
        start_time,
        end_time,
        earliest_start_time,
        calc_earliest_start_time,
        calc_latest_end_time,
        plannable,
        appoint_plannable,
        routing_id,
        appoint_routing_id,
        routing_type,
        lead_time,
        appoint_lead_time,
        appoint_customer_id,
        appoint_demand_type,
        appoint_demand_order_id,
        appoint_customer_order_id,
        appoint_parent_work_order_id,
        sync_failure_reason,
        stocking_quantity,
        planned_production_date,
        planned_output_date,
        fulfilled_quantity,
        fulfillment_info,
        unfulfilled_quantity,
        fulfillment_status,
        counting_unit_id,
        currency_unit_id,
        participate_scheduling,
        fixed_quantity_status,
        quantity_consistent,
        remark,
        enabled,
        operation_conflict,
        creator,
        create_time,
        modifier,
        modify_time,
        bom_type,
        planned_production_place_Id,
        planned_time,
        dispatch_place_id,
        ending_inventory_min_safe_diff,
        demand_category,
        id,
        published_log_id,
        order_type,
        test_order_number)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.newId,jdbcType=VARCHAR},
        #{entity.orderNo,jdbcType=VARCHAR},
        #{entity.productStockPointId,jdbcType=VARCHAR},
        #{entity.stockPointId,jdbcType=VARCHAR},
        #{entity.productId,jdbcType=VARCHAR},
        #{entity.organizationId,jdbcType=VARCHAR},
        #{entity.parentId,jdbcType=VARCHAR},
        #{entity.bomVersionId,jdbcType=VARCHAR},
        #{entity.upperOrderId,jdbcType=VARCHAR},
        #{entity.topOrderId,jdbcType=VARCHAR},
        #{entity.lowerOrderId,jdbcType=VARCHAR},
        #{entity.bottomOrderId,jdbcType=VARCHAR},
        #{entity.batchSplit,jdbcType=VARCHAR},
        #{entity.quantity,jdbcType=VARCHAR},
        #{entity.amount,jdbcType=VARCHAR},
        #{entity.orderTime,jdbcType=TIMESTAMP},
        #{entity.dueDate,jdbcType=TIMESTAMP},
        #{entity.delayPenalty,jdbcType=VARCHAR},
        #{entity.orderStatus,jdbcType=VARCHAR},
        #{entity.priority,jdbcType=INTEGER},
        #{entity.delayStatus,jdbcType=VARCHAR},
        #{entity.fixed,jdbcType=VARCHAR},
        #{entity.feedbackStatus,jdbcType=VARCHAR},
        #{entity.overProduction,jdbcType=VARCHAR},
        #{entity.resyncAllowed,jdbcType=VARCHAR},
        #{entity.planStatus,jdbcType=VARCHAR},
        #{entity.kitStatus,jdbcType=VARCHAR},
        #{entity.syncStatus,jdbcType=VARCHAR},
        #{entity.startTime,jdbcType=TIMESTAMP},
        #{entity.endTime,jdbcType=TIMESTAMP},
        #{entity.earliestStartTime,jdbcType=TIMESTAMP},
        #{entity.calcEarliestStartTime,jdbcType=TIMESTAMP},
        #{entity.calcLatestEndTime,jdbcType=TIMESTAMP},
        #{entity.plannable,jdbcType=VARCHAR},
        #{entity.appointPlannable,jdbcType=VARCHAR},
        #{entity.routingId,jdbcType=VARCHAR},
        #{entity.appointRoutingId,jdbcType=VARCHAR},
        #{entity.routingType,jdbcType=VARCHAR},
        #{entity.leadTime,jdbcType=INTEGER},
        #{entity.appointLeadTime,jdbcType=INTEGER},
        #{entity.appointCustomerId,jdbcType=VARCHAR},
        #{entity.appointDemandType,jdbcType=VARCHAR},
        #{entity.appointDemandOrderId,jdbcType=VARCHAR},
        #{entity.appointCustomerOrderId,jdbcType=VARCHAR},
        #{entity.appointParentWorkOrderId,jdbcType=VARCHAR},
        #{entity.syncFailureReason,jdbcType=VARCHAR},
        #{entity.stockingQuantity,jdbcType=VARCHAR},
        #{entity.plannedProductionDate,jdbcType=TIMESTAMP},
        #{entity.plannedOutputDate,jdbcType=TIMESTAMP},
        #{entity.fulfilledQuantity,jdbcType=VARCHAR},
        #{entity.fulfillmentInfo,jdbcType=VARCHAR},
        #{entity.unfulfilledQuantity,jdbcType=VARCHAR},
        #{entity.fulfillmentStatus,jdbcType=VARCHAR},
        #{entity.countingUnitId,jdbcType=VARCHAR},
        #{entity.currencyUnitId,jdbcType=VARCHAR},
        #{entity.participateScheduling,jdbcType=VARCHAR},
        #{entity.fixedQuantityStatus,jdbcType=VARCHAR},
        #{entity.quantityConsistent,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.operationConflict,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.bomType,jdbcType=VARCHAR},
        #{entity.plannedProductionPlaceId,jdbcType=VARCHAR},
        #{entity.plannedTime,jdbcType=TIMESTAMP},
        #{entity.dispatchPlaceId,jdbcType=VARCHAR},
        #{entity.endingInventoryMinSafeDiff,jdbcType=INTEGER},
        #{entity.demandCategory,jdbcType=VARCHAR},
        #{entity.id,jdbcType=VARCHAR},
        #{entity.publishedLogId,jdbcType=VARCHAR},
        #{entity.orderType,jdbcType=VARCHAR},
        #{entity.testOrderNumber,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mps.operationPublished.infrastructure.po.WorkOrderPublishedPO">
        update sds_ord_work_order_published set
        order_no = #{orderNo,jdbcType=VARCHAR},
        product_stock_point_id = #{productStockPointId,jdbcType=VARCHAR},
        stock_point_id = #{stockPointId,jdbcType=VARCHAR},
        product_id = #{productId,jdbcType=VARCHAR},
        organization_id = #{organizationId,jdbcType=VARCHAR},
        parent_id = #{parentId,jdbcType=VARCHAR},
        bom_version_id = #{bomVersionId,jdbcType=VARCHAR},
        upper_order_id = #{upperOrderId,jdbcType=VARCHAR},
        top_order_id = #{topOrderId,jdbcType=VARCHAR},
        lower_order_id = #{lowerOrderId,jdbcType=VARCHAR},
        bottom_order_id = #{bottomOrderId,jdbcType=VARCHAR},
        batch_split = #{batchSplit,jdbcType=VARCHAR},
        quantity = #{quantity,jdbcType=VARCHAR},
        amount = #{amount,jdbcType=VARCHAR},
        order_time = #{orderTime,jdbcType=TIMESTAMP},
        due_date = #{dueDate,jdbcType=TIMESTAMP},
        delay_penalty = #{delayPenalty,jdbcType=VARCHAR},
        order_status = #{orderStatus,jdbcType=VARCHAR},
        priority = #{priority,jdbcType=INTEGER},
        delay_status = #{delayStatus,jdbcType=VARCHAR},
        fixed = #{fixed,jdbcType=VARCHAR},
        feedback_status = #{feedbackStatus,jdbcType=VARCHAR},
        over_production = #{overProduction,jdbcType=VARCHAR},
        resync_allowed = #{resyncAllowed,jdbcType=VARCHAR},
        plan_status = #{planStatus,jdbcType=VARCHAR},
        kit_status = #{kitStatus,jdbcType=VARCHAR},
        sync_status = #{syncStatus,jdbcType=VARCHAR},
        start_time = #{startTime,jdbcType=TIMESTAMP},
        end_time = #{endTime,jdbcType=TIMESTAMP},
        earliest_start_time = #{earliestStartTime,jdbcType=TIMESTAMP},
        calc_earliest_start_time = #{calcEarliestStartTime,jdbcType=TIMESTAMP},
        calc_latest_end_time = #{calcLatestEndTime,jdbcType=TIMESTAMP},
        plannable = #{plannable,jdbcType=VARCHAR},
        appoint_plannable = #{appointPlannable,jdbcType=VARCHAR},
        routing_id = #{routingId,jdbcType=VARCHAR},
        appoint_routing_id = #{appointRoutingId,jdbcType=VARCHAR},
        routing_type = #{routingType,jdbcType=VARCHAR},
        lead_time = #{leadTime,jdbcType=INTEGER},
        appoint_lead_time = #{appointLeadTime,jdbcType=INTEGER},
        appoint_customer_id = #{appointCustomerId,jdbcType=VARCHAR},
        appoint_demand_type = #{appointDemandType,jdbcType=VARCHAR},
        appoint_demand_order_id = #{appointDemandOrderId,jdbcType=VARCHAR},
        appoint_customer_order_id = #{appointCustomerOrderId,jdbcType=VARCHAR},
        appoint_parent_work_order_id = #{appointParentWorkOrderId,jdbcType=VARCHAR},
        sync_failure_reason = #{syncFailureReason,jdbcType=VARCHAR},
        stocking_quantity = #{stockingQuantity,jdbcType=VARCHAR},
        planned_production_date = #{plannedProductionDate,jdbcType=TIMESTAMP},
        planned_output_date = #{plannedOutputDate,jdbcType=TIMESTAMP},
        fulfilled_quantity = #{fulfilledQuantity,jdbcType=VARCHAR},
        fulfillment_info = #{fulfillmentInfo,jdbcType=VARCHAR},
        unfulfilled_quantity = #{unfulfilledQuantity,jdbcType=VARCHAR},
        fulfillment_status = #{fulfillmentStatus,jdbcType=VARCHAR},
        counting_unit_id = #{countingUnitId,jdbcType=VARCHAR},
        currency_unit_id = #{currencyUnitId,jdbcType=VARCHAR},
        participate_scheduling = #{participateScheduling,jdbcType=VARCHAR},
        fixed_quantity_status = #{fixedQuantityStatus,jdbcType=VARCHAR},
        quantity_consistent = #{quantityConsistent,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        operation_conflict = #{operationConflict,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        bom_type = #{bomType,jdbcType=VARCHAR},
        planned_production_place_Id = #{plannedProductionPlaceId,jdbcType=VARCHAR},
        planned_time = #{plannedTime,jdbcType=TIMESTAMP},
        dispatch_place_id = #{dispatchPlaceId,jdbcType=VARCHAR},
        ending_inventory_min_safe_diff = #{endingInventoryMinSafeDiff,jdbcType=INTEGER},
        demand_category = #{demandCategory,jdbcType=VARCHAR},
        id = #{id,jdbcType=VARCHAR},
        published_log_id = #{publishedLogId,jdbcType=VARCHAR},
        order_type      = #{orderType,jdbcType=VARCHAR},
        test_order_number      = #{testOrderNumber,jdbcType=VARCHAR}
        where new_id = #{newId,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mps.operationPublished.infrastructure.po.WorkOrderPublishedPO">
        update sds_ord_work_order_published
        <set>
            <if test="item.orderNo != null and item.orderNo != ''">
                order_no = #{item.orderNo,jdbcType=VARCHAR},
            </if>
            <if test="item.productStockPointId != null and item.productStockPointId != ''">
                product_stock_point_id = #{item.productStockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointId != null and item.stockPointId != ''">
                stock_point_id = #{item.stockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.productId != null and item.productId != ''">
                product_id = #{item.productId,jdbcType=VARCHAR},
            </if>
            <if test="item.organizationId != null and item.organizationId != ''">
                organization_id = #{item.organizationId,jdbcType=VARCHAR},
            </if>
            <if test="item.parentId != null and item.parentId != ''">
                parent_id = #{item.parentId,jdbcType=VARCHAR},
            </if>
            <if test="item.bomVersionId != null and item.bomVersionId != ''">
                bom_version_id = #{item.bomVersionId,jdbcType=VARCHAR},
            </if>
            <if test="item.upperOrderId != null and item.upperOrderId != ''">
                upper_order_id = #{item.upperOrderId,jdbcType=VARCHAR},
            </if>
            <if test="item.topOrderId != null and item.topOrderId != ''">
                top_order_id = #{item.topOrderId,jdbcType=VARCHAR},
            </if>
            <if test="item.lowerOrderId != null and item.lowerOrderId != ''">
                lower_order_id = #{item.lowerOrderId,jdbcType=VARCHAR},
            </if>
            <if test="item.bottomOrderId != null and item.bottomOrderId != ''">
                bottom_order_id = #{item.bottomOrderId,jdbcType=VARCHAR},
            </if>
            <if test="item.batchSplit != null and item.batchSplit != ''">
                batch_split = #{item.batchSplit,jdbcType=VARCHAR},
            </if>
            <if test="item.quantity != null">
                quantity = #{item.quantity,jdbcType=VARCHAR},
            </if>
            <if test="item.amount != null">
                amount = #{item.amount,jdbcType=VARCHAR},
            </if>
            <if test="item.orderTime != null">
                order_time = #{item.orderTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.dueDate != null">
                due_date = #{item.dueDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.delayPenalty != null">
                delay_penalty = #{item.delayPenalty,jdbcType=VARCHAR},
            </if>
            <if test="item.orderStatus != null and item.orderStatus != ''">
                order_status = #{item.orderStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.priority != null">
                priority = #{item.priority,jdbcType=INTEGER},
            </if>
            <if test="item.delayStatus != null and item.delayStatus != ''">
                delay_status = #{item.delayStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.fixed != null and item.fixed != ''">
                fixed = #{item.fixed,jdbcType=VARCHAR},
            </if>
            <if test="item.feedbackStatus != null and item.feedbackStatus != ''">
                feedback_status = #{item.feedbackStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.overProduction != null and item.overProduction != ''">
                over_production = #{item.overProduction,jdbcType=VARCHAR},
            </if>
            <if test="item.resyncAllowed != null and item.resyncAllowed != ''">
                resync_allowed = #{item.resyncAllowed,jdbcType=VARCHAR},
            </if>
            <if test="item.planStatus != null and item.planStatus != ''">
                plan_status = #{item.planStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.kitStatus != null and item.kitStatus != ''">
                kit_status = #{item.kitStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.syncStatus != null and item.syncStatus != ''">
                sync_status = #{item.syncStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.startTime != null">
                start_time = #{item.startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.endTime != null">
                end_time = #{item.endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.earliestStartTime != null">
                earliest_start_time = #{item.earliestStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.calcEarliestStartTime != null">
                calc_earliest_start_time = #{item.calcEarliestStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.calcLatestEndTime != null">
                calc_latest_end_time = #{item.calcLatestEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.plannable != null and item.plannable != ''">
                plannable = #{item.plannable,jdbcType=VARCHAR},
            </if>
            <if test="item.appointPlannable != null and item.appointPlannable != ''">
                appoint_plannable = #{item.appointPlannable,jdbcType=VARCHAR},
            </if>
            <if test="item.routingId != null and item.routingId != ''">
                routing_id = #{item.routingId,jdbcType=VARCHAR},
            </if>
            <if test="item.appointRoutingId != null and item.appointRoutingId != ''">
                appoint_routing_id = #{item.appointRoutingId,jdbcType=VARCHAR},
            </if>
            <if test="item.routingType != null and item.routingType != ''">
                routing_type = #{item.routingType,jdbcType=VARCHAR},
            </if>
            <if test="item.leadTime != null">
                lead_time = #{item.leadTime,jdbcType=INTEGER},
            </if>
            <if test="item.appointLeadTime != null">
                appoint_lead_time = #{item.appointLeadTime,jdbcType=INTEGER},
            </if>
            <if test="item.appointCustomerId != null and item.appointCustomerId != ''">
                appoint_customer_id = #{item.appointCustomerId,jdbcType=VARCHAR},
            </if>
            <if test="item.appointDemandType != null and item.appointDemandType != ''">
                appoint_demand_type = #{item.appointDemandType,jdbcType=VARCHAR},
            </if>
            <if test="item.appointDemandOrderId != null and item.appointDemandOrderId != ''">
                appoint_demand_order_id = #{item.appointDemandOrderId,jdbcType=VARCHAR},
            </if>
            <if test="item.appointCustomerOrderId != null and item.appointCustomerOrderId != ''">
                appoint_customer_order_id = #{item.appointCustomerOrderId,jdbcType=VARCHAR},
            </if>
            <if test="item.appointParentWorkOrderId != null and item.appointParentWorkOrderId != ''">
                appoint_parent_work_order_id = #{item.appointParentWorkOrderId,jdbcType=VARCHAR},
            </if>
            <if test="item.syncFailureReason != null and item.syncFailureReason != ''">
                sync_failure_reason = #{item.syncFailureReason,jdbcType=VARCHAR},
            </if>
            <if test="item.stockingQuantity != null">
                stocking_quantity = #{item.stockingQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.plannedProductionDate != null">
                planned_production_date = #{item.plannedProductionDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.plannedOutputDate != null">
                planned_output_date = #{item.plannedOutputDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.fulfilledQuantity != null">
                fulfilled_quantity = #{item.fulfilledQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.fulfillmentInfo != null and item.fulfillmentInfo != ''">
                fulfillment_info = #{item.fulfillmentInfo,jdbcType=VARCHAR},
            </if>
            <if test="item.unfulfilledQuantity != null">
                unfulfilled_quantity = #{item.unfulfilledQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.fulfillmentStatus != null and item.fulfillmentStatus != ''">
                fulfillment_status = #{item.fulfillmentStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.countingUnitId != null and item.countingUnitId != ''">
                counting_unit_id = #{item.countingUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.currencyUnitId != null and item.currencyUnitId != ''">
                currency_unit_id = #{item.currencyUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.participateScheduling != null and item.participateScheduling != ''">
                participate_scheduling = #{item.participateScheduling,jdbcType=VARCHAR},
            </if>
            <if test="item.fixedQuantityStatus != null and item.fixedQuantityStatus != ''">
                fixed_quantity_status = #{item.fixedQuantityStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.quantityConsistent != null and item.quantityConsistent != ''">
                quantity_consistent = #{item.quantityConsistent,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.operationConflict != null and item.operationConflict != ''">
                operation_conflict = #{item.operationConflict,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.bomType != null and item.bomType != ''">
                bom_type = #{item.bomType,jdbcType=VARCHAR},
            </if>
            <if test="item.plannedProductionPlaceId != null and item.plannedProductionPlaceId != ''">
                planned_production_place_Id = #{item.plannedProductionPlaceId,jdbcType=VARCHAR},
            </if>
            <if test="item.plannedTime != null">
                planned_time = #{item.plannedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.dispatchPlaceId != null and item.dispatchPlaceId != ''">
                dispatch_place_id = #{item.dispatchPlaceId,jdbcType=VARCHAR},
            </if>
            <if test="item.endingInventoryMinSafeDiff != null">
                ending_inventory_min_safe_diff = #{item.endingInventoryMinSafeDiff,jdbcType=INTEGER},
            </if>
            <if test="item.demandCategory != null and item.demandCategory != ''">
                demand_category = #{item.demandCategory,jdbcType=VARCHAR},
            </if>
            <if test="item.id != null and item.id != ''">
                id = #{item.id,jdbcType=VARCHAR},
            </if>
            <if test="item.publishedLogId != null and item.publishedLogId != ''">
                published_log_id = #{item.publishedLogId,jdbcType=VARCHAR},
            </if>
            <if test="item.orderType != null and item.orderType != ''">
	            order_type = #{item.orderType,jdbcType=VARCHAR},
	        </if>
	        <if test="item.testOrderNumber != null and item.testOrderNumber != ''">
	            test_order_number = #{item.testOrderNumber,jdbcType=VARCHAR},
	        </if>
        </set>
        where new_id = #{item.newId,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update sds_ord_work_order_published
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="order_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.orderNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_stock_point_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.productStockPointId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.stockPointId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.productId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="organization_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.organizationId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="parent_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.parentId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="bom_version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.bomVersionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="upper_order_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.upperOrderId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="top_order_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.topOrderId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="lower_order_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.lowerOrderId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="bottom_order_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.bottomOrderId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="batch_split = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.batchSplit,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.quantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="amount = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.amount,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="order_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.orderTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="due_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.dueDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="delay_penalty = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.delayPenalty,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="order_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.orderStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="priority = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.priority,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="delay_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.delayStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="fixed = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.fixed,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="feedback_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.feedbackStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="over_production = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.overProduction,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="resync_allowed = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.resyncAllowed,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="plan_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.planStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="kit_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.kitStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sync_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.syncStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.startTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="end_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.endTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="earliest_start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.earliestStartTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="calc_earliest_start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.calcEarliestStartTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="calc_latest_end_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.calcLatestEndTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="plannable = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.plannable,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="appoint_plannable = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.appointPlannable,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="routing_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.routingId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="appoint_routing_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.appointRoutingId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="routing_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.routingType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="lead_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.leadTime,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="appoint_lead_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.appointLeadTime,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="appoint_customer_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.appointCustomerId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="appoint_demand_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.appointDemandType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="appoint_demand_order_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.appointDemandOrderId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="appoint_customer_order_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.appointCustomerOrderId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="appoint_parent_work_order_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.appointParentWorkOrderId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sync_failure_reason = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.syncFailureReason,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stocking_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.stockingQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="planned_production_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.plannedProductionDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="planned_output_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.plannedOutputDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="fulfilled_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.fulfilledQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="fulfillment_info = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.fulfillmentInfo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="unfulfilled_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.unfulfilledQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="fulfillment_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.fulfillmentStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="counting_unit_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.countingUnitId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="currency_unit_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.currencyUnitId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="participate_scheduling = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.participateScheduling,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="fixed_quantity_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.fixedQuantityStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="quantity_consistent = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.quantityConsistent,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="operation_conflict = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.operationConflict,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="bom_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.bomType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="planned_production_place_Id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.plannedProductionPlaceId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="planned_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.plannedTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="dispatch_place_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.dispatchPlaceId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="ending_inventory_min_safe_diff = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.endingInventoryMinSafeDiff,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="demand_category = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.demandCategory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.id,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="published_log_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.publishedLogId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="order_type = case" suffix="end,">
	            <foreach collection="list" index="index" item="item">
	                when id = #{item.id,jdbcType=VARCHAR} then #{item.orderType,jdbcType=VARCHAR}
	            </foreach>
	        </trim>
	        <trim prefix="test_order_number = case" suffix="end,">
	            <foreach collection="list" index="index" item="item">
	                when id = #{item.id,jdbcType=VARCHAR} then #{item.testOrderNumber,jdbcType=VARCHAR}
	            </foreach>
	        </trim>
        </trim>
        where new_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.newId,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update sds_ord_work_order_published
        <set>
            <if test="item.orderNo != null and item.orderNo != ''">
                order_no = #{item.orderNo,jdbcType=VARCHAR},
            </if>
            <if test="item.productStockPointId != null and item.productStockPointId != ''">
                product_stock_point_id = #{item.productStockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointId != null and item.stockPointId != ''">
                stock_point_id = #{item.stockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.productId != null and item.productId != ''">
                product_id = #{item.productId,jdbcType=VARCHAR},
            </if>
            <if test="item.organizationId != null and item.organizationId != ''">
                organization_id = #{item.organizationId,jdbcType=VARCHAR},
            </if>
            <if test="item.parentId != null and item.parentId != ''">
                parent_id = #{item.parentId,jdbcType=VARCHAR},
            </if>
            <if test="item.bomVersionId != null and item.bomVersionId != ''">
                bom_version_id = #{item.bomVersionId,jdbcType=VARCHAR},
            </if>
            <if test="item.upperOrderId != null and item.upperOrderId != ''">
                upper_order_id = #{item.upperOrderId,jdbcType=VARCHAR},
            </if>
            <if test="item.topOrderId != null and item.topOrderId != ''">
                top_order_id = #{item.topOrderId,jdbcType=VARCHAR},
            </if>
            <if test="item.lowerOrderId != null and item.lowerOrderId != ''">
                lower_order_id = #{item.lowerOrderId,jdbcType=VARCHAR},
            </if>
            <if test="item.bottomOrderId != null and item.bottomOrderId != ''">
                bottom_order_id = #{item.bottomOrderId,jdbcType=VARCHAR},
            </if>
            <if test="item.batchSplit != null and item.batchSplit != ''">
                batch_split = #{item.batchSplit,jdbcType=VARCHAR},
            </if>
            <if test="item.quantity != null">
                quantity = #{item.quantity,jdbcType=VARCHAR},
            </if>
            <if test="item.amount != null">
                amount = #{item.amount,jdbcType=VARCHAR},
            </if>
            <if test="item.orderTime != null">
                order_time = #{item.orderTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.dueDate != null">
                due_date = #{item.dueDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.delayPenalty != null">
                delay_penalty = #{item.delayPenalty,jdbcType=VARCHAR},
            </if>
            <if test="item.orderStatus != null and item.orderStatus != ''">
                order_status = #{item.orderStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.priority != null">
                priority = #{item.priority,jdbcType=INTEGER},
            </if>
            <if test="item.delayStatus != null and item.delayStatus != ''">
                delay_status = #{item.delayStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.fixed != null and item.fixed != ''">
                fixed = #{item.fixed,jdbcType=VARCHAR},
            </if>
            <if test="item.feedbackStatus != null and item.feedbackStatus != ''">
                feedback_status = #{item.feedbackStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.overProduction != null and item.overProduction != ''">
                over_production = #{item.overProduction,jdbcType=VARCHAR},
            </if>
            <if test="item.resyncAllowed != null and item.resyncAllowed != ''">
                resync_allowed = #{item.resyncAllowed,jdbcType=VARCHAR},
            </if>
            <if test="item.planStatus != null and item.planStatus != ''">
                plan_status = #{item.planStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.kitStatus != null and item.kitStatus != ''">
                kit_status = #{item.kitStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.syncStatus != null and item.syncStatus != ''">
                sync_status = #{item.syncStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.startTime != null">
                start_time = #{item.startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.endTime != null">
                end_time = #{item.endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.earliestStartTime != null">
                earliest_start_time = #{item.earliestStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.calcEarliestStartTime != null">
                calc_earliest_start_time = #{item.calcEarliestStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.calcLatestEndTime != null">
                calc_latest_end_time = #{item.calcLatestEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.plannable != null and item.plannable != ''">
                plannable = #{item.plannable,jdbcType=VARCHAR},
            </if>
            <if test="item.appointPlannable != null and item.appointPlannable != ''">
                appoint_plannable = #{item.appointPlannable,jdbcType=VARCHAR},
            </if>
            <if test="item.routingId != null and item.routingId != ''">
                routing_id = #{item.routingId,jdbcType=VARCHAR},
            </if>
            <if test="item.appointRoutingId != null and item.appointRoutingId != ''">
                appoint_routing_id = #{item.appointRoutingId,jdbcType=VARCHAR},
            </if>
            <if test="item.routingType != null and item.routingType != ''">
                routing_type = #{item.routingType,jdbcType=VARCHAR},
            </if>
            <if test="item.leadTime != null">
                lead_time = #{item.leadTime,jdbcType=INTEGER},
            </if>
            <if test="item.appointLeadTime != null">
                appoint_lead_time = #{item.appointLeadTime,jdbcType=INTEGER},
            </if>
            <if test="item.appointCustomerId != null and item.appointCustomerId != ''">
                appoint_customer_id = #{item.appointCustomerId,jdbcType=VARCHAR},
            </if>
            <if test="item.appointDemandType != null and item.appointDemandType != ''">
                appoint_demand_type = #{item.appointDemandType,jdbcType=VARCHAR},
            </if>
            <if test="item.appointDemandOrderId != null and item.appointDemandOrderId != ''">
                appoint_demand_order_id = #{item.appointDemandOrderId,jdbcType=VARCHAR},
            </if>
            <if test="item.appointCustomerOrderId != null and item.appointCustomerOrderId != ''">
                appoint_customer_order_id = #{item.appointCustomerOrderId,jdbcType=VARCHAR},
            </if>
            <if test="item.appointParentWorkOrderId != null and item.appointParentWorkOrderId != ''">
                appoint_parent_work_order_id = #{item.appointParentWorkOrderId,jdbcType=VARCHAR},
            </if>
            <if test="item.syncFailureReason != null and item.syncFailureReason != ''">
                sync_failure_reason = #{item.syncFailureReason,jdbcType=VARCHAR},
            </if>
            <if test="item.stockingQuantity != null">
                stocking_quantity = #{item.stockingQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.plannedProductionDate != null">
                planned_production_date = #{item.plannedProductionDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.plannedOutputDate != null">
                planned_output_date = #{item.plannedOutputDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.fulfilledQuantity != null">
                fulfilled_quantity = #{item.fulfilledQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.fulfillmentInfo != null and item.fulfillmentInfo != ''">
                fulfillment_info = #{item.fulfillmentInfo,jdbcType=VARCHAR},
            </if>
            <if test="item.unfulfilledQuantity != null">
                unfulfilled_quantity = #{item.unfulfilledQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.fulfillmentStatus != null and item.fulfillmentStatus != ''">
                fulfillment_status = #{item.fulfillmentStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.countingUnitId != null and item.countingUnitId != ''">
                counting_unit_id = #{item.countingUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.currencyUnitId != null and item.currencyUnitId != ''">
                currency_unit_id = #{item.currencyUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.participateScheduling != null and item.participateScheduling != ''">
                participate_scheduling = #{item.participateScheduling,jdbcType=VARCHAR},
            </if>
            <if test="item.fixedQuantityStatus != null and item.fixedQuantityStatus != ''">
                fixed_quantity_status = #{item.fixedQuantityStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.quantityConsistent != null and item.quantityConsistent != ''">
                quantity_consistent = #{item.quantityConsistent,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.operationConflict != null and item.operationConflict != ''">
                operation_conflict = #{item.operationConflict,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.bomType != null and item.bomType != ''">
                bom_type = #{item.bomType,jdbcType=VARCHAR},
            </if>
            <if test="item.plannedProductionPlaceId != null and item.plannedProductionPlaceId != ''">
                planned_production_place_Id = #{item.plannedProductionPlaceId,jdbcType=VARCHAR},
            </if>
            <if test="item.plannedTime != null">
                planned_time = #{item.plannedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.dispatchPlaceId != null and item.dispatchPlaceId != ''">
                dispatch_place_id = #{item.dispatchPlaceId,jdbcType=VARCHAR},
            </if>
            <if test="item.endingInventoryMinSafeDiff != null">
                ending_inventory_min_safe_diff = #{item.endingInventoryMinSafeDiff,jdbcType=INTEGER},
            </if>
            <if test="item.demandCategory != null and item.demandCategory != ''">
                demand_category = #{item.demandCategory,jdbcType=VARCHAR},
            </if>
            <if test="item.id != null and item.id != ''">
                id = #{item.id,jdbcType=VARCHAR},
            </if>
            <if test="item.publishedLogId != null and item.publishedLogId != ''">
                published_log_id = #{item.publishedLogId,jdbcType=VARCHAR},
            </if>
            <if test="item.orderType != null and item.orderType != ''">
	            order_type = #{item.orderType,jdbcType=VARCHAR},
	        </if>
	        <if test="item.testOrderNumber != null and item.testOrderNumber != ''">
	            test_order_number = #{item.testOrderNumber,jdbcType=VARCHAR},
	        </if>
        </set>
        where new_id = #{item.newId,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from sds_ord_work_order_published where new_id = #{newId,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from sds_ord_work_order_published where new_id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <update id="truncateTable">
        TRUNCATE TABLE sds_ord_work_order_published
    </update>

    <insert id="doSnapshotData">
	 INSERT INTO sds_ord_work_order_published (
			new_id,
			id,
			order_no,
			product_stock_point_id,
			stock_point_id,
			product_id,
			organization_id,
			parent_id,
			bom_version_id,
			upper_order_id,
			top_order_id,
			lower_order_id,
			bottom_order_id,
			batch_split,
			quantity,
			amount,
			order_time,
			due_date,
			delay_penalty,
			order_status,
			priority,
			delay_status,
			fixed,
			feedback_status,
			over_production,
			resync_allowed,
			plan_status,
			kit_status,
			sync_status,
			start_time,
			end_time,
			earliest_start_time,
			calc_earliest_start_time,
			calc_latest_end_time,
			plannable,
			appoint_plannable,
			routing_id,
			appoint_routing_id,
			routing_type,
			lead_time,
			appoint_lead_time,
			appoint_customer_id,
			appoint_demand_type,
			appoint_demand_order_id,
			appoint_customer_order_id,
			appoint_parent_work_order_id,
			sync_failure_reason,
			stocking_quantity,
			planned_production_date,
			planned_output_date,
			fulfilled_quantity,
			fulfillment_info,
			unfulfilled_quantity,
			fulfillment_status,
			counting_unit_id,
			currency_unit_id,
			participate_scheduling,
			fixed_quantity_status,
			quantity_consistent,
			remark,
			enabled,
			operation_conflict,
			creator,
			create_time,
			modifier,
			modify_time,
			bom_type,
			planned_production_place_Id,
			planned_time,
			dispatch_place_id,
			ending_inventory_min_safe_diff,
			demand_category,
			published_log_id,
	        order_type,
	        test_order_number
		)
		SELECT
			CONCAT(id, ${publishedTime}) as new_id,
			id as id,
			order_no,
			product_stock_point_id,
			stock_point_id,
			product_id,
			organization_id,
			parent_id,
			bom_version_id,
			upper_order_id,
			top_order_id,
			lower_order_id,
			bottom_order_id,
			batch_split,
			quantity,
			amount,
			order_time,
			due_date,
			delay_penalty,
			order_status,
			priority,
			delay_status,
			fixed,
			feedback_status,
			over_production,
			resync_allowed,
			plan_status,
			kit_status,
			sync_status,
			start_time,
			end_time,
			earliest_start_time,
			calc_earliest_start_time,
			calc_latest_end_time,
			plannable,
			appoint_plannable,
			routing_id,
			appoint_routing_id,
			routing_type,
			lead_time,
			appoint_lead_time,
			appoint_customer_id,
			appoint_demand_type,
			appoint_demand_order_id,
			appoint_customer_order_id,
			appoint_parent_work_order_id,
			sync_failure_reason,
			stocking_quantity,
			planned_production_date,
			planned_output_date,
			fulfilled_quantity,
			fulfillment_info,
			unfulfilled_quantity,
			fulfillment_status,
			counting_unit_id,
			currency_unit_id,
			participate_scheduling,
			fixed_quantity_status,
			quantity_consistent,
			remark,
			enabled,
			operation_conflict,
			creator,
			create_time,
			modifier,
			modify_time,
			bom_type,
			planned_production_place_Id,
			planned_time,
			dispatch_place_id,
			ending_inventory_min_safe_diff,
			demand_category,
			${publishedLogId} AS published_log_id,
			order_type,
			test_order_number
		FROM
			sds_ord_work_order
	</insert>

	<delete id="deleteByPublishedLogId" parameterType="java.lang.String">
        delete from sds_ord_work_order_published where published_log_id = #{publishedLogId,jdbcType=VARCHAR}
    </delete>

    <select id="selectAllCount" resultType="java.lang.Integer">
        select
        	count(*)
        from sds_ord_work_order
    </select>

    <sql id="Base_Column_List_Work_Order">
        <!--项目自定义字段,参考示例,逗号不能缺失-->
        <include refid="com.yhl.scp.sds.order.infrastructure.dao.WorkOrderDao.VO_Column_List"/>
        <!--,new_column-->
        ,ending_inventory_min_safe_diff,demand_category,order_type,test_order_number
    </sql>

    <select id="selectUnFinishedOrder" resultMap="POResultMap">
        SELECT
			*
		FROM
			sds_ord_work_order
		where plan_status != 'FINISHED'
		and product_id in
		<foreach collection="productIds" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <delete id="deleteByPublishedLogIds">
        delete
        from
        	sds_ord_work_order_published
    	where
    		published_log_id in
    		<foreach collection="publishedLogIds" item="item" index="index" open="(" separator="," close=")">
            	#{item,jdbcType=VARCHAR}
        	</foreach>
    </delete>

</mapper>
