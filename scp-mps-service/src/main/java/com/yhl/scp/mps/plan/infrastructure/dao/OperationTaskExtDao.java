package com.yhl.scp.mps.plan.infrastructure.dao;

import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataDetailVO;
import com.yhl.scp.dfp.material.vo.PartRiskLevelVO;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordVO;
import com.yhl.scp.mds.curingTime.vo.MasterPlanCuringTimeVO;
import com.yhl.scp.mds.extension.calendar.vo.ResourceCalendarVO;
import com.yhl.scp.mds.extension.organization.vo.ProductionOrganizationVO;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.resource.vo.StandardResourceVO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.productBox.vo.ProductBoxRelationVO;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.algorithm.schedule.output.RzzOperationDTO;
import com.yhl.scp.mps.demand.dto.OperationEarlyWarningDTO;
import com.yhl.scp.mps.demand.vo.OperationEarilWarningInfoVO;
import com.yhl.scp.mps.plan.req.MasterPlanReq;
import com.yhl.scp.mps.plan.vo.MasterPlanTaskVO;
import com.yhl.scp.mps.plan.vo.OperationDemandVO;
import com.yhl.scp.mps.plan.vo.OperationPlanVO;
import com.yhl.scp.sds.basic.feedback.infrastructure.po.FeedbackProductionBasicPO;
import com.yhl.scp.sds.extension.order.infrastructure.po.OperationExtendPO;
import com.yhl.scp.sds.extension.order.infrastructure.po.OperationPO;
import com.yhl.scp.sds.extension.order.infrastructure.po.OperationTaskPO;
import com.yhl.scp.sds.extension.order.infrastructure.po.WorkOrderPO;
import com.yhl.scp.sds.extension.order.vo.OperationInputVO;
import com.yhl.scp.sds.extension.order.vo.OperationTaskVO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import com.yhl.scp.sds.extension.pegging.vo.DemandVO;
import com.yhl.scp.sds.extension.pegging.vo.SupplyVO;
import com.yhl.scp.sds.order.infrastructure.dao.OperationTaskDao;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface OperationTaskExtDao extends OperationTaskDao {

    /**
     * 主生产计划查询
     *
     * @param masterPlanReq req
     * @return OperationTaskPO {@link OperationTaskPO}
     */
    List<MasterPlanTaskVO> selectByMasterReq(@Param("masterPlanReq") MasterPlanReq masterPlanReq);

    /**
     * 主生产计划-制造订单查询
     *
     * @param masterPlanReq
     * @return
     */
    List<WorkOrderPO> selectWorkOrderByMasterReq(@Param("masterPlanReq") MasterPlanReq masterPlanReq);

    /**
     * 根据关键工序的subOperationId查询它对应工单下所以subOperation
     *
     * @param ids
     * @return
     */
    List<OperationVO> selectSubOperationById(@Param("ids") List<String> ids);

    List<OperationPlanVO> selectUnPlanOperation(@Param("masterPlanReq") MasterPlanReq masterPlanReq);

    /**
     * 查询关键工序对应资源
     *
     * @return
     */
    List<PhysicalResourceVO> selectPhysicalResourceVO();

    List<StandardResourceVO> selectStandardResource(@Param("organizationCode") String organizationCode, @Param("productionPlanner") String productionPlanner);

    List<PhysicalResourceVO> selectPhysicalResource(@Param("organizationCode") String organizationCode, @Param("standardResourceId") String standardResourceId, @Param("standardResourceCode") String standardResourceCode);

    List<PhysicalResourceVO> selectPhysicalResourceByPlanner(@Param("productionPlanner") String productionPlanner);

    /**
     * 查询指定时间是否有工序任务
     *
     * @param time
     * @return
     */
    List<OperationTaskPO> selectOperationTaskVOByTime(@Param("time") String time, @Param("resourceId") String resourceId);

    /**
     * 查询指定时间是否有工单任务
     *
     * @param time
     * @param productCode
     * @return
     */
    List<WorkOrderPO> selectWorkOrderVOByTime(@Param("time") Date time, @Param("productCode") String productCode);

    /**
     * 查询资源在该时间段内是否有工序任务(异常报工，工序顺延)
     *
     * @param resourceId
     * @param startTime
     * @param endTime
     * @return
     */
    OperationTaskPO selectTimeIntervalOperationTask(@Param("resourceId") String resourceId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询主计划（工序异常报警）
     *
     * @param dto
     * @return
     */
    List<OperationEarilWarningInfoVO> queryOperationList(@Param("params") OperationEarlyWarningDTO dto, @Param("userId") String userId);

    /**
     * 通过制造订单ID查询所有父工序id
     *
     * @param orderIds
     * @return
     */
    List<OperationEarilWarningInfoVO> queryOperationListByOrderIds(@Param("orderIds") List<String> orderIds);

    List<WarehouseReleaseRecordVO> selectWarehouseReleaseRecord(@Param("productCodeList") List<String> productCodeList);

    List<PartRiskLevelVO> selectPartRiskLevels(@Param("productCodeList") List<String> productCodeList);

    List<ProductBoxRelationVO> selectBoxPieces(@Param("productCodes") List<String> productCodes);

    /**
     * 查询所有存在于operationTask表里的物理资源（手工调整算法使用）
     *
     * @return
     */
    List<String> selectPhysicalResourceByOperationTask();

    List<String> selectPhysicalResourceByOperationTaskByRoutingStepId(@Param("routingStepIds") List<String> routingStepIds, @Param("resourceCodeList") List<String> resourceCodeList);


    List<ProductionOrganizationVO> selectProductionOrganizationWithPermission(@Param("userId") String userId);

    /**
     * 查询所有类型是本厂，组织类型是生产组织的库存点
     *
     * @return
     */
    List<NewStockPointVO> selectBCStockPoint();

    List<OperationDemandVO> selectOperationDemandByOperationIds(@Param("operationIds") List<String> operationIds);

    List<MasterPlanTaskVO> selectByMasterReq2(@Param("masterPlanReq") MasterPlanReq masterPlanReq);

    List<OperationPlanVO> selectUnPlanOperation2(@Param("masterPlanReq") MasterPlanReq masterPlanReq);

    List<PhysicalResourceVO> selectPhysicalResourceByUserId(@Param("userId") String id);


    List<MasterPlanTaskVO> selectToolOperationTasks(@Param("operationIds") List<String> operationIds);

    List<MasterPlanTaskVO> selectPhysicalOperation2(@Param("operationIds") List<String> operationIds);

    /**
     * 根据制造时间范围筛选operationTask数据
     *
     * @param startDate
     * @param endDate
     * @param physicalResourceId
     * @return
     */
    OperationTaskVO selectLastOperationTaskBetweenDates(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("physicalResourceId") String physicalResourceId);

    List<String> getPlanWorkOrderIds(@Param("physicalResourceIds") List<String> physicalResourceIds);

    List<String> selectRunResources(@Param("resourceIds") List<String> runningResource);


    List<String> selectSaProductCodes(@Param("productCode") String productCode);

    List<NewProductStockPointVO> selectProductCascadeByIds(@Param("productIds") List<String> productIds);

    List<ResourceCalendarVO> selectResourceCalendarsByParams(@Param("params") Map<String, Object> params);

    int updateBatchOperations(@Param("list") List<OperationPO> operations);

    int updateBatchOperationExtend(@Param("list") List<OperationExtendPO> operationExtendDTOS);

    int updateBatchWorkOrder(@Param("list") List<WorkOrderPO> workOrderPOS);

    List<InventoryBatchDetailVO> selectCollectGroupByLoadingPosit();

    List<InventoryBatchDetailVO> selectCollectGroupByNoLoadingPosit();

    List<OperationInputVO> selectOperationInputsByParams(@Param("params") Map<String, Object> params);

    List<DemandVO> selectDemandVosByParams(@Param("params") Map<String, Object> params);

    List<SupplyVO> selectSupplyVosByParams(@Param("params") Map<String, Object> params);

    List<String> selectMainYztResource(@Param("code") String code);

    @Select("SELECT DISTINCT product_code from fdp_delivery_plan_published")
    List<String> selectAllDeliveryProduct();

    @Select("SELECT DISTINCT product_code from mds_product_stock_point_base where line_group is not null")
    List<String> selectAllLineGroupProduct();


    int updateBatchWorkOrderByCaseWhen(@Param("list") List<WorkOrderPO> workOrderPOS);

    List<StandardResourceVO> selectResourcePermissions(@Param("userId") String creator);

    List<PhysicalResourceVO> selectPhysicalResourcePermissions(@Param("userId") String creator, @Param("standardResourceIds") List<String> standardResourceIds);


    List<RzzOperationDTO> selectOperationInfoByWorkOrderIds(@Param("params") Map<String, Object> param);

    List<OperationVO> selectOperationByResourceIds(@Param("resourceIds") List<String> resourceIds);


    List<FeedbackProductionBasicPO> selectFeedBackQuantityByOperationIds(@Param("params") Map<String, Object> param);

    int deleteTaskByOperationIds(@Param("operationIds") List<String> operationIds);

    int deleteSubTaskByOperationIds(@Param("operationIds") List<String> operationIds);

    List<StandardStepVO> selectStandardStep();

    List<String> selectInfiniteCapacityResourceIds();

    List<String> selectKeyStepUnPlanOrderIds(@Param("allOrderIds") List<String> allOrderIds);

    List<String> selectPlannedOrderResource(@Param("productLineList") List<String> productLineList,
                                            @Param("plannedWorkOrderIds") List<String> plannedWorkOrderIds);

    List<String> selectFgProductCodes(@Param("productCode")String productCode);

    void updateOperationRemark(@Param("operationId") String operationId, @Param("remark") String remark);

    void updateOperationTaskRemark(@Param("operationTaskId") String operationTaskId, @Param("remark") String remark);

    List<ResourceCalendarVO> selectResourceCalendars(@Param("resourceIds") List<String> resourceIds);

    List<MasterPlanTaskVO> selectResourceTask(@Param("reportingStart") Date reportingStart, @Param("resourceId") String resourceId);

    void updateWorkOrderStatus(@Param("operationIds") List<String> operationIds);

    List<WorkOrderVO> selectWorkOrderWithParentProductId(@Param("workOrderIds") List<String> workOrderIds);

    NewStockPointVO selectBcSaleStockPoint();

    List<MasterPlanCuringTimeVO> selectBeforeCuringTime(@Param("stockPointCodes") Set<String> stockPointCodes, @Param("productCodes") Set<String> productCodes);

    List<MasterPlanCuringTimeVO> selectAfterCuringTime(@Param("stockPointCode") String stockPointCode, @Param("productCodes") Set<String> productCodes);

    String selectMaxVersionId(@Param("planPeriod") String planPeriod);

    List<ConsistenceDemandForecastDataDetailVO> selectMasterPlanConsistenceDemandForecastDataDetails(@Param("versionId") String versionId, @Param("forecastTime") String forecastTime, @Param("productCodes") Set<String> productCodes);

    List<WarehouseReleaseRecordVO> selectWarehouseReleaseRecordByTime(@Param("deliveryTime") String deliveryTime, @Param("productCodeList") Set<String> productCodeList);

    List<WorkOrderVO> selectByItemCodeAndPlantCode(@Param("productCodes") List<String> itemCodes);

    List<PhysicalResourceVO> selectPhysicalResourceByStockCodeList(@Param("stockPointCodeList") List<String> stockPointCodeList);

    List<PhysicalResourceVO> selectPhysicalResourceBySequenceList(@Param("valueMeaningList") List<String> valueMeaningList);

}