package com.yhl.scp.mps.plan.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.ddd.BasePO;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.BigDecimalUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.basic.routing.vo.RoutingBasicVO;
import com.yhl.scp.mds.basic.routing.vo.RoutingStepBasicVO;
import com.yhl.scp.mds.basic.routing.vo.StandardStepBasicVO;
import com.yhl.scp.mds.basic.rule.enums.RuleEncodingsEnum;
import com.yhl.scp.mds.enums.ProductTypeEnum;
import com.yhl.scp.mds.enums.StandardStepEnum;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.resource.vo.StandardResourceVO;
import com.yhl.scp.mds.extension.routing.domain.entity.RoutingDO;
import com.yhl.scp.mds.extension.routing.vo.BomRoutingStepInputVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingVO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.extension.rule.vo.RuleEncodingsVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.enums.PlanStatusEnum;
import com.yhl.scp.mps.feedback.req.FeedBackReq;
import com.yhl.scp.mps.plan.dto.MasterPlanInsertDTO;
import com.yhl.scp.mps.plan.infrastructure.dao.MasterPlanExtDao;
import com.yhl.scp.mps.plan.infrastructure.dao.OperationTaskExtDao;
import com.yhl.scp.mps.plan.req.MasterQuantityReq;
import com.yhl.scp.mps.plan.service.MasterPlanInsertOrEditService;
import com.yhl.scp.mps.plan.service.MasterPlanInsertService;
import com.yhl.scp.mps.plan.vo.MasterPlanTaskVO;
import com.yhl.scp.mps.plan.vo.WorkOrderYieldVO;
import com.yhl.scp.mps.schedule.HandworkUnScheduleDTO;
import com.yhl.scp.sds.basic.enums.ReportingTypeEnum;
import com.yhl.scp.sds.basic.enums.TaskTypeEnum;
import com.yhl.scp.sds.basic.feedback.vo.FeedbackProductionBasicVO;
import com.yhl.scp.sds.basic.order.enums.PlannedStatusEnum;
import com.yhl.scp.sds.basic.order.infrastructure.po.OperationBasicPO;
import com.yhl.scp.sds.basic.order.vo.OperationResourceBasicVO;
import com.yhl.scp.sds.basic.order.vo.OperationSubTaskBasicVO;
import com.yhl.scp.sds.basic.order.vo.OperationTaskBasicVO;
import com.yhl.scp.sds.basic.order.vo.WorkOrderBasicVO;
import com.yhl.scp.sds.extension.feedback.infrastructure.po.FeedbackProductionPO;
import com.yhl.scp.sds.extension.feedback.vo.FeedbackProductionVO;
import com.yhl.scp.sds.extension.order.dto.OperationDTO;
import com.yhl.scp.sds.extension.order.dto.OperationSubTaskDTO;
import com.yhl.scp.sds.extension.order.dto.OperationTaskDTO;
import com.yhl.scp.sds.extension.order.dto.WorkOrderDTO;
import com.yhl.scp.sds.extension.order.infrastructure.po.OperationPO;
import com.yhl.scp.sds.extension.order.infrastructure.po.WorkOrderPO;
import com.yhl.scp.sds.extension.order.vo.*;
import com.yhl.scp.sds.feedback.convertor.FeedbackProductionConvertor;
import com.yhl.scp.sds.feedback.infrastructure.dao.FeedbackProductionDao;
import com.yhl.scp.sds.feedback.service.FeedbackProductionService;
import com.yhl.scp.sds.order.convertor.OperationConvertor;
import com.yhl.scp.sds.order.convertor.OperationSubTaskConvertor;
import com.yhl.scp.sds.order.convertor.OperationTaskConvertor;
import com.yhl.scp.sds.order.convertor.WorkOrderConvertor;
import com.yhl.scp.sds.order.infrastructure.dao.OperationDao;
import com.yhl.scp.sds.order.infrastructure.dao.WorkOrderDao;
import com.yhl.scp.sds.order.service.*;
import com.yhl.scp.sds.pegging.infrastructure.dao.DemandDao;
import com.yhl.scp.sds.pegging.infrastructure.dao.FulfillmentDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.yhl.platform.common.utils.DateUtils.COMMON_DATE_STR1;

/**
 * <code>MasterPlanInsertOrEditServiceImpl</code>
 * <p>
 * 主生产计划-新增，编辑实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-07 17:02:19
 */
@Slf4j
@Service
public class MasterPlanInsertOrEditServiceImpl implements MasterPlanInsertOrEditService {

    @Resource
    private OperationDao operationDao;
    @Resource
    private OperationService operationService;
    @Resource
    private NewMdsFeign newMdsFeign;
    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private WorkOrderDao workOrderDao;
    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private OperationTaskService operationTaskService;
    @Resource
    private OperationResourceService operationResourceService;
    @Resource
    private OperationSubTaskService operationSubTaskService;
    @Resource
    protected FulfillmentDao fulfillmentDao;
    @Resource
    protected DemandDao demandDao;
    @Resource
    private MasterPlanExtDao masterPlanExtDao;
    @Resource
    private MasterPlanInsertService masterPlanInsertService;
    @Resource
    private IpsFeign ipsFeign;
    @Resource
    private FeedbackProductionService feedbackProductionService;
    @Resource
    private FeedbackProductionDao feedbackProductionDao;
    @Resource
    private OperationTaskExtDao operationTaskExtDao;


    @Override
    public void insertOperationCheck(MasterPlanInsertDTO dto) {
        //参数校验
        paramsCheck(dto);
        List<OperationPO> operationPOList = getOperationByPhysicalResource(dto.getPhysicalResourceId());
        operationPOList = operationPOList.stream().filter(t -> {
            long nowTime = dto.getStartTime().getTime();
            long startTime = t.getStartTime().getTime();
            long endTime = t.getEndTime().getTime();
            return nowTime >= startTime && nowTime <= endTime;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(operationPOList)) {
            operationPOList = operationPOList.stream().sorted(Comparator.comparing(OperationPO::getStartTime)).collect(Collectors.toList());
            throw new BusinessException("该资源在" + DateUtils.dateToString(dto.getStartTime(), COMMON_DATE_STR1) + "排产期间有排产任务,起止时间为"
                    + DateUtils.dateToString(operationPOList.get(0).getStartTime(), COMMON_DATE_STR1) + "-"
                    + DateUtils.dateToString(operationPOList.get(0).getEndTime(), COMMON_DATE_STR1));
        }
        //校验通过直接就新增
        insertData(dto);
    }


    @Override
    public HandworkUnScheduleDTO insertData(MasterPlanInsertDTO dto) {
        log.info("手工新增开始时间{}", System.currentTimeMillis());

        paramsCheck(dto);
        String mdsScenario = getMdsScenario();
        Map<String, Object> params = new HashMap<>();
        params.put("productCode", dto.getProductCode());
        //获取工艺路径信息
        List<RoutingDO> routingList = newMdsFeign.getRoutingDOByParams(params);
        RoutingDO routingDO = routingList.stream().filter(t -> YesOrNoEnum.YES.getCode().equals(t.getEnabled())).findFirst().orElse(null);
        if (routingDO == null) {
            throw new BusinessException("该产品没有工艺路径，请确认！");
        }
        List<RoutingStepVO> routingStepVOList = newMdsFeign.getRoutingStepByRoutingIds(mdsScenario, Collections.singletonList(routingDO.getId()));
        if (CollectionUtils.isEmpty(routingStepVOList)) {
            throw new BusinessException("该产品没有工艺路径步骤数据！");
        }
        List<NewStockPointVO> newStockPointVOList = newMdsFeign.selectAllStockPoint(mdsScenario);
        Map<String, NewStockPointVO> stockPointMap = newStockPointVOList.stream().collect(Collectors.toMap(NewStockPointVO::getStockPointCode, Function.identity(), (k1, k2) -> k1));
        //查询编码规则
        List<RuleEncodingsVO> prsRulRuleEncodingsVOS = newMdsFeign.getRuleEncoding();
        Map<String, RuleEncodingsVO> ruleEncodingsMap = prsRulRuleEncodingsVOS.stream()
                .collect(Collectors.toMap(RuleEncodingsVO::getRuleName, v -> v));
        List<StandardStepVO> standardStepVOS = newMdsFeign.selectStandardStepAll(SystemHolder.getScenario());
        List<String> keyStepIds = standardStepVOS.stream()
                .filter(t -> YesOrNoEnum.YES.getCode().equals(t.getKeyStep()))
                .map(BaseVO::getId).collect(Collectors.toList());
        List<String> fgRoutingStepList = routingStepVOList.stream()
                .filter(t -> keyStepIds.contains(t.getStandardStepId()))
                .map(t -> t.getSequenceNo().toString()).collect(Collectors.toList());
        HandworkUnScheduleDTO handworkUnScheduleDTO = new HandworkUnScheduleDTO();
        if (fgRoutingStepList.contains(dto.getRoutingStepSequenceNo())) {
            List<RoutingStepVO> yieldList = routingStepVOList.stream()
                    .filter(p -> p.getSequenceNo() >= Integer.parseInt(dto.getRoutingStepSequenceNo()))
                    .sorted(Comparator.comparing(RoutingStepVO::getSequenceNo))
                    .collect(Collectors.toList());
            // 根据工序成品率计算成品数量
            BigDecimal finishedQty = finishedQty(dto.getQuantity(), yieldList);

            dto.setQuantity(finishedQty);
            //链式bom
            masterPlanInsertService.doHandleSimpleBom(dto, routingDO, stockPointMap, ruleEncodingsMap, keyStepIds, handworkUnScheduleDTO);
        } else {
            List<RoutingStepVO> routingStepVOS = masterPlanExtDao.selectSemiStep(dto.getProductCode());
            // 取成型工序成品率最高的工序计算
            RoutingStepVO routingStepVO = routingStepVOS.stream()
                    .filter(p -> p.getStandardStepCode().equals(StandardStepEnum.FORMING_PROCESS.getCode()))
                    .max(Comparator.comparing(RoutingStepVO::getYield))
                    .orElseThrow(() -> new BusinessException("未找到成型工序"));
            String routingId = routingStepVO.getRoutingId();
            // 检查 stepMap.get(routingId) 是否为空
            List<RoutingStepVO> useStepList = routingStepVOS.stream()
                    .filter(p -> p.getRoutingId().equals(routingId))
                    .filter(p -> p.getSequenceNo() >= routingStepVO.getSequenceNo())
                    .collect(Collectors.toList());
            // 计算半成品数量
            BigDecimal finishedQty = calculateFinalQuantity(dto.getQuantity(), useStepList, routingStepVOList);
            dto.setQuantity(finishedQty);
            //多层bom
            masterPlanInsertService.doHandleComplexBom(dto, routingDO, stockPointMap, ruleEncodingsMap, keyStepIds, handworkUnScheduleDTO);
        }
        // 走手工编辑
        masterPlanInsertService.doAdjust(dto, handworkUnScheduleDTO.getKeyOperationList(), handworkUnScheduleDTO.getOperationOfResourceIdMap(), handworkUnScheduleDTO.getSzWorkOrderIds());
        //更新制造订单流水号
        newMdsFeign.selectiveUpdateRuleEncodings(ruleEncodingsMap.get(RuleEncodingsEnum.WORK_ORDER_NO.getDesc()));
        newMdsFeign.selectiveUpdateRuleEncodings(ruleEncodingsMap.get(RuleEncodingsEnum.WORK_ORDER_DEMAND.getDesc()));
        newMdsFeign.selectiveUpdateRuleEncodings(ruleEncodingsMap.get(RuleEncodingsEnum.WORK_ORDER_SUPPLY.getDesc()));
        log.info("手工新增结束时间{}", System.currentTimeMillis());
        return handworkUnScheduleDTO;
    }

    private BigDecimal calculateFinalQuantity(BigDecimal initialQuantity, List<RoutingStepVO> semiSteps, List<RoutingStepVO> finalSteps) {
        // 成品计算半成品数量
        BigDecimal semiQuantity = initialQuantity;
        for (RoutingStepVO filteredYieldVO : semiSteps) {
            // 逐个计算成品率算出成品的数量
            BigDecimal yield = filteredYieldVO.getYield();
            semiQuantity = BigDecimalUtils.multiply(semiQuantity, yield, 6);
        }
        finalSteps.sort(Comparator.comparing(RoutingStepVO::getSequenceNo));
        // 计算最终成品数量
        BigDecimal finishedQty = finishedQty(semiQuantity, finalSteps);
        return finishedQty;
    }

    private BigDecimal finishedQty(BigDecimal difference, List<RoutingStepVO> filteredYieldVOS) {
        for (RoutingStepVO filteredYieldVO : filteredYieldVOS) {
            // 逐个计算成品率算出成品的数量
            BigDecimal yield = filteredYieldVO.getYield();
            difference = BigDecimalUtils.multiply(difference, yield, 6);
        }
        difference = difference.setScale(0, RoundingMode.DOWN);
        return difference.compareTo(BigDecimal.ZERO) > 0 ? difference : BigDecimal.ONE;
    }


    @Override
    public List<LabelValue<String>> operationDropDown(String productCode) {
        if (StringUtils.isEmpty(productCode)) {
            return null;
        }
        String mdsScenario = SystemHolder.getScenario();
        Map<String, Object> params = new HashMap<>();
        params.put("productCode", productCode);
        //获取工艺路径信息
        List<RoutingVO> routingVOS = newMdsFeign.selectRoutingByParams(mdsScenario, params);
        RoutingVO routingVO = routingVOS.stream().filter(t -> YesOrNoEnum.YES.getCode().equals(t.getEnabled())).findFirst().orElseThrow(() -> new BusinessException("该产品没有对应工艺路径！"));

        params.put("productType", ProductTypeEnum.SA);
        List<BomRoutingStepInputVO> bomRoutingStepInputVOS = newMdsFeign.selectBomRoutingStepInputByParams(SystemHolder.getScenario(), params);
        List<String> routingIds = new ArrayList<>();
        routingIds.add(routingVO.getId());
        List<String> stockPointCodeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(bomRoutingStepInputVOS)) {
            List<String> inputProductIdsList = bomRoutingStepInputVOS.stream().map(BomRoutingStepInputVO::getInputProductId).distinct().collect(Collectors.toList());
            params = new HashMap<>();
            params.put("productIds", inputProductIdsList);
            List<RoutingVO> inputRoutingVOS = newMdsFeign.selectRoutingByParams(mdsScenario, params);
            if (CollectionUtils.isEmpty(inputRoutingVOS)) {
                List<NewProductStockPointVO> productStockPointVOS = newMdsFeign.selectProductStockPointByIds(mdsScenario, inputProductIdsList);
                if (CollectionUtils.isNotEmpty(productStockPointVOS)) {
                    inputRoutingVOS = newMdsFeign.selectRoutingByParams(mdsScenario, ImmutableMap.of("productCode", productStockPointVOS.get(0).getProductCode()));
                }
            }
            List<RoutingVO> routingVOList = inputRoutingVOS.stream().filter(t -> YesOrNoEnum.YES.getCode().equals(t.getEnabled())).collect(Collectors.toList());
            routingIds.addAll(routingVOList.stream().map(RoutingVO::getId).collect(Collectors.toList()));
            stockPointCodeList = routingVOList.stream().map(RoutingBasicVO::getStockPointId).distinct().collect(Collectors.toList());
        } else {
            stockPointCodeList.add(routingVO.getStockPointId());
        }
        List<RoutingStepVO> routingStepVOList =
                newMdsFeign.getRoutingStepByRoutingIds(mdsScenario, routingIds).stream()
                        .filter(x -> YesOrNoEnum.YES.getCode().equals(x.getEnabled()))
                        .collect(Collectors.toList());
        List<StandardStepVO> standardStepVOS = newMdsFeign.selectStandardStepAll(SystemHolder.getScenario());
        String type = "NORMEAL_PROCESS";
        List<String> finalStockPointCodeList = stockPointCodeList;
        List<String> keyStep = standardStepVOS.stream()
                .filter(standardStepVO -> StrUtil.isNotEmpty(standardStepVO.getStandardStepType()) && !type.equals(standardStepVO.getStandardStepType())
                        && standardStepVO.getStockPointCode().equals(finalStockPointCodeList.get(0)))
                .map(StandardStepBasicVO::getId).collect(Collectors.toList());
        //对于多层bom只保留两个半品的一道关键工序
        routingStepVOList = new ArrayList<>(routingStepVOList.stream().filter(t -> keyStep.contains(t.getStandardStepId()))
                .collect(Collectors.toMap(RoutingStepBasicVO::getStandardStepCode, Function.identity(), (v1, v2) -> v1)).values());
        return routingStepVOList.stream()
                .map(standardStepVO -> new LabelValue<>(standardStepVO.getStandardStepCode(), standardStepVO.getId())).collect(Collectors.toList());
    }

    @Override
    public List<LabelValue<String>> resourceDropDown(String productCode, String routingStepId) {
        if (StringUtils.isEmpty(productCode) || StringUtils.isEmpty(routingStepId)) {
            return null;
        }
        List<PhysicalResourceVO> stepResourceVOS = masterPlanExtDao.selectResourceByUserIdAndRoutingStepId(SystemHolder.getUserId(), routingStepId);
        return stepResourceVOS.stream().map(t ->
                new LabelValue<>(String.join("-", t.getPhysicalResourceCode(), t.getPhysicalResourceName()),
                        t.getId())).distinct().collect(Collectors.toList());
    }

    private void paramsCheck(MasterPlanInsertDTO dto) {
        if (dto == null) {
            throw new BusinessException("参数不能为空！");
        }
        if (StringUtils.isEmpty(dto.getPhysicalResourceId())) {
            throw new BusinessException("排产资源不能为空！");
        }
        if (StringUtils.isEmpty(dto.getProductCode())) {
            throw new BusinessException("产品编码不能为空！");
        }
        if (dto.getRoutingStepSequenceNo() == null) {
            throw new BusinessException("排产工序不能为空！");
        }
        if (dto.getQuantity() == null || dto.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("排产数量不能为0或为空！");
        }
        if (dto.getStartTime() == null) {
            throw new BusinessException("排产开始时间不能为空！");
        }
    }

    private List<OperationPO> getOperationByPhysicalResource(String physicalResourceId) {
        Map<String, Object> params = new HashMap<>();
        params.put("plannedResourceId", physicalResourceId);
        return operationDao.selectByParams(params).stream().filter(t -> t.getParentId() == null).collect(Collectors.toList());
    }

    private String getMdsScenario() {
        return SystemHolder.getScenario();
    }


    @Override
    public void doCompleteSetOfWorkOrderSplit(List<String> subOperationIds) {
        if (CollectionUtils.isEmpty(subOperationIds)) {
            return;
        }
        //查询子工序
        List<OperationPO> subOperationPOList = operationDao.selectByPrimaryKeys(subOperationIds);
        if (CollectionUtils.isEmpty(subOperationPOList)) {
            throw new BusinessException("工序不存在！");
        }
        Map<String, List<OperationPO>> workOrderSubOperationMap = subOperationPOList.stream().collect(Collectors.groupingBy(OperationBasicPO::getOrderId));
        List<String> parentIds = subOperationPOList.stream().map(OperationPO::getParentId).distinct().collect(Collectors.toList());
        //查询子工序对应父工序
        List<OperationPO> parentOperationPOList = operationDao.selectByPrimaryKeys(parentIds);
        if (CollectionUtils.isEmpty(parentOperationPOList)) {
            throw new BusinessException("工序数据对应的父工序不存在！");
        }
        //查询对应工单
        List<String> workOrderList = parentOperationPOList.stream().map(OperationPO::getOrderId).distinct().collect(Collectors.toList());
        List<WorkOrderVO> workOrderVOS = workOrderService.selectByPrimaryKeys(workOrderList);
        if (CollectionUtils.isEmpty(workOrderVOS)) {
            throw new BusinessException("工序数据对应的工单不存在！");
        }
        //查询对应operationTask
        List<OperationTaskVO> operationTaskVOS = operationTaskService.selectByOperationIds(subOperationIds);
        Map<String, OperationTaskVO> operationTaskVOMap = operationTaskVOS.stream().collect(Collectors.toMap(OperationTaskBasicVO::getOperationId, Function.identity()));
        List<String> taskIds = operationTaskVOS.stream().map(BaseVO::getId).collect(Collectors.toList());
        Map<String, Object> params = new HashMap<>();
        params.put("taskIds", taskIds);
        List<OperationSubTaskVO> operationSubTaskVOS = operationSubTaskService.selectByParams(params);
        Map<String, List<OperationSubTaskVO>> operationSubTaskVOMap = operationSubTaskVOS.stream().collect(Collectors.groupingBy(OperationSubTaskBasicVO::getOperationId));
        List<String> parentOperationIds = parentOperationPOList.stream().map(BasePO::getId).collect(Collectors.toList());
        List<OperationResourceVO> operationResourceVOList = operationResourceService.selectByParams(ImmutableMap.of("operationIds", parentOperationIds));
        Map<String, BigDecimal> beatMap = operationResourceVOList.stream().collect(Collectors.toMap(t -> String.join("-", t.getOperationId(), t.getPhysicalResourceId()), OperationResourceBasicVO::getUnitProductionTime));

        List<OperationDTO> operationInsertList = new ArrayList<>();
        List<OperationTaskDTO> operationTaskInsertList = new ArrayList<>();
        List<OperationSubTaskDTO> operationSubTaskInsertList = new ArrayList<>();
        List<OperationPO> operationUpdateList = new ArrayList<>();
        List<OperationTaskDTO> operationTaskUpdateList = new ArrayList<>();
        List<OperationSubTaskDTO> operationSubTaskUpdateList = new ArrayList<>();
        for (WorkOrderVO workOrderVO : workOrderVOS) {
            //未齐套数量
            BigDecimal subtract = workOrderVO.getQuantity().subtract(workOrderVO.getFulfilledQuantity());
            List<OperationPO> subOperationPOs = workOrderSubOperationMap.get(workOrderVO.getId());
            for (OperationPO subOperationPO : subOperationPOs) {
                OperationDTO operationDTO = new OperationDTO();
                BeanUtils.copyProperties(subOperationPO, operationDTO);
                operationDTO.setId(UUIDUtil.getUUID());
                operationDTO.setQuantity(subtract);
                subOperationPO.setQuantity(subOperationPO.getQuantity().subtract(subtract));
                OperationTaskVO taskVO = operationTaskVOMap.get(subOperationPO.getId());
                //更新时间
                BigDecimal beat = beatMap.get(String.join("-", subOperationPO.getParentId(), taskVO.getPhysicalResourceId()));
                BigDecimal productTime = subOperationPO.getQuantity().multiply(beat);
                //现在新的结束时间，也是拆分工序的开始时间
                Date end = DateUtil.offsetSecond(subOperationPO.getStartTime(), productTime.intValue());
                // Date start = DateUtil.offsetSecond(end, 1);

                OperationTaskDTO operationTaskDTO = new OperationTaskDTO();
                BeanUtils.copyProperties(taskVO, operationTaskDTO);
                operationTaskDTO.setId(UUIDUtil.getUUID());
                operationTaskDTO.setOperationId(operationDTO.getId());
                operationTaskDTO.setStartTime(end);
                operationTaskDTO.setEndTime(taskVO.getEndTime());

                taskVO.setEndTime(end);

                operationTaskInsertList.add(operationTaskDTO);
                operationTaskUpdateList.add(OperationTaskConvertor.INSTANCE.vo2Dto(taskVO));
                List<OperationSubTaskVO> subTaskVOS = operationSubTaskVOMap.get(subOperationPO.getId());
                //过滤出包含这个时间的subTask，这里只有一个
                OperationSubTaskVO subTaskVO = subTaskVOS.stream().filter(t -> t.getStartTime().before(end) && t.getEndTime().after(end)).findFirst().get();
                OperationSubTaskDTO operationSubTaskDTO = new OperationSubTaskDTO();
                BeanUtils.copyProperties(subTaskVO, operationSubTaskDTO);
                operationSubTaskDTO.setId(UUIDUtil.getUUID());
                operationSubTaskDTO.setOperationId(operationDTO.getId());
                operationSubTaskDTO.setTaskId(operationTaskDTO.getId());
                operationSubTaskDTO.setStartTime(end);
                operationSubTaskDTO.setEndTime(subTaskVO.getEndTime());
                operationSubTaskInsertList.add(operationSubTaskDTO);

                subTaskVO.setEndTime(end);

                operationSubTaskUpdateList.add(OperationSubTaskConvertor.INSTANCE.vo2Dto(subTaskVO));

                //过滤出开始时间在这个时间之后的，它们要挂到新的subOperation下面
                List<OperationSubTaskVO> subTaskVOS3 = subTaskVOS.stream().filter(t -> t.getStartTime().after(end)).sorted(Comparator.comparing(OperationSubTaskBasicVO::getStartTime)).collect(Collectors.toList());
                subTaskVOS3.forEach(t -> {
                    t.setTaskId(operationTaskDTO.getId());
                    t.setOperationId(operationDTO.getId());
                });
                operationSubTaskUpdateList.addAll(OperationSubTaskConvertor.INSTANCE.vo2Dtos(subTaskVOS3));

                operationDTO.setStartTime(end);
                operationInsertList.add(operationDTO);

                subOperationPO.setEndTime(end);
                operationUpdateList.add(subOperationPO);
            }
        }

        if (CollectionUtils.isNotEmpty(operationInsertList)) {
            operationService.doCreateBatch(operationInsertList);
        }
        if (CollectionUtils.isNotEmpty(operationUpdateList)) {
            BasePOUtils.updateBatchFiller(operationUpdateList);
            operationDao.updateBatch(operationUpdateList);
        }
        if (CollectionUtils.isNotEmpty(operationTaskInsertList)) {
            operationTaskService.doCreateBatch(operationTaskInsertList);
        }
        if (CollectionUtils.isNotEmpty(operationTaskUpdateList)) {
            operationTaskService.doUpdateBatch(operationTaskUpdateList);
        }
        if (CollectionUtils.isNotEmpty(operationSubTaskInsertList)) {
            operationSubTaskService.doCreateBatch(operationSubTaskInsertList);
        }
        if (CollectionUtils.isNotEmpty(operationSubTaskUpdateList)) {
            operationSubTaskService.doUpdateBatch(operationSubTaskUpdateList);
        }
    }

    private final List<String> orderType = Arrays.asList("SZ", "XSB", "SYTS");

    @Override
    public void doSaveTestOrderNum(WorkOrderDTO workOrderDTO) {
        String testOrderNumber = workOrderDTO.getTestOrderNumber();
        if (StringUtils.isEmpty(testOrderNumber)) {
            return;
        }
        if (StringUtils.isEmpty(workOrderDTO.getId())) {
            return;
        }
        String regex = "^[A-Z]{4}(\\d{6})\\d{4}$";

        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(testOrderNumber);
        if (!matcher.matches()) {
            throw new BusinessException("试制单号不符合规则");
        }
        List<WorkOrderVO> workOrderVOS = workOrderService.selectByParams(ImmutableMap.of("testOrderNumber", testOrderNumber));
        List<String> productIds = workOrderVOS.stream().map(WorkOrderBasicVO::getProductId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(productIds) || productIds.size() > 1) {
            throw new BusinessException("同一试制卡号对应的本厂编码只能一个");
        }
        WorkOrderVO workOrderVO = workOrderService.selectByPrimaryKey(workOrderDTO.getId());
        if (workOrderVO == null) {
            throw new BusinessException("制造订单不存在");
        }
        // 订单类型不在限制类的不用填
        if (!orderType.contains(workOrderVO.getOrderType())) {
            return;
        }
        // topWorkOrder只有一个
        if (StringUtils.isNotEmpty(workOrderVO.getTopOrderId())) {
            List<WorkOrderVO> topWorkOrderList = workOrderService.selectByParentIds(Collections.singletonList(workOrderVO.getTopOrderId()));
            if (CollectionUtils.isNotEmpty(topWorkOrderList)) {
                workOrderVO = topWorkOrderList.get(0);
            } else {
                throw new BusinessException("顶层制造订单不存在");
            }
        }
        // 试制单号保存到顶层制造订单上
        WorkOrderPO workOrderPO = new WorkOrderPO();
        BeanUtils.copyProperties(workOrderVO, workOrderPO);
        workOrderPO.setTestOrderNumber(testOrderNumber);
        BasePOUtils.updateFiller(workOrderPO);
        workOrderDao.updateSelective(workOrderPO);
    }

    @Override
    public BigDecimal calculateWorkOrderQty(MasterQuantityReq masterQuantityReq) {
        // 原始值
        BigDecimal originalOperationQty = masterQuantityReq.getOriginalOperationQty();
        // 修改后的值
        BigDecimal newOperationQty = masterQuantityReq.getNewOperationQty();
        // 当前订单
        String workOrderNumber = masterQuantityReq.getWorkOrderNumber();
        // 顶层订单
        String topWorkOrderNumber = masterQuantityReq.getTopWorkOrderNumber();
        // 顶层订单数量
        BigDecimal topWorkOrderQuantity = masterQuantityReq.getTopWorkOrderQuantity();
        Integer routingStepSequenceNo = masterQuantityReq.getRoutingStepSequenceNo();

        // 如果原始值和新值相同，直接返回原始值
        if (originalOperationQty.compareTo(newOperationQty) == 0) {
            return originalOperationQty;
        }

        // 计算数量差值的绝对值
        BigDecimal difference = newOperationQty.subtract(originalOperationQty).abs();

        // 获取当前订单和顶层订单的成品率数据
        List<String> workOrderList = ListUtil.of(workOrderNumber, topWorkOrderNumber);
        Map<String, List<WorkOrderYieldVO>> yieldMap = StreamUtils.mapListByColumn(masterPlanExtDao.selectWorkOrderYield(workOrderList),
                WorkOrderYieldVO::getWorkOrderNumber);

        // 检查 yieldMap 是否为空
        if (yieldMap == null || yieldMap.isEmpty()) {
            throw new BusinessException("成品率数据为空");
        }

        // 过滤并排序当前订单的成品率数据
        List<WorkOrderYieldVO> filteredYieldVOS = yieldMap.get(workOrderNumber).stream()
                .filter(p -> p.getSequenceNo() >= routingStepSequenceNo)
                .sorted(Comparator.comparing(WorkOrderYieldVO::getSequenceNo))
                .collect(Collectors.toList());

        // 计算当前路径步骤成品率对应数量
        difference = doQuantity(difference, filteredYieldVOS);

        // 如果当前订单和顶层订单不同，计算顶层订单的成品率对应数量
        if (!workOrderNumber.equals(topWorkOrderNumber)) {
            List<WorkOrderYieldVO> topFilteredYieldVOS = yieldMap.get(topWorkOrderNumber).stream()
                    .sorted(Comparator.comparing(WorkOrderYieldVO::getSequenceNo))
                    .collect(Collectors.toList());
            difference = doQuantity(difference, topFilteredYieldVOS);
        }

        // 根据原始值和新值的大小关系更新顶层订单数量
        if (newOperationQty.compareTo(originalOperationQty) > 0) {
            topWorkOrderQuantity = topWorkOrderQuantity.add(difference);
        } else {
            topWorkOrderQuantity = topWorkOrderQuantity.subtract(difference);
        }

        return topWorkOrderQuantity;
    }

    @Override
    public String getResourceCode(MasterPlanInsertDTO dto) {
        String physicalResourceId = dto.getPhysicalResourceId();
        if (StringUtils.isEmpty(physicalResourceId)) {
            log.error("当前{}对象中的物理资源Id为空", dto);
            throw new BusinessException("当前对象中的物理资源Id为空。");
        } else {
            StandardResourceVO standardResourceVO = newMdsFeign.getStandResourceByPhysicalId(SystemHolder.getScenario(), physicalResourceId);
            if (Objects.isNull(standardResourceVO)) {
                log.error("通过物理资源id为{}的数据没有匹配到标准资源数据。", physicalResourceId);
                throw new BusinessException("通过物理资源id为" + physicalResourceId + "的数据没有匹配到标准资源数据。");
            } else {
                return standardResourceVO.getStandardResourceCode();
            }
        }
    }

    private BigDecimal doQuantity(BigDecimal difference, List<WorkOrderYieldVO> filteredYieldVOS) {
        for (WorkOrderYieldVO filteredYieldVO : filteredYieldVOS) {
            // 逐个计算成品率算出成品的数量
            BigDecimal yield = filteredYieldVO.getYield();
            difference = BigDecimalUtils.multiply(difference, yield, 6);
        }
        return difference.setScale(0, RoundingMode.DOWN);
    }

    public OperationPO check(String operationId) {
        OperationPO operationPO = operationDao.selectByPrimaryKey(operationId);
        if (operationPO == null) {
            throw new BusinessException("报工工序不存在");
        }
        if (PlanStatusEnum.UNPLAN.getCode().equals(operationPO.getPlanStatus())) {
            throw new BusinessException("未计划工序不允许报工");
        }
        return operationPO;
    }


    @Override
    public void doUpdateFeedback(FeedBackReq feedBackReq) {
        String operationId = feedBackReq.getOperationId();
        String reportingQuantity = feedBackReq.getReportingQuantity();
        String whetherUpdateFinished = feedBackReq.getWhetherUpdateFinished();
        String reportingStatus = feedBackReq.getReportingStatus();
        if (StringUtils.isEmpty(operationId) || StringUtils.isEmpty(reportingQuantity)) {
            return;
        }
        OperationPO operationPO = check(operationId);
        String planStatus = operationPO.getPlanStatus();
        String parentId = operationPO.getParentId();
        String plannedResourceId = operationPO.getPlannedResourceId();
        List<PhysicalResourceVO> physicalResourceVOS = newMdsFeign.selectPhysicalResourceByParams(SystemHolder.getScenario(),
                ImmutableMap.of("id", plannedResourceId));
        if (CollectionUtils.isEmpty(physicalResourceVOS)) {
            throw new BusinessException("工序排产资源不存在");
        }
        Date reportingStartTime = DateUtils.stringToDate(feedBackReq.getReportingStartTime(), COMMON_DATE_STR1);
        Date reportingEndTime = DateUtils.stringToDate(feedBackReq.getReportingEndTime(), COMMON_DATE_STR1);
        if (StrUtil.isNotEmpty(whetherUpdateFinished) && whetherUpdateFinished.equals(YesOrNoEnum.YES.getCode())) {
            // 更新当工序所在资源的前工序为非完工的工序为‘已完工’
            updateBatchFinished(plannedResourceId, reportingStartTime, feedBackReq);
        }

        BigDecimal quantity = new BigDecimal(reportingQuantity);
        boolean finishedFlag = false;
        if (quantity.compareTo(operationPO.getQuantity()) >= 0) {
            quantity = operationPO.getQuantity();
            finishedFlag = true;
        }
        PhysicalResourceVO physicalResourceVO = physicalResourceVOS.get(0);
        List<CollectionValueVO> hwLimitResult = ipsFeign.getByCollectionCode("HW_LIMIT_STAND_RESOURCE_CODE");
        List<String> hwLimitResultList = hwLimitResult.stream().map(CollectionValueVO::getCollectionValue).collect(Collectors.toList());
        String standardResourceCode = physicalResourceVO.getStandardResourceCode();
        List<FeedbackProductionVO> updateList = new ArrayList<>();
        List<FeedbackProductionVO> insertList = new ArrayList<>();
        Date now = new Date();

        if (StringUtils.isEmpty(parentId) && hwLimitResultList.contains(standardResourceCode)) {
            List<OperationPO> subOperationPOList = operationDao.selectByParentIds(Collections.singletonList(operationId));
            List<String> subOperationIds = subOperationPOList.stream().map(BasePO::getId).collect(Collectors.toList());
            // 子工序对应生产反馈
            List<FeedbackProductionVO> feebackList = feedbackProductionService.selectByOperationIds(subOperationIds);
            if (!planStatus.equals(PlanStatusEnum.PLANNED.getCode()) && reportingStatus.equals(PlannedStatusEnum.PLANNED.getCode())) {
                // 将‘已开始/已完工’状态变为已计划，则删除对应生产反馈数据,修改对应工序状态为‘已计划’
                handleDeleteFeedBack(feebackList, subOperationPOList);
                return;
            }
            Map<String, FeedbackProductionVO> feedbackProductionVOMap = feebackList.stream().collect(Collectors.toMap(FeedbackProductionBasicVO::getOperationId, Function.identity(),
                    (v1, v2) -> v1));
            BigDecimal size = new BigDecimal(subOperationPOList.size());
            // 整除，减少循环次数
            BigDecimal quotient = quantity.divide(size, 0, RoundingMode.DOWN);
            // 取余
            BigDecimal remainder = quantity.remainder(size);
            //均分
            subOperationPOList.sort(Comparator.comparing(OperationPO::getStartTime));
            Map<String, Pair<Boolean, BigDecimal>> qtyMap = new HashMap<>();
            for (OperationPO subOperationPO : subOperationPOList) {
                BigDecimal num;
                // 尾差加到第一道工序
                num = quotient;
                boolean subFinishedFlag = false;
                if (num.compareTo(subOperationPO.getQuantity()) > 0) {
                    remainder = remainder.add(num.subtract(subOperationPO.getQuantity()));
                    num = subOperationPO.getQuantity();
                    subFinishedFlag = true;
                }
                qtyMap.put(subOperationPO.getId(), Pair.of(subFinishedFlag, num));
            }
            // 把余数均分到每道子工序上面
            while (remainder.compareTo(BigDecimal.ZERO) > 0) {
                for (OperationPO subOperationPO : subOperationPOList) {
                    operationPO.setQuantity(operationPO.getQuantity().subtract(BigDecimal.ONE));
                    Pair<Boolean, BigDecimal> pair = qtyMap.get(subOperationPO.getId());
                    BigDecimal right = pair.getRight();
                    BigDecimal num = right.add(BigDecimal.ONE);
                    remainder = remainder.subtract(BigDecimal.ONE);
                    boolean subFinishedFlag = false;
                    if (num.compareTo(subOperationPO.getQuantity()) >= 0) {
                        subFinishedFlag = true;
                    }
                    qtyMap.put(subOperationPO.getId(), Pair.of(subFinishedFlag, num));
                    if (remainder.compareTo(BigDecimal.ZERO) == 0) {
                        break;
                    }
                }
            }
            for (OperationPO subOperationPO : subOperationPOList) {
                Pair<Boolean, BigDecimal> pair = qtyMap.get(subOperationPO.getId());
                dataProcess(physicalResourceVO, updateList, insertList,
                        now, feedbackProductionVOMap, subOperationPO, pair.getRight(), pair.getLeft(),
                        reportingStartTime, reportingEndTime, reportingStatus);
            }
        } else {
            List<FeedbackProductionVO> feebackList = feedbackProductionService.selectByOperationIds(Collections.singletonList(operationId));
            if (!planStatus.equals(PlanStatusEnum.PLANNED.getCode()) && reportingStatus.equals(PlannedStatusEnum.PLANNED.getCode())) {
                handleDeleteFeedBack(feebackList, ListUtil.of(operationPO));
                return;
            }
            Map<String, FeedbackProductionVO> feedbackProductionVOMap = feebackList.stream().collect(Collectors
                    .toMap(FeedbackProductionBasicVO::getOperationId, Function.identity(),
                            (v1, v2) -> v1));
            dataProcess(physicalResourceVO, updateList, insertList,
                    now, feedbackProductionVOMap, operationPO, quantity, finishedFlag,
                    reportingStartTime, reportingEndTime, reportingStatus);
        }

        List<FeedbackProductionPO> updateFeedbackProductionPOS = FeedbackProductionConvertor.INSTANCE.vo2Pos(updateList);
        List<FeedbackProductionPO> insertFeedbackProductionPOS = FeedbackProductionConvertor.INSTANCE.vo2Pos(insertList);

        if (CollectionUtils.isNotEmpty(updateFeedbackProductionPOS)) {
            BasePOUtils.updateBatchFiller(updateFeedbackProductionPOS);
            feedbackProductionDao.updateBatch(updateFeedbackProductionPOS);
        }
        if (CollectionUtils.isNotEmpty(insertFeedbackProductionPOS)) {
            BasePOUtils.insertBatchFiller(insertFeedbackProductionPOS);
            feedbackProductionDao.insertBatch(insertFeedbackProductionPOS);
        }
        // 更新报工工序的前工序为’FINISHED‘
        handleFeedBackPreOperation(operationPO);
    }

    private void handleFeedBackPreOperation(OperationPO operationPO) {
        String operationPOId = operationPO.getId();
        Integer routingStepSequenceNo = operationPO.getRoutingStepSequenceNo();
        List<OperationVO> operationVOS = masterPlanExtDao.selectOrderOperation(ListUtil.of(operationPOId));
        List<String> statusList = ListUtil.of(PlannedStatusEnum.STARTED.getCode(), PlannedStatusEnum.PLANNED.getCode());
        // 修改关键工序及它的得前工序不为‘FINISHED’得工序
        List<OperationVO> updateOperation = operationVOS.stream()
                .filter(p -> statusList.contains(p.getPlanStatus()))
                .filter(p -> p.getRoutingStepSequenceNo() < routingStepSequenceNo)
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(updateOperation)){
            updateOperation.forEach(w -> w.setPlanStatus(PlannedStatusEnum.FINISHED.getCode()));
            operationService.doUpdateBatch(OperationConvertor.INSTANCE.vo2Dtos(updateOperation));
        }
    }

    private void handleDeleteFeedBack(List<FeedbackProductionVO> feebackList, List<OperationPO> subOperationPOList) {
        if (CollectionUtils.isEmpty(feebackList)) {
            return;
        }
        // 修改子工序及父工序为‘已计划’
        List<String> parentOperationId = StreamUtils.columnToList(subOperationPOList, OperationPO::getParentId);
        List<OperationVO> updateOperation = operationService.selectByParentIds(parentOperationId);
        updateOperation.addAll(OperationConvertor.INSTANCE.po2Vos(subOperationPOList));
        updateOperation.forEach(p -> p.setPlanStatus(PlannedStatusEnum.PLANNED.getCode()));
        operationService.doUpdateBatch(OperationConvertor.INSTANCE.vo2Dtos(updateOperation));
        // 修改制造订单为已计划
        List<String> orderIds = StreamUtils.columnToList(updateOperation, OperationVO::getOrderId);
        List<WorkOrderVO> workOrderVOList = workOrderService.selectByPrimaryKeys(orderIds);
        workOrderVOList.forEach(p -> p.setPlanStatus(PlannedStatusEnum.PLANNED.getCode()));
        workOrderService.doUpdateBatch(WorkOrderConvertor.INSTANCE.vo2Dtos(workOrderVOList));
        // 删除子工序报工对应生产反馈数据
        List<String> ids = StreamUtils.columnToList(feebackList, FeedbackProductionVO::getId);
        feedbackProductionService.doDelete(ids);
    }

    private void dataProcess(PhysicalResourceVO physicalResourceVO,
                             List<FeedbackProductionVO> updateList,
                             List<FeedbackProductionVO> insertList,
                             Date now,
                             Map<String, FeedbackProductionVO> feedbackProductionVOMap,
                             OperationPO subOperationPO,
                             BigDecimal num,
                             boolean subFinishedFlag,
                             Date reportingStartTime, Date reportingEndTime,
                             String reportingStatus) {
        if (feedbackProductionVOMap.containsKey(subOperationPO.getId())) {
            FeedbackProductionVO exist = feedbackProductionVOMap.get(subOperationPO.getId());
            exist.setReportingQuantity(num);
            exist.setReportingStatus(subFinishedFlag ? PlannedStatusEnum.FINISHED.getCode() : PlannedStatusEnum.STARTED.getCode());
            exist.setStartTime(reportingStartTime);
            exist.setReportingTime(reportingEndTime);
            updateList.add(exist);
        } else {
            FeedbackProductionVO vo = new FeedbackProductionVO();
            vo.setId(UUIDUtil.getUUID());
            vo.setOperationId(subOperationPO.getId());
            vo.setWorkOrderId(subOperationPO.getOrderId());
            vo.setTaskType(TaskTypeEnum.PRODUCTION.getCode());
            vo.setReportingType(ReportingTypeEnum.SON.getCode());
            vo.setPhysicalResourceId(physicalResourceVO.getId());
            vo.setReportingQuantity(num);
            vo.setReportingStatus(subFinishedFlag ? PlannedStatusEnum.FINISHED.getCode() : PlannedStatusEnum.STARTED.getCode());
            vo.setReportingTime(reportingEndTime);
            vo.setReportingScrap(BigDecimal.ZERO);
            vo.setStartTime(reportingStartTime);
            vo.setRoutingStepId(subOperationPO.getRoutingStepId());
            vo.setWhetherFeedBack(YesOrNoEnum.NO.getCode());
            insertList.add(vo);
        }
    }

    @Override
    public void updateFeedbackCheck(FeedBackReq feedBackReq) {
        String operationId = feedBackReq.getOperationId();
        String reportingStartTime = feedBackReq.getReportingStartTime();
        String productCode = feedBackReq.getProductCode();
        // 1. 校验并获取工序对象
        OperationPO operationPO = check(operationId);
        String plannedResourceId = operationPO.getPlannedResourceId();
        // 2. 获取物理资源信息
        List<PhysicalResourceVO> physicalResourceVOS = newMdsFeign.selectPhysicalResourceByParams(
                SystemHolder.getScenario(), ImmutableMap.of("id", plannedResourceId));
        if (CollectionUtils.isEmpty(physicalResourceVOS)) {
            throw new BusinessException("工序排产资源不存在");
        }
        PhysicalResourceVO physicalResourceVO = physicalResourceVOS.get(0);
        String resourceId = physicalResourceVO.getId();
        // 3. 解析上报开始时间
        Date reportingStart = DateUtils.stringToDate(reportingStartTime, COMMON_DATE_STR1);
        // 4. 查询资源在当前时间前的任务
        List<MasterPlanTaskVO> masterPlanTaskVOS = operationTaskExtDao.selectResourceTask(reportingStart, resourceId);
        if (CollectionUtils.isEmpty(masterPlanTaskVOS)) {
            return;
        }
        if (CollectionUtils.isNotEmpty(masterPlanTaskVOS)) {
            String errorMsg = "%s资源，%s产品之前有未完工的计划，是否批量更新为已完工？";
            throw new BusinessException(String.format(errorMsg, physicalResourceVO.getPhysicalResourceCode(), productCode));
        }
    }

    private void updateBatchFinished(String plannedResourceId, Date reportingStartTime, FeedBackReq feedBackReq) {
        // 未完工工序更新为已完工
        List<MasterPlanTaskVO> masterPlanTaskVOS = operationTaskExtDao.selectResourceTask(reportingStartTime, plannedResourceId);
        if (CollectionUtils.isEmpty(masterPlanTaskVOS)) {
            return;
        }
        List<String> operationIds = StreamUtils.columnToList(masterPlanTaskVOS, MasterPlanTaskVO::getOperationId);
        List<OperationVO> operationVOS = masterPlanExtDao.selectOrderOperation(operationIds);
        Map<String, OperationVO> operationMap = StreamUtils.mapByColumn(operationVOS, OperationVO::getId);
        Map<String, List<OperationVO>> orderOperationMap = StreamUtils.mapListByColumn(operationVOS, OperationVO::getOrderId);
        List<String> statusList = ListUtil.of(PlannedStatusEnum.STARTED.getCode(), PlannedStatusEnum.PLANNED.getCode());
        List<OperationVO> updateOperationList = new ArrayList<>();
        for (String operationId : operationIds) {
            OperationVO operationVO = operationMap.get(operationId);
            Integer routingStepSequenceNo = operationVO.getRoutingStepSequenceNo();
            String orderId = operationVO.getOrderId();
            // 全工序
            List<OperationVO> operationAll = orderOperationMap.get(orderId);
            // 修改关键工序及它的得前工序不为‘FINISHED’得工序
            List<OperationVO> updateOperation = operationAll.stream()
                    .filter(p -> statusList.contains(p.getPlanStatus()))
                    .filter(p -> p.getRoutingStepSequenceNo() <= routingStepSequenceNo)
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(updateOperation)){
                updateOperation.forEach(w -> w.setPlanStatus(PlannedStatusEnum.FINISHED.getCode()));
                updateOperationList.addAll(updateOperation);
            }
        }
        operationService.doUpdateBatch(OperationConvertor.INSTANCE.vo2Dtos(updateOperationList));
        // 更新制造订单状态为已完工
        List<String> orderIds = StreamUtils.columnToList(operationVOS, OperationVO::getOrderId);
        List<WorkOrderVO> workOrderVOS = workOrderService.selectByParams(ImmutableMap.of("ids", orderIds));
        workOrderVOS.forEach(w -> w.setPlanStatus(PlannedStatusEnum.FINISHED.getCode()));
        workOrderService.doUpdateBatch(WorkOrderConvertor.INSTANCE.vo2Dtos(workOrderVOS));
    }

}