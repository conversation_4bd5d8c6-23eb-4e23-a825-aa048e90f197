package com.yhl.scp.mps.manualAdjust.service.impl;

import static com.yhl.platform.common.utils.DateUtils.COMMON_DATE_STR1;

import cn.hutool.core.map.MapUtil;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.ams.extension.schedule.dto.AdjustmentParam;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.resource.vo.StandardResourceVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mps.algorithm.dto.RzzAdjustmentParam;
import com.yhl.scp.mps.enums.PlanStatusEnum;
import com.yhl.scp.mps.manualAdjust.dto.ManualAdjustParam;
import com.yhl.scp.mps.plan.infrastructure.dao.OperationTaskExtDao;
import com.yhl.scp.mps.schedule.service.HandworkScheduleService;
import com.yhl.scp.sds.basic.order.enums.PlannedStatusEnum;
import com.yhl.scp.sds.basic.order.infrastructure.po.OperationBasicPO;
import com.yhl.scp.sds.extension.order.infrastructure.po.OperationPO;
import com.yhl.scp.sds.extension.order.infrastructure.po.WorkOrderPO;
import com.yhl.scp.sds.extension.order.vo.OperationTaskVO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import com.yhl.scp.sds.order.infrastructure.dao.OperationDao;
import com.yhl.scp.sds.order.infrastructure.dao.WorkOrderDao;
import com.yhl.scp.sds.order.service.OperationService;
import com.yhl.scp.sds.order.service.WorkOrderService;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 手动调整服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ManualAdjustAlgorithmService {

  @Resource private HandworkScheduleService handworkScheduleService;

  @Resource private OperationTaskExtDao operationTaskExtDao;

  @Resource private OperationService operationService;

  @Resource private OperationDao operationDao;

  @Resource private WorkOrderDao workOrderDao;

  @Resource private WorkOrderService workOrderService;

  /**
   * 执行算法
   *
   * @param planningHorizon
   * @param adjustmentParamList
   */
  public void doHandworkScheduleBatch(
      PlanningHorizonVO planningHorizon, List<AdjustmentParam> adjustmentParamList) {
    handworkScheduleService.doHandworkSchedule(
        adjustmentParamList, planningHorizon, Lists.newArrayList(), null, null);
  }

  /**
   * 获取连续炉的所有子工序
   *
   * @param adjustmentParam
   * @param operationVO
   * @param adjustmentParamList
   * @return
   */
  public List<OperationVO> getLxlPlanParams(
      RzzAdjustmentParam adjustmentParam,
      OperationVO operationVO,
      List<AdjustmentParam> adjustmentParamList) {
    // 编辑的是未计划的父工序，无论是否顺延都走手工调整
    adjustmentParam.setChangeFlag(true);
    // 未计划的同制造订单其他子工序都需要走手工调整排程
    return operationService.selectByParentIds(Collections.singletonList(operationVO.getId()));
  }

  /**
   * 获取调整参数
   *
   * @param planningHorizon
   * @param adjustmentParam
   * @param operationIds
   * @param subOperationVO
   * @param adjustmentParamList
   * @param qtyChangeFlag
   * @param timeOrResourceChangeFlag
   * @return
   */
  public List<AdjustmentParam> getAdjustmentParams(
      PlanningHorizonVO planningHorizon,
      RzzAdjustmentParam adjustmentParam,
      List<String> operationIds,
      OperationVO subOperationVO,
      List<AdjustmentParam> adjustmentParamList,
      boolean qtyChangeFlag,
      boolean timeOrResourceChangeFlag) {
    String targetResourceId = adjustmentParam.getTargetResourceId();
    String sourceResourceId = adjustmentParam.getSourceResourceId();

    OperationTaskVO operationTaskVO =
        operationTaskExtDao.selectLastOperationTaskBetweenDates(
            planningHorizon.getPlanStartTime(),
            DateUtils.stringToDate(adjustmentParam.getAppointStartTime(), COMMON_DATE_STR1),
            targetResourceId);
    AdjustmentParam param = new AdjustmentParam();
    param.setOperationIds(operationIds);
    param.setSourceResourceId(sourceResourceId);
    param.setTargetResourceId(targetResourceId);
    param.setAdjustmentType(AdjustmentParam.AdjustmentType.ADJUSTMENT.name());
    param.setAppointStartTime(adjustmentParam.getAppointStartTime());
    param.setOperandsType(AdjustmentParam.OperandsType.OPERATION.name());
    if (operationTaskVO != null) {
      param.setPreOperationId(operationTaskVO.getOperationId());
    } else {
      param.setStartDate(planningHorizon.getPlanStartTime());
    }
    param.setRoutingStepSequenceNo(subOperationVO.getRoutingStepSequenceNo());
    param.setOnlyQtyChangeFlag(qtyChangeFlag && timeOrResourceChangeFlag);
    adjustmentParamList.add(param);
    adjustmentParamList =
        adjustmentParamList.stream()
            .sorted(Comparator.comparing(AdjustmentParam::getRoutingStepSequenceNo))
            .collect(Collectors.toList());
    return adjustmentParamList;
  }

  /**
   * 调整位置相同的数据合并
   *
   * @param allAdjustmentParamList
   * @return
   */
  public List<AdjustmentParam> mergeAdjustmentParamList(
      List<AdjustmentParam> allAdjustmentParamList) {
    List<AdjustmentParam> result = Lists.newArrayList();
    Map<String, List<AdjustmentParam>> map =
        allAdjustmentParamList.stream()
            .collect(
                Collectors.groupingBy(
                    t -> String.join("-", t.getPreOperationId(), t.getTargetResourceId())));
    for (Map.Entry<String, List<AdjustmentParam>> entry : map.entrySet()) {
      List<AdjustmentParam> adjustmentParamList = entry.getValue();
      AdjustmentParam adjustmentParam = adjustmentParamList.get(0);
      List<String> operationIds =
          adjustmentParamList.stream()
              .flatMap(t -> t.getOperationIds().stream())
              .collect(Collectors.toList());
      adjustmentParam.setOperationIds(operationIds);
      result.add(adjustmentParam);
    }
    return result;
  }

  public List<AdjustmentParam> getHandworkScheduleBatchParams(
      PlanningHorizonVO planningHorizon, List<RzzAdjustmentParam> adjustmentParams) {
    List<AdjustmentParam> allAdjustmentParamList = Lists.newArrayList();
    List<String> operationIds =
        adjustmentParams.stream()
            .map(RzzAdjustmentParam::getOperationId)
            .collect(Collectors.toList());
    List<OperationVO> operationVOList =
        operationService.selectVOByParams(ImmutableMap.of("ids", operationIds));
    Map<String, OperationVO> operationMap =
        operationVOList.stream().collect(Collectors.toMap(BaseVO::getId, Function.identity()));
    List<OperationVO> adjustOperationList = Lists.newArrayList();
    for (RzzAdjustmentParam adjustmentParam : adjustmentParams) {
      String targetResourceId = adjustmentParam.getTargetResourceId();
      String sourceResourceId = adjustmentParam.getSourceResourceId();
      // 查询原来的subOperation
      OperationVO subOperationVO = operationMap.get(adjustmentParam.getOperationId());
      // 查询原来的operationTask,上面有排产资源
      String parentId = subOperationVO.getParentId();
      List<AdjustmentParam> adjustmentParamList = Lists.newArrayList();

      // 调整的是连续炉
      if (StringUtils.isBlank(parentId)
          && PlannedStatusEnum.PLANNED.getCode().equals(subOperationVO.getPlanStatus())) {
        List<OperationVO> unPlanParams =
            this.getLxlPlanParams(adjustmentParam, subOperationVO, adjustmentParamList);
        adjustOperationList.addAll(unPlanParams);
      }
      boolean qtyChangeFlag = false;
      boolean timeOrResourceChangeFlag = false;
      // 排产数量发生了变化
      if (adjustmentParam.getQty().compareTo(subOperationVO.getQuantity()) != 0) {
        qtyChangeFlag = true;
      }
      // 排产时间或资源发生变化
      if (!targetResourceId.equals(sourceResourceId)
          || DateUtils.stringToDate(adjustmentParam.getAppointStartTime(), COMMON_DATE_STR1)
                  .compareTo(subOperationVO.getStartTime())
              != 0) {
        timeOrResourceChangeFlag = true;
      }
      // 构建手工调整参数
      adjustmentParamList =
          this.getAdjustmentParams(
              planningHorizon,
              adjustmentParam,
              Collections.singletonList(adjustmentParam.getOperationId()),
              subOperationVO,
              adjustmentParamList,
              qtyChangeFlag,
              timeOrResourceChangeFlag);
      allAdjustmentParamList.addAll(adjustmentParamList);
    }
    List<OperationVO> existOperationList =
        adjustOperationList.stream()
            .filter(t -> operationIds.contains(t.getId()))
            .collect(Collectors.toList());
    if (CollectionUtils.isEmpty(existOperationList)) {
      adjustOperationList.addAll(operationVOList);
    }
    return this.mergeAdjustmentParamList(allAdjustmentParamList);
  }

  /**
   * 组装子工序手工调整算法的参数
   *
   * @param subOperations
   * @param parentAdjustParam
   * @return
   */
  public List<RzzAdjustmentParam> getRzzAdjustmentParams(
      List<OperationPO> subOperations, ManualAdjustParam parentAdjustParam) {
    List<RzzAdjustmentParam> adjustmentParams = Lists.newArrayList();
    for (OperationPO subOperation : subOperations) {
      RzzAdjustmentParam adjustmentParam = new RzzAdjustmentParam();
      adjustmentParam.setOperationId(subOperation.getId());
      adjustmentParam.setQty(subOperation.getQuantity());
      adjustmentParam.setSourceResourceId(subOperation.getPlannedResourceId());
      adjustmentParam.setTargetResourceId(parentAdjustParam.getTargetInfo().getResourceId());
      adjustmentParam.setAppointStartTime(parentAdjustParam.getTargetInfo().getAppointStartTime());
      adjustmentParam.setAdjustQuantityFlag(false);
      adjustmentParams.add(adjustmentParam);
    }
    return adjustmentParams;
  }

  /**
   * 获取排产计划参数
   *
   * @param adjustOperation
   * @param adjustParam
   * @return
   */
  public List<RzzAdjustmentParam> getPlannedAdjustParams(
      OperationPO adjustOperation, ManualAdjustParam adjustParam) {
    String parentId = adjustOperation.getId();
    List<OperationPO> subOperations =
        operationDao.selectByParentIds(Collections.singletonList(parentId));
    if (CollectionUtils.isEmpty(subOperations)) {
      return Lists.newArrayList();
    }
    return getRzzAdjustmentParams(subOperations, adjustParam);
  }

  public boolean checkHWOperation(
      ManualAdjustParam adjustParam,
      OperationPO adjustOperation,
      Map<String, PhysicalResourceVO> physicalResourceVOMap,
      Map<String, StandardResourceVO> standardResourceVOMap) {
    PhysicalResourceVO physicalResourceVO =
        physicalResourceVOMap.get(adjustParam.getTargetInfo().getResourceId());
    StandardResourceVO standardResourceVO =
        standardResourceVOMap.get(physicalResourceVO.getStandardResourceId());
    if (PlanStatusEnum.UNPLAN.getCode().equals(adjustOperation.getPlanStatus())) {
      return false;
    } else {
      // 调整工序是已计划的则判断是否是连续炉之间的调整
      PhysicalResourceVO sourcePhysicalResourceVO =
          physicalResourceVOMap.get(adjustParam.getSourceInfo().getResourceId());
      StandardResourceVO sourceStandardResourceVO =
          standardResourceVOMap.get(sourcePhysicalResourceVO.getStandardResourceId());
      return "S1HW".equals(standardResourceVO.getStandardResourceCode())
          && "S1HW".equals(sourceStandardResourceVO.getStandardResourceCode());
    }
  }

  /**
   * 根据工序寻找对应的大小片工序
   *
   * @param operationIds
   * @return
   */
  public List<String> getBigAndSmallOperationIds(List<String> operationIds) {
    List<String> bigAndSmallOperationIds = operationIds;
    List<OperationPO> operationPOS = operationDao.selectByPrimaryKeys(operationIds);
    if (CollectionUtils.isEmpty(operationPOS)) {
      return bigAndSmallOperationIds;
    }
    Integer routingStepSequenceNo = operationPOS.get(0).getRoutingStepSequenceNo();
    Map<String, OperationPO> operationMap =
        operationPOS.stream()
            .collect(Collectors.toMap(OperationPO::getId, Function.identity(), (v1, v2) -> v1));
    List<String> workOrderIds =
        operationPOS.stream()
            .map(OperationBasicPO::getOrderId)
            .distinct()
            .collect(Collectors.toList());
    List<WorkOrderPO> workOrders = workOrderDao.selectByPrimaryKeys(workOrderIds);
    Map<String, WorkOrderPO> workOrderMap =
        workOrders.stream()
            .collect(Collectors.toMap(WorkOrderPO::getId, Function.identity(), (v1, v2) -> v1));
    List<String> topWorkOrderIds =
        workOrders.stream()
            .map(WorkOrderPO::getTopOrderId)
            .filter(StringUtils::isNotBlank)
            .distinct()
            .collect(Collectors.toList());
    if (CollectionUtils.isEmpty(topWorkOrderIds)) {
      return bigAndSmallOperationIds;
    }
    List<WorkOrderVO> workOrderVOS = workOrderService.selectByParentIds(topWorkOrderIds);
    // 包含大小片的所有订单
    for (WorkOrderVO workOrderVO : workOrderVOS) {
      if (workOrderIds.contains(workOrderVO.getId())) {
        continue;
      }
      workOrderIds.add(workOrderVO.getId());
    }
    Map<String, List<WorkOrderVO>> topWorkOrderMap =
        CollectionUtils.isEmpty(workOrderVOS)
            ? MapUtil.newHashMap()
            : workOrderVOS.stream().collect(Collectors.groupingBy(WorkOrderVO::getTopOrderId));
    List<OperationVO> totalOperations = operationService.selectByWorkOrderIds(workOrderIds);
    Map<String, List<OperationVO>> orderOperationMap =
        totalOperations.stream().collect(Collectors.groupingBy(OperationVO::getOrderId));
    List<String> newOperationIds = Lists.newArrayList();
    for (String operationId : operationIds) {
      if (newOperationIds.contains(operationId)) {
        continue;
      }
      OperationPO operationPO = operationMap.get(operationId);
      String orderId = operationPO.getOrderId();
      WorkOrderPO orderPO = workOrderMap.get(orderId);
      String topOrderId = orderPO.getTopOrderId();
      if (StringUtils.isBlank(topOrderId)) {
        newOperationIds.add(operationId);
        continue;
      }
      List<WorkOrderVO> workOrderVOList = topWorkOrderMap.get(topOrderId);
      if (CollectionUtils.isEmpty(workOrderVOList)) {
        newOperationIds.add(operationId);
        continue;
      }
      for (WorkOrderVO workOrderVO : workOrderVOList) {
        List<OperationVO> operationVOS = orderOperationMap.get(workOrderVO.getId());
        if (CollectionUtils.isEmpty(operationVOS)) {
          continue;
        }
        operationVOS.stream()
            .filter(
                x ->
                    StringUtils.isNotBlank(x.getParentId())
                        && x.getRoutingStepSequenceNo().equals(routingStepSequenceNo))
            .forEach(x -> newOperationIds.add(x.getId()));
      }
    }
    bigAndSmallOperationIds = newOperationIds;
    return bigAndSmallOperationIds;
  }
}
