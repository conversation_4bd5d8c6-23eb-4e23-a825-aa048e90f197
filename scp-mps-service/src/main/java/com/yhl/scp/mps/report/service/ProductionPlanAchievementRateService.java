package com.yhl.scp.mps.report.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mds.extension.calendar.vo.ResourceCalendarVO;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.resource.vo.StandardResourceVO;
import com.yhl.scp.mps.capacityBalance.service.CapacityBalanceVersionService;
import com.yhl.scp.mps.manualAdjust.dao.ManualAdjustHandleDao;
import com.yhl.scp.mps.report.dto.ProductionPlanAchievementRateDTO;
import com.yhl.scp.mps.report.dto.ProductionPlanAchievementRateOrganizationDTO;
import com.yhl.scp.mps.report.dto.ProductionPlanAchievementRateStandardResourceDTO;
import com.yhl.scp.mps.report.vo.ProductionPlanAchievementRateOperation;
import com.yhl.scp.mps.report.vo.ProductionPlanAchievementRatePhysicalResource;
import com.yhl.scp.mps.report.vo.ProductionPlanAchievementRateStandardResource;
import com.yhl.scp.mps.report.vo.ProductionPlanAchievementRateVO;
import com.yhl.scp.sds.extension.feedback.vo.FeedbackProductionVO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.feedback.service.FeedbackProductionService;
import com.yhl.scp.sds.order.service.OperationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ProductionPlanAchievementRateService {

    @Resource
    private ManualAdjustHandleDao manualAdjustHandleDao;

    @Resource
    private CapacityBalanceVersionService capacityBalanceVersionService;

    @Resource
    private OperationService operationService;

    @Resource
    private FeedbackProductionService feedbackProductionService;


    /**
     * 查询标准资源下拉框
     *
     * @param param
     * @return
     */
    public List<LabelValue<String>> selectStandardResourceDropdown(ProductionPlanAchievementRateOrganizationDTO param) {
        List<LabelValue<String>> result = Lists.newArrayList();
        if (Objects.isNull(param)) {
            return result;
        }
        List<String> organizationIds = param.getOrganizationIds();
        if (CollectionUtils.isEmpty(organizationIds)) {
            return result;
        }
        List<StandardResourceVO> standardResources = manualAdjustHandleDao.selectStandardResourceByOrganizationIds(organizationIds);
        if (CollectionUtils.isEmpty(standardResources)) {
            return result;
        }
        standardResources.forEach(x -> result.add(new LabelValue<>(x.getStandardResourceCode() + "-" + x.getStandardResourceName(), x.getId())));
        return result;
    }

    /**
     * 查询物理资源下拉框
     *
     * @param param
     * @return
     */
    public List<LabelValue<String>> selectPhysicalResourceDropdown(ProductionPlanAchievementRateStandardResourceDTO param) {
        List<LabelValue<String>> result = Lists.newArrayList();
        if (Objects.isNull(param)) {
            return result;
        }
        List<String> standardResourceIds = param.getStandardResourceIds();
        if (CollectionUtils.isEmpty(standardResourceIds)) {
            return result;
        }
        List<PhysicalResourceVO> physicalResources = manualAdjustHandleDao.selectPhysicalResourceByStandardResourceIds(standardResourceIds);
        if (CollectionUtils.isEmpty(physicalResources)) {
            return result;
        }
        physicalResources.forEach(x -> result.add(new LabelValue<>(x.getPhysicalResourceCode() + "-" + x.getPhysicalResourceName(), x.getId())));
        return result;
    }

    /**
     * 查询
     *
     * @param param
     * @return
     */
    public BaseResponse<ProductionPlanAchievementRateVO> selectProductionPlanAchievementRateByPage(Pagination pagination, ProductionPlanAchievementRateDTO param) {
        paramsCheck(param);
        ProductionPlanAchievementRateVO result = new ProductionPlanAchievementRateVO();
        // 处理表头动态列
        List<String> headerNames = Lists.newArrayList();
        List<Date> headerDates = getHeaderDates(headerNames, param.getStartDate(), param.getEndDate());
        result.setHeaderNames(headerNames);
        try {
            List<String> standardResourceIds = param.getStandardResourceIds();
            PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
            List<ProductionPlanAchievementRateStandardResource> standardResourceAchievementRates = manualAdjustHandleDao.selectStandardResourceByIds(standardResourceIds);
            if (CollectionUtils.isEmpty(standardResourceAchievementRates)) {
                result.setPageInfo(new PageInfo<>(standardResourceAchievementRates));
                return BaseResponse.success(result);
            }
            result.setPageInfo(new PageInfo<>(standardResourceAchievementRates));
            List<PhysicalResourceVO> physicalResources = manualAdjustHandleDao.selectPhysicalResourceByStandardResourceIds(standardResourceIds);
            if (CollectionUtils.isEmpty(physicalResources)) {
                result.setPageInfo(new PageInfo<>(standardResourceAchievementRates));
                return BaseResponse.success(result);
            }
            Date startDate = headerDates.stream().min(Comparator.naturalOrder()).get();
            Date endDate = headerDates.stream().max(Comparator.naturalOrder()).get();
            fillData(standardResourceAchievementRates, physicalResources, headerDates, startDate, endDate, true);
        } catch (Exception ex) {
            log.error("查询异常", ex);
            return BaseResponse.error("查询异常：" + ex.getMessage());
        }
        return BaseResponse.success(result);
    }

    private void fillData(List<ProductionPlanAchievementRateStandardResource> standardResourceAchievementRates, List<PhysicalResourceVO> physicalResources, List<Date> headerDates, Date startDate, Date endDate, Boolean merge) {
        List<String> physicalResourceIds = physicalResources.stream().map(PhysicalResourceVO::getId).distinct().collect(Collectors.toList());
        Map<String, BigDecimal> targetMap = MapUtil.newHashMap();
        String scenario = SystemHolder.getScenario();
        CompletableFuture<Void> targetFuture = CompletableFuture.runAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            targetMap.putAll(capacityBalanceVersionService.getFirstVersionResourceQuantityByPlanPeriod(DateUtils.dateToString(org.apache.commons.lang.time.DateUtils.addDays(new Date(), -1), DateUtils.YEAR_MONTH)));
            DynamicDataSourceContextHolder.clearDataSource();
        });
        Map<String, List<OperationVO>> operationResourceMap = MapUtil.newHashMap();
        CompletableFuture<Void> operationFuture = CompletableFuture.runAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            List<OperationVO> operationVOS = manualAdjustHandleDao.selectOperationByResourceIdsAndDate(startDate, org.apache.commons.lang.time.DateUtils.addDays(endDate, 1), physicalResourceIds);
            operationResourceMap.putAll(CollectionUtils.isEmpty(operationVOS) ? MapUtil.newHashMap() : operationVOS.stream().collect(Collectors.groupingBy(OperationVO::getPlannedResourceId)));
            DynamicDataSourceContextHolder.clearDataSource();
        });
        Map<String, List<FeedbackProductionVO>> feedbackProductionMap = MapUtil.newHashMap();
        CompletableFuture<Void> feedbackProductionFuture = CompletableFuture.runAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            List<FeedbackProductionVO> feedbackProductionVOS = manualAdjustHandleDao.selectFeedbackProductionByResourceIdsAndDate(startDate, org.apache.commons.lang.time.DateUtils.addDays(endDate, 2), physicalResourceIds);
            feedbackProductionMap.putAll(CollectionUtils.isEmpty(feedbackProductionVOS) ? MapUtil.newHashMap() : feedbackProductionVOS.stream().collect(Collectors.groupingBy(FeedbackProductionVO::getPhysicalResourceId)));
            DynamicDataSourceContextHolder.clearDataSource();
        });
        Map<String, List<ResourceCalendarVO>> resourceCalendarMap = MapUtil.newHashMap();
        CompletableFuture<Void> resourceCalendarFuture = CompletableFuture.runAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            resourceCalendarMap.putAll(getResourceCalendarMap(physicalResourceIds, headerDates));
            DynamicDataSourceContextHolder.clearDataSource();
        });
        CompletableFuture.allOf(targetFuture, operationFuture, feedbackProductionFuture, resourceCalendarFuture).join();
        Map<String, List<PhysicalResourceVO>> physicalResourceMap = physicalResources.stream().collect(Collectors.groupingBy(PhysicalResourceVO::getStandardResourceId));
        List<ProductionPlanAchievementRateStandardResource> newStandardResourceAchievementRates = Lists.newArrayList();
        for (ProductionPlanAchievementRateStandardResource standardResource : standardResourceAchievementRates) {
            String standardResourceId = standardResource.getStandardResourceId();
            List<PhysicalResourceVO> physicalResourceList = physicalResourceMap.get(standardResourceId);
            List<ProductionPlanAchievementRatePhysicalResource> physicalResourceRows = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(physicalResourceList)) {
                for (PhysicalResourceVO physicalResourceVO : physicalResourceList) {
                    ProductionPlanAchievementRatePhysicalResource productionPlanAchievementRatePhysicalResource = new ProductionPlanAchievementRatePhysicalResource();
                    String physicalResourceId = physicalResourceVO.getId();
                    String physicalResourceCode = physicalResourceVO.getPhysicalResourceCode();
                    String physicalResourceName = physicalResourceVO.getPhysicalResourceName();
                    productionPlanAchievementRatePhysicalResource.setPhysicalResourceId(physicalResourceId);
                    productionPlanAchievementRatePhysicalResource.setPhysicalResourceCode(physicalResourceCode);
                    productionPlanAchievementRatePhysicalResource.setPhysicalResourceName(physicalResourceName);
                    productionPlanAchievementRatePhysicalResource.setTargetQuantity(targetMap.get(physicalResourceCode));
                    Map<String, ProductionPlanAchievementRateOperation> operationMap = getOperationMap(headerDates, operationResourceMap.getOrDefault(physicalResourceId, Lists.newArrayList()), feedbackProductionMap.getOrDefault(physicalResourceId, Lists.newArrayList()), resourceCalendarMap.getOrDefault(physicalResourceId, Lists.newArrayList()));
                    productionPlanAchievementRatePhysicalResource.setOperationMap(operationMap);
                    ProductionPlanAchievementRateOperation totalOperations = getProductionPlanAchievementRateOperation(operationMap);
                    productionPlanAchievementRatePhysicalResource.setTotalOperations(totalOperations);
                    physicalResourceRows.add(productionPlanAchievementRatePhysicalResource);
                }
            }
            standardResource.setPhysicalResources(physicalResourceRows);
            newStandardResourceAchievementRates.add(standardResource);
            if (merge) {
                // 添加合计行
                newStandardResourceAchievementRates.add(getMergeRow(headerDates, physicalResourceRows));
            }
        }
        standardResourceAchievementRates.clear();
        standardResourceAchievementRates.addAll(newStandardResourceAchievementRates);
    }

    /**
     * 达成量作为合计总量
     *
     * @param headerDates
     * @param physicalResourceRows
     * @return
     */
    private ProductionPlanAchievementRateStandardResource getMergeRow(List<Date> headerDates, List<ProductionPlanAchievementRatePhysicalResource> physicalResourceRows) {
        ProductionPlanAchievementRateStandardResource mergeResource = new ProductionPlanAchievementRateStandardResource();
        mergeResource.setStandardResourceId("合计");
        mergeResource.setStandardResourceCode("合计");
        mergeResource.setStandardResourceName("合计");
        List<ProductionPlanAchievementRatePhysicalResource> physicalResources = Lists.newArrayList();
        ProductionPlanAchievementRatePhysicalResource ratePhysicalResource = new ProductionPlanAchievementRatePhysicalResource();
        ratePhysicalResource.setPhysicalResourceId("合计");
        ratePhysicalResource.setPhysicalResourceCode("合计");
        ratePhysicalResource.setPhysicalResourceName("合计");
        BigDecimal targetQuantity = null;
        BigDecimal totalAchieveQuantity = null;
        ProductionPlanAchievementRateOperation totalOperations = new ProductionPlanAchievementRateOperation();
        for (ProductionPlanAchievementRatePhysicalResource physicalResourceRow : physicalResourceRows) {
            BigDecimal rowTargetQuantity = physicalResourceRow.getTargetQuantity();
            if (Objects.nonNull(rowTargetQuantity)) {
                if (Objects.isNull(targetQuantity)) {
                    targetQuantity = BigDecimal.ZERO;
                }
                targetQuantity = targetQuantity.add(rowTargetQuantity);
            }
            BigDecimal rowAchieveQuantity = physicalResourceRow.getTotalOperations().getAchieveQuantity();
            if (Objects.nonNull(rowAchieveQuantity)) {
                if (Objects.isNull(totalAchieveQuantity)) {
                    totalAchieveQuantity = BigDecimal.ZERO;
                }
                totalAchieveQuantity = totalAchieveQuantity.add(rowAchieveQuantity);
            }
        }
        totalOperations.setPlanQuantity(totalAchieveQuantity);
        ratePhysicalResource.setTargetQuantity(targetQuantity);
        ratePhysicalResource.setTotalOperations(totalOperations);
        Map<String, ProductionPlanAchievementRateOperation> mergeOperationMap = MapUtil.newHashMap();
        for (Date headerDate : headerDates) {
            String key = DateUtils.dateToString(headerDate, DateUtils.COMMON_DATE_STR3);
            ProductionPlanAchievementRateOperation mergeRateOperation = new ProductionPlanAchievementRateOperation();
            BigDecimal mergeAchieveQuantity = null;
            for (ProductionPlanAchievementRatePhysicalResource physicalResourceRow : physicalResourceRows) {
                Map<String, ProductionPlanAchievementRateOperation> operationMap = physicalResourceRow.getOperationMap();
                if (MapUtil.isEmpty(operationMap)) {
                    continue;
                }
                ProductionPlanAchievementRateOperation productionPlanAchievementRateOperation = operationMap.get(key);
                BigDecimal achieveQuantity = productionPlanAchievementRateOperation.getAchieveQuantity();
                if (Objects.nonNull(achieveQuantity)) {
                    if (Objects.isNull(mergeAchieveQuantity)) {
                        mergeAchieveQuantity = BigDecimal.ZERO;
                    }
                    mergeAchieveQuantity = mergeAchieveQuantity.add(achieveQuantity);
                }
            }
            mergeRateOperation.setPlanQuantity(mergeAchieveQuantity);
            mergeOperationMap.put(key, mergeRateOperation);
        }
        ratePhysicalResource.setOperationMap(mergeOperationMap);
        physicalResources.add(ratePhysicalResource);
        mergeResource.setPhysicalResources(physicalResources);
        return mergeResource;
    }

    private Map<String, List<ResourceCalendarVO>> getResourceCalendarMap(List<String> resourceIds, List<Date> headerDates) {
        Map<String, List<ResourceCalendarVO>> result = MapUtil.newHashMap();
        if (CollectionUtils.isEmpty(resourceIds) || CollectionUtils.isEmpty(headerDates)) {
            return result;
        }
        Map<String, Object> queryParam = MapUtil.newHashMap();
        queryParam.put("physicalResourceIds", resourceIds);
        queryParam.put("workDayList", headerDates);
        queryParam.put("enabled", YesOrNoEnum.YES.getCode());
        List<ResourceCalendarVO> resourceCalendarVOS = manualAdjustHandleDao.selectResourceCalendarsByParams(queryParam);
        if (CollectionUtils.isEmpty(resourceCalendarVOS)) {
            return result;
        }
        return resourceCalendarVOS.stream().collect(Collectors.groupingBy(ResourceCalendarVO::getPhysicalResourceId));
    }

    /**
     * 获取动态列信息
     *
     * @param headerDates
     * @param operationVOS
     * @param feedbackProductionVOS
     * @param resourceCalendarVOS
     * @return
     */
    private Map<String, ProductionPlanAchievementRateOperation> getOperationMap(List<Date> headerDates, List<OperationVO> operationVOS, List<FeedbackProductionVO> feedbackProductionVOS, List<ResourceCalendarVO> resourceCalendarVOS) {
        Map<String, ProductionPlanAchievementRateOperation> result = MapUtil.newHashMap();
        Map<String, BigDecimal> planQuantityMap = MapUtil.newHashMap();
        Map<String, BigDecimal> achieveQuantityMap = MapUtil.newHashMap();
        operationVOS.stream().collect(Collectors.groupingBy(x -> DateUtils.dateToString(x.getStartTime(), DateUtils.COMMON_DATE_STR3))).forEach((key, value) -> {
            BigDecimal planQuantity = value.stream().map(OperationVO::getQuantity).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            planQuantityMap.put(key, planQuantity);
        });
        feedbackProductionVOS.stream().collect(Collectors.groupingBy(x -> getCalculateDate(resourceCalendarVOS, x.getReportingTime()))).forEach((key, value) -> {
            BigDecimal planQuantity = value.stream().map(FeedbackProductionVO::getReportingQuantity).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            achieveQuantityMap.put(key, planQuantity);
        });
        for (Date headerDate : headerDates) {
            String key = DateUtils.dateToString(headerDate, DateUtils.COMMON_DATE_STR3);
            ProductionPlanAchievementRateOperation achievementRateOperation = new ProductionPlanAchievementRateOperation();
            BigDecimal planQuantity = planQuantityMap.get(key);
            achievementRateOperation.setPlanQuantity(planQuantity);
            if (Objects.nonNull(planQuantity)) {
                BigDecimal achieveQuantity = achieveQuantityMap.get(key);
                achievementRateOperation.setAchieveQuantity(achieveQuantity);
            }
            result.put(key, achievementRateOperation);
        }
        return result;
    }

    /**
     * 计算时间所属班次时间
     *
     * @param resourceCalendarVOS
     * @param reportingTime
     * @return
     */
    private String getCalculateDate(List<ResourceCalendarVO> resourceCalendarVOS, Date reportingTime) {
        for (ResourceCalendarVO resourceCalendarVO : resourceCalendarVOS) {
            if (!reportingTime.after(resourceCalendarVO.getEndTime()) && !reportingTime.before(resourceCalendarVO.getStartTime())) {
                return DateUtils.dateToString(resourceCalendarVO.getWorkDay(), DateUtils.COMMON_DATE_STR3);
            }
        }
        return "0";
    }

    /**
     * 获取动态列合计
     *
     * @param operationMap
     * @return
     */
    private ProductionPlanAchievementRateOperation getProductionPlanAchievementRateOperation(Map<String, ProductionPlanAchievementRateOperation> operationMap) {
        ProductionPlanAchievementRateOperation totalOperations = new ProductionPlanAchievementRateOperation();
        BigDecimal planQuantity = null;
        BigDecimal achieveQuantity = null;
        for (ProductionPlanAchievementRateOperation value : operationMap.values()) {
            BigDecimal columnPlanQuantity = value.getPlanQuantity();
            BigDecimal columnAchieveQuantity = value.getAchieveQuantity();
            if (Objects.nonNull(columnPlanQuantity)) {
                if (Objects.isNull(planQuantity)) {
                    planQuantity = BigDecimal.ZERO;
                }
                planQuantity = planQuantity.add(columnPlanQuantity);
            }
            if (Objects.nonNull(columnAchieveQuantity)) {
                if (Objects.isNull(achieveQuantity)) {
                    achieveQuantity = BigDecimal.ZERO;
                }
                achieveQuantity = achieveQuantity.add(columnAchieveQuantity);
            }
        }
        totalOperations.setPlanQuantity(planQuantity);
        totalOperations.setAchieveQuantity(achieveQuantity);
        return totalOperations;
    }

    /**
     * 获取30天的表头日期
     *
     * @return
     */
    private List<Date> getHeaderDates(List<String> headerNames, Date startDate, Date endDate) {
        List<Date> result = Lists.newArrayList();
        if (startDate == null && endDate == null) {
            Date currentDate = DateUtils.formatDate(DateUtil.date(), DateUtils.COMMON_DATE_STR3);
            for (int i = 30; i > 0; i--) {
                Date date = org.apache.commons.lang.time.DateUtils.addDays(currentDate, -i);
                headerNames.add(DateUtils.dateToString(date, DateUtils.COMMON_DATE_STR3));
                result.add(date);
            }
        } else if (startDate != null && endDate != null) {
            while (!startDate.after(endDate)) {
                headerNames.add(DateUtils.dateToString(startDate, DateUtils.COMMON_DATE_STR3));
                result.add(startDate);
                startDate = org.apache.commons.lang.time.DateUtils.addDays(startDate, 1);
            }
        }
        return result;
    }

    /**
     * 导出
     *
     * @param param
     * @param response
     */
    public void export(ProductionPlanAchievementRateDTO param, HttpServletResponse response) {
        paramsCheck(param);
        // 处理表头动态列
        List<String> headerNames = Lists.newArrayList();
        List<Date> headerDates = getHeaderDates(headerNames, param.getStartDate(), param.getEndDate());
        try {
            List<String> standardResourceIds = param.getStandardResourceIds();
            List<ProductionPlanAchievementRateStandardResource> standardResourceAchievementRates = manualAdjustHandleDao.selectStandardResourceByIds(standardResourceIds);
            if (CollectionUtils.isEmpty(standardResourceAchievementRates)) {
                throw new RuntimeException("未查询到标准资源信息");
            }
            List<PhysicalResourceVO> physicalResources = manualAdjustHandleDao.selectPhysicalResourceByStandardResourceIds(standardResourceIds);
            if (CollectionUtils.isEmpty(physicalResources)) {
                throw new RuntimeException("未查询到排产资源信息");
            }
            Date startDate = headerDates.stream().min(Comparator.naturalOrder()).get();
            Date endDate = headerDates.stream().max(Comparator.naturalOrder()).get();
            fillData(standardResourceAchievementRates, physicalResources, headerDates, startDate, endDate, true);
            // 获取完整头部
            List<String> totalHeaderNames = getAllHeaderNames(headerNames);
            exportData(response, totalHeaderNames, headerNames, standardResourceAchievementRates);
        } catch (Exception ex) {
            log.error("查询异常", ex);
            throw new RuntimeException("导出失败：" + ex.getMessage());
        }
    }

    private void exportData(HttpServletResponse response, List<String> totalHeaderNames, List<String> headerNames, List<ProductionPlanAchievementRateStandardResource> standardResourceAchievementRates) {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("生产计划达成率报表");
            // 创建表头
            Row headerRow = sheet.createRow(0);
            for (int i = 0; i < totalHeaderNames.size(); i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(totalHeaderNames.get(i));
            }
            // 设置标题行样式
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerFont.setColor(IndexedColors.BLACK.getIndex());
            headerStyle.setFont(headerFont);

            for (Cell cell : headerRow) {
                cell.setCellStyle(headerStyle);
            }

            AtomicInteger rowNum = new AtomicInteger(1);
            AtomicInteger lastRow = new AtomicInteger(0);

            for (ProductionPlanAchievementRateStandardResource resource : standardResourceAchievementRates) {
                String standardResourceCode = resource.getStandardResourceCode();
                boolean merge = "合计".equals(standardResourceCode);
                List<ProductionPlanAchievementRatePhysicalResource> physicalResources = resource.getPhysicalResources();
                if (CollectionUtils.isEmpty(physicalResources)) {
                    Row row = sheet.createRow(rowNum.getAndIncrement());
                    row.createCell(0).setCellValue(standardResourceCode);
                    continue;
                }
                for (ProductionPlanAchievementRatePhysicalResource physicalResource : physicalResources) {
                    String physicalResourceCode = physicalResource.getPhysicalResourceCode();
                    if (merge) {
                        handleMergeRow(physicalResource, workbook, sheet, rowNum, lastRow, headerNames);
                        continue;
                    }
                    Row firstRow = sheet.createRow(rowNum.getAndIncrement());
                    firstRow.createCell(0).setCellValue(standardResourceCode);
                    firstRow.createCell(1).setCellValue(physicalResourceCode);
                    Cell firstRowCell2 = firstRow.createCell(2);
                    if (Objects.nonNull(physicalResource.getTargetQuantity())) {
                        firstRowCell2.setCellValue(physicalResource.getTargetQuantity().doubleValue());
                    }
                    ProductionPlanAchievementRateOperation firstTotalOperations = physicalResource.getTotalOperations();
                    Cell firstRowCell3 = firstRow.createCell(3);
                    if (Objects.nonNull(firstTotalOperations.getPlanQuantity())) {
                        firstRowCell3.setCellValue(firstTotalOperations.getPlanQuantity().doubleValue());
                    }
                    Map<String, ProductionPlanAchievementRateOperation> firstOperationMap = physicalResource.getOperationMap();
                    int firstColIndex = 4;
                    for (String dateKey : headerNames) {
                        ProductionPlanAchievementRateOperation op = firstOperationMap.get(dateKey);
                        Cell cell = firstRow.createCell(firstColIndex++);
                        if (Objects.nonNull(op.getPlanQuantity())) {
                            cell.setCellValue(op.getPlanQuantity().doubleValue());
                        } else {
                            cell.setCellValue("停机");
                        }
                    }
                    Row secondRow = sheet.createRow(rowNum.getAndIncrement());
                    secondRow.createCell(0).setCellValue(standardResourceCode);
                    secondRow.createCell(1).setCellValue(physicalResourceCode);
                    Cell secondRowCell2 = secondRow.createCell(2);
                    if (Objects.nonNull(physicalResource.getTargetQuantity())) {
                        secondRowCell2.setCellValue(physicalResource.getTargetQuantity().doubleValue());
                    }
                    ProductionPlanAchievementRateOperation secondTotalOperations = physicalResource.getTotalOperations();
                    Cell secondRowCell3 = secondRow.createCell(3);
                    if (Objects.nonNull(secondTotalOperations.getAchieveQuantity())) {
                        secondRowCell3.setCellValue(secondTotalOperations.getAchieveQuantity().doubleValue());
                    }

                    Map<String, ProductionPlanAchievementRateOperation> secondOperationMap = physicalResource.getOperationMap();
                    int secondColIndex = 4;
                    for (String dateKey : headerNames) {
                        ProductionPlanAchievementRateOperation op = secondOperationMap.get(dateKey);
                        String value = (Objects.nonNull(op.getAchieveQuantity()) ? op.getAchieveQuantity().toString() : "");
                        secondRow.createCell(secondColIndex++).setCellValue(value);
                    }
                    Row thirdRow = sheet.createRow(rowNum.getAndIncrement());
                    thirdRow.createCell(0).setCellValue(standardResourceCode);
                    thirdRow.createCell(1).setCellValue(physicalResourceCode);
                    Cell thirdRowCell2 = thirdRow.createCell(2);
                    if (Objects.nonNull(physicalResource.getTargetQuantity())) {
                        thirdRowCell2.setCellValue(physicalResource.getTargetQuantity().doubleValue());
                    }

                    ProductionPlanAchievementRateOperation thirdTotalOperations = physicalResource.getTotalOperations();
                    thirdRow.createCell(3).setCellValue((Objects.nonNull(thirdTotalOperations.getAchieveRate()) ? thirdTotalOperations.getAchieveRate().toString() + "%" : ""));

                    Map<String, ProductionPlanAchievementRateOperation> thirdOperationMap = physicalResource.getOperationMap();
                    int thirdColIndex = 4;
                    for (String dateKey : headerNames) {
                        ProductionPlanAchievementRateOperation op = thirdOperationMap.get(dateKey);
                        String value = (Objects.nonNull(op.getAchieveRate()) ? op.getAchieveRate().toString() + "%" : "");
                        Cell cell = thirdRow.createCell(thirdColIndex++);
                        cell.setCellValue(value);
                        CellStyle style = workbook.createCellStyle();
                        style.setAlignment(HorizontalAlignment.CENTER);
                        style.setVerticalAlignment(VerticalAlignment.CENTER);
                        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                        style.setBorderTop(BorderStyle.MEDIUM);
                        style.setBorderBottom(BorderStyle.MEDIUM);
                        style.setBorderLeft(BorderStyle.MEDIUM);
                        style.setBorderRight(BorderStyle.MEDIUM);
                        if (Objects.nonNull(op.getAchieveRate()) && op.getAchieveRate().compareTo(new BigDecimal(95)) <= 0) {
                            style.setFillForegroundColor(IndexedColors.RED.getIndex());
                        } else {
                            style.setFillForegroundColor(IndexedColors.GREEN.getIndex());
                        }
                        cell.setCellStyle(style);
                    }
                    sheet.addMergedRegion(new CellRangeAddress(rowNum.get() - 3, rowNum.get() - 1, 1, 1));
                    sheet.addMergedRegion(new CellRangeAddress(rowNum.get() - 3, rowNum.get() - 1, 2, 2));
                }
                if (!merge) {
                    int physicalSize = 3 * physicalResources.size();
                    if (physicalSize > 1) {
                        sheet.addMergedRegion(new CellRangeAddress(lastRow.get() + 1, lastRow.get() + physicalSize, 0, 0));
                    }
                    lastRow.addAndGet(physicalSize);
                }
            }
            // 自动调整列宽
            // 设置列宽并应用边框样式
            for (int i = 0; i < totalHeaderNames.size(); i++) {
                sheet.autoSizeColumn(i);
                // 设置最小宽度为15个字符
                int currentWidth = sheet.getColumnWidth(i);
                // 256是POI中1个字符的宽度单位
                int minWidth = 15 * 256;
                if (currentWidth < minWidth) {
                    sheet.setColumnWidth(i, minWidth);
                }
            }
            // 为所有行设置边框，包括已有样式的单元格
            for (Row row : sheet) {
                for (Cell cell : row) {
                    CellStyle cellStyle = cell.getCellStyle();
                    CellStyle newStyle = workbook.createCellStyle();
                    if (Objects.nonNull(cellStyle)) {
                        newStyle.cloneStyleFrom(cellStyle);
                    }
                    newStyle.setBorderTop(BorderStyle.MEDIUM);
                    newStyle.setBorderBottom(BorderStyle.MEDIUM);
                    newStyle.setBorderLeft(BorderStyle.MEDIUM);
                    newStyle.setBorderRight(BorderStyle.MEDIUM);
                    newStyle.setAlignment(HorizontalAlignment.CENTER);
                    newStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                    cell.setCellStyle(newStyle);
                }
            }
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("生产计划达成率报表", "UTF-8") + ".xlsx");
            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            log.error("导出Excel异常", e);
            throw new RuntimeException("导出失败：" + e.getMessage());
        }
    }

    private void handleMergeRow(ProductionPlanAchievementRatePhysicalResource physicalResource, Workbook workbook, Sheet sheet, AtomicInteger rowNum, AtomicInteger lastRow, List<String> headerNames) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderTop(BorderStyle.MEDIUM);
        style.setBorderBottom(BorderStyle.MEDIUM);
        style.setBorderLeft(BorderStyle.MEDIUM);
        style.setBorderRight(BorderStyle.MEDIUM);
        style.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        Row mergeRow = sheet.createRow(rowNum.getAndIncrement());
        Cell cell0 = mergeRow.createCell(0);
        cell0.setCellStyle(style);
        cell0.setCellValue("合计");
        Cell cell1 = mergeRow.createCell(1);
        cell1.setCellStyle(style);
        cell1.setCellValue("合计");
        sheet.addMergedRegion(new CellRangeAddress(lastRow.get() + 1, lastRow.get() + 1, 0, 1));
        Cell cell2 = mergeRow.createCell(2);
        cell2.setCellStyle(style);
        if (Objects.nonNull(physicalResource.getTargetQuantity())) {
            cell2.setCellValue(physicalResource.getTargetQuantity().doubleValue());
        }
        Cell cell3 = mergeRow.createCell(3);
        cell3.setCellStyle(style);
        ProductionPlanAchievementRateOperation totalOperations = physicalResource.getTotalOperations();
        if (Objects.nonNull(totalOperations) && Objects.nonNull(totalOperations.getPlanQuantity())) {
            cell3.setCellValue(totalOperations.getPlanQuantity().doubleValue());
        }
        Map<String, ProductionPlanAchievementRateOperation> operationMap = physicalResource.getOperationMap();
        int secondColIndex = 4;
        for (String dateKey : headerNames) {
            ProductionPlanAchievementRateOperation op = operationMap.get(dateKey);
            Cell cell = mergeRow.createCell(secondColIndex++);
            cell.setCellStyle(style);
            if (Objects.nonNull(op.getPlanQuantity())) {
                cell.setCellValue(op.getPlanQuantity().doubleValue());
            }
        }
        lastRow.getAndIncrement();
    }

    /**
     * 获取完整头部
     *
     * @param headerNames
     * @return
     */
    private List<String> getAllHeaderNames(List<String> headerNames) {
        List<String> result = Lists.newArrayList();
        result.add("产线组");
        result.add("产线");
        result.add("产销会目标");
        result.add("计划数量/累计达成数");
        result.addAll(headerNames);
        return result;
    }

    private void paramsCheck(ProductionPlanAchievementRateDTO param) {
        Date startDate = param.getStartDate();
        Date endDate = param.getEndDate();
        if (startDate == null && endDate == null) {
            return; // 合法：同时为 null
        }
        if (startDate != null && endDate != null) {
            if (startDate.after(endDate)) {
                throw new BusinessException("开始时间不能晚于结束时间");
            }
            return; // 合法：均不为 null 且 startDate <= endDate
        }

        throw new BusinessException("开始时间 和 结束时间 必须同时为 null 或同时不为 null");

    }
}
