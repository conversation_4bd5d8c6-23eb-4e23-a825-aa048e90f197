<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mps.feedback.infrastructure.dao.MesFeedbackDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mps.feedback.infrastructure.po.MesFeedbackPO">
        <!--@Table sds_mes_feedback-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="item_code" jdbcType="VARCHAR" property="itemCode"/>
        <result column="cell_operation_num" jdbcType="VARCHAR" property="cellOperationNum"/>
        <result column="property_value" jdbcType="VARCHAR" property="propertyValue"/>
        <result column="transaction_code" jdbcType="VARCHAR" property="transactionCode"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="trx_qty" jdbcType="VARCHAR" property="trxQty"/>
        <result column="business_unit" jdbcType="VARCHAR" property="businessUnit"/>
        <result column="transaction_desc" jdbcType="VARCHAR" property="transactionDesc"/>
        <result column="transaction_type" jdbcType="VARCHAR" property="transactionType"/>
        <result column="wip_entity_name" jdbcType="VARCHAR" property="wipEntityName"/>
        <result column="operation_num" jdbcType="INTEGER" property="operationNum"/>
        <result column="kid" jdbcType="INTEGER" property="kid"/>
        <result column="shift_date" jdbcType="TIMESTAMP" property="shiftDate"/>
        <result column="prod_line_code" jdbcType="VARCHAR" property="prodLineCode"/>
        <result column="event_time" jdbcType="TIMESTAMP" property="eventTime"/>
        <result column="plant_code" jdbcType="VARCHAR" property="plantCode"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mps.feedback.vo.MesFeedbackVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        iditem_codecell_operation_numproperty_valuetransaction_codeuser_nametrx_qtybusiness_unittransaction_desctransaction_typewip_entity_nameoperation_numkidshift_dateprod_line_codeevent_timeplant_coderemarkenabledcreatorcreate_timemodifiermodify_time

    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.itemCode != null and params.itemCode != ''">
                and item_code = #{params.itemCode,jdbcType=VARCHAR}
            </if>
            <if test="params.cellOperationNum != null and params.cellOperationNum != ''">
                and cell_operation_num = #{params.cellOperationNum,jdbcType=VARCHAR}
            </if>
            <if test="params.propertyValue != null and params.propertyValue != ''">
                and property_value = #{params.propertyValue,jdbcType=VARCHAR}
            </if>
            <if test="params.transactionCode != null and params.transactionCode != ''">
                and transaction_code = #{params.transactionCode,jdbcType=VARCHAR}
            </if>
            <if test="params.userName != null and params.userName != ''">
                and user_name = #{params.userName,jdbcType=VARCHAR}
            </if>
            <if test="params.trxQty != null">
                and trx_qty = #{params.trxQty,jdbcType=VARCHAR}
            </if>
            <if test="params.businessUnit != null and params.businessUnit != ''">
                and business_unit = #{params.businessUnit,jdbcType=VARCHAR}
            </if>
            <if test="params.transactionDesc != null and params.transactionDesc != ''">
                and transaction_desc = #{params.transactionDesc,jdbcType=VARCHAR}
            </if>
            <if test="params.transactionType != null and params.transactionType != ''">
                and transaction_type = #{params.transactionType,jdbcType=VARCHAR}
            </if>
            <if test="params.wipEntityName != null and params.wipEntityName != ''">
                and wip_entity_name = #{params.wipEntityName,jdbcType=VARCHAR}
            </if>
            <if test="params.operationNum != null">
                and operation_num = #{params.operationNum,jdbcType=INTEGER}
            </if>
            <if test="params.kid != null">
                and kid = #{params.kid,jdbcType=INTEGER}
            </if>
            <if test="params.shiftDate != null">
                and shift_date = #{params.shiftDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.prodLineCode != null and params.prodLineCode != ''">
                and prod_line_code = #{params.prodLineCode,jdbcType=VARCHAR}
            </if>
            <if test="params.eventTime != null">
                and event_time = #{params.eventTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.plantCode != null and params.plantCode != ''">
                and plant_code = #{params.plantCode,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sds_mes_feedback
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sds_mes_feedback
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_sds_mes_feedback
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sds_mes_feedback
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_sds_mes_feedback
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增（带主键） -->
    <insert id="insert" parameterType="com.yhl.scp.mps.feedback.infrastructure.po.MesFeedbackPO">
        insert into sds_mes_feedback(id,
                                     item_code,
                                     cell_operation_num,
                                     property_value,
                                     transaction_code,
                                     user_name,
                                     trx_qty,
                                     business_unit,
                                     transaction_desc,
                                     transaction_type,
                                     wip_entity_name,
                                     operation_num,
                                     kid,
                                     shift_date,
                                     prod_line_code,
                                     event_time,
                                     plant_code,
                                     remark,
                                     enabled,
                                     creator,
                                     create_time,
                                     modifier,
                                     modify_time)
        values (#{id,jdbcType=VARCHAR},
                #{itemCode,jdbcType=VARCHAR},
                #{cellOperationNum,jdbcType=VARCHAR},
                #{propertyValue,jdbcType=VARCHAR},
                #{transactionCode,jdbcType=VARCHAR},
                #{userName,jdbcType=VARCHAR},
                #{trxQty,jdbcType=VARCHAR},
                #{businessUnit,jdbcType=VARCHAR},
                #{transactionDesc,jdbcType=VARCHAR},
                #{transactionType,jdbcType=VARCHAR},
                #{wipEntityName,jdbcType=VARCHAR},
                #{operationNum,jdbcType=INTEGER},
                #{kid,jdbcType=INTEGER},
                #{shiftDate,jdbcType=TIMESTAMP},
                #{prodLineCode,jdbcType=VARCHAR},
                #{eventTime,jdbcType=TIMESTAMP},
                #{plantCode,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP})
    </insert>

    <!-- 批量新增（带主键） -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into sds_mes_feedback(
        id,
        item_code,
        cell_operation_num,
        property_value,
        transaction_code,
        user_name,
        trx_qty,
        business_unit,
        transaction_desc,
        transaction_type,
        wip_entity_name,
        operation_num,
        kid,
        shift_date,
        prod_line_code,
        event_time,
        plant_code,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.itemCode,jdbcType=VARCHAR},
            #{entity.cellOperationNum,jdbcType=VARCHAR},
            #{entity.propertyValue,jdbcType=VARCHAR},
            #{entity.transactionCode,jdbcType=VARCHAR},
            #{entity.userName,jdbcType=VARCHAR},
            #{entity.trxQty,jdbcType=VARCHAR},
            #{entity.businessUnit,jdbcType=VARCHAR},
            #{entity.transactionDesc,jdbcType=VARCHAR},
            #{entity.transactionType,jdbcType=VARCHAR},
            #{entity.wipEntityName,jdbcType=VARCHAR},
            #{entity.operationNum,jdbcType=INTEGER},
            #{entity.kid,jdbcType=INTEGER},
            #{entity.shiftDate,jdbcType=TIMESTAMP},
            #{entity.prodLineCode,jdbcType=VARCHAR},
            #{entity.eventTime,jdbcType=TIMESTAMP},
            #{entity.plantCode,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mps.feedback.infrastructure.po.MesFeedbackPO">
        update sds_mes_feedback
        set item_code          = #{itemCode,jdbcType=VARCHAR},
            cell_operation_num = #{cellOperationNum,jdbcType=VARCHAR},
            property_value     = #{propertyValue,jdbcType=VARCHAR},
            transaction_code   = #{transactionCode,jdbcType=VARCHAR},
            user_name          = #{userName,jdbcType=VARCHAR},
            trx_qty            = #{trxQty,jdbcType=VARCHAR},
            business_unit      = #{businessUnit,jdbcType=VARCHAR},
            transaction_desc   = #{transactionDesc,jdbcType=VARCHAR},
            transaction_type   = #{transactionType,jdbcType=VARCHAR},
            wip_entity_name    = #{wipEntityName,jdbcType=VARCHAR},
            operation_num      = #{operationNum,jdbcType=INTEGER},
            kid                = #{kid,jdbcType=INTEGER},
            shift_date         = #{shiftDate,jdbcType=TIMESTAMP},
            prod_line_code     = #{prodLineCode,jdbcType=VARCHAR},
            event_time         = #{eventTime,jdbcType=TIMESTAMP},
            plant_code         = #{plantCode,jdbcType=VARCHAR},
            remark             = #{remark,jdbcType=VARCHAR},
            enabled            = #{enabled,jdbcType=VARCHAR},
            modifier           = #{modifier,jdbcType=VARCHAR},
            modify_time        = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mps.feedback.infrastructure.po.MesFeedbackPO">
        update sds_mes_feedback
        <set>
            <if test="item.itemCode != null and item.itemCode != ''">
                item_code = #{item.itemCode,jdbcType=VARCHAR},
            </if>
            <if test="item.cellOperationNum != null and item.cellOperationNum != ''">
                cell_operation_num = #{item.cellOperationNum,jdbcType=VARCHAR},
            </if>
            <if test="item.propertyValue != null and item.propertyValue != ''">
                property_value = #{item.propertyValue,jdbcType=VARCHAR},
            </if>
            <if test="item.transactionCode != null and item.transactionCode != ''">
                transaction_code = #{item.transactionCode,jdbcType=VARCHAR},
            </if>
            <if test="item.userName != null and item.userName != ''">
                user_name = #{item.userName,jdbcType=VARCHAR},
            </if>
            <if test="item.trxQty != null">
                trx_qty = #{item.trxQty,jdbcType=VARCHAR},
            </if>
            <if test="item.businessUnit != null and item.businessUnit != ''">
                business_unit = #{item.businessUnit,jdbcType=VARCHAR},
            </if>
            <if test="item.transactionDesc != null and item.transactionDesc != ''">
                transaction_desc = #{item.transactionDesc,jdbcType=VARCHAR},
            </if>
            <if test="item.transactionType != null and item.transactionType != ''">
                transaction_type = #{item.transactionType,jdbcType=VARCHAR},
            </if>
            <if test="item.wipEntityName != null and item.wipEntityName != ''">
                wip_entity_name = #{item.wipEntityName,jdbcType=VARCHAR},
            </if>
            <if test="item.operationNum != null">
                operation_num = #{item.operationNum,jdbcType=INTEGER},
            </if>
            <if test="item.kid != null">
                kid = #{item.kid,jdbcType=INTEGER},
            </if>
            <if test="item.shiftDate != null">
                shift_date = #{item.shiftDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.prodLineCode != null and item.prodLineCode != ''">
                prod_line_code = #{item.prodLineCode,jdbcType=VARCHAR},
            </if>
            <if test="item.eventTime != null">
                event_time = #{item.eventTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.plantCode != null and item.plantCode != ''">
                plant_code = #{item.plantCode,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update sds_mes_feedback
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="item_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.itemCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cell_operation_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.cellOperationNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="property_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.propertyValue,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transaction_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transactionCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="user_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.userName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="trx_qty = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.trxQty,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="business_unit = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.businessUnit,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transaction_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transactionDesc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transaction_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transactionType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="wip_entity_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.wipEntityName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="operation_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.operationNum,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="kid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.kid,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="shift_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.shiftDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="prod_line_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.prodLineCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="event_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.eventTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="plant_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.plantCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update sds_mes_feedback
            <set>
                <if test="item.itemCode != null and item.itemCode != ''">
                    item_code = #{item.itemCode,jdbcType=VARCHAR},
                </if>
                <if test="item.cellOperationNum != null and item.cellOperationNum != ''">
                    cell_operation_num = #{item.cellOperationNum,jdbcType=VARCHAR},
                </if>
                <if test="item.propertyValue != null and item.propertyValue != ''">
                    property_value = #{item.propertyValue,jdbcType=VARCHAR},
                </if>
                <if test="item.transactionCode != null and item.transactionCode != ''">
                    transaction_code = #{item.transactionCode,jdbcType=VARCHAR},
                </if>
                <if test="item.userName != null and item.userName != ''">
                    user_name = #{item.userName,jdbcType=VARCHAR},
                </if>
                <if test="item.trxQty != null">
                    trx_qty = #{item.trxQty,jdbcType=VARCHAR},
                </if>
                <if test="item.businessUnit != null and item.businessUnit != ''">
                    business_unit = #{item.businessUnit,jdbcType=VARCHAR},
                </if>
                <if test="item.transactionDesc != null and item.transactionDesc != ''">
                    transaction_desc = #{item.transactionDesc,jdbcType=VARCHAR},
                </if>
                <if test="item.transactionType != null and item.transactionType != ''">
                    transaction_type = #{item.transactionType,jdbcType=VARCHAR},
                </if>
                <if test="item.wipEntityName != null and item.wipEntityName != ''">
                    wip_entity_name = #{item.wipEntityName,jdbcType=VARCHAR},
                </if>
                <if test="item.operationNum != null">
                    operation_num = #{item.operationNum,jdbcType=INTEGER},
                </if>
                <if test="item.kid != null">
                    kid = #{item.kid,jdbcType=INTEGER},
                </if>
                <if test="item.shiftDate != null">
                    shift_date = #{item.shiftDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.prodLineCode != null and item.prodLineCode != ''">
                    prod_line_code = #{item.prodLineCode,jdbcType=VARCHAR},
                </if>
                <if test="item.eventTime != null">
                    event_time = #{item.eventTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.plantCode != null and item.plantCode != ''">
                    plant_code = #{item.plantCode,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from sds_mes_feedback
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from sds_mes_feedback where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
