package com.yhl.scp.mps.delivery.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mps.delivery.convertor.DeliveryPlanPublishedConvertor;
import com.yhl.scp.mps.delivery.domain.entity.DeliveryPlanPublishedDO;
import com.yhl.scp.mps.delivery.domain.service.DeliveryPlanPublishedDomainService;
import com.yhl.scp.mps.delivery.dto.DeliveryPlanPublishedDTO;
import com.yhl.scp.mps.delivery.dto.DeliveryPlanPublishedParamsDTO;
import com.yhl.scp.mps.delivery.infrastructure.dao.DeliveryPlanPublishedDao;
import com.yhl.scp.mps.delivery.infrastructure.po.DeliveryPlanPublishedPO;
import com.yhl.scp.mps.delivery.service.DeliveryPlanPublishedService;
import com.yhl.scp.mps.delivery.vo.DeliveryPlanPublishedDetailVO;
import com.yhl.scp.mps.delivery.vo.DeliveryPlanPublishedVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanInventoryShiftPageVO;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>DeliveryPlanPublishedServiceImpl</code>
 * <p>
 * 发货计划发布表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-06 14:41:27
 */
@Slf4j
@Service
public class DeliveryPlanPublishedServiceImpl extends AbstractService implements DeliveryPlanPublishedService {

    @Resource
    private DeliveryPlanPublishedDao deliveryPlanPublishedDao;

    @Resource
    private DeliveryPlanPublishedDomainService deliveryPlanPublishedDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    public BaseResponse<Void> doCreate(DeliveryPlanPublishedDTO deliveryPlanPublishedDTO) {
        // 0.数据转换
        DeliveryPlanPublishedDO deliveryPlanPublishedDO = DeliveryPlanPublishedConvertor.INSTANCE.dto2Do(deliveryPlanPublishedDTO);
        DeliveryPlanPublishedPO deliveryPlanPublishedPO = DeliveryPlanPublishedConvertor.INSTANCE.dto2Po(deliveryPlanPublishedDTO);
        // 1.数据校验
        deliveryPlanPublishedDomainService.validation(deliveryPlanPublishedDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(deliveryPlanPublishedPO);
        deliveryPlanPublishedDao.insert(deliveryPlanPublishedPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(DeliveryPlanPublishedDTO deliveryPlanPublishedDTO) {
        // 0.数据转换
        DeliveryPlanPublishedDO deliveryPlanPublishedDO = DeliveryPlanPublishedConvertor.INSTANCE.dto2Do(deliveryPlanPublishedDTO);
        DeliveryPlanPublishedPO deliveryPlanPublishedPO = DeliveryPlanPublishedConvertor.INSTANCE.dto2Po(deliveryPlanPublishedDTO);
        // 1.数据校验
        deliveryPlanPublishedDomainService.validation(deliveryPlanPublishedDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(deliveryPlanPublishedPO);
        deliveryPlanPublishedDao.update(deliveryPlanPublishedPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<DeliveryPlanPublishedDTO> list) {
        List<DeliveryPlanPublishedPO> newList = DeliveryPlanPublishedConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        deliveryPlanPublishedDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<DeliveryPlanPublishedDTO> list) {
        List<DeliveryPlanPublishedPO> newList = DeliveryPlanPublishedConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        deliveryPlanPublishedDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return deliveryPlanPublishedDao.deleteBatch(idList);
        }
        return deliveryPlanPublishedDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public DeliveryPlanPublishedVO selectByPrimaryKey(String id) {
        DeliveryPlanPublishedPO po = deliveryPlanPublishedDao.selectByPrimaryKey(id);
        return DeliveryPlanPublishedConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "DELIVERY_PLAN_PUBLISHED")
    public List<DeliveryPlanPublishedVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "DELIVERY_PLAN_PUBLISHED")
    public List<DeliveryPlanPublishedVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<DeliveryPlanPublishedVO> list = new ArrayList<>();
        List<DeliveryPlanPublishedVO> dataList = deliveryPlanPublishedDao.selectByCondition(sortParam, queryCriteriaParam);
        Map<String, List<DeliveryPlanPublishedVO>> collect = dataList.stream().collect(Collectors.groupingBy(DeliveryPlanPublishedVO::getProductCode));
        for (Map.Entry<String, List<DeliveryPlanPublishedVO>> entry : collect.entrySet()) {
            List<DeliveryPlanPublishedVO> value = entry.getValue();
            List<DeliveryPlanPublishedDetailVO> detailList = new ArrayList<>();
            for (DeliveryPlanPublishedVO deliveryPlanPublishedVO : value) {
                DeliveryPlanPublishedDetailVO deliveryPlanPublishedDetailVO = new DeliveryPlanPublishedDetailVO();
                deliveryPlanPublishedDetailVO.setDemandTime(deliveryPlanPublishedVO.getDemandTime());
                deliveryPlanPublishedDetailVO.setDemandQuantity(deliveryPlanPublishedVO.getDemandQuantity());
                detailList.add(deliveryPlanPublishedDetailVO);
            }
            List<Date> dateList = value.stream().map(DeliveryPlanPublishedVO::getDemandTime).sorted().collect(Collectors.toList());
            DeliveryPlanPublishedVO deliveryPlanPublishedVO = value.get(0);
            deliveryPlanPublishedVO.setDateList(dateList);
            deliveryPlanPublishedVO.setDetaDetailList(detailList);
            list.add(deliveryPlanPublishedVO);
        }
        return list;
    }

    @Override
    public List<DeliveryPlanPublishedVO> selectByParams(Map<String, Object> params) {
        List<DeliveryPlanPublishedPO> list = deliveryPlanPublishedDao.selectByParams(params);
        return DeliveryPlanPublishedConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<DeliveryPlanPublishedVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public PageInfo<DeliveryPlanPublishedVO> selectDateByParams(DeliveryPlanPublishedParamsDTO paramsDTO) {
        List<DeliveryPlanPublishedVO> list = new ArrayList<>();
        Map<String, Object> params = new HashMap<>(2);
        params.put("demandCategory", paramsDTO.getDemandCategory());
        params.put("oemCode", paramsDTO.getOemCode());
        params.put("productCode", paramsDTO.getProductCode());
        params.put("demandStartTime", paramsDTO.getStartTime());
        params.put("demandEndTime", paramsDTO.getEndTime());
        params.put("publisher", paramsDTO.getPublisher());
        if (StringUtils.isNotBlank(paramsDTO.getProductCode())) {
            List<DeliveryPlanPublishedVO> deliveryPlanPublishedVOS = this.selectByParams(ImmutableMap.of("productCode", paramsDTO.getProductCode()));
            if (CollectionUtils.isEmpty(deliveryPlanPublishedVOS)) {
                return new PageInfo<>();
            }
        }
        PageHelper.startPage(paramsDTO.getPageNum(), paramsDTO.getPageSize());
        List<String> productCodes = deliveryPlanPublishedDao.selectProdcuts(params);
        if (CollectionUtils.isEmpty(productCodes)) {
            return new PageInfo<>();
        }
        PageInfo<String> pageInfo = new PageInfo<>(productCodes);
        Map<String, Object> params2 = new HashMap<>(2);
        params2.put("productCodes", productCodes);
        List<DeliveryPlanPublishedVO> dataList = this.selectVOByParams(params2);
        Map<String, List<DeliveryPlanPublishedVO>> collect = dataList.stream().collect(Collectors.groupingBy(DeliveryPlanPublishedVO::getProductCode));
        for (Map.Entry<String, List<DeliveryPlanPublishedVO>> entry : collect.entrySet()) {
            List<DeliveryPlanPublishedVO> value = entry.getValue();
            List<DeliveryPlanPublishedDetailVO> detailList = new ArrayList<>();
            for (DeliveryPlanPublishedVO deliveryPlanPublishedVO : value) {
                DeliveryPlanPublishedDetailVO deliveryPlanPublishedDetailVO = new DeliveryPlanPublishedDetailVO();
                deliveryPlanPublishedDetailVO.setDemandTime(deliveryPlanPublishedVO.getDemandTime());
                deliveryPlanPublishedDetailVO.setDemandQuantity(deliveryPlanPublishedVO.getDemandQuantity());
                detailList.add(deliveryPlanPublishedDetailVO);
            }
            List<Date> dateList;
            if ((paramsDTO.getStartTime() != null && paramsDTO.getEndTime() != null)) {
                dateList = value.stream()
                        .filter(Objects::nonNull)
                        .map(DeliveryPlanPublishedVO::getDemandTime)
                        .filter(Objects::nonNull)
                        .filter(demandTime -> !demandTime.before(paramsDTO.getStartTime()) && !demandTime.after(paramsDTO.getEndTime()))
                        .distinct()
                        .sorted()
                        .collect(Collectors.toList());
            } else {
                dateList = value.stream().map(DeliveryPlanPublishedVO::getDemandTime).distinct().sorted().collect(Collectors.toList());
            }

            DeliveryPlanPublishedVO deliveryPlanPublishedVO = value.get(0);
            DeliveryPlanPublishedVO maxDeliveryPlanPublishedVO = value.stream()
                    .max(Comparator.comparing(DeliveryPlanPublishedVO::getPublishTime))
                    .orElse(null); // 如果没有数据，返回 null
            deliveryPlanPublishedVO.setPublishTime(maxDeliveryPlanPublishedVO.getPublishTime());
            deliveryPlanPublishedVO.setDateList(dateList);
            deliveryPlanPublishedVO.setDetaDetailList(detailList);
            list.add(deliveryPlanPublishedVO);
        }
        PageInfo<DeliveryPlanPublishedVO> pageInfoResult = new PageInfo<>(list);
        pageInfoResult.setTotal(pageInfo.getTotal());
        pageInfoResult.setPages(pageInfo.getPages());
        return pageInfoResult;
    }

    @Override
    public List<LabelValue<String>> getOem() {
        Map<String, Object> params = new HashMap<>(2);
        params.put("enabled", YesOrNoEnum.YES.getCode());
        List<DeliveryPlanPublishedVO> deliveryPlanPublishedVOS = this.selectVOByParams(params);
        if (CollectionUtils.isNotEmpty(deliveryPlanPublishedVOS)) {
            List<LabelValue<String>> list = deliveryPlanPublishedVOS.stream()
                    .map(deliveryPlanPublishedVO -> new LabelValue<>(deliveryPlanPublishedVO.getOemName(), deliveryPlanPublishedVO.getOemCode()))
                    .distinct()
                    .collect(Collectors.toList());
            return removeDuplicates(list);
        }
        return Collections.emptyList();
    }

    @Override
    public List<LabelValue<String>> getProductCode() {
        Map<String, Object> params = new HashMap<>(2);
        params.put("enabled", YesOrNoEnum.YES.getCode());
        List<DeliveryPlanPublishedVO> deliveryPlanPublishedVOS = this.selectVOByParams(params);
        if (CollectionUtils.isNotEmpty(deliveryPlanPublishedVOS)) {
            List<LabelValue<String>> list = deliveryPlanPublishedVOS.stream()
                    .map(deliveryPlanPublishedVO -> new LabelValue<>(deliveryPlanPublishedVO.getProductCode(), deliveryPlanPublishedVO.getProductCode()))
                    .distinct()
                    .collect(Collectors.toList());
            return removeDuplicates(list);
        }
        return Collections.emptyList();
    }

    private List<LabelValue<String>> removeDuplicates(List<LabelValue<String>> list) {
        return list.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                lv -> Arrays.asList(lv.getLabel(), lv.getValue()),
                                lv -> lv,
                                (existing, replacement) -> existing
                        ),
                        map -> new ArrayList<>(map.values())
                ));
    }

    private List<DeliveryPlanPublishedVO> selectVOByParams(Map<String, Object> params) {
        return deliveryPlanPublishedDao.selectVOByParams(params);
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.DELIVERY_PLAN_PUBLISHED.getCode();
    }

    @Override
    public List<DeliveryPlanPublishedVO> invocation(List<DeliveryPlanPublishedVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

}
