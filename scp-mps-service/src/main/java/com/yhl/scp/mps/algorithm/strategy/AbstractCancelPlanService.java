package com.yhl.scp.mps.algorithm.strategy;

import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.basic.enums.FrozenStatusEnum;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.feign.MdsFeign;
import com.yhl.scp.mps.algorithm.command.ScheduleCommonService;
import com.yhl.scp.mps.algorithm.schedule.output.RzzOperationDTO;
import com.yhl.scp.mps.domain.algorithm.RzzAMSSupportService;
import com.yhl.scp.mps.order.infrastructure.dao.WorkOrderDeletionDao;
import com.yhl.scp.sds.basic.order.enums.PlannedStatusEnum;
import com.yhl.scp.sds.basic.order.vo.OperationBasicVO;
import com.yhl.scp.sds.extension.order.dto.OperationDTO;
import com.yhl.scp.sds.extension.order.dto.WorkOrderDTO;
import com.yhl.scp.sds.extension.order.infrastructure.po.WorkOrderPO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import com.yhl.scp.sds.extension.pegging.dto.DemandDTO;
import com.yhl.scp.sds.extension.pegging.dto.SupplyDTO;
import com.yhl.scp.sds.extension.pegging.vo.DemandVO;
import com.yhl.scp.sds.extension.pegging.vo.SupplyVO;
import com.yhl.scp.sds.order.convertor.OperationConvertor;
import com.yhl.scp.sds.order.convertor.WorkOrderConvertor;
import com.yhl.scp.sds.order.infrastructure.dao.OperationDao;
import com.yhl.scp.sds.order.infrastructure.dao.WorkOrderDao;
import com.yhl.scp.sds.order.service.*;
import com.yhl.scp.sds.pegging.convertor.DemandConvertor;
import com.yhl.scp.sds.pegging.convertor.SupplyConvertor;
import com.yhl.scp.sds.pegging.infrastructure.dao.DemandDao;
import com.yhl.scp.sds.pegging.infrastructure.dao.SupplyDao;
import com.yhl.scp.sds.pegging.service.DemandService;
import com.yhl.scp.sds.pegging.service.SupplyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>AbstractCancelPlanService</code>
 * <p>
 * 实现计划取消
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-16 17:09:22
 */
@Slf4j
public abstract class AbstractCancelPlanService extends ScheduleCommonService implements ScheduleCommand {

    @Resource
    private OperationService operationService;
    @Resource
    private OperationDao operationDao;
    @Resource
    private RzzAMSSupportService rzzAMSSupportService;
    @Resource
    private OperationInputService operationInputService;
    @Resource
    private OperationOutputService operationOutputService;
    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private WorkOrderDao workOrderDao;
    @Resource
    private DemandDao demandDao;
    @Resource
    private DemandService demandService;
    @Resource
    private SupplyService supplyService;
    @Resource
    private SupplyDao supplyDao;
    @Resource
    private MdsFeign mdsFeign;
    @Resource
    private OperationTaskService operationTaskService;
    @Resource
    private OperationResourceService operationResourceService;
    @Resource
    private OperationExtendService operationExtendService;

    @Resource
    private WorkOrderDeletionDao workOrderDeletionDao;

    /**
     * AMS取消工序接口
     *
     * @param cancelOperations
     */
    public void doCancelAmsOperationPlan(List<OperationVO> cancelOperations) {
        if (CollectionUtils.isEmpty(cancelOperations)) {
            return;
        }
        //查询计划期间
        PlanningHorizonVO planningHorizon = mdsFeign.getPlanningHorizon();
        List<String> deleteSubOperationIds = cancelOperations.stream().map(OperationVO::getId).collect(Collectors.toList());
        ;
        //拿到父工序
        List<String> parentOperationIds = cancelOperations.stream().map(OperationVO::getParentId).collect(Collectors.toList());
        List<OperationVO> parentOperationVOS = operationService.getVOListByIds(parentOperationIds);
        //删除子工序以及对应工序任务
        // operationService.doDeleteWithRelation(deleteSubOperationIds);
        // List<OperationInputVO> operationInputVOS = this.operationInputService.selectByParams(ImmutableMap.of("operationIds", parentOperationIds));
        // List<OperationInputDTO> operationInputDTOS = OperationInputConvertor.INSTANCE.vo2Dtos(operationInputVOS);
        // operationInputService.doDeleteWithDemands(operationInputDTOS);
        // List<OperationOutputVO> deleteOperationOutputVOS = this.operationOutputService.selectByParams(ImmutableMap.of("operationIds", parentOperationIds));
        // List<OperationOutputDTO> operationOutputDTOS = OperationOutputConvertor.INSTANCE.vo2Dtos(deleteOperationOutputVOS);
        // operationOutputService.doDeleteWithSupplies(operationOutputDTOS);
        operationTaskService.doDeleteByOperationIds(deleteSubOperationIds);
        // operationResourceService.doDeleteByParams(ImmutableMap.of("operationIds", parentOperationIds));
        operationService.doDelete(deleteSubOperationIds);
        //删除子工序的扩展表数据
        operationExtendService.doDeleteByOperationIds(deleteSubOperationIds);
        Map<String, OperationVO> parentOperationIdMap = parentOperationVOS.stream().collect(Collectors.toMap(BaseVO::getId, v -> v));
        List<OperationVO> waitingUpdateOperation = new ArrayList<>();
        //查询当前父工序下的子工序
        List<OperationVO> subOperations = operationDao.selectVOByParams(ImmutableMap.of("parentIds", parentOperationIds));
        Map<String, List<OperationVO>> parentIdToSubOperationMaps = subOperations.stream().collect(Collectors.groupingBy(OperationVO::getParentId));
        for (OperationVO parentOperationVO : parentOperationVOS) {
            OperationVO operationVO = parentOperationIdMap.get(parentOperationVO.getId());
            //更新父工序状态
            List<OperationVO> subOperationOfThis = parentIdToSubOperationMaps.get(parentOperationVO.getId());
//            List<OperationDTO> subOperationDTOOfThis = OperationConvertor.INSTANCE.vo2Dtos(subOperationOfThis);
//            String parentOperationStatus = this.getParentOperationStatus(operationVO.getQuantity(), subOperationDTOOfThis);
//            parentOperationVO.setPlanStatus(parentOperationStatus);
            //更新上次计划信息
            parentOperationVO.setLastCleanupEndTime(operationVO.getCleanupEndTime());
            parentOperationVO.setLastCleanupStartTime(operationVO.getCleanupStartTime());
            parentOperationVO.setLastLockStartTime(operationVO.getLockStartTime());
            parentOperationVO.setLastLockEndTime(operationVO.getLockEndTime());
            parentOperationVO.setLastSetupStartTime(operationVO.getSetupStartTime());
            parentOperationVO.setLastSetupEndTime(operationVO.getSetupEndTime());
            parentOperationVO.setLastProductionStartTime(operationVO.getProductionStartTime());
            parentOperationVO.setLastProductionEndTime(operationVO.getProductionEndTime());
            parentOperationVO.setLastMainResourceId(operationVO.getPlannedMainResourceId());
            parentOperationVO.setLastToolResourceId(operationVO.getPlannedToolResourceId());

            //更新当前计划资源信息
            if (CollectionUtils.isEmpty(subOperationOfThis)) {
                parentOperationVO.setPlannedMainResourceId(null);
                parentOperationVO.setPlannedToolResourceId(null);
                parentOperationVO.setCleanupStartTime(null);
                parentOperationVO.setCleanupEndTime(null);
                parentOperationVO.setCleanupDuration(null);
                parentOperationVO.setLockStartTime(null);
                parentOperationVO.setLockEndTime(null);
                parentOperationVO.setLockDuration(null);
                parentOperationVO.setSetupStartTime(null);
                parentOperationVO.setSetupEndTime(null);
                parentOperationVO.setSetupDuration(null);
                parentOperationVO.setProductionStartTime(null);
                parentOperationVO.setProductionEndTime(null);
                parentOperationVO.setProductionDuration(null);
                parentOperationVO.setStartTime(null);
                parentOperationVO.setEndTime(null);
                parentOperationVO.setPlannedResourceId(null);
            } else {
                //更新冻结状态
                this.setParentFrozenStatus(Collections.singletonList(parentOperationVO), subOperationOfThis);
                List<String> mainResourceIds = subOperationOfThis.stream().map(OperationBasicVO::getPlannedMainResourceId).collect(Collectors.toList());
                List<String> toolResourceIds = subOperationOfThis.stream().map(OperationBasicVO::getPlannedToolResourceId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(mainResourceIds)) {
                    parentOperationVO.setPlannedMainResourceId(StringUtils.join(mainResourceIds, ","));
                }
                if (CollectionUtils.isNotEmpty(toolResourceIds)) {
                    parentOperationVO.setPlannedToolResourceId(StringUtils.join(toolResourceIds, ","));
                }
                parentOperationVO.setCleanupStartTime(subOperationOfThis.stream().filter(k -> k.getCleanupStartTime() != null).map(OperationVO::getCleanupStartTime).min(Comparator.comparing(Date::getTime)).orElse(null));
                parentOperationVO.setCleanupEndTime(subOperationOfThis.stream().filter(k -> k.getCleanupEndTime() != null).map(OperationVO::getCleanupEndTime).max(Comparator.comparing(Date::getTime)).orElse(null));
                parentOperationVO.setCleanupDuration(subOperationOfThis.stream().filter(k -> k.getCleanupDuration() != null).map(OperationVO::getCleanupDuration).reduce(0, Integer::sum));
                parentOperationVO.setLockStartTime(subOperationOfThis.stream().filter(k -> k.getLockStartTime() != null).map(OperationVO::getLockStartTime).min(Comparator.comparing(Date::getTime)).orElse(null));
                parentOperationVO.setLockEndTime(subOperationOfThis.stream().filter(k -> k.getLockEndTime() != null).map(OperationVO::getLockEndTime).max(Comparator.comparing(Date::getTime)).orElse(null));
                parentOperationVO.setLockDuration(subOperationOfThis.stream().filter(k -> k.getLockDuration() != null).map(OperationVO::getLockDuration).reduce(0, Integer::sum));
                parentOperationVO.setSetupStartTime(subOperationOfThis.stream().filter(k -> k.getSetupStartTime() != null).map(OperationVO::getSetupStartTime).min(Comparator.comparing(Date::getTime)).orElse(null));
                parentOperationVO.setSetupEndTime(subOperationOfThis.stream().filter(k -> k.getSetupEndTime() != null).map(OperationVO::getSetupEndTime).max(Comparator.comparing(Date::getTime)).orElse(null));
                parentOperationVO.setSetupDuration(subOperationOfThis.stream().filter(k -> k.getSetupDuration() != null).map(OperationVO::getSetupDuration).reduce(0, Integer::sum));
                parentOperationVO.setProductionStartTime(subOperationOfThis.stream().filter(k -> k.getProductionStartTime() != null).map(OperationVO::getProductionStartTime).min(Comparator.comparing(Date::getTime)).orElse(null));
                parentOperationVO.setProductionEndTime(subOperationOfThis.stream().filter(k -> k.getProductionEndTime() != null).map(OperationVO::getProductionEndTime).max(Comparator.comparing(Date::getTime)).orElse(null));
                parentOperationVO.setProductionDuration(subOperationOfThis.stream().filter(k -> k.getProductionDuration() != null).map(OperationVO::getProductionDuration).reduce(0, Integer::sum));
                parentOperationVO.setStartTime(parentOperationVO.getSetupStartTime() == null ? parentOperationVO.getProductionStartTime() : parentOperationVO.getSetupStartTime());
                parentOperationVO.setEndTime(parentOperationVO.getCleanupEndTime() == null ? parentOperationVO.getProductionEndTime() : parentOperationVO.getCleanupEndTime());
            }
            waitingUpdateOperation.add(parentOperationVO);
        }
        List<OperationDTO> operationDTOS = OperationConvertor.INSTANCE.vo2Dtos(waitingUpdateOperation);
        //operationService.doUpdateBatch(operationDTOS);

        //更新制造订单状态
        this.doBatchUpdateWithPlanStatus(operationDTOS);

        List<String> productStockPointIds = new ArrayList<>();
        //获取受到影响的supply
        List<SupplyVO> supplyVOList = supplyDao.selectByOperationIds(parentOperationIds);
        List<SupplyDTO> supplyDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(supplyVOList)) {
            supplyDTOS = SupplyConvertor.INSTANCE.vo2Dtos(supplyVOList);
            productStockPointIds.addAll(supplyDTOS.stream().map(SupplyDTO::getProductStockPointId).distinct().collect(Collectors.toList()));
        }

        //获取受到影响的demand
        List<DemandVO> demandVOList = demandDao.selectByOperationIds(parentOperationIds);
        List<DemandDTO> demandDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(demandVOList)) {
            demandDTOS = DemandConvertor.INSTANCE.vo2Dtos(demandVOList);
            productStockPointIds.addAll(demandDTOS.stream().map(DemandDTO::getProductStockPointId).distinct().collect(Collectors.toList()));
        }

        //获取受影响的制造订单
        List<String> workOrderIdList = parentOperationVOS.stream().map(OperationVO::getOrderId).distinct().collect(Collectors.toList());
        List<WorkOrderVO> workOrderVOList = workOrderService.selectByPrimaryKeys(workOrderIdList);

        productStockPointIds.addAll(workOrderVOList.stream().map(WorkOrderVO::getProductStockPointId).distinct().collect(Collectors.toList()));
        Map<String, WorkOrderVO> workOrderIdMaps = workOrderVOList.stream().collect(Collectors.toMap(WorkOrderVO::getId, Function.identity()));

        //重新查询一下父工序对应数据（计划状态已经更新）
        parentOperationVOS = operationService.getVOListByIds(parentOperationIds);
        Map<String, RzzOperationDTO> parentOperationDtoIdMap = rzzAMSSupportService.operationVOToRzzDto(parentOperationVOS);
        rzzAMSSupportService.calculateCommon(workOrderIdMaps, parentOperationDtoIdMap, parentOperationIds, planningHorizon);
    }

    public void setParentFrozenStatus(List<OperationVO> parentOperations, List<OperationVO> subOperations) {
        for (OperationVO parentOperation : parentOperations) {
            //筛选父工序下的所有子工序
            List<OperationVO> subOperationsOfCurrentParentOperation = subOperations.stream().filter(k -> parentOperation.getId().equals(k.getParentId())).collect(Collectors.toList());
            if (com.yhl.platform.common.utils.CollectionUtils.isEmpty(subOperationsOfCurrentParentOperation)) {
                parentOperation.setFrozen(FrozenStatusEnum.UNFROZEN.getCode());
                continue;
            }
            //冻结数量
            long frozenCount = subOperationsOfCurrentParentOperation.stream().filter(k -> FrozenStatusEnum.FROZEN.getCode().equals(k.getFrozen())).count();
            if (frozenCount == subOperationsOfCurrentParentOperation.size()) {
                parentOperation.setFrozen(FrozenStatusEnum.FROZEN.getCode());
            } else if (frozenCount == 0) {
                parentOperation.setFrozen(FrozenStatusEnum.UNFROZEN.getCode());
            } else {
                parentOperation.setFrozen(FrozenStatusEnum.PARTIAL_FROZEN.getCode());
            }
        }
    }

    public void doBatchUpdateWithPlanStatus(List<OperationDTO> operationDTOList) {
        if (CollectionUtils.isEmpty(operationDTOList)) {
            return;
        }
        //这里更新了除planStatus外的字段
        //operationService.doUpdateBatch(operationDTOList);
        operationDTOList.forEach(t->t.setRemark("取消计划更新父工序除计划状态外字段"));
        rzzAMSSupportService.doOperationUpdateBatch(operationDTOList);

        //过滤出子工序
        List<OperationDTO> childOperationList = operationDTOList.stream().filter(item -> StringUtils.isNotBlank(item.getParentId())).collect(Collectors.toList());
        //过滤出父工序
        List<OperationDTO> parentOperationDTOList = operationDTOList.stream().filter(item -> StringUtils.isBlank(item.getParentId())).collect(Collectors.toList());
        List<String> parentOperationIdList = new ArrayList<>();
        List<OperationVO> parentOperationList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(parentOperationDTOList)) {
            parentOperationList.addAll(parentOperationDTOList.stream().map(OperationConvertor.INSTANCE::dto2Vo).collect(Collectors.toList()));
            parentOperationIdList.addAll(parentOperationDTOList.stream().map(OperationDTO::getId).collect(Collectors.toList()));
        }

        if (CollectionUtils.isNotEmpty(childOperationList)) {
            //更新父工序的状态
            parentOperationIdList.addAll(childOperationList.stream().map(OperationDTO::getParentId).distinct().collect(Collectors.toList()));
            //查询父工序
            parentOperationList.addAll(operationDao.selectVOByPrimaryKeys(parentOperationIdList));
        }

        //查询父工序关联的子工序
        List<OperationVO> operationVOS = operationService.selectByParentIds(parentOperationIdList);
        Map<String, List<OperationVO>> operationMapOfParentId = operationVOS.stream().collect(Collectors.groupingBy(OperationVO::getParentId));
        for (OperationVO parentOperationVO : parentOperationList) {
            //获取父工序关联的子工序
            List<OperationVO> list = operationMapOfParentId.get(parentOperationVO.getId());
            if (CollectionUtils.isEmpty(operationVOS)) {
                //更新为未计划
                parentOperationVO.setPlanStatus(PlannedStatusEnum.UNPLAN.getCode());
                continue;
            }
            //计算父工序状态
            String parentOperationStatus = operationService.getParentOperationStatus(parentOperationVO.getQuantity(), OperationConvertor.INSTANCE.vo2Dtos(list));
            parentOperationVO.setPlanStatus(parentOperationStatus);
        }
        //更新父工序状态
        //operationService.doUpdateBatch(OperationConvertor.INSTANCE.vo2Dtos(parentOperationList));
        parentOperationList.forEach(t->t.setRemark("取消计划更新父工序状态"));
        rzzAMSSupportService.doOperationUpdateBatch(OperationConvertor.INSTANCE.vo2Dtos(parentOperationList));

        //修改的工序未父工序也要添加进去，但不可重复添加
        if (CollectionUtils.isNotEmpty(parentOperationDTOList)) {
            if (CollectionUtils.isEmpty(parentOperationList)) {
                parentOperationList.addAll(OperationConvertor.INSTANCE.dto2Vos(parentOperationDTOList));
            } else {
                List<String> operationIds = parentOperationList.stream().map(OperationVO::getId).collect(Collectors.toList());
                for (OperationDTO operationDTO : parentOperationDTOList) {
                    if (!operationIds.contains(operationDTO.getId())) {
                        parentOperationList.add(OperationConvertor.INSTANCE.dto2Vo(operationDTO));
                    }
                }
            }
        }

        //更新未拆分、子制造订单状态
        List<String> workOrderIdList = parentOperationList.stream().map(OperationVO::getOrderId).distinct().collect(Collectors.toList());
        //查询制造订单
        List<WorkOrderVO> workOrderVOList = WorkOrderConvertor.INSTANCE.po2Vos(workOrderDao.selectByPrimaryKeys(workOrderIdList));
        //根据制造订单查询关联的工序信息
        List<OperationVO> operationVOList = operationService.selectByWorkOrderIds(workOrderIdList);
        Map<String, List<OperationVO>> operationMapOfOrderId = operationVOList.stream().collect(Collectors.groupingBy(OperationVO::getOrderId));
        if (CollectionUtils.isNotEmpty(workOrderVOList)) {
            for (WorkOrderVO workOrderVO : workOrderVOList) {
                //获取制造订单关联的工序信息
                List<OperationVO> list = operationMapOfOrderId.get(workOrderVO.getId());
                //计算制造订单状态
                String workOrderStatus = workOrderService.getWorkOrderStatus(OperationConvertor.INSTANCE.vo2Pos(list));
                if (PlannedStatusEnum.UNPLAN.getCode().equals(workOrderStatus)) {
                    workOrderVO.setStartTime(null);
                    workOrderVO.setEndTime(null);
                } else {
                    list.stream().filter(k -> k.getStartTime() != null).min(Comparator.comparing(OperationVO::getStartTime)).ifPresent(k -> workOrderVO.setStartTime(k.getStartTime()));
                    list.stream().filter(k -> k.getEndTime() != null).max(Comparator.comparing(OperationVO::getEndTime)).ifPresent(k -> workOrderVO.setEndTime(k.getEndTime()));
                }
                workOrderVO.setPlanStatus(workOrderStatus);
            }
        }

        //更新未拆分、子制造订单状态, 原方法有设置productStockPointId的逻辑，由于改造了库存点物品相关逻辑，所以弃用
        //workOrderService.doBatchUpdateWithPlanStatus(WorkOrderConvertor.INSTANCE.vo2Dtos(workOrderVOList));
        List<WorkOrderDTO> list = WorkOrderConvertor.INSTANCE.vo2Dtos(workOrderVOList);
        List<WorkOrderPO> newList = WorkOrderConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        List<List<WorkOrderPO>> lists = com.yhl.platform.common.utils.CollectionUtils.splitList(newList, 2000);
        for (List<WorkOrderPO> workOrderPOS : lists) {
            workOrderDao.updateBatch(workOrderPOS);
        }
    }
}
