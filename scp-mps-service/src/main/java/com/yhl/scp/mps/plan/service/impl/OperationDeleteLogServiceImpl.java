package com.yhl.scp.mps.plan.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mps.plan.convertor.OperationDeleteLogConvertor;
import com.yhl.scp.mps.plan.domain.entity.OperationDeleteLogDO;
import com.yhl.scp.mps.plan.domain.service.OperationDeleteLogDomainService;
import com.yhl.scp.mps.plan.dto.OperationDeleteLogDTO;
import com.yhl.scp.mps.plan.infrastructure.dao.OperationDeleteLogDao;
import com.yhl.scp.mps.plan.infrastructure.po.OperationDeleteLogPO;
import com.yhl.scp.mps.plan.service.OperationDeleteLogService;
import com.yhl.scp.mps.plan.vo.OperationDeleteLogVO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <code>OperationDeleteLogServiceImpl</code>
 * <p>
 * OperationDeleteLogServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-10 20:20:09
 */
@Slf4j
@Service
public class OperationDeleteLogServiceImpl extends AbstractService implements OperationDeleteLogService {

    @Resource
    private OperationDeleteLogDao operationDeleteLogDao;

    @Resource
    private OperationDeleteLogDomainService operationDeleteLogDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    public BaseResponse<Void> doCreate(OperationDeleteLogDTO operationDeleteLogDTO) {
        // 0.数据转换
        OperationDeleteLogDO operationDeleteLogDO = OperationDeleteLogConvertor.INSTANCE.dto2Do(operationDeleteLogDTO);
        OperationDeleteLogPO operationDeleteLogPO = OperationDeleteLogConvertor.INSTANCE.dto2Po(operationDeleteLogDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        operationDeleteLogDomainService.validation(operationDeleteLogDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(operationDeleteLogPO);
        operationDeleteLogDao.insert(operationDeleteLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(OperationDeleteLogDTO operationDeleteLogDTO) {
        // 0.数据转换
        OperationDeleteLogDO operationDeleteLogDO = OperationDeleteLogConvertor.INSTANCE.dto2Do(operationDeleteLogDTO);
        OperationDeleteLogPO operationDeleteLogPO = OperationDeleteLogConvertor.INSTANCE.dto2Po(operationDeleteLogDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        operationDeleteLogDomainService.validation(operationDeleteLogDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(operationDeleteLogPO);
        operationDeleteLogDao.update(operationDeleteLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<OperationDeleteLogDTO> list) {
        List<OperationDeleteLogPO> newList = OperationDeleteLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        operationDeleteLogDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<OperationDeleteLogDTO> list) {
        List<OperationDeleteLogPO> newList = OperationDeleteLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        operationDeleteLogDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return operationDeleteLogDao.deleteBatch(idList);
        }
        return operationDeleteLogDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public OperationDeleteLogVO selectByPrimaryKey(String id) {
        OperationDeleteLogPO po = operationDeleteLogDao.selectByPrimaryKey(id);
        return OperationDeleteLogConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_sds_ord_operation_delete_log")
    public List<OperationDeleteLogVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_sds_ord_operation_delete_log")
    public List<OperationDeleteLogVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<OperationDeleteLogVO> dataList = operationDeleteLogDao.selectByCondition(sortParam, queryCriteriaParam);
        OperationDeleteLogServiceImpl target = SpringBeanUtils.getBean(OperationDeleteLogServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<OperationDeleteLogVO> selectByParams(Map<String, Object> params) {
        List<OperationDeleteLogPO> list = operationDeleteLogDao.selectByParams(params);
        return OperationDeleteLogConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<OperationDeleteLogVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<OperationDeleteLogVO> invocation(List<OperationDeleteLogVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public void record(List<OperationVO> deleteOperations, String deleteReason) {
        if (CollectionUtils.isEmpty(deleteOperations)) {
            return;
        }
        String scenario = SystemHolder.getScenario();
        CompletableFuture.runAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            log.info("开始记录被删除的operation");
            List<OperationDeleteLogPO> operationDeleteLogPOList = OperationDeleteLogConvertor.INSTANCE.operationVO2Po(deleteOperations);

            operationDeleteLogPOList.forEach(
                    t->t.setDeleteReason(deleteReason)
            );
            try {
                operationDeleteLogDao.insertBatch(operationDeleteLogPOList);
            }catch (Exception e){
                log.error("记录被删除的operation失败",e);
            }
            log.info("记录被删除的operation结束");
            DynamicDataSourceContextHolder.clearDataSource();
        });
    }
}
