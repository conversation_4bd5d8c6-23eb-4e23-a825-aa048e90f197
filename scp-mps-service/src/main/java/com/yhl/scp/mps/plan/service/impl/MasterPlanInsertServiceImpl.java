package com.yhl.scp.mps.plan.service.impl;

import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.basic.routing.vo.BomRoutingStepInputBasicVO;
import com.yhl.scp.mds.basic.rule.enums.RuleEncodingsEnum;
import com.yhl.scp.mds.enums.ProductTypeEnum;
import com.yhl.scp.mds.extension.routing.domain.entity.RoutingDO;
import com.yhl.scp.mds.extension.routing.domain.entity.RoutingStepDO;
import com.yhl.scp.mds.extension.routing.vo.BomRoutingStepInputVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingVO;
import com.yhl.scp.mds.extension.rule.vo.RuleEncodingsVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.rule.util.RuleEncodingsUtils;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.adjust.support.AdjustPlanSupport;
import com.yhl.scp.mps.algorithm.dto.RzzAdjustmentParam;
import com.yhl.scp.mps.dispatch.output.RzzMpsAlgorithmOutput;
import com.yhl.scp.mps.domain.sync.IWorkOrderSync;
import com.yhl.scp.mps.domain.sync.process.AbstractOperationSync;
import com.yhl.scp.mps.plan.dto.MasterPlanInsertDTO;
import com.yhl.scp.mps.plan.service.MasterPlanInsertService;
import com.yhl.scp.mps.plan.support.MasterPlanSplitSupport;
import com.yhl.scp.mps.schedule.HandworkUnScheduleDTO;
import com.yhl.scp.mps.schedule.service.HandworkScheduleService;
import com.yhl.scp.sds.basic.order.enums.PlannedStatusEnum;
import com.yhl.scp.sds.basic.order.vo.OperationResourceBasicVO;
import com.yhl.scp.sds.extension.order.infrastructure.po.WorkOrderPO;
import com.yhl.scp.sds.extension.order.vo.OperationResourceVO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.order.infrastructure.dao.OperationDao;
import com.yhl.scp.sds.order.infrastructure.dao.WorkOrderDao;
import com.yhl.scp.sds.order.service.OperationResourceService;
import com.yhl.scp.sds.order.service.OperationService;
import com.yhl.scp.sds.pegging.infrastructure.dao.DemandDao;
import com.yhl.scp.sds.pegging.infrastructure.dao.FulfillmentDao;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.yhl.platform.common.utils.DateUtils.COMMON_DATE_STR1;

/**
 * <code>MasterPlanInsertServiceImpl</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-18 20:00:22
 */
@Service
public class MasterPlanInsertServiceImpl implements MasterPlanInsertService {

    @Resource
    private OperationDao operationDao;
    @Resource
    private OperationService operationService;
    @Resource
    private NewMdsFeign newMdsFeign;
    @Resource
    private IWorkOrderSync workOrderSync;
    @Resource
    private HandworkScheduleService handworkScheduleService;
    @Resource
    private WorkOrderDao workOrderDao;
    @Resource
    private OperationResourceService operationResourceService;
    @Resource
    AdjustPlanSupport adjustPlanSupport;
    @Resource
    protected FulfillmentDao fulfillmentDao;
    @Resource
    protected DemandDao demandDao;
    @Resource
    private MasterPlanSplitSupport masterPlanSplitSupport;


    public void doHandleSimpleBom(MasterPlanInsertDTO dto,
                                  RoutingDO routingDO,
                                  Map<String, NewStockPointVO> stockPointMap,
                                  Map<String, RuleEncodingsVO> ruleEncodingsMap,
                                  List<String> keyStepIds,
                                  HandworkUnScheduleDTO handworkUnScheduleDTO) {
        RuleEncodingsVO ruleEncodings = ruleEncodingsMap.get(RuleEncodingsEnum.WORK_ORDER_NO.getDesc());
        String serialNumber = RuleEncodingsUtils.getSerialNumber(ruleEncodings);
        String orderNo = RuleEncodingsUtils.getCode(ruleEncodings, null, serialNumber);
        ruleEncodings.setSerialNumberMaxValue(serialNumber);
        //创建制造订单
        String workOrderId = UUIDUtil.getUUID();
        WorkOrderPO workOrder = new WorkOrderPO();
        workOrder.setId(workOrderId);
        workOrder.setOrderNo(orderNo);
        workOrder.setQuantity(dto.getQuantity());
        workOrder.setProductId(routingDO.getProductId());
        workOrder.setProductStockPointId(routingDO.getProductId());
        workOrder.setStockPointId(stockPointMap.get(routingDO.getStockPointId()).getId());
        workOrder.setPlanStatus(PlannedStatusEnum.UNPLAN.getCode());
        //交期暂时设置为当前时间，推移完之后会更新交期
        workOrder.setDueDate(new Date());
        workOrder.setDemandCategory(ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode());
        String orderType = StringUtils.isEmpty(dto.getDemandType()) ? "LC" : dto.getDemandType();
        workOrder.setOrderType(orderType);
        workOrder.setTestOrderNumber(dto.getTestOrderNumber());
        workOrder.setRemark("手动新增");
        BasePOUtils.insertFiller(workOrder);
        workOrderDao.insert(workOrder);

        //制造订单展开
        RzzMpsAlgorithmOutput mpsAlgorithmOutput = new RzzMpsAlgorithmOutput();
        mpsAlgorithmOutput.setWorkOrderIds(Collections.singletonList(workOrderId));
        workOrderSync.doSyncOrder(mpsAlgorithmOutput);
        //推移工序时间

        List<OperationVO> operationVOList = operationService.selectByWorkOrderIds(Collections.singletonList(workOrderId));
        // 关键工序
        OperationVO parentOperationVO = operationVOList.stream()
                .filter(t -> dto.getRoutingStepSequenceNo().equals(t.getRoutingStepSequenceNo().toString()))
                .findFirst().orElseThrow(() -> new BusinessException("该产品对应工艺路径下没有该工序！"));
        //查询工序候选资源
        List<String> parentIds = operationVOList.stream().map(BaseVO::getId).collect(Collectors.toList());
        List<OperationResourceVO> operationResourceVOList = operationResourceService.selectVOByParams(ImmutableMap.of("operationIds", parentIds));
        Map<String, String> operationOfResourceIdMap = new HashMap<>();
        Map<String, List<OperationResourceVO>> operationMap = operationResourceVOList.stream().collect(Collectors.groupingBy(OperationResourceBasicVO::getOperationId));
        //父operation
        List<OperationVO> operationVOS = operationVOList.stream().filter(t -> StringUtils.isEmpty(t.getParentId())).collect(Collectors.toList());
        // 获取最高优先级资源
        getHighPriorityOperationResources(operationVOS, operationMap, operationOfResourceIdMap, dto, keyStepIds);
        List<OperationVO> keyOperationList = operationVOS.stream()
                .filter(t -> t.getRoutingStepSequenceNo() >= parentOperationVO.getRoutingStepSequenceNo()).collect(Collectors.toList());

        List<String> szWorkOrderIds = new ArrayList<>();
        if (!"LC".equals(dto.getDemandType())) {
            szWorkOrderIds.add(workOrderId);
        }

        handworkUnScheduleDTO.setWorkOrderPO(workOrder);
        handworkUnScheduleDTO.setMasterPlanInsertDTO(dto);
        handworkUnScheduleDTO.setKeyOperationList(keyOperationList);
        handworkUnScheduleDTO.setOperationOfResourceIdMap(operationOfResourceIdMap);
        handworkUnScheduleDTO.setSzWorkOrderIds(szWorkOrderIds);
    }

    /**
     * 复杂Bom新增
     * 多层级bom productCode是成品的，选择的工序是半成品的关键工序，选择的资源也是半成品的两个资源，
     * 开始时间作为其中一个半品40工序的开始时间，然后另一个半品的40工序开始时间就接续前一个，它的结束时间就作为成品45工序的开始时间
     *
     * @param dto
     * @param routingDO        成品对应的工艺路径
     * @param ruleEncodingsMap 编码规则
     * @param keyStepIds
     */
    public void doHandleComplexBom(MasterPlanInsertDTO dto,
                                   RoutingDO routingDO,
                                   Map<String, NewStockPointVO> stockPointMap,
                                   Map<String, RuleEncodingsVO> ruleEncodingsMap,
                                   List<String> keyStepIds,
                                   HandworkUnScheduleDTO handworkUnScheduleDTO) {
        Map<String, Object> params = new HashMap<>();
        params.put("productCode", dto.getProductCode());
        params.put("productType", ProductTypeEnum.SA);
        List<BomRoutingStepInputVO> bomRoutingStepInputVOS = newMdsFeign.selectBomRoutingStepInputByParams(SystemHolder.getScenario(), params);
        if (CollectionUtils.isEmpty(bomRoutingStepInputVOS)) {
            return;
        }
        //两个半品的对应的物料id
        List<String> inputProductList = bomRoutingStepInputVOS.stream().map(BomRoutingStepInputVO::getInputProductId).collect(Collectors.toList());
        Map<String, BomRoutingStepInputVO> sourceBomMap = bomRoutingStepInputVOS.stream()
                .collect(Collectors.toMap(BomRoutingStepInputBasicVO::getInputProductId, Function.identity()));
        params = new HashMap<>();
        params.put("productIds", inputProductList);
        String sourceInputProductId = null;
        String sourceStockPointId = null;
        String scenario = SystemHolder.getScenario();
        // 特殊情况输入物品是S2的，但是工艺路径是S1的
        List<RoutingVO> inputRoutingVOS = newMdsFeign.selectRoutingByParams(scenario, params);
        if (CollectionUtils.isEmpty(inputRoutingVOS)) {
            List<NewProductStockPointVO> productStockPointVOS = newMdsFeign.selectProductStockPointByIds(scenario, inputProductList);
            if (CollectionUtils.isNotEmpty(productStockPointVOS)) {
                inputRoutingVOS = newMdsFeign.selectRoutingByParams(scenario, ImmutableMap.of("productCode", productStockPointVOS.get(0).getProductCode()));
                sourceInputProductId = inputProductList.get(0);
                sourceStockPointId = sourceBomMap.get(sourceInputProductId).getStockPointId();
                inputProductList = inputRoutingVOS.stream().map(RoutingVO::getProductId).distinct().collect(Collectors.toList());
                List<BomRoutingStepInputVO> inputVOList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(inputProductList)) {
                    for (RoutingVO inputRoutingVO : inputRoutingVOS) {
                        BomRoutingStepInputVO inputVO = new BomRoutingStepInputVO();
                        inputVO.setInputProductId(inputRoutingVO.getProductId());
                        inputVO.setStockPointId(stockPointMap.get(inputRoutingVO.getStockPointId()).getId());
                        inputVOList.add(inputVO);
                    }
                }
                bomRoutingStepInputVOS = inputVOList;
            }
        }
        if (CollectionUtils.isEmpty(bomRoutingStepInputVOS)) {
            return;
        }

        //新增三个制造订单，两个半品一个成品
        List<WorkOrderPO> workOrderPOList = generateWorkOrderPO(dto, handworkUnScheduleDTO, routingDO, stockPointMap, bomRoutingStepInputVOS, ruleEncodingsMap);
        BasePOUtils.insertBatchFiller(workOrderPOList);
        workOrderDao.insertBatch(workOrderPOList);
        //两个半品对应的制造订单
        List<String> finalInputProductList = inputProductList;
        Map<String, WorkOrderPO> inputWorkOrderMap = workOrderPOList.stream().filter(t -> finalInputProductList.contains(t.getProductId())).collect(Collectors.toMap(WorkOrderPO::getId, Function.identity()));
        //成品对应的制造订单
        WorkOrderPO workOrder = workOrderPOList.stream().filter(t -> !finalInputProductList.contains(t.getProductId())).findFirst().orElse(new WorkOrderPO());
        //生成 fulfillment 和 supply,demand
        masterPlanSplitSupport.generateDemandAndSupplyAndFulfill(new ArrayList<>(inputWorkOrderMap.values()),
                workOrder.getId(), "手工新增制造订单生成", sourceInputProductId, sourceStockPointId);
        //制造订单展开
        List<String> needSyncWorkOrderIds = workOrderPOList.stream().map(WorkOrderPO::getId).collect(Collectors.toList());
        RzzMpsAlgorithmOutput mpsAlgorithmOutput = new RzzMpsAlgorithmOutput();
        mpsAlgorithmOutput.setWorkOrderIds(needSyncWorkOrderIds);
        workOrderSync.doSyncOrder(mpsAlgorithmOutput);
        //推移工序时间

        List<OperationVO> operationVOList = operationService.selectByWorkOrderIds(needSyncWorkOrderIds);
        List<String> parentIds = operationVOList.stream().filter(t -> StringUtils.isEmpty(t.getParentId())).map(OperationVO::getId).collect(Collectors.toList());
        //查询工序候选资源
        List<OperationResourceVO> operationResourceVOList = operationResourceService.selectVOByParams(ImmutableMap.of("operationIds", parentIds));
        Map<String, String> operationOfResourceIdMap = new HashMap<>();
        Map<String, List<OperationResourceVO>> operationMap = operationResourceVOList.stream().collect(Collectors.groupingBy(OperationResourceBasicVO::getOperationId));
        //父operation
        List<OperationVO> operationVOS = operationVOList.stream().filter(t -> StringUtils.isEmpty(t.getParentId())).collect(Collectors.toList());
        // 获取最高优先级资源
        getHighPriorityOperationResources(operationVOS, operationMap, operationOfResourceIdMap, dto, keyStepIds);
        Map<String, List<OperationVO>> inputOperationMap = operationVOS.stream().filter(t -> inputWorkOrderMap.containsKey(t.getOrderId())).collect(Collectors.groupingBy(OperationVO::getOrderId));
        //成品对应工序
        List<OperationVO> collect = operationVOS.stream().filter(t -> !inputWorkOrderMap.containsKey(t.getOrderId())).sorted(Comparator.comparing(OperationVO::getRoutingStepSequenceNo)).collect(Collectors.toList());
        //前面经过两次循环后，startTime已经是第二个半品的结束时间了，即是成品第一道工序的开始时间
        List<OperationVO> keyOperationVOS = inputOperationMap.values().stream().flatMap(Collection::stream)
                .filter(t -> dto.getRoutingStepSequenceNo().equals(t.getRoutingStepSequenceNo().toString())).collect(Collectors.toList());
        keyOperationVOS.addAll(collect);
        List<String> szWorkOrderIds = new ArrayList<>();
        if (!"LC".equals(dto.getDemandType())) {
            szWorkOrderIds = needSyncWorkOrderIds;
        }
        handworkUnScheduleDTO.setMasterPlanInsertDTO(dto);
        handworkUnScheduleDTO.setKeyOperationList(keyOperationVOS);
        handworkUnScheduleDTO.setOperationOfResourceIdMap(operationOfResourceIdMap);
        handworkUnScheduleDTO.setSzWorkOrderIds(szWorkOrderIds);
    }

    public void doAdjust(MasterPlanInsertDTO dto, List<OperationVO> keyOperationVOS, Map<String, String> operationOfResourceIdMap, List<String> workOrderIds) {
        List<RzzAdjustmentParam> adjustmentParams = new ArrayList<>();
        for (OperationVO keyOperationVO : keyOperationVOS) {
            RzzAdjustmentParam adjustmentParam = new RzzAdjustmentParam();
            adjustmentParam.setOperationId(keyOperationVO.getId());
            adjustmentParam.setQty(keyOperationVO.getQuantity());
            adjustmentParam.setTargetResourceId(StringUtils.isEmpty(keyOperationVO.getPlannedResourceId()) ? operationOfResourceIdMap.get(keyOperationVO.getId()) : keyOperationVO.getPlannedResourceId());
            adjustmentParam.setAppointStartTime(DateUtils.dateToString(dto.getStartTime(), COMMON_DATE_STR1));
            adjustmentParam.setAdjustQuantityFlag(false);
            adjustmentParam.setDetailIds(dto.getDetailIds());
            adjustmentParam.setSzWorkOrderIds(workOrderIds);
            adjustmentParams.add(adjustmentParam);
        }
        //走手工调整
        handworkScheduleService.doHandworkScheduleBatch(adjustmentParams);
    }


    /**
     * 获取最高优先级资源
     *
     * @param operationVOS
     * @param operationMap
     * @param operationOfResourceIdMap
     * @param dto
     * @param keyStepIds
     */
    private static void getHighPriorityOperationResources(List<OperationVO> operationVOS,
                                                          Map<String, List<OperationResourceVO>> operationMap,
                                                          Map<String, String> operationOfResourceIdMap,
                                                          MasterPlanInsertDTO dto, List<String> keyStepIds) {
        List<String> errorMsg = new ArrayList<>();
        for (OperationVO operationVO : operationVOS) {
            // 是关键工序
            if (keyStepIds.contains(operationVO.getStandardStepId())) {
                operationOfResourceIdMap.put(operationVO.getId(), dto.getPhysicalResourceId());
                continue;
            }
            if (operationMap.containsKey(operationVO.getId())) {
                //获取优先级最高的候选资源
                OperationResourceVO operationResourceVO = operationMap.get(operationVO.getId()).stream()
                        .filter(t -> t.getPriority() != null)
                        .min(Comparator.comparing(OperationResourceVO::getPriority)).orElse(null);
                if (operationResourceVO == null) {
                    // 有候选资源，但是都没优先级，就随便取一个
                    operationResourceVO = operationMap.get(operationVO.getId()).get(0);
                }
                operationOfResourceIdMap.put(operationVO.getId(), operationResourceVO.getPhysicalResourceId());
            } else {
                errorMsg.add("未找到" + operationVO.getRoutingStepSequenceNo() + "对应的候选资源");
            }
        }
        if (CollectionUtils.isNotEmpty(errorMsg)) {
            throw new BusinessException(String.join(";", errorMsg));
        }
    }

    private List<WorkOrderPO> generateWorkOrderPO(MasterPlanInsertDTO dto, HandworkUnScheduleDTO handworkUnScheduleDTO, RoutingDO routingDO, Map<String, NewStockPointVO> stockPointMap, List<BomRoutingStepInputVO> bomRoutingStepInputVOS, Map<String, RuleEncodingsVO> ruleEncodingsMap) {
        List<WorkOrderPO> insertWorkOrderPOList = new ArrayList<>();
        String testOrderNumber = dto.getTestOrderNumber();
        String orderNo = getOrderNo(ruleEncodingsMap);
        //创建成品制造订单
        String workOrderId = UUIDUtil.getUUID();
        WorkOrderPO workOrder = new WorkOrderPO();
        workOrder.setId(workOrderId);
        workOrder.setOrderNo(orderNo);
        workOrder.setQuantity(dto.getQuantity());
        workOrder.setProductId(routingDO.getProductId());
        workOrder.setProductStockPointId(routingDO.getProductId());
        workOrder.setStockPointId(stockPointMap.get(routingDO.getStockPointId()).getId());
        workOrder.setTestOrderNumber(testOrderNumber);
        insertWorkOrderPOList.add(workOrder);
        handworkUnScheduleDTO.setWorkOrderPO(workOrder);
        if (CollectionUtils.isNotEmpty(bomRoutingStepInputVOS)) {
            List<RoutingStepDO> routingStepDOList = routingDO.getRoutingStepDOList();
            RoutingStepDO routingStepDO = routingStepDOList.stream().min(Comparator.comparing(RoutingStepDO::getSequenceNo)).orElse(null);
            if (routingStepDO == null) {
                throw new BusinessException("没有工艺路径步骤数据");
            }
            Map<Integer, BigDecimal> integerBigDecimalMap = AbstractOperationSync.computeOperationQty(workOrder.getQuantity(), routingStepDOList);
            BigDecimal bigDecimal = integerBigDecimalMap.get(routingStepDO.getSequenceNo());
            //创建半品制造订单
            for (BomRoutingStepInputVO bomRoutingStepInputVO : bomRoutingStepInputVOS) {
                String no = getOrderNo(ruleEncodingsMap);
                WorkOrderPO inputWorkOrder = new WorkOrderPO();
                String inputWorkOrderId = UUIDUtil.getUUID();
                inputWorkOrder.setId(inputWorkOrderId);
                inputWorkOrder.setOrderNo(no);
                //良品率默认为1
                BigDecimal yield = BigDecimal.ONE;
                if (bomRoutingStepInputVO.getYield() != null && bomRoutingStepInputVO.getYield().compareTo(BigDecimal.ONE) > 0) {
                    yield = bomRoutingStepInputVO.getYield();
                }
                String productId = bomRoutingStepInputVO.getInputProductId();
                inputWorkOrder.setQuantity(bigDecimal.divide(yield, 0, RoundingMode.UP));
                inputWorkOrder.setProductId(productId);
                inputWorkOrder.setProductStockPointId(productId);
                inputWorkOrder.setStockPointId(bomRoutingStepInputVO.getStockPointId());
                inputWorkOrder.setParentId(workOrderId);
                inputWorkOrder.setTopOrderId(workOrderId);
                inputWorkOrder.setTestOrderNumber(testOrderNumber);
                insertWorkOrderPOList.add(inputWorkOrder);
            }
        }
        Date dueDate = new Date();
        String orderType = StringUtils.isEmpty(dto.getDemandType()) ? "LC" : dto.getDemandType();
        insertWorkOrderPOList.forEach(t -> {
            t.setPlanStatus(PlannedStatusEnum.UNPLAN.getCode());
            //交期暂时设置为当前时间，推移完之后会更新交期
            t.setDueDate(dueDate);
            t.setDemandCategory(ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode());
            t.setOrderType(orderType);
            t.setRemark("手动新增");
        });
        return insertWorkOrderPOList;
    }

    private String getOrderNo(Map<String, RuleEncodingsVO> ruleEncodingsMap) {
        RuleEncodingsVO ruleEncoding = ruleEncodingsMap.get(RuleEncodingsEnum.WORK_ORDER_NO.getDesc());
        String serialNumber = RuleEncodingsUtils.getSerialNumber(ruleEncoding);
        String orderNo = RuleEncodingsUtils.getCode(ruleEncoding, null, serialNumber);
        ruleEncoding.setSerialNumberMaxValue(serialNumber);
        return orderNo;
    }

}
