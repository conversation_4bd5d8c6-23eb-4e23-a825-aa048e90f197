#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import os

def fix_chinese_comments(file_path):
    """Fix Chinese comments in Java file to avoid encoding issues"""
    
    # Read the file
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Replace Chinese comments with English equivalents
    replacements = {
        '找不到发货计划': 'Delivery plan not found',
        '如果有查询条件，使用MDS服务的查询（临时使用原有方法）': 'If query conditions exist, use MDS service query',
        '如果没有查询条件，从发货计划中获取产品编码': 'If no query conditions, get product codes from delivery plan',
        '优化：异步获取发货计划数据 - 只查询当前页面需要的产品': 'Optimization: async get delivery plan data',
        '优化：异步获取库存数据 - 只查询当前页面需要的产品': 'Optimization: async get inventory data',
        '获取并组装中转库存': 'Get and assemble transit inventory',
        '销售组织列表': 'Sales organization list',
        '主机厂物料映射': 'OEM material mapping',
        '获取并组装在途库存': 'Get and assemble transporting inventory',
        '主机厂产品映射': 'OEM product mapping',
        '最小运输时间': 'Minimum transport time',
        '根据主机厂分组': 'Group by OEM',
        '优化：异步获取装载需求数据': 'Optimization: async get loading demand data',
        '原始需求版本不存在': 'Original demand version not found',
        '获取销售组织信息': 'Get sales organization info',
        '使用传入的目标产品编码列表': 'Use input target product code list',
        '处理每个产品的数据': 'Process data for each product',
        '获取销售组织信息': 'Get sales organization info',
        '销售组织信息为空': 'Sales organization info is empty',
        '获取产品库存点映射': 'Get product stock point mapping',
        '查询库存点数据，用于过滤非本厂库存': 'Query stock point data to filter non-factory inventory',
        '获取本厂生产组织类型的仓库(半成品库存点)': 'Get factory production organization warehouses',
        '工序在制量': 'Work in process quantity',
        '处理发货计划动态数据': 'Process delivery plan dynamic data',
        '处理装车需求动态数据': 'Process loading demand dynamic data',
        '处理发货计划动态数据': 'Process delivery plan dynamic data',
        '处理装车需求动态数据': 'Process loading demand dynamic data',
        '处理详情数据 - 累积扣减库存，装车和需求分别处理': 'Process detail data - cumulative inventory deduction',
        '累积扣减变量 :分别为装车和需求的库存': 'Cumulative deduction variables for loading and demand',
        '装车数量的累积扣减库存': 'Cumulative inventory deduction for loading quantity',
        '需求数量的累积扣减库存': 'Cumulative inventory deduction for demand quantity',
        '一旦出现红色，后续都为红色': 'Once red appears, all subsequent are red',
        '处理装车数量和颜色': 'Process loading quantity and color',
        '红色：无法满足': 'Red: cannot satisfy',
        '浅绿色：中转在途+成品库存能满足': 'Light green: transit+finished goods can satisfy',
        '蓝色：中转在途+成品+在制品能满足': 'Blue: transit+finished+WIP can satisfy',
        '装车数量的累积扣减': 'Cumulative deduction for loading quantity',
        '处理需求数量和颜色': 'Process demand quantity and color',
        '需求数量颜色判断（基于累积扣减后的库存）': 'Demand quantity color judgment',
        '需求数量的累积扣减': 'Cumulative deduction for demand quantity',
        '库存数组，按优先级顺序排列': 'Inventory array in priority order',
        '贴膜': 'Lamination',
        '使用SQL查询需求发货生产报表（新方法）': 'Use SQL to query demand delivery production report',
        '补充库存数据（根据模式选择）': 'Supplement inventory data based on mode',
        '处理动态数据（发货计划和装车需求的日期数据）': 'Process dynamic data',
        '处理统一查询结果的动态数据': 'Process dynamic data for unified query results',
        '获取所有产品编码': 'Get all product codes',
        '批量查询装车需求数据': 'Batch query loading demand data',
        '为每个产品设置动态数据': 'Set dynamic data for each product',
        '处理发货计划动态数据': 'Process delivery plan dynamic data',
        '处理装车需求动态数据': 'Process loading demand dynamic data',
        '批量获取装车需求数据映射': 'Batch get loading demand data mapping'
    }
    
    # Apply replacements
    for chinese, english in replacements.items():
        content = content.replace(chinese, english)
    
    # Remove remaining Chinese characters in comments
    # Pattern to match Chinese characters in comments
    chinese_pattern = r'(//.*?|/\*.*?\*/|^\s*\*.*?)[\u4e00-\u9fff]+'
    
    def replace_chinese_in_comment(match):
        comment = match.group(0)
        # Remove Chinese characters but keep the comment structure
        cleaned = re.sub(r'[\u4e00-\u9fff]+', '', comment)
        # If comment becomes empty, replace with generic comment
        if not cleaned.strip() or cleaned.strip() in ['//', '/*', '*/', '*']:
            if comment.startswith('//'):
                return '// TODO: Add comment'
            elif comment.startswith('/*'):
                return '/* TODO: Add comment */'
            elif comment.strip().startswith('*'):
                return ' * TODO: Add comment'
        return cleaned
    
    content = re.sub(chinese_pattern, replace_chinese_in_comment, content, flags=re.MULTILINE | re.DOTALL)
    
    # Write back to file
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Fixed encoding issues in {file_path}")

if __name__ == "__main__":
    file_path = "scp-dfp-service/src/main/java/com/yhl/scp/dfp/report/service/impl/DemandDeliveryProductionServiceImpl.java"
    if os.path.exists(file_path):
        fix_chinese_comments(file_path)
    else:
        print(f"File not found: {file_path}")
