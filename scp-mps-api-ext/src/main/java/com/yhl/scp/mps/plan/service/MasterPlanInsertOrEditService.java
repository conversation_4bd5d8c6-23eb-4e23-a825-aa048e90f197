package com.yhl.scp.mps.plan.service;

import com.yhl.platform.common.LabelValue;
import com.yhl.scp.mps.feedback.req.FeedBackReq;
import com.yhl.scp.mps.plan.dto.MasterPlanInsertDTO;
import com.yhl.scp.mps.plan.req.MasterQuantityReq;
import com.yhl.scp.mps.schedule.HandworkUnScheduleDTO;
import com.yhl.scp.sds.extension.order.dto.WorkOrderDTO;
import com.yhl.scp.mps.schedule.HandworkUnScheduleDTO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <code>MasterPlanInsertOrEditService</code>
 * <p>
 * 主生产计划-新增，编辑接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-07 17:01:47
 */
public interface MasterPlanInsertOrEditService {

    /**
     * 主计划新增校验接口
     * @param dto
     */
    void insertOperationCheck(MasterPlanInsertDTO dto);

    /**
     * 新增
     * @param dto
     */
    HandworkUnScheduleDTO insertData(MasterPlanInsertDTO dto);



    /**
     * 工序下拉-根据产品
     * @return
     */
    List<LabelValue<String>> operationDropDown(String productCode);

    /**
     * 候选资源下拉-根据产品和工序
     * @return
     */
    List<LabelValue<String>> resourceDropDown(String productCode, String routingStepId);


    /**
     * 齐套拆分
     * @param
     */
    void doCompleteSetOfWorkOrderSplit(List<String> subOperationIds);

    /**
     * 保存试制订单号
     *
     * @param workOrderDTO
     */
    void doSaveTestOrderNum(WorkOrderDTO workOrderDTO);

    BigDecimal calculateWorkOrderQty(MasterQuantityReq masterQuantityReq);

    String getResourceCode(MasterPlanInsertDTO dto);

    /**
     * 保存完工数量
     * @param operationId
     * @param reportingQuantity
     */
    void doUpdateFeedback(FeedBackReq feedBackReq);

    void updateFeedbackCheck(FeedBackReq feedBackReq);

}
