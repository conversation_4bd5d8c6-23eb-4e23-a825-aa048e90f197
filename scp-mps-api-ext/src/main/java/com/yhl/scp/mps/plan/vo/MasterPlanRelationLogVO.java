package com.yhl.scp.mps.plan.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>MasterPlanRelationLogVO</code>
 * <p>
 * 计划单关联表中间表VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-24 16:55:03
 */
@ApiModel(value = "计划单关联表中间表VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MasterPlanRelationLogVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 744149950960193644L;

    /**
     * 组织id
     */
    @ApiModelProperty(value = "组织id")
    @FieldInterpretation(value = "组织id")
    private String orgId;
    /**
     * erp工单号
     */
    @ApiModelProperty(value = "erp工单号")
    @FieldInterpretation(value = "erp工单号")
    private String wipEntityName;
    /**
     * 计划单号
     */
    @ApiModelProperty(value = "计划单号")
    @FieldInterpretation(value = "计划单号")
    private String reqNumber;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @FieldInterpretation(value = "状态")
    private String statusType;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    @FieldInterpretation(value = "数量")
    private Integer qty;
    /**
     * 计划单行号
     */
    @ApiModelProperty(value = "计划单行号")
    @FieldInterpretation(value = "计划单行号")
    private String lineNum;
    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间")
    @FieldInterpretation(value = "最后更新时间")
    private Date lastUpdateDate;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @FieldInterpretation(value = "版本")
    private Integer versionValue;
    /**
     * 计划表的id
     */
    @ApiModelProperty(value = "计划表的id")
    @FieldInterpretation(value = "计划表的id")
    private String headId;

    @Override
    public void clean() {

    }

}
