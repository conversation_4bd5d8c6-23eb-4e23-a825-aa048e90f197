package com.yhl.scp.mps.operationPublished.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import com.yhl.scp.mps.plan.dto.DeliveryChangeRecordDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>OperationResourcePublishedDTO</code>
 * <p>
 * 工序资源发布信息表DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-20 09:38:21
 */
@ApiModel(value = "工序资源发布信息表DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OperationResourcePublishedDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 204226591473643924L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String newId;
    
    /**
     * 源数据id
     */
    @ApiModelProperty(value = "源数据id")
    private String id;
    
    /**
     * 制造订单ID
     */
    @ApiModelProperty(value = "制造订单ID")
    private String orderId;
    /**
     * 计划批次ID
     */
    @ApiModelProperty(value = "计划批次ID")
    private String planUnitId;
    /**
     * 工序ID
     */
    @ApiModelProperty(value = "工序ID")
    private String operationId;
    /**
     * 路径步骤ID
     */
    @ApiModelProperty(value = "路径步骤ID")
    private String routingStepId;
    /**
     * 路径步骤顺序号
     */
    @ApiModelProperty(value = "路径步骤顺序号")
    private Integer routingStepSequenceNo;
    /**
     * 计划资源ID
     */
    @ApiModelProperty(value = "计划资源ID")
    private String plannedResourceId;
    /**
     * 物理资源ID
     */
    @ApiModelProperty(value = "物理资源ID")
    private String physicalResourceId;
    /**
     * 订单类型
     */
    @ApiModelProperty(value = "订单类型")
    private String orderType;
    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private Integer priority;
    /**
     * 设置时间
     */
    @ApiModelProperty(value = "设置时间")
    private Integer setupDuration;
    /**
     * 固定工时
     */
    @ApiModelProperty(value = "固定工时")
    private Integer fixedWorkHours;
    /**
     * 每小时产量
     */
    @ApiModelProperty(value = "每小时产量")
    private BigDecimal unitsPerHour;
    /**
     * 单件生产时间
     */
    @ApiModelProperty(value = "单件生产时间")
    private BigDecimal unitProductionTime;
    /**
     * 清洗时间
     */
    @ApiModelProperty(value = "清洗时间")
    private Integer cleanupDuration;
    /**
     * 制造时间
     */
    @ApiModelProperty(value = "制造时间")
    private Integer processDuration;
    /**
     * 替代工器具组号
     */
    @ApiModelProperty(value = "替代工器具组号")
    private String altToolCode;
    /**
     * 配套使用号
     */
    @ApiModelProperty(value = "配套使用号")
    private String matchCode;
    /**
     * 最大制造批量
     */
    @ApiModelProperty(value = "最大制造批量")
    private BigDecimal maxLotSize;
    /**
     * 单位制造量
     */
    @ApiModelProperty(value = "单位制造量")
    private BigDecimal lotSize;
    /**
     * 设置必要资源量
     */
    @ApiModelProperty(value = "设置必要资源量")
    private BigDecimal setupUnitBatchSize;
    /**
     * 制造必要资源量
     */
    @ApiModelProperty(value = "制造必要资源量")
    private BigDecimal productionUnitBatchSize;
    /**
     * 清洗必要资源量
     */
    @ApiModelProperty(value = "清洗必要资源量")
    private BigDecimal cleanupUnitBatchSize;
    /**
     * 最大设置中断时间
     */
    @ApiModelProperty(value = "最大设置中断时间")
    private Integer maxSetupSuspendDuration;
    /**
     * 最大制造中断时间
     */
    @ApiModelProperty(value = "最大制造中断时间")
    private Integer maxProductionSuspendDuration;
    /**
     * 最大清洗中断时间
     */
    @ApiModelProperty(value = "最大清洗中断时间")
    private Integer maxCleanupSuspendDuration;
    /**
     * 生产线
     */
    @ApiModelProperty(value = "生产线")
    private String productionLine;
    /**
     * 严格遵守生产线约束
     */
    @ApiModelProperty(value = "严格遵守生产线约束")
    private String strictProductionLineConstraints;
    /**
     * 生产排程预留时间
     */
    @ApiModelProperty(value = "生产排程预留时间")
    private Integer schedulingSpace;
    /**
     * 是否允许拆分子工序
     */
    @ApiModelProperty(value = "是否允许拆分子工序")
    private String splitAllowed;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String enabled;
    /**
     * 模具数量限制
     */
    @ApiModelProperty(value = "模具数量限制")
    private BigDecimal moldQuantityLimit;
    
    /**
     * 主生产计划发布日志id
     */
    @ApiModelProperty(value = "主生产计划发布日志id")
    private String publishedLogId;

}
