package com.yhl.scp.dcp.oauth.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <code>TestController</code>
 * <p>
 * TestController
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-24 11:51:09
 */
@RestController
@RequestMapping("/")
@Slf4j
public class TestController {

    @Autowired
    private PasswordEncoder passwordEncoder;

    @GetMapping("openapi/private")
    // @PreAuthorize("hasRole('USER')")
    public ResponseEntity<String> test() {
        return ResponseEntity.ok("Protected Resource");
    }

    @GetMapping("public")
    public ResponseEntity<String> test1() {
        log.info("passwordEncoder0:{}", passwordEncoder.encode("123456"));
        PasswordEncoder passwordEncoder1 =  new BCryptPasswordEncoder();
        String encode = passwordEncoder1.encode("654321");
        log.info("passwordEncoder1:{}", encode);
        return ResponseEntity.ok("Granted Resource");
    }

}
