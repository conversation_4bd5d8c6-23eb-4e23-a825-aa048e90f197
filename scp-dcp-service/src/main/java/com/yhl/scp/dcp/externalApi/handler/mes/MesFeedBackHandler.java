package com.yhl.scp.dcp.externalApi.handler.mes;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesFeedBack;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesOpYield;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponseData;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.enmus.MesApiReqCodeEnum;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mps.feign.MpsFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;

/**
 * <code>MesFeedBackHandler</code>
 * <p>
 * MesFeedBackHandler
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-16 15:02:41
 */
@Component
@Slf4j
public class MesFeedBackHandler extends SyncDataHandler<List<MesFeedBack>> {

    @Resource
    private AuthHandler authHandler;

    @Resource
    private MpsFeign mpsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private ExtApiLogService extApiLogService;

    @Override
    protected List<MesFeedBack> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            log.error("MES同步生产反馈数据为空！");
            return Collections.emptyList();
        }
        return JSON.parseArray(body, MesFeedBack.class);
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<MesFeedBack> mesFeedBacks) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (CollectionUtils.isEmpty(mesFeedBacks)) {
            log.error("上海MES生产反聩数据为空");
            return null;
        }
        BaseResponse<String> mdsScenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MPS.getCode(), TenantCodeEnum.FYQB.getCode());
        Assert.isTrue(mdsScenario.getSuccess(), mdsScenario.getMsg());
        log.info("上海MES生产反聩数据大小:{}", mesFeedBacks.size());
        mpsFeign.handleMesFeedBack(mdsScenario.getData(), mesFeedBacks);
        this.saveSyncCtrl(apiConfigVO, params, mesFeedBacks);
        return null;
    }

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        if (log.isInfoEnabled()) {
            log.info("开始同步MES生产发聩:{},{}", apiConfigVO, params);
        }
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        try {
            String mesToken = authHandler.handle(MapUtil.newHashMap());
            String apiUri = apiConfigVO.getApiUri();
            String apiParams = apiConfigVO.getApiParams();
            String systemNumber = apiConfigVO.getSystemNumber();
            String url = apiUri + apiParams;
            if (log.isInfoEnabled()) {
                log.info("mesToken={},apiUri={},apiParams={},systemNumber={},url={}", mesToken, apiUri, apiParams, systemNumber, url);
            }
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.set("Authorization", "Bearer " + mesToken);
            // 指定请求码
            String reqCode = MesApiReqCodeEnum.FY_ENTITY_REPORT_FOR_BPIM.getCode();
            List<Object> result = Lists.newArrayList();
            Date startTime = new Date();
            Date offsetHourTime = DateUtil.offsetHour(startTime, -1);
            int currentPage = 1;
            boolean hasNextSize = true;
            while (hasNextSize) {
                HashMap<Object, Object> paramMap = MapUtil.newHashMap();
                paramMap.put("currentPage", currentPage);
                paramMap.put("pageSize", 10000);
                paramMap.put("reqCode", reqCode);
                paramMap.put("beginTime", DateUtils.dateToString(offsetHourTime, DateUtils.COMMON_DATE_STR1));
                paramMap.put("endTime", DateUtils.dateToString(startTime, DateUtils.COMMON_DATE_STR1));
                if (log.isInfoEnabled()) {
                    log.info("request paramMap={}", paramMap);
                }
                // 创建子日志
                ExtApiLogDTO subLog = extApiLogService.createLog(apiConfigVO, params, mainLog, httpHeaders.toString(), JSONObject.toJSONString(paramMap));
                HttpEntity<String> httpEntity = new HttpEntity<>(JSON.toJSONString(paramMap), httpHeaders);
                ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
                int statusCodeValue = responseEntity.getStatusCodeValue();
                if (HttpStatus.OK.value() != statusCodeValue) {
                    extApiLogService.updateResponse(subLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                }
                Assert.isTrue(HttpStatus.OK.value() == statusCodeValue, "MES同步生产反馈信息失败！");
                String body = responseEntity.getBody();
                log.info("同步MES生产反馈完成,返回数据:{}!", body);
                MesResponse mesResponse = JSON.parseObject(body, MesResponse.class);
                MesResponseData data = Objects.requireNonNull(mesResponse).getData();
                extApiLogService.updateResponse(subLog, responseEntity, data.getMessage().size(), DcpConstants.TASKS_STATUS_SUCCESS);
                if (Objects.nonNull(data)) {
                    result.addAll(data.getMessage());
                    if (data.getTotalPage() <= data.getCurrentPage()) {
                        hasNextSize = false;
                    } else {
                        currentPage++;
                    }
                }
            }
            return JSON.toJSONString(result);
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            log.error("上海MES生产反馈信息同步失败！", e);
            throw e;
        }
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.MES.getCode(), ApiCategoryEnum.MES_FEED_BACK.getCode());
    }

    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params, List<MesFeedBack> mesFeedBacks) {
        Date lastUpdateDate = mesFeedBacks.stream().map(MesFeedBack::getShiftdate).max(Date::compareTo).get();
        return DateUtils.dateToString(lastUpdateDate, DateUtils.COMMON_DATE_STR1);
    }

}
