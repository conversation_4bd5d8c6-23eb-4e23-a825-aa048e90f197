package com.yhl.scp.dcp.token;

import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.scp.ips.enums.RedisKeyManageEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <code>TokenManager</code>
 * <p>
 * TOKEN管理器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-23 14:57:15
 */
@Component
public class TokenManager {
    @Autowired
    private RedisUtil redisUtil;

    /**
     * 删除TOKEN
     *
     * @param command
     */
    public void revoke(String command) {
        redisUtil.delete(this.buildKey(command));
    }

    public String buildKey(String command) {
        return String.format(RedisKeyManageEnum.EXTERNAL_API_TOKEN.getKey(), command);
    }
}
