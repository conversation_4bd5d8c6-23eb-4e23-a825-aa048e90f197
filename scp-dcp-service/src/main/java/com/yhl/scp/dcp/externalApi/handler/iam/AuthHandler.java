package com.yhl.scp.dcp.externalApi.handler.iam;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.ips.enums.RedisKeyManageEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;
import java.util.UUID;

import static com.yhl.scp.dcp.externalApi.handler.Handler.CMD_DELIMITER;

/**
 * description:
 * author：李杰
 * email: <EMAIL>
 * date: 2024/11/7
 */
@Slf4j
@Component("authHandler4IAM")
public class AuthHandler {

    @Value("${iam.apiId}")
    private String apiId;

    @Value("${iam.apiSecret}")
    private String apiSecret;

    @Resource
    private RedisUtil redisUtil;

    public String getToken() {
        String tokenKey = String.format(RedisKeyManageEnum.IAM_TOKEN.getKey(), getCommand());
        Object object = redisUtil.get(tokenKey);
        if (Objects.nonNull(object)) {
            return object.toString();
        }
        String token = JWT.create().withIssuer(apiId).withIssuedAt(new Date()).withJWTId(UUID.randomUUID().toString()).sign(Algorithm.HMAC256(apiSecret));
        token = String.join(" ", "Bearer", token);
        redisUtil.set(tokenKey, token, 200);
        return token;
    }

    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.IAM.getCode(), ApiCategoryEnum.AUTH.getCode());
    }
}
