package com.yhl.scp.dcp.externalApi.handler.erp;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpPrCreate;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpResponse;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.mrp.MrpFeign.MrpFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <code>PrCreateHandler</code>
 * <p>
 * PrCreateHandler
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-10 11:10:53
 */
@Component
@Slf4j
public class PrCreateHandler extends SyncDataHandler<List<ErpPrCreate>> {
    @Resource
    private MrpFeign mrpFeign;

    @Resource
    private AuthHandler authHandler;

    @Resource
    private ExtApiLogService extApiLogService;

    private String logId;

    @Override
    protected List<ErpPrCreate> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            log.error("ERP获取PR查询为空");
            return null;
        }
        ErpResponse erpResponse = JSON.parseObject(body, ErpResponse.class);
        Object data = erpResponse.getData();
        return JSON.parseArray(data.toString(), ErpPrCreate.class);
    }


    /**
     * 获取最新的更新时间
     *
     * @param erpPrCreate
     * @return
     */
    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params,
                                            List<ErpPrCreate> erpPrCreate) {
        String lastUpdateDate = erpPrCreate.stream().map(ErpPrCreate::getNeedByDate).max(String::compareTo).get();
        return lastUpdateDate;
    }


    /**
     * 处理报文体
     *
     * @param apiConfigVO
     * @param params
     * @param erpPrCreateList
     * @return
     */
    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<ErpPrCreate> erpPrCreateList) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (CollectionUtils.isEmpty(erpPrCreateList)) {
            log.error("PR创建为空");
            return null;
        }

        BaseResponse<String> scenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MRP.getCode(),
                TenantCodeEnum.FYQB.getCode());
        Assert.isTrue(scenario.getSuccess(), scenario.getMsg());
        log.info("开始创建PR");
        mrpFeign.handlePrCreate(scenario.getData(), erpPrCreateList);
        log.info("结束创建PR");
        this.saveSyncCtrl(apiConfigVO, params, erpPrCreateList);
        // 保存同步控制信息
        return this.logId;
    }

    /**
     * 调用后台请求
     *
     * @param apiConfigVO
     * @param params
     * @return
     */
    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        log.info("开始同步ERP_PR创建:{},{}", apiConfigVO, params);
        try {
            String erpToken = authHandler.handle(MapUtil.newHashMap());
            String apiUri = apiConfigVO.getApiUri();
            String systemNumber = apiConfigVO.getSystemNumber();
            String lastUpdateDate = this.getSyncRefValue(apiConfigVO, params);
            Date calculateDate = DateUtils.stringToDate(lastUpdateDate, DateUtils.COMMON_DATE_STR3);
            lastUpdateDate = DateUtils.dateToString(calculateDate);
           // List<ErpPrCreate> createList = (List<ErpPrCreate>) params.get("createList");
            String jsonString = params.get("createList").toString();
            Type listType = new TypeToken<List<ErpPrCreate>>() {}.getType();
            List<ErpPrCreate> createList = new Gson().fromJson(jsonString, listType);
            String url = apiUri + "/" + systemNumber + "/" + sequenceService.getSuffix(systemNumber, getCommand(), 5) + "?token=" + erpToken;

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);

            params.put("lastUpdateDate", lastUpdateDate);
            String bodyStr = JSONObject.toJSONString(params);
            if (log.isInfoEnabled()) {
                log.info("erpToken={},apiUri={},systemNumber={},lastUpdateDate={},url={},bodyStr={}", erpToken, apiUri
                        , systemNumber, lastUpdateDate, url, bodyStr);
            }

            // 打印请求体内容
            HttpEntity<String> httpEntity = new HttpEntity<>(JSON.toJSONString(createList), httpHeaders);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
            int statusCodeValue = responseEntity.getStatusCodeValue();
            if (HttpStatus.OK.value() != statusCodeValue) {
                extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                throw new BusinessException(StrUtil.format("上传ERP_PR创建请求失败,HTTP状态码:{}!", statusCodeValue));
            }

            String body = responseEntity.getBody();
            int applyCount=0;
            ErpResponse erpResponse = JSON.parseObject(body, ErpResponse.class);
            if(erpResponse.getSuccess()){
                Object data = erpResponse.getData();
                List<ErpPrCreate> erpPrCreates = JSON.parseArray(data.toString(), ErpPrCreate.class);
                applyCount=erpPrCreates.size();
                List<ErpPrCreate> batchIdList = erpPrCreates.stream()
                        .filter(t -> "VALID".equals(t.getValidataFlag()))
                        .collect(Collectors.toList());
                String bpimLineNumbers =  createList.stream()
                        .filter(create -> batchIdList.stream()
                                .anyMatch(batch -> batch.getBatchId().equals(create.getBatchId())))
                        .map(ErpPrCreate::getBpimLineNumber)
                        .collect(Collectors.joining(","));
                mainLog.setRemark(bpimLineNumbers);
            }
            mainLog.setApplyCount(applyCount);
            extApiLogService.updateResponse(mainLog, responseEntity, createList.size(), DcpConstants.TASKS_STATUS_SUCCESS);
            log.info("上传ERP_PR创建处理完成,返回数据:{}!", body);
            this.logId = mainLog.getId();
            return body;
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.ERP.getCode(), ApiCategoryEnum.PR_CREATE.getCode());
    }
}
