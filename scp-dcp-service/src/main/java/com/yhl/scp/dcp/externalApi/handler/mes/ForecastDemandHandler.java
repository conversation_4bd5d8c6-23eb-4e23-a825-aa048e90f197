package com.yhl.scp.dcp.externalApi.handler.mes;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponseData;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.dfp.common.enums.GranularityEnum;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.originDemand.dto.FdpOriginDemandForecastInterfaceLogDTO;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;

/**
 * <code>ShanghaiMesForecastHandler</code>
 * <p>
 * FYE-MES预测需求接口
 * 同步方式：增量
 * 不分页
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-30 17:20:58
 */
@Component
@Slf4j
public class ForecastDemandHandler extends SyncDataHandler<List<FdpOriginDemandForecastInterfaceLogDTO>> {
    @Resource
    private AuthHandler authHandler;

    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private ExtApiLogService extApiLogService;

    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params, List<FdpOriginDemandForecastInterfaceLogDTO> fdpOriginDemandForecastInterfaceLogDTOS) {
        Date lastUpdateDate = fdpOriginDemandForecastInterfaceLogDTOS.stream().map(FdpOriginDemandForecastInterfaceLogDTO::getLastUpdateDate).max(Date::compareTo).get();
        return DateUtils.dateToString(lastUpdateDate, DateUtils.COMMON_DATE_STR1);
    }

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        if (log.isInfoEnabled()) {
            log.info("开始同步MES需求数据:{},{}", apiConfigVO, params);
        }
        long methodStartTime = System.currentTimeMillis();

        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        try {
            String mesToken = authHandler.handle(MapUtil.newHashMap());
            String apiUri = apiConfigVO.getApiUri();
            String apiParams = apiConfigVO.getApiParams();

            String url = apiUri +  apiParams;

            if (log.isInfoEnabled()) {
                log.info("mesToken={},apiUri={},apiParams={},url={}", mesToken, apiUri, apiParams,url);
            }
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.set("Authorization", "Bearer " + mesToken);
            String reqCode = "FYE_FORECAST_FOR_ERP";
            String lastUpdateDateStr = this.getSyncRefValue(apiConfigVO, params);
            Date calculateDate = DateUtils.stringToDate(lastUpdateDateStr, DateUtils.COMMON_DATE_STR3);
            Date currentDate = DateUtils.truncateTimeOfDate(new Date());
            int period = (int) DateUtil.between(calculateDate, currentDate, DateUnit.DAY);
            int calculatePeriod = 5;
            int count = period / calculatePeriod + 1;
            int pageSize = Objects.isNull(apiConfigVO.getOffsetSize())?10000:apiConfigVO.getOffsetSize(); // 根据接口说明设置每页记录数
            Date beginTime = calculateDate;
            List<Object> result = Lists.newArrayList();
            for (int i = 0; i < count; i++) {
                int currentPage = 1;
                boolean hasNextSize = true;
                while (hasNextSize) {
                    long pageStartTime = System.currentTimeMillis();

                    HashMap<Object, Object> paramMap = MapUtil.newHashMap();
                    paramMap.put("currentPage", currentPage);
                    paramMap.put("pageSize", pageSize);
                    paramMap.put("reqCode", reqCode);
                    paramMap.put("beginTime", DateUtils.dateToString(beginTime, DateUtils.COMMON_DATE_STR1));
                    Date endTime = org.apache.commons.lang3.time.DateUtils.addDays(beginTime, calculatePeriod);
                    paramMap.put("endTime", DateUtils.dateToString(endTime, DateUtils.COMMON_DATE_STR1));
                    if (log.isInfoEnabled()) {
                        log.info("request paramMap={}", paramMap);
                    }
                    // 创建子日志
                    ExtApiLogDTO subLog = extApiLogService.createLog(apiConfigVO, params, mainLog,
                            httpHeaders.toString(),
                            JSONObject.toJSONString(paramMap));
                    HttpEntity<String> httpEntity = new HttpEntity<>(JSON.toJSONString(paramMap), httpHeaders);
                    ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
                    int statusCodeValue = responseEntity.getStatusCodeValue();
                    if (HttpStatus.OK.value() != statusCodeValue) {
                        extApiLogService.updateResponse(subLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                    }
                    Assert.isTrue(HttpStatus.OK.value() == statusCodeValue, "MES同步预测数据失败！");
                    // 计算单个页请求耗时（秒）
                    long pageEndTime = System.currentTimeMillis();
                    double pageTimeSeconds = (pageEndTime - pageStartTime) / 1000.0;
                    String body = responseEntity.getBody();

                    MesResponse mesResponse = JSON.parseObject(body, MesResponse.class);
                    if (mesResponse.getCode() != 0) {
                        String errorStr = mesResponse.getData().getMessage().toString();
                        log.error("数据报错，{}", mesResponse.getData().getMessage().toString());
                        throw new BusinessException(StrUtil.format("MES调用接口报错:{}!", errorStr));
                    }
                    MesResponseData data = Objects.requireNonNull(mesResponse).getData();
                    extApiLogService.updateResponse(subLog, null, data.getMessage().size(), DcpConstants.TASKS_STATUS_SUCCESS);
                    if (Objects.nonNull(data)) {
                        result.addAll(data.getMessage());
                        if (data.getTotalPage() <= data.getCurrentPage()) {
                            hasNextSize = false;
                            beginTime = endTime;
                        } else {
                            currentPage++;
                        }
                    }
                    log.info("MES预测接口第{}批,第{}页请求完成，数据量：{}，耗时：{}秒",i, currentPage, data.getMessage().size(),pageTimeSeconds);

                }
            }
            // 3. 完成主日志
            extApiLogService.updateResponse(mainLog, null, result.size(), DcpConstants.TASKS_STATUS_SUCCESS);
            long methodEndTime = System.currentTimeMillis();
            double totalTimeSeconds = (methodEndTime - methodStartTime) / 1000.0;

            log.info("MES预测数据,数据条数{}，总耗时：{}秒,", result.size(),totalTimeSeconds);
            return JSONObject.toJSONString(result);
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    protected List<FdpOriginDemandForecastInterfaceLogDTO> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            log.error("MES获取预测数据为空");
            return null;
        }
        return JSONArray.parseArray(body, FdpOriginDemandForecastInterfaceLogDTO.class);
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<FdpOriginDemandForecastInterfaceLogDTO> list) {
        long handleStartTime = System.currentTimeMillis();

        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (CollectionUtils.isEmpty(list)) {
            log.error("MES预测数据为空");
            return null;
        }
        BaseResponse<String> scenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
        Assert.isTrue(scenario.getSuccess(), scenario.getMsg());
        list.forEach(dto -> {
            dto.setImportType(ApiSourceEnum.MES.getCode());
            dto.setSubmissionType(GranularityEnum.MONTH.getCode());
            dto.setEnabled(YesOrNoEnum.YES.getCode());

        });
        log.info("接口返回MES预测数据大小:{}",list.size());
        long dfpCallStartTime = System.currentTimeMillis();

        dfpFeign.syncOriginDemandForecastLog(scenario.getData(), list, ApiSourceEnum.MES.getCode());
        long dfpCallEndTime = System.currentTimeMillis();

        // 计算总处理时间和DFP调用时间（转换为秒）
        long handleEndTime = System.currentTimeMillis();
        double totalHandleTimeSeconds = (handleEndTime - handleStartTime) / 1000.0;
        double dfpCallTimeSeconds = (dfpCallEndTime - dfpCallStartTime) / 1000.0;
        log.info("MES预测数据处理完成，总处理耗时：{}秒，其中DFP服务调用耗时：{}秒",totalHandleTimeSeconds, dfpCallTimeSeconds);
        this.saveSyncCtrl(apiConfigVO, params, list);
        return null;
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.MES.getCode(), ApiCategoryEnum.MES_FORECAST_DEMAND.getCode());

    }
}
