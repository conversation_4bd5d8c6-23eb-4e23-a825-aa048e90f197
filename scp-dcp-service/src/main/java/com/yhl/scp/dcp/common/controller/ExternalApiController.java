package com.yhl.scp.dcp.common.controller;

import cn.hutool.core.map.MapUtil;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.scp.dcp.apiConfig.service.ApiConfigService;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.apiLog.vo.ExtApiLogVO;
import com.yhl.scp.dcp.externalApi.ExternalApiHandler;
import com.yhl.scp.dcp.externalApi.handler.Handler;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <code>ExternalApiController</code>
 * <p>
 * ExternalApiController
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-09 09:21:05
 */
@Slf4j
@Api(tags = "外部接口控制器")
@RestController
@RequestMapping("externalApi")
public class ExternalApiController extends BaseController {

    @Resource
    private ExternalApiHandler externalApiHandler;

    @Resource
    private ApiConfigService apiConfigService;

    @Resource
    private ExtApiLogService extApiLogService;

    @ApiOperation("调用外部系统接口")
    @PostMapping("call")
    public BaseResponse<String> callExternalApi(@RequestParam("tenantCode") String tenantCode,
                                              @RequestParam("apiSource") String apiSource,
                                              @RequestParam("apiCategory") String apiCategory,
                                              @RequestBody Map<String, Object> params) {
        String command = String.join(Handler.CMD_DELIMITER, tenantCode, apiSource, apiCategory);
        log.info("调用外部系统接口：{}", command);
        // 清空线程数据源
        DynamicDataSourceContextHolder.clearDataSource();
        return externalApiHandler.handle(command, params);
    }

    @ApiOperation("调用外部系统接口（有返回）")
    @PostMapping("callReturn")
    public BaseResponse<String> callExternalApiReturn(@RequestParam("tenantCode") String tenantCode,
                                                      @RequestParam("apiSource") String apiSource,
                                                      @RequestParam("apiCategory") String apiCategory,
                                                      @RequestBody Map<String, Object> params) {
        String command = String.join(Handler.CMD_DELIMITER, tenantCode, apiSource, apiCategory);
        log.info("调用外部系统接口：{}", command);
        // 清空线程数据源
        DynamicDataSourceContextHolder.clearDataSource();
        return externalApiHandler.handle(command, params);
    }

    @ApiOperation("调用对应系统数据")
    @PostMapping("apiConfig")
    public BaseResponse<Object> getApiConfig(@RequestParam("tenantCode") String tenantCode,
                                             @RequestParam("apiSource") String apiSource,
                                             @RequestParam("apiCategory") String apiCategory) {
        Map<String, Object> apiConfigParam = MapUtil.newHashMap();
        apiConfigParam.put("apiSource", apiSource);
        apiConfigParam.put("apiCategory", apiCategory);
        apiConfigParam.put("enabled", YesOrNoEnum.YES.getCode());
        List<ApiConfigVO> apiConfigVOS = apiConfigService.selectByParams(apiConfigParam);
        if (!apiConfigVOS.isEmpty()) {
            return BaseResponse.builder().data(apiConfigVOS.get(0)).success(true).build();

        }
        return BaseResponse.error();
    }

    @ApiOperation("获取接口日志")
    @PostMapping("apiLog")
    public ExtApiLogVO getExtApiLog(@RequestParam(value = "id") String id) {
        return extApiLogService.selectByPrimaryKey(id);
    }

    @ApiOperation("获取所有外部接口标识")
    @PostMapping("allApiLog")
    List<ApiConfigVO> getAllApiConfig(){
        return apiConfigService.selectAll();
    }

    @ApiOperation("根据参数获取外部接口标识")
    @PostMapping("apiMapLog")
    List<ExtApiLogVO> getByParamsExtApiLog(@RequestBody Map<String, Object> params){
        return extApiLogService.selectByParams(params);
    }
}