package com.yhl.scp.mds.feign.common;

import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.FeignConfig;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.common.constants.ServletContextConstants;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.*;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mdm.MdmProductionTime;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.*;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.srm.SrmSupplierPurchase;
import com.yhl.scp.mds.algo.pojo.AlgorithmPolymerization;
import com.yhl.scp.mds.algorithmnew.input.pojo.MdsAlgorithmInputNew;
import com.yhl.scp.mds.algorithmnew.input.pojo.PlanHorizonInputNewData;
import com.yhl.scp.mds.baseResource.dto.PhysicalResourceLogDTO;
import com.yhl.scp.mds.baseResource.dto.StandardResourceDTO;
import com.yhl.scp.mds.bom.dto.ProductAboutBomDTO;
import com.yhl.scp.mds.bom.vo.ProductBomVO;
import com.yhl.scp.mds.bom.vo.ProductBomVersionVO;
import com.yhl.scp.mds.bom.vo.ProductRiskLevelVO;
import com.yhl.scp.mds.box.vo.BoxInfoVO;
import com.yhl.scp.mds.calendar.dto.CalendarForFeedbackDTO;
import com.yhl.scp.mds.customer.vo.CustomerVO;
import com.yhl.scp.mds.extension.calendar.dto.ResourceCalendarDTO;
import com.yhl.scp.mds.extension.calendar.vo.ResourceCalendarVO;
import com.yhl.scp.mds.extension.organization.vo.ProductionOrganizationVO;
import com.yhl.scp.mds.extension.param.vo.PlanCancellationParamVO;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.resource.vo.StandardResourceVO;
import com.yhl.scp.mds.extension.routing.domain.entity.RoutingDO;
import com.yhl.scp.mds.extension.routing.dto.RoutingStepDTO;
import com.yhl.scp.mds.extension.routing.vo.*;
import com.yhl.scp.mds.extension.rule.vo.RuleEncodingsVO;
import com.yhl.scp.mds.extension.supplier.infrastructure.po.SupplierPO;
import com.yhl.scp.mds.extension.supplier.vo.SupplierVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.overdeadlineday.vo.MdsOverDeadlineDaysVO;
import com.yhl.scp.mds.product.dto.ResourceCalendarQueryDTO;
import com.yhl.scp.mds.product.vo.ProductCandidateResourceTimeVO;
import com.yhl.scp.mds.product.vo.ProductDemandSupplyCalculateVO;
import com.yhl.scp.mds.productBox.vo.ProductBoxRelationVO;
import com.yhl.scp.mds.production.vo.NewProductionOrganizationVO;
import com.yhl.scp.mds.production.vo.ProductionOrganizeVO;
import com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO;
import com.yhl.scp.mds.routing.vo.BomTreeNewVO;
import com.yhl.scp.mds.routing.vo.NewRoutingStepInputVO;
import com.yhl.scp.mds.routing.vo.ProductCandidateResourceVO;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mds.substitution.dto.ProductSubstitutionRelationshipDTO;
import com.yhl.scp.mds.substitution.vo.ProductSubstitutionRelationshipVO;
import com.yhl.scp.mds.supplier.vo.SupplierAddressVO;
import com.yhl.scp.mds.supplier.vo.SupplierPurchaseRationMap;
import io.swagger.annotations.ApiOperation;
import org.apache.ibatis.annotations.Param;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @ClassName NewMdsFeign
 * @Description TODO
 * @Date 2024-07-26 14:16:23
 * @Version 1.0
 */
@FeignClient(value = ServletContextConstants.MDS, path = "/", configuration = FeignConfig.class, url = "${mds.feign.url:}")
public interface NewMdsFeign {

    @ApiOperation(value = "库存点物品查询")
    @PostMapping(value = "newProductStockPoint/selectProductStockPointByParams")
    List<NewProductStockPointVO> selectProductStockPointByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);


    @ApiOperation(value = "库存点物品编码查询")
    @PostMapping(value = "newProductStockPoint/selectProductCodeByParams")
    List<String> selectProductCodeByParams(@RequestBody Map<String, Object> params);


    /**
     * 查询物品，dynamicColumns返回字段
     *
     * @param feignDynamicParam 动态字段 + 参数
     * @return NewProductStockPointVO {@link NewProductStockPointVO}
     */
    @ApiOperation(value = "库存点物品查询-返回指定列")
    @PostMapping(value = "newProductStockPoint/selectProductListByParamOnDynamicColumns")
    List<NewProductStockPointVO> selectProductListByParamOnDynamicColumns(
            @RequestHeader("scenario") String scenario,
            @RequestBody FeignDynamicParam feignDynamicParam);

    @ApiOperation(value = "库存点物品查询")
    @PostMapping(value = "newProductStockPoint/selectProductStockPointVOByParams")
    List<NewProductStockPointVO> selectProductStockPointVOByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    @ApiOperation(value = "库存点物品查询By物料列表")
    @PostMapping(value = "newProductStockPoint/selectProductStockPointVOByProductCodes")
    List<NewProductStockPointVO> selectProductStockPointVOByProductCodes(@RequestHeader("scenario") String scenario,
                                                                         @RequestBody List<String> productCodes);


    @ApiOperation(value = "库存点物品查询")
    @PostMapping(value = "newProductStockPoint/selectByVehicleModelCode")
    List<NewProductStockPointVO> selectByVehicleModelCode(@RequestHeader("scenario") String scenario, @RequestBody List<String> vehicleModelCodeList);

    @ApiOperation(value = "库存点物品查询ByVehicleByStock")
    @PostMapping(value = "newProductStockPoint/selectProductCodeLikeByVehicleByStock")
    List<NewProductStockPointVO> selectProductCodeLikeByVehicleByStock(@RequestHeader("scenario") String scenario,
                                                                       @RequestParam("productCode") String productCode, @RequestBody List<String> vehicleModelCodeList,
                                                                       @RequestParam("stockPointCode") String stockPointCode);

    @ApiOperation(value = "根据id查询物品")
    @PostMapping(value = "newProductStockPoint/selectProductStockPointByIds")
    List<NewProductStockPointVO> selectProductStockPointByIds(@RequestHeader("scenario") String scenario, @RequestBody List<String> ids);


    @ApiOperation(value = "批量查询标准资源下物理资源日历集合")
    @PostMapping("resourceCalendar/selectByStandardResourceIds1")
    Map<String, List<ResourceCalendarVO>> selectByStandardResourceIds(@RequestHeader("scenario") String scenario, @RequestBody List<String> standardResourceIds);

    @ApiOperation(value = "根据物理资源ids获取物理资源日历集合")
    @PostMapping("/resourceCalendar/selectByPhysicalResourceIds")
    List<ResourceCalendarVO> selectByPhysicalResourceIds(@RequestHeader("scenario") String scenario, @RequestBody List<String> physicalResourceIds);

    @ApiOperation(value = "根据标准资源ids/物理资源ids/时间范围获取物理资源日历集合")
    @PostMapping("/resourceCalendar/selectByResourceIdsAndDate")
    List<ResourceCalendarVO> selectByResourceIdsAndDate(@RequestHeader("scenario") String scenario, @RequestBody ResourceCalendarQueryDTO calendarQueryDTO);

    @ApiOperation(value = "查询标准资源（资源组）")
    @PostMapping("/standardResource/selectStandardResourceVOS")
    List<StandardResourceVO> selectStandardResourceVOS(@RequestHeader("scenario") String scenario);

    @ApiOperation(value = "根据参数查询标准资源（资源组）")
    @PostMapping("/standardResource/selectStandardResourceVOSByParams")
    List<StandardResourceVO> selectStandardResourceVOSByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    @ApiOperation(value = "根据标准资源（资源组）查询物理资源")
    @PostMapping("/physicalResource/selectPhysicalResourceByStandResourceIds")
    List<PhysicalResourceVO> selectPhysicalResourceByStandResourceIds(@RequestHeader("scenario") String scenario, @RequestBody List<String> standardResourceIds);

    @ApiOperation(value = "根据物理资源ids创建物理资源日历——产能平衡计算造测试数据使用")
    @PostMapping("/resourceCalendar/createByPhysicalResourceIds")
    BaseResponse<Void> createByPhysicalResourceIds(@RequestHeader("scenario") String scenario, @RequestBody List<ResourceCalendarDTO> dtoList);


    @ApiOperation(value = "根据参数查询资源日历集合")
    @PostMapping("/resourceCalendar/selectResourceCalendarByParams")
    List<ResourceCalendarVO> selectResourceCalendarByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    @ApiOperation(value = "根据物品id查询路径")
    @PostMapping("/routing/getRoutingByProductIdList")
    List<RoutingVO> getRoutingByProductIdList(@RequestHeader("scenario") String scenario, @RequestBody List<String> productIdList);

    @ApiOperation(value = "根据路径id查询路径步骤")
    @PostMapping("/routingStep/getRoutingStepByRoutingIds")
    List<RoutingStepVO> getRoutingStepByRoutingIds(@RequestHeader("scenario") String scenario, @RequestBody List<String> routingIds);

    @ApiOperation(value = "根据参数查询库存点数据")
    @PostMapping("/newStockPoint/selectStockPointByParams")
    List<NewStockPointVO> selectStockPointByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    @ApiOperation(value = "查询库存点全部数据")
    @PostMapping("/newStockPoint/selectAll")
    List<NewStockPointVO> selectAllStockPoint(@RequestHeader("scenario") String scenario);

    @ApiOperation(value = "查询物料全部数据")
    @PostMapping("/newProductStockPoint/selectAll")
    List<NewProductStockPointVO> selectAllProductStockPoint(@RequestHeader("scenario") String scenario);

    @ApiOperation(value = "查询最新计划时间")
    @GetMapping("/planningHorizon/selectPlanningHorizon")
    PlanningHorizonVO selectPlanningHorizon(@RequestHeader("scenario") String scenario);

    @ApiOperation(value = "MDS算法输入数据")
    @PostMapping(value = "/algo/getAlgorithmInputNewData")
    MdsAlgorithmInputNew getAlgorithmInputNewData(@RequestHeader("scenario") String scenario, @RequestBody List<String> productCodes);

    @ApiOperation(value = "库存点物品查询2")
    @PostMapping(value = "newProductStockPoint/selectByProductCode2")
    List<NewProductStockPointVO> selectByProductCode(@RequestHeader("scenario") String scenario, @RequestBody List<String> codeList);

    @ApiOperation(value = "同步库存点数据")
    @PostMapping("/productionOrganize/handleData")
    BaseResponse<Void> handleProductionOrganizes(@RequestHeader("scenario") String scenario, @RequestBody List<MesProductionOrganize> o);

    @PostMapping(value = "newProductStockPoint/handleData")
    BaseResponse<Void> handleProductStockPoints(@RequestHeader("scenario") String scenario, @RequestBody List<ErpProduct> o);

    @ApiOperation(value = "EOP/SOP数据同步")
    @PostMapping("newProductStockPoint/handleData1")
    BaseResponse<Void> handleProductStockPoints1(@RequestHeader("scenario") String scenario,
                                                 @RequestBody List<MdmProductionTime> mdmProductionTimes);

    @ApiOperation(value = "库存点物品下拉查询")
    @PostMapping(value = "newProductStockPoint/selectProductStockPointLabelValueByParams")
    List<LabelValue<String>> selectProductStockPointLabelValueByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    @ApiOperation(value = "库存点物品下拉查询")
    @PostMapping(value = "newProductStockPoint/selectProductVehicleModel")
    Map<String, String> selectProductVehicleModel(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> productParams);

    @ApiOperation(value = "根据库存点编码查询物品")
    @PostMapping(value = "newProductStockPoint/selectProductStockPointByStockPointCodes")
    List<NewProductStockPointVO> selectProductStockPointByStockPointCodes(@RequestHeader("scenario") String scenario, @RequestBody List<String> stockPointCodes);

    @ApiOperation(value = "查询用户下有效生产组织数据")
    @PostMapping("productionOrganize/selectProductionOrganizeByParams")
    List<ProductionOrganizeVO> selectProductionOrganizeByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    @ApiOperation(value = "查询库存点下本厂编码数据")
    @PostMapping("newProductStockPoint/selectProductCodeLikeByParams")
    List<LabelValue<String>> selectProductCodeLikeByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    @ApiOperation(value = "查询全部物理资源")
    @PostMapping("physicalResource/selectAllPhysicalResource")
    List<PhysicalResourceVO> selectAllPhysicalResource(@RequestHeader("scenario") String scenario);

    @ApiOperation(value = "根据零件号查询与箱体的关系")
    @PostMapping("productBoxRelation/selectProductBoxRelationVOByProductCodeList")
    List<ProductBoxRelationVO> selectProductBoxRelationVOByProductCodeList(@RequestHeader("scenario") String scenario, @RequestBody List<String> productCodeList);

    @ApiOperation(value = "根据ID查询箱体信息")
    @PostMapping("boxInfo/selectBoxInfoVOByBoxIdList")
    List<BoxInfoVO> selectBoxInfoVOByBoxIdList(@RequestHeader("scenario") String scenario, @RequestBody List<String> boxIdList);

    @ApiOperation(value = "根据ID查询箱体信息")
    @PostMapping("newProductStockPoint/selectByUserId")
    List<NewProductStockPointVO> selectByUserId(@RequestHeader("scenario") String scenario, @RequestParam("userId") String userId, @RequestBody List<String> stockPointCodeList);

    @ApiOperation(value = "查询产品工艺基础属性数据")
    @PostMapping("mdsProductStockPointBase/selectByParams")
    List<MdsProductStockPointBaseVO> selectProductStockPointBaseByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    @ApiOperation(value = "根据工艺路径步骤id查询候选资源")
    @PostMapping("routingStepResource/selectByRoutingStepResourceByRoutingStepIds")
    List<RoutingStepResourceVO> selectRoutingStepResourceByRoutingStepIds(@RequestHeader("scenario") String scenario, @RequestBody List<String> routingStepIds);

    @ApiOperation(value = "根据工艺路径步骤id查询候选资源2")
    @PostMapping("routingStepResource/selectRoutingStepResourceByRoutingStepIds2")
    List<RoutingStepResourceVO> selectRoutingStepResourceByRoutingStepIds2(@RequestBody List<String> routingStepIds);


    @ApiOperation(value = "根据主键查询产品工艺基础属性数据")
    @PostMapping("routingStepResource/selectByRoutingStepResourceByIds")
    List<RoutingStepResourceVO> selectRoutingStepResourceByIds(@RequestHeader("scenario") String scenario, @RequestBody List<String> ids);

    @ApiOperation(value = "根据参数查询产品资源生产数据")
    @PostMapping("productCandidateResourceTime/selectProductCandidateResourceTimeByParams")
    List<ProductCandidateResourceTimeVO> selectProductCandidateResourceTimeByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    @ApiOperation(value = "查询产品资源生产数据-获取设备和工艺关系")
    @GetMapping("productCandidateResourceTime/selectResourceAndOperation")
    List<StandardResourceVO> selectResourceAndOperation();

    @ApiOperation(value = "根据候选资源主键查询对应优先级")
    @PostMapping("productCandidateResourceTime/selectProductCandidateResourceTimeByCandidateResourceId")
    List<ProductCandidateResourceTimeVO> selectProductCandidateResourceTimeByCandidateResourceId(@RequestHeader("scenario") String scenario, @RequestBody @Param("candidateResourceIdList") List<String> candidateResourceIdList);

    @ApiOperation(value = "根据工序和生效时间查询产品资源生产关系（仅用于工序库存与日计划发货量查找资源）")
    @PostMapping("productCandidateResourceTime/selectProductCandidateResourceTimeByOperationCodeAndEffectiveTime")
    List<ProductCandidateResourceTimeVO> selectProductCandidateResourceTimeByOperationCodeAndEffectiveTime(@RequestHeader("scenario") String scenario,
                                                                                                           @RequestParam("operationCode") String operationCode, @RequestParam("effectiveTime") String effectiveTime);


    @ApiOperation(value = "删除时间范围外的产品资源生产关系数据")
    @PostMapping("productCandidateResourceTime/deleteProductCandidateResourceTimeByStartAndEndTime")
    void deleteProductCandidateResourceTimeByStartAndEndTime(@RequestParam("startTime") Date startTime, @RequestParam("endTime") Date endTime);

    @ApiOperation(value = "保存产品资源生产关系数据")
    @PostMapping("productCandidateResourceTime/saveProductCandidateResourceTimeVO")
    void saveProductCandidateResourceTimeVO(@RequestHeader("scenario") String scenario, @RequestBody List<ProductCandidateResourceTimeVO> vos);


    @ApiOperation(value = "根据id查询生产组织")
    @PostMapping("productionOrganization/selectOrganizationByPrimaryIds")
    List<ProductionOrganizationVO> selectOrganizationByPrimaryIds(@RequestHeader("scenario") String scenario, @RequestBody List<String> ids);

    @ApiOperation(value = "根据工艺路径步骤id查询输入物品")
    @PostMapping("routingStepInput/selectInputByRoutingStepIds")
    List<RoutingStepInputVO> selectInputByRoutingStepIds(@RequestHeader("scenario") String scenario, @RequestBody List<String> routingStepIds);

    @ApiOperation(value = "根据工艺路径步骤id查询输出物品")
    @PostMapping("routingStepOutput/selectOutputByRoutingStepIds")
    List<RoutingStepOutputVO> selectOutputByRoutingStepIds(@RequestHeader("scenario") String scenario, @RequestBody List<String> routingStepIds);

    @ApiOperation(value = "查询工艺路径输入物品")
    @PostMapping("routingStepInput/selectByParamsRoutingStepInput")
    List<RoutingStepInputVO> selectByParamsRoutingStepInput(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    @ApiOperation(value = "根据输入物品id,产品类型查询顶层物品")
    @PostMapping("bom/selectRoutingByInputProductIds")
    List<BomTreeNewVO> selectRoutingByInputProductIds(@RequestHeader("scenario") String scenario, @RequestBody BomTreeNewVO vo);

    @ApiOperation(value = "根据ids查询物理资源集合")
    @PostMapping("physicalResource/selectByPhysicalIds")
    List<PhysicalResourceVO> selectByPhysicalIds(@RequestHeader("scenario") String scenario, @RequestBody List<String> ids);

    @ApiOperation(value = "查询箱子全部数据")
    @PostMapping("box/productBoxRelationAll")
    List<ProductBoxRelationVO> selectAllBox(@RequestHeader("scenario") String scenario);

    @ApiOperation(value = "根据路径步骤ids获取路径步骤")
    @PostMapping("routingStep/getRoutingStepByRoutingStepIds")
    List<RoutingStepVO> getRoutingStepByRoutingStepIds(@RequestHeader("scenario") String scenario, @RequestBody List<String> routingStepId);

    @ApiOperation(value = "查询标准工艺all")
    @PostMapping("standardRouting/selectAll")
    List<StandardStepVO> selectStandardStepAll(@RequestHeader("scenario") String scenario);

    @ApiOperation(value = "查询工艺路径all")
    @PostMapping("routingStep/selectAllRoutingStep")
    List<RoutingStepVO> selectAllRoutingStep(@RequestHeader("scenario") String scenario);


    @ApiOperation(value = "查询工艺路径下所有的供料信息（委外转产材料供应计算）")
    @PostMapping("routingStep/selectRoutingStepInputForSupplyCalculate")
    ProductDemandSupplyCalculateVO selectRoutingStepInputForSupplyCalculate(@RequestHeader("scenario") String scenario,
                                                                            @RequestParam("productCode") String productCode,
                                                                            @RequestParam("productId") String productId,
                                                                            @RequestBody List<Integer> sequenceNos);


    @ApiOperation(value = "查询工艺路径步骤输入物品Ids")
    @PostMapping("routingStep/selectAllProductIdsFromBom")
    List<String> selectAllProductIdsFromBom();

    @ApiOperation(value = "查询物料两个数据")
    @PostMapping("newProductStockPoint/selectTwoProductStockPoint")
    List<NewProductStockPointVO> selectTwoProductStockPoint(@RequestHeader("scenario") String scenario);

    @ApiOperation(value = "同步箱体信息")
    @PostMapping("foundationBoxInfo/handleData")
    BaseResponse<Void> handleFoundationBoxInfo(@RequestHeader("scenario") String scenario,
                                               @RequestBody List<MesBoxInfo> o, @RequestParam("ebsOuId") String ebsOuId);

    @ApiOperation(value = "根据ids查询工艺路径")
    @PostMapping("routing/selectRoutingByRoutingIds")
    List<RoutingVO> selectRoutingByRoutingIds(@RequestBody List<String> routingIds);

    @ApiOperation(value = "根据场景、ids查询工艺路径")
    @PostMapping("routing/selectRoutingByScenarioRoutingIds")
    List<RoutingVO> selectRoutingByScenarioRoutingIds(@RequestHeader("scenario") String scenario, @RequestBody List<String> routingIds);

    @ApiOperation(value = "查询工艺路径数据")
    @PostMapping("routing/selectRoutingByParams")
    List<RoutingVO> selectRoutingByParams(@RequestHeader("scenario") String scenario,
                                          @RequestBody Map<String, Object> params);

    @GetMapping("routing/getAllRouting")
    List<RoutingVO> getAllRouting();

    @GetMapping("routing/getAllRoutingStep")
    List<RoutingStepVO> getAllRoutingStep();

    @GetMapping("routing/getAllRoutingStepInput")
    List<RoutingStepInputVO> getAllRoutingStepInput();

    @GetMapping("routing/getAllRoutingStepOutput")
    List<RoutingStepOutputVO> getAllRoutingStepOutput();

    @ApiOperation(value = "查询全量生产组织")
    @GetMapping("productionOrganization/selectAll")
    List<ProductionOrganizationVO> selectProductionOrganizeAll();

    @ApiOperation(value = "物料替代关系")
    @GetMapping("productSubstitutionRelationshipVO/getAll")
    List<ProductSubstitutionRelationshipVO> getProductSubstitutionRelationship();

    @ApiOperation(value = "物料替代关系")
    @PostMapping("productSubstitutionRelationshipVO/getProductSubstitutionRelationshipByParams")
    List<ProductSubstitutionRelationshipVO> getProductSubstitutionRelationshipVOByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    @ApiOperation(value = "根据箱体编码查询箱体信息")
    @PostMapping("boxInfo/selectPiecePerBoxByBoxCode")
    List<BoxInfoVO> selectPiecePerBoxByBoxCode(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    @ApiOperation(value = "查询生产组织")
    @PostMapping("newProductionOrganization/selectNewProductionOrganizationVO")
    List<NewProductionOrganizationVO> selectNewProductionOrganizationVO(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> objectObjectHashMap);

    @ApiOperation(value = "生产异常反馈维护设备异常日历")
    @PostMapping("calendarRule/doResourceCalendarForFeedback")
    List<String> doResourceCalendarForFeedback(@RequestHeader("scenario") String scenario, @RequestBody CalendarForFeedbackDTO calendarForFeedbackDTO);

    @ApiOperation(value = "镀膜维保生成设备异常日历")
    @PostMapping("calendarRule/coatingMaintenanceSetting")
    List<String> doResourceCalendarForCoatingMaintenanceSetting(@RequestHeader("scenario") String scenario, @RequestBody CalendarForFeedbackDTO calendarForFeedbackDTO);

    @ApiOperation(value = "同步获取bom相关数据")
    @PostMapping("productAboutBom/syncProductAboutBomData")
    BaseResponse<Void> syncProductAboutBomData(@RequestHeader("scenario") String scenario, @RequestBody List<ProductAboutBomDTO> list);

    @ApiOperation(value = "同步箱体信息")
    @PostMapping("productBoxRelation/handleData")
    BaseResponse<Void> handleProductBoxRelation(@RequestHeader("scenario") String scenario,
                                                @RequestBody List<MesProductBoxRelation> o);

    @ApiOperation(value = "同步获取生产路径步骤输入物品")
    @PostMapping("productSubstitutionRelationship/syncProductSubstitutionData")
    BaseResponse<Void> syncProductSubstitutionData(@RequestHeader("scenario") String scenario, @RequestBody List<ProductSubstitutionRelationshipDTO> list, @RequestHeader("organizationCodes") String organizationCodes);

    @PostMapping(value = "newProductStockPoint/syncProductStockPoints")
    BaseResponse<Void> syncProductStockPoints(@RequestHeader("scenario") String scenario, @RequestParam("tenantId") String tenantId);

    @ApiOperation(value = "定时任务同步获取箱体信息")
    @PostMapping(value = "boxInfo/synBoxInfo")
    BaseResponse<Void> synBoxInfo(@RequestHeader("scenario") String scenario,
                                  @RequestBody Map<String, Object> params);

    @ApiOperation(value = "定时任务同步获取库存点")
    @PostMapping(value = "stockPoint/synStockPoint")
    BaseResponse<Void> synStockPoint(@RequestHeader("scenario") String scenario, @RequestParam("tenantId") String tenantId);

    @ApiOperation(value = "定时任务同步成品箱关系")
    @PostMapping(value = "productBoxRelation/synProductBoxRelation")
    BaseResponse<Void> synProductBoxRelation(@RequestHeader("scenario") String scenario, @RequestParam("tenantId") String tenantId);

    @ApiOperation(value = "定时任务同步MDM中EOP/SOP")
    @PostMapping(value = "newProductStockPoint/syncProductionTime")
    BaseResponse<Void> syncProductionTime(@RequestHeader("scenario") String scenario,
                                          @RequestParam("tenantId") String tenantId);

    @ApiOperation(value = "同步产品工艺基础数据")
    @PostMapping("productStockPointBase/syncProductStockPointBase")
    BaseResponse<Void> syncProductStockPointBase(@RequestHeader("scenario") String scenario,
                                                 @RequestBody List<ErpProductStockPointBase> productStockPointBases);

    @ApiOperation(value = "查询销售组织")
    @PostMapping(value = "stockPoint/selectSaleOrganize")
    List<NewStockPointVO> selectSaleOrganize(@RequestHeader("scenario") String scenario);

    @ApiOperation(value = "查询库存点")
    @PostMapping(value = "newStockPoint/selectNewStockPointVOByParams")
    List<NewStockPointVO> selectNewStockPointVOByParams(@RequestHeader("scenario") String scenario,
                                                        @RequestBody Map<String, Object> params);

    @ApiOperation(value = "查询物料所有的车型")
    @PostMapping("newProductStockPoint/selectAllVehicleModel")
    List<String> selectAllVehicleModel(@RequestHeader("scenario") String scenario);

    @ApiOperation(value = "根据物料ID,去BOM中找到对应的成品数据信息(递归)")
    @PostMapping("routingStepInput/selectFinishedProductByInputProductId")
    List<NewProductStockPointVO> selectFinishedProductByInputProductId(@RequestHeader("scenario") String scenario, @RequestParam("tenantId") String inputProductId);

    @ApiOperation(value = "同步生产节拍")
    @PostMapping("routingStepResource/handleData")
    BaseResponse<Void> handleRoutingStepResource(@RequestHeader("scenario") String scenario,
                                                 @RequestBody List<MesMoldChangeTime> o);

    @ApiOperation(value = "供应商查询")
    @PostMapping(value = "supplier/selectSupplierByParams")
    List<SupplierVO> selectSupplierByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    @ApiOperation(value = "供应商查询PO")
    @PostMapping(value = "supplierPo/selectSupplierPoByParams")
    List<SupplierPO> selectSupplierPoByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String,
            Object> params);

    @ApiOperation(value = "供应商查询PO")
    @PostMapping(value = "supplierByPrimaryKey/selectSupplierByPrimaryKey")
    SupplierPO selectSupplierByPrimaryKey(@RequestHeader("scenario") String scenario, @RequestParam("supplierId") String supplierId);

    @ApiOperation(value = "供应商根据主键查询")
    @PostMapping(value = "supplier/selectByPrimaryKeys")
    List<SupplierVO> selectSupplierByPrimaryKeys(@RequestParam("supplierIds") List<String> supplierIds);

    @ApiOperation(value = "供应商单个更新")
    @PostMapping("supplierUpdate/update")
    BaseResponse<Void> supplierUpdate(@RequestHeader("scenario") String scenario,
                                      @RequestBody SupplierPO o);


    @ApiOperation(value = "冗余供应商")
    @PostMapping("supplierAddSupplierColumn/addSupplierColumn")
    BaseResponse<Void> supplierAddSupplierColumn(@RequestHeader("scenario") String scenario,
                                                 @RequestBody SupplierPurchaseRationMap map);

    @PostMapping(value = "supplier/handleData")
    BaseResponse<Void> handleSupplier(@RequestHeader("scenario") String scenario, @RequestBody List<ErpSupplier> erpSuppliers);


    @ApiOperation(value = "物料查询根据装车位置")
    @PostMapping("newProductStockPoint/selectByLoadPosition")
    List<NewProductStockPointVO> selectByLoadPosition(@RequestHeader("scenario") String scenario, @RequestBody List<String> accessPositionList);

    @ApiOperation(value = "产品与成品箱关系查询根据物料Id")
    @PostMapping("productBoxRelation/selectProductBoxRelationByProductStockPointId")
    List<ProductBoxRelationVO> selectProductBoxRelationByProductStockPointId(@RequestHeader("scenario") String scenario, @RequestBody List<String> productStockPointIdList);

    @PostMapping(value = "productQualifiedSupplier/handleData")
    BaseResponse<Void> handleProductQualifiedSupplier(@RequestHeader("scenario") String scenario, @RequestBody List<MesProductQualifiedSupplier> o);

    @PostMapping(value = "productRoutings/handleData")
    BaseResponse<Void> handleProductRoutings(@RequestHeader("scenario") String scenario, @RequestBody List<ErpProductRouting> o);

    @PostMapping(value = "deleteGroups/handleData")
    BaseResponse<Void> handleDeleteGroups(@RequestHeader("scenario") String scenario, @RequestBody List<ErpDeleteGroup> o);

    @PostMapping(value = "opYields/handleData")
    BaseResponse<Void> handleOpYield(@RequestHeader("scenario") String scenario, @RequestBody List<MesOpYield> mesOpYields);

    @ApiOperation(value = "根据参数组装RoutingDO")
    @PostMapping("mdsWorkOrderSyncRouting/getRoutingDOByParams")
    List<RoutingDO> getRoutingDOByParams(@RequestBody Map<String, Object> params);

    @ApiOperation(value = "更新流水号")
    @PostMapping("/rule/selectiveUpdateRuleEncodings")
    void selectiveUpdateRuleEncodings(@RequestBody RuleEncodingsVO ruleEncodingsVO);

    @PostMapping(value = "api/productStockPoint/loadingDemandSpecify")
    List<NewProductStockPointVO> selectProduct4LoadingDemandSubmission(@RequestHeader("scenario") String scenario);

    @PostMapping(value = "planHorizonSequence/selectTimeSequence")
    List<PlanHorizonInputNewData> selectTimeSequence();

    @GetMapping(value = "/api/rule/getRuleEncoding")
    List<RuleEncodingsVO> getRuleEncoding();

    @ApiOperation(value = "查询物品表车型")
    @PostMapping(value = "newProductStockPoint/selectVehiclesByProductCode")
    List<String> selectVehiclesByProductCode(@RequestHeader("scenario") String scenario,
                                             @RequestBody List<String> codeList);

    @ApiOperation(value = "查询取消计划参数")
    @PostMapping(value = "/planCancellationParam/selectCancellationParam")
    List<PlanCancellationParamVO> selectCancellationParam(@RequestBody Map<String, Object> params);

    @ApiOperation(value = "同步MES资源组数据")
    @PostMapping("standardResource/syncStandardResourceData")
    BaseResponse<Void> syncStandardResourceData(@RequestHeader("scenario") String scenario,
                                                @RequestBody List<StandardResourceDTO> list);

    @ApiOperation(value = "补齐资源组-标准资源字段")
    @PostMapping("standardResource/completeStandardResourceData")
    BaseResponse<Void> completeStandardResourceData(@RequestHeader("scenario") String scenario);

    @ApiOperation(value = "同步MES主资源")
    @PostMapping("physicalResource/syncPhysicalResourceData")
    BaseResponse<Void> syncPhysicalResourceData(@RequestHeader("scenario") String scenario,
                                                @RequestBody List<PhysicalResourceLogDTO> list);

    @ApiOperation(value = "补齐主资源-物理资源字段")
    @PostMapping("physicalResource/completePhysicalResourceData")
    BaseResponse<Void> completePhysicalResourceData(@RequestHeader("scenario") String scenario);

    @ApiOperation(value = "同步系统模具工装族与工装编号关系")
    @PostMapping("MoldTooling/handleData")
    BaseResponse<Void> handleMoldTooling(@RequestHeader("scenario") String scenario,
                                         @RequestBody List<MesMoldTooling> o);

    @ApiOperation(value = "同步模具工装族表数据")
    @PostMapping("MoldToolingGroup/handleData")
    BaseResponse<Void> handleMoldToolingGroup(@RequestHeader("scenario") String scenario,
                                              @RequestBody List<MesMoldToolingGroup> o);

    @ApiOperation(value = "同步系统模具工装族与目录号关系数据")
    @PostMapping("MoldToolingGroupDir/handleData")
    BaseResponse<Void> handleMoldToolingGroupDir(@RequestHeader("scenario") String scenario,
                                                 @RequestBody List<MesMoldToolingGroupDir> o);

    @ApiOperation(value = "同步客户表")
    @PostMapping("customer/handleData")
    BaseResponse<Void> handleCustomer(@RequestHeader("scenario") String scenario,
                                      @RequestBody List<ErpCustomer> o);


    @ApiOperation(value = "查询物品候选资源信息")
    @PostMapping("productCandidateResource/selectByParams")
    List<ProductCandidateResourceVO> selectProductCandidateResourceByParams(@RequestHeader("scenario") String scenario,
                                                                            @RequestBody Map<String, Object> params);

    @ApiOperation(value = "查询物品候选资源信息（视图）")
    @PostMapping("productCandidateResource/selectProductCandidateResourceVOByParams")
    List<ProductCandidateResourceVO> selectProductCandidateResourceVOByParams(@RequestBody Map<String, Object> params);

    @ApiOperation(value = "查询模具限制数量")
    @PostMapping("productCandidateResource/selectMoldQuantityLimit")
    List<ProductCandidateResourceVO> selectMoldQuantityLimit();


    @ApiOperation(value = "获取成品对应的物料风险等级")
    @PostMapping(value = "newRouting/selectProductRiskLevelMap")
    Map<String, List<ProductRiskLevelVO>> selectProductRiskLevelMap(@RequestHeader("scenario") String scenario,
                                                                    @RequestBody List<String> productCodeList);

    @ApiOperation(value = "查询库龄超期界定天数")
    @PostMapping(value = "mdsOverDeadlineDays/selectOverDeadlineDaysByParams")
    List<MdsOverDeadlineDaysVO> selectOverDeadlineDaysByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    @ApiOperation(value = "Srm同步数据下的库存点查询")
    @PostMapping(value = "newProductStockPoint/selectByProductCodeStockPointCode")
    List<NewProductStockPointVO> selectSrmByStockCodeAndProductCode(@RequestHeader("scenario") String scenario,
                                                                    @RequestBody List<SrmSupplierPurchase> value);


    @ApiOperation(value = "获取客户数据")
    @PostMapping(value = "customer/selectCustomerByParams1")
    List<CustomerVO> selectCustomerByParams1(@RequestHeader("scenario") String scenario,
                                             @RequestBody HashMap<String, Object> map);

    @ApiOperation(value = "查询客户表数据")
    @PostMapping(value = "customer/selectCustomerByParams")
    List<CustomerVO> selectCustomerByParams(@RequestBody HashMap<String, Object> customerMap);

    @ApiOperation(value = "查询物料列表数据")
    @PostMapping(value = "newProductStockPoint/selectProductStockPointByProducts")
    List<NewProductStockPointVO> selectProductStockPointByProducts(@RequestBody List<String> productCodes);

    @ApiOperation(value = "查询库存表数据")
    @PostMapping(value = "newStockPoint/selectStockPointBySubCategory")
    List<NewStockPointVO> selectStockPointBySubCategory(@RequestBody HashMap<String, Object> subCategoryMap);

    @ApiOperation(value = "查询成品下面的半品物料数据")
    @PostMapping(value = "bomTree/selectBomRoutingStepInputByParams")
    List<BomRoutingStepInputVO> selectBomRoutingStepInputByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    @ApiOperation(value = "产品与成品箱关系查询根据物料Id")
    @PostMapping("productBoxRelation/selectByProductStockPointIds")
    List<ProductBoxRelationVO> selectProductBoxRelationByProductStockPointIds(@RequestHeader("scenario") String scenario, @RequestBody List<String> productStockPointIdList);

    @ApiOperation(value = "物理资源查询")
    @PostMapping("physicalResource/selectAllPhysicalResourcesVOByParams")
    List<PhysicalResourceVO> selectPhysicalResourceByParams(@RequestHeader("scenario") String scenario,
                                                            @RequestBody Map<String, Object> params);

    @ApiOperation(value = "物品BOM查询")
    @PostMapping("mdsProductBom/selectProductBomVOByParams")
    List<ProductBomVO> selectProductBomVOByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    @ApiOperation(value = "物理资源查询")
    @PostMapping("mdsProductBomVersion/selectProductBomVersionVOByParams")
    List<ProductBomVersionVO> selectProductBomVersionVOByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);


    @ApiOperation(value = "获取产品与产线组的对应关系")
    @PostMapping("mdsProductStockPointBase/selectByLineGroup")
    Map<String, String> selectByLineGroup(@RequestBody List<String> lineGroupList);

    @ApiOperation(value = "根据登陆人id获取负责物料编码集合")
    @GetMapping("productBase/getPlannerProduct")
    List<String> getPlannerProduct(@RequestHeader("scenario") String scenario,
                                   @RequestParam(value = "userId") String userId);

    @ApiOperation(value = "根据指定的产线组获取负责物料编码集合")
    @GetMapping("productBase/getPlannerProductByLineGroups")
    List<String> selectPermissionsByLineGroupList(@RequestHeader("scenario") String scenario,
                                                  @RequestParam(value = "lineGroups") List<String> lineGroups);

    @ApiOperation(value = "根据登陆人id获取所属产线组")
    @GetMapping("productBase/getPlannerStandardResource")
    List<String> getPlannerStandardResource(@RequestParam(value = "userId") String userId);

    @ApiOperation(value = "根据登陆人id获取所属产线组下的物理资源")
    @GetMapping("productBase/getPlannerPhysicalResource")
    List<PhysicalResourceVO> getPlannerPhysicalResource(@RequestParam(value = "userId") String userId);

    @ApiOperation(value = "修改路径步骤")
    @PostMapping("routingStep/doUpdateRoutingStep")
    void doUpdateRoutingStep(@RequestBody List<RoutingStepDTO> routingStepVOList);

    @ApiOperation(value = "修改路径步骤")
    @PostMapping("newProductStockPoint/getYpProductCodes")
    List<String> getYpProductCodes(@RequestHeader("scenario") String scenario);

    @ApiOperation(value = "原片本厂编码车型信息")
    @PostMapping("newProductStockPoint/getYpFactoryCode")
    List<NewProductStockPointVO> getYpFactoryCode(@RequestHeader("scenario") String scenario);

    @ApiOperation(value = "获取MPS算法输入聚合对象")
    @PostMapping("algorithmPolymerization/getAlgorithmPolymerization")
    AlgorithmPolymerization getAlgorithmPolymerization();


    @ApiOperation(value = "供应商地点查询")
    @PostMapping(value = "supplierAddress/selectSupplierAddressByParams")
    List<SupplierAddressVO> selectSupplierAddressByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    /**
     * 查询工艺路径，dynamicColumns返回字段
     *
     * @param feignDynamicParam 动态字段 + 参数
     * @return RoutingVO {@link RoutingVO}
     */
    @ApiOperation(value = "工艺路径查询-返回指定列")
    @PostMapping(value = "routing/selectRoutingByParamOnDynamicColumns")
    List<RoutingVO> selectRoutingByParamOnDynamicColumns(
            @RequestHeader("scenario") String scenario,
            @RequestBody FeignDynamicParam feignDynamicParam
    );

    /**
     * 查询工艺路径步骤，dynamicColumns返回字段
     *
     * @param feignDynamicParam 动态字段 + 参数
     * @return RoutingStepVO {@link RoutingStepVO}
     */
    @ApiOperation(value = "工艺路径查询-返回指定列")
    @PostMapping(value = "routingStep/selectRoutingStepByParamOnDynamicColumns")
    List<RoutingStepVO> selectRoutingStepByParamOnDynamicColumns(@RequestHeader("scenario") String scenario,
                                                                 @RequestBody FeignDynamicParam feignDynamicParam);

    /**
     * 查询工艺路径步骤输入，dynamicColumns返回字段
     *
     * @param feignDynamicParam 动态字段 + 参数
     * @return RoutingStepInputVO {@link RoutingStepInputVO}
     */
    @ApiOperation(value = "工艺路径查询-返回指定列")
    @PostMapping(value = "routingStepInput/selectRoutingStepInputByParamOnDynamicColumns")
    List<RoutingStepInputVO> selectRoutingStepInputByParamOnDynamicColumns(@RequestHeader("scenario") String scenario,
                                                                           @RequestBody FeignDynamicParam feignDynamicParam);

    @ApiOperation(value = "查询输入物品")
    @PostMapping(value = "routingStepInput/selectNewRoutingStepInputVO")
    List<NewRoutingStepInputVO> selectNewRoutingStepInputVO(@RequestBody Map<String, Object> params);

    @ApiOperation(value = "查询物料关键工序")
    @PostMapping("newRoutingStepInput/selectFormingProcess")
    List<NewRoutingStepInputVO> selectFormingProcess(@RequestHeader("scenario") String scenario, @RequestBody List<String> inputProductIds);


    @ApiOperation(value = "查询物料关键工序")
    @PostMapping("newRoutingStepInput/selectDirectFormingProcess")
    List<StandardStepVO> selectDirectFormingProcess(@RequestHeader("scenario") String scenario, @RequestBody String inputProductId);

    @ApiOperation(value = "校验产品工艺路径")
    @PostMapping("newProductStockPoint/checkPorductRouting")
    Map<String, String> checkPorductRouting(@RequestHeader("scenario") String scenario, @RequestBody List<String> productIds);

    @ApiOperation(value = "根据登陆人id获取所属产线组下的物理资源")
    @GetMapping("productBase/getPlannerProductIdList")
    List<String> getPlannerProductIdList(@RequestParam(value = "creator") String creator);

    @ApiOperation(value = "根据物理资源id获取标准资源")
    @GetMapping("standardResource/getStandResourceByPhysicalId")
    StandardResourceVO getStandResourceByPhysicalId(@RequestHeader("scenario") String scenario, @RequestParam(value = "physicalResourceId") String physicalResourceId);

    @ApiOperation(value = "根据物理资源编码获取标准资源")
    @PostMapping("standardResource/selectStandardByPhysicalCodes")
    List<StandardResourceVO> selectStandardByPhysicalCodes(@RequestHeader("scenario") String scenario, @RequestBody List<String> collect);

    @PostMapping(value = "curingTime/handleData")
    BaseResponse<Void> handleCuringTime(@RequestHeader("scenario") String scenario, @RequestBody List<MesCuringTime> mesCuringTimes);


    @PostMapping(value = "finishedProductDelivery/handleData")
    BaseResponse<Void> handleFinishedProductDelivery(@RequestHeader("scenario") String scenario, @RequestBody List<MesFinishedProductDelivery> mesFinishedProductDeliveries);

    @ApiOperation(value = "根据登陆人id获取所属产线组下的物理资源")
    @PostMapping("productBase/getPlannerProductIdByLineGroup")
    List<String> getPlannerProductIdByLineGroup(@RequestBody List<String> lineGroupList);

    @PostMapping(value = "routing/selectFg2SaMap")
    Map<String, List<String>> selectFg2SaMap(@RequestHeader("scenario") String scenario,
                                             @RequestBody List<String> fgCodes);


    @ApiOperation(value = "查询车型最大EOP最小SOP")
    @PostMapping(value = "newProductStockPoint/selectMaxEopMinSop")
    Map<String, NewProductStockPointVO> selectMaxEopMinSop(@RequestBody Map<String, Object> params);

    @ApiOperation(value = "查询产品工艺基础数据表获取信息")
    @PostMapping("/selectProductStockPointBaseInfo")
    List<MdsProductStockPointBaseVO> selectMdsProductStockPointBaseInfo(@RequestBody List<String> productCodes);

    @ApiOperation(value = "查询获取主工序信息")
    @PostMapping("/mainProcess")
    Map<String, String> selectMainProcessByProductCodes(@RequestBody List<String> productCodes);

    @ApiOperation(value = "更新物料计划员及物料状态")
    @PostMapping(value = "newProductStockPoint/updateOrderPlanner")
    void updateOrderPlanner(@RequestHeader("scenario") String scenario,
                            @RequestBody List<String> productCodes, @RequestParam("orderPlanner") String orderPlanner);

    @ApiOperation(value = "库存点物品编码查询")
    @PostMapping(value = "newProductStockPoint/selectDistinctProductCodesByParams")
    List<String> selectDistinctProductCodesByParams(@RequestBody Map<String, Object> params);

    @ApiOperation(value = "获取物理资源")
    @PostMapping("/selectVOByPhysicalIds")
    List<PhysicalResourceVO> selectVOByPhysicalIds(@RequestBody List<String> resourceIds);

    @ApiOperation(value = "查询模具工装族")
    @PostMapping(value = "selectMoldToolingGroup")
    List<LabelValue<String>> selectMoldToolingGroupByParams(@RequestHeader("scenario") String scenario,
                                                            @RequestParam(value = "organizationId", required = false) String organizationId,
                                                            @RequestParam(value = "standardResourceCode", required = false) String standardResourceCode);

    @ApiOperation(value = "查询模具工装小类")
    @PostMapping(value = "selectMoldToolingByStandResourceId")
    List<LabelValue<String>> selectMoldToolingByStandResourceId(@RequestHeader("scenario") String scenario,
                                                                @RequestParam(value = "standardResourceId", required = false) String standardResourceId);
}

