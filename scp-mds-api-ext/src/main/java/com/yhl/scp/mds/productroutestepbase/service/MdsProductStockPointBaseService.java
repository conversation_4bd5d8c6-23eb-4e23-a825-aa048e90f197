package com.yhl.scp.mds.productroutestepbase.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpProductStockPointBase;
import com.yhl.scp.mds.productroutestepbase.dto.MdsProductStockPointBaseDTO;
import com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <code>MdsProductStockPointBaseService</code>
 * <p>
 * 产品工艺基础数据应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-19 11:28:43
 */
public interface MdsProductStockPointBaseService extends BaseService<MdsProductStockPointBaseDTO, MdsProductStockPointBaseVO> {

    /**
     * 查询所有
     *
     * @return list {@link MdsProductStockPointBaseVO}
     */
    List<MdsProductStockPointBaseVO> selectAll();

    /**
     * 导出
     *
     * @param response
     */
    void export(HttpServletResponse response);

    BaseResponse<Void> syncProductStockPointBase(String scenario,
                                                 List<ErpProductStockPointBase> productStockPointBases);

    List<MdsProductStockPointBaseVO> selectByProduct(Map<String, Object> params);

    BaseResponse<Void> syncData(String tenantId);

    List<String> selectAllProductCodes();

    void updateItemFlagByProductCode(String productCode, String newItemFlag);

    BaseResponse<MdsProductStockPointBaseVO> processItemFlags(List<String> productCodes);

    void updateLineGroupByProductCode(String key, String value);

    BaseResponse<MdsProductStockPointBaseVO> updateProductLineGroup(List<String> productCodes);

    void updateStandardResourceIdByProductCode(String key, String value);

    BaseResponse<MdsProductStockPointBaseVO> updateStandardResourceForToolCategory(String scenario, List<String> productCodes);

    Map<String, String> selectByLineGroup(List<String> lineGroupList);

    void processSyncedData(String scenario);

    List<String> getRecentlyModifiedProductCodes();

    BaseResponse<Void> processSelectedProducts(List<String> selectedProductCodes);

    List<String> selectProductCodesWithEmptyLineGroup();

    Map<String, List<String>> selectFg2SaList(List<String> fgCodes);

    List<MdsProductStockPointBaseVO> selectByProductCodes(List<String> productCodes);

    void syncPartNum(List<String> productCodes);

    List<String> getModifiedProductCodes();

    void syncPartNumber();
}