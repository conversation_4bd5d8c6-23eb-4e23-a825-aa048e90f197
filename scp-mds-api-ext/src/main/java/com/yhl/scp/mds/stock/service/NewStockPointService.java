package com.yhl.scp.mds.stock.service;

import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mds.common.dto.RemoveVersionDTO;
import com.yhl.scp.mds.stock.dto.NewStockPointDTO;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <code>StockPointService</code>
 * <p>
 * 库存点应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-22 15:10:24
 */
public interface NewStockPointService extends BaseService<NewStockPointDTO, NewStockPointVO> {

    /**
     * 查询所有
     *
     * @return list {@link NewStockPointVO}
     */
    List<NewStockPointVO> selectAll();

    int deleteBatchVersion(List<RemoveVersionDTO> removeVersionDTOS);

    BaseResponse<Void> syncStockPoints(String tenantId);

    List<NewStockPointVO> selectSaleOrgaByOrganizeType();

    /**
     * 查询所有的车型
     * @return
     */
	List<String> selectAllVehicleModel();

	/**
	 * 通过组织类型查询库存点信息
	 * @param organizeTypes
	 * @return
	 */
	List<NewStockPointVO> selectByOrganizeTypes(List<String> organizeTypes);

    List<LabelValue<String>> pointDown(String stockPointCode);

    /**
     * 导出模板
     */
    void exportTemplate(HttpServletResponse response);

    List<LabelValue<String>> getInterfaceFlag();

    List<LabelValue<String>> stockPointDownOnly();

    List<LabelValue<String>> stockPointNameDropDown(String stockPointName);

}
