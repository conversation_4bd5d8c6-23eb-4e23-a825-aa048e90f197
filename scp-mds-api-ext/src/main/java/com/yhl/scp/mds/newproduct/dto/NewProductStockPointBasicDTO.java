package com.yhl.scp.mds.newproduct.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yhl.platform.common.ddd.BaseDTO;
import com.yhl.scp.common.excel.ExcelPropertyCheck;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@ApiModel(value = "新-物品DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class NewProductStockPointBasicDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -50018535555106026L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 库存点编码
     */
    @ApiModelProperty(value = "库存点编码")
    @ExcelProperty(value = "库存点编码*")
    @ExcelPropertyCheck(required = true)
    private String stockPointCode;
    /**
     * 物品代码
     */
    @ApiModelProperty(value = "物品代码")
    @ExcelProperty(value = "物品代码*")
    @ExcelPropertyCheck(required = true)
    private String productCode;
    /**
     * 物品名称
     */
    @ApiModelProperty(value = "物品名称")
    @ExcelProperty(value = "物品名称*")
    @ExcelPropertyCheck(required = true)
    private String productName;
    /**
     * 物品类型
     */
    @ApiModelProperty(value = "物品类型")
    @ExcelProperty(value = "物品类型")
    private String productType;
    /**
     * 物品分类
     */
    @ApiModelProperty(value = "物品分类")
    @ExcelProperty(value = "物品分类")
    private String productClassify;
    /**
     * 分类说明
     */
    @ApiModelProperty(value = "分类说明")
    @ExcelProperty(value = "分类说明")
    private String classifyDesc;
    /**
     * 车型编码
     */
    @ApiModelProperty(value = "车型编码")
    @ExcelProperty(value = "车型编码*")
    @ExcelPropertyCheck(required = true)
    private String vehicleModelCode;
    /**
     * 供应类型
     */
    @ApiModelProperty(value = "供应类型")
    @ExcelProperty(value = "供应类型")
    private String supplyType;
    /**
     * 计量单位
     */
    @ApiModelProperty(value = "计量单位")
    @ExcelProperty(value = "计量单位")
    private String measurementUnit;
    /**
     * 销售类型
     */
    @ApiModelProperty(value = "销售类型")
    @ExcelProperty(value = "销售类型")
    private String saleType;
    /**
     * 装车位置
     */
    @ApiModelProperty(value = "装车位置")
    @ExcelProperty(value = "装车位置*")
    @ExcelPropertyCheck(required = true)
    private String loadingPosition;
    /**
     * 装车位置小类
     */
    @ApiModelProperty(value = "装车位置小类")
    @ExcelProperty(value = "装车位置小类")
    private String loadingPositionSub;
    /**
     * 车型类型
     */
    @ApiModelProperty(value = "车型类型")
    @ExcelProperty(value = "车型类型")
    private String vehicleModelType;
    /**
     * 业务特性
     */
    @ApiModelProperty(value = "业务特性")
    @ExcelProperty(value = "业务特性")
    private String businessSpecial;
    /**
     * 核心工序
     */
    @ApiModelProperty(value = "核心工序")
    @ExcelProperty(value = "核心工序")
    private String coreProcess;
    /**
     * 产品特性
     */
    @ApiModelProperty(value = "产品特性")
    @ExcelProperty(value = "产品特性")
    private String productSpecial;
    /**
     * 长
     */
    @ApiModelProperty(value = "长")
    @ExcelProperty(value = "长")
    private BigDecimal productLength;
    /**
     * 宽
     */
    @ApiModelProperty(value = "宽")
    @ExcelProperty(value = "宽")
    private BigDecimal productWidth;
    /**
     * 厚度
     */
    @ApiModelProperty(value = "厚度")
    @ExcelProperty(value = "厚度")
    private BigDecimal productThickness;
    /**
     * 颜色
     */
    @ApiModelProperty(value = "颜色")
    @ExcelProperty(value = "颜色")
    private String productColor;
    /**
     * 面积M2
     */
    @ApiModelProperty(value = "面积M2")
    @ExcelProperty(value = "面积M2")
    private BigDecimal productArea;
    /**
     * 重量
     */
    @ApiModelProperty(value = "重量")
    @ExcelProperty(value = "重量")
    private BigDecimal productWeight;
    /**
     * 重量单位
     */
    @ApiModelProperty(value = "重量单位")
    @ExcelProperty(value = "重量单位")
    private String weightUnit;
    /**
     * 每卷/每箱数量
     */
    @ApiModelProperty(value = "每卷/每箱数量")
//    @ExcelProperty(value = "每卷/每箱数量")
    private BigDecimal productQuantity;
    /**
     * 保质期
     */
    @ApiModelProperty(value = "保质期")
    @ExcelProperty(value = "保质期")
    private Integer expireDate;
    /**
     * 特性说明
     */
    @ApiModelProperty(value = "特性说明")
    @ExcelProperty(value = "特性说明")
    private String specialDesc;
    /**
     * 最小起订量
     */
    @ApiModelProperty(value = "最小起订量")
//    @ExcelProperty(value = "最小起订量")
    private BigDecimal minOrderQuantity;
    /**
     * 运输周期
     */
    @ApiModelProperty(value = "运输周期")
    @ExcelProperty(value = "运输周期")
    private BigDecimal transportCycle;
    /**
     * 采购周期预加工
     */
    @ApiModelProperty(value = "采购周期预加工")
//    @ExcelProperty(value = "采购周期预加工")
    private String purchaseProcessPre;
    /**
     * 采购周期加工中
     */
    @ApiModelProperty(value = "采购周期加工中")
//    @ExcelProperty(value = "采购周期加工中")
    private String purchaseProcessIng;
    /**
     * 采购周期加工后
     */
    @ApiModelProperty(value = "采购周期加工后")
//    @ExcelProperty(value = "采购周期加工后")
    private String purchaseProcessAfter;
    /**
     * 物料采购锁定期
     */
    @ApiModelProperty(value = "物料采购锁定期")
//    @ExcelProperty(value = "物料采购锁定期")
    private BigDecimal purchaseLockPeriod;
    /**
     * 物料计划员
     */
    @ApiModelProperty(value = "物料计划员")
//    @ExcelProperty(value = "物料计划员")
    private String productPlanUser;
    /**
     * 人员
     */
    @ApiModelProperty(value = "人员")
    @ExcelProperty(value = "人员")
    private String productUser;
    /**
     * 订单计划员
     */
    @ApiModelProperty(value = "订单计划员")
    @ExcelProperty(value = "订单计划员")
    private String orderPlanner;
    /**
     * 生产计划员
     */
    @ApiModelProperty(value = "生产计划员")
    @ExcelProperty(value = "生产计划员")
    private String productionPlanner;
    /**
     * 材料计划员
     */
    @ApiModelProperty(value = "材料计划员")
    @ExcelProperty(value = "材料计划员")
    private String materialPlanner;

    /**
     * 采购计划员
     */
    @ApiModelProperty(value = "采购计划员")
    @ExcelProperty(value = "采购计划员")
    private String purchasePlanner;
    /**
     * SOP
     */
    @ApiModelProperty(value = "SOP")
    @ExcelProperty(value = "SOP")
    private Date productSop;
    /**
     * EOP
     */
    @ApiModelProperty(value = "EOP")
    @ExcelProperty(value = "EOP")
    private Date productEop;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @ExcelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String modifier;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private Integer versionValue;
    /**
     * 计划员
     */
    @ApiModelProperty(value = "计划员")
    private String plannerCode;
    /**
     * 采购计划类别
     */
    @ApiModelProperty(value = "采购计划类别")
    private String poCategory;
    /**
     * 组织ID
     */
    @ApiModelProperty(value = "组织ID")
    private String organizationId;
    /**
     * 物料ID
     */
    @ApiModelProperty(value = "物料ID")
    private String inventoryItemId;
    /**
     * 物料状态
     * Active
     * Inactive
     * PR-STOP
     * WIP-STOP
     */
    @ApiModelProperty(value = "物料状态")
    private String inventoryItemStatusCode;
    /**
     * 物料大类
     */
    @ApiModelProperty(value = "物料大类")
    private String productCategory;
    /**
     * 最近更新时间
     */
    @ApiModelProperty(value = "最近更新时间")
    private Date lastUpdateDate;

    @ApiModelProperty(value = "材料状态")
    private String materialDemandStatus;

    /**
     * edi标志
     */
    @ApiModelProperty(value = "edi标志")
    private String ediFlag;
    /**
     * 物料标识
     */
    @ApiModelProperty(value = "物料标识")
    private String itemFlag;
    /**
     * 编码类型标识
     */
    @ApiModelProperty(value = "编码类型标识")
    private String isbj;

    @ApiModelProperty(value = "模数量限制")
    private Integer moldQuantityLimit;
    
    /**
     * 自提类型：自提，非自提
     */
    @ApiModelProperty(value = "自提类型：自提，非自提")
    private String pickUpType;
    
    /**
     * 理货单模式，MES，GRP
     */
    @ApiModelProperty(value = "理货单模式，MES，GRP")
    private String tallyOrderMode;
    
    /**
     * 是否整箱
     */
    @ApiModelProperty(value = "是否整箱")
    private String fullBoxFlag;
    /***
     * 标准成本
     */
    @ApiModelProperty(value = "标准成本")
    private String itemCost;
}
