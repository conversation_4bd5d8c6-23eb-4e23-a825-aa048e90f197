package com.yhl.scp.mds.supplier.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpProductRouting;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesProductQualifiedSupplier;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mds.supplier.dto.SupplierOwnerInfoDTO;
import com.yhl.scp.mds.supplier.vo.SupplierOwnerInfoVO;

import java.util.List;

/**
 * <code>SupplierOwnerInfoService</code>
 * <p>
 * 应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-02 16:21:17
 */
public interface SupplierOwnerInfoService extends BaseService<SupplierOwnerInfoDTO, SupplierOwnerInfoVO> {

    /**
     * 查询所有
     *
     * @return list {@link SupplierOwnerInfoVO}
     */
    List<SupplierOwnerInfoVO> selectAll();
    List<SupplierOwnerInfoVO> selectBySupplierIds(List<String> supplierIds);

    BaseResponse<Void> syncProductQualifiedSupplier(String tenantId);
    BaseResponse<Void> sync(List<MesProductQualifiedSupplier> o);

}
