package com.yhl.scp.mds.deleteGroup.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>MdsDeleteGroupsVO</code>
 * <p>
 * ERP删除组接口中间表VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-14 20:24:36
 */
@ApiModel(value = "ERP删除组接口中间表VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MdsDeleteGroupsVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 232447540966949669L;

    /**
     * 库存点代码
     */
    @ApiModelProperty(value = "库存点代码")
    @FieldInterpretation(value = "库存点代码")
    private String stockPointCode;
    /**
     * 库存点名称
     */
    @ApiModelProperty(value = "库存点名称")
    @FieldInterpretation(value = "库存点名称")
    private String stockPointName;
    /**
     * 删除组
     */
    @ApiModelProperty(value = "删除组")
    @FieldInterpretation(value = "删除组")
    private String deleteGroupName;
    /**
     * 删除组类型（1:物料,2:清单,3:工艺路线,4:组件,5:工序,6:清单、工艺路线,7:物料、清单、工艺路线）
     */
    @ApiModelProperty(value = "删除组类型（1:物料,2:清单,3:工艺路线,4:组件,5:工序,6:清单、工艺路线,7:物料、清单、工艺路线）")
    @FieldInterpretation(value = "删除组类型（1:物料,2:清单,3:工艺路线,4:组件,5:工序,6:清单、工艺路线,7:物料、清单、工艺路线）")
    private String deleteType;
    /**
     * 删除组头层
     */
    @ApiModelProperty(value = "删除组头层")
    @FieldInterpretation(value = "删除组头层")
    private String itemConcatSegments;
    /**
     * 头层说明
     */
    @ApiModelProperty(value = "头层说明")
    @FieldInterpretation(value = "头层说明")
    private String itemDescription;
    /**
     * 删除类型（1:物料,2:物料清单,3:工艺路线,4:组件,5:工序）
     */
    @ApiModelProperty(value = "删除类型（1:物料,2:物料清单,3:工艺路线,4:组件,5:工序）")
    @FieldInterpretation(value = "删除类型（1:物料,2:物料清单,3:工艺路线,4:组件,5:工序）")
    private String deleteEntity;
    /**
     * 删除BOM头ID
     */
    @ApiModelProperty(value = "删除BOM头ID")
    @FieldInterpretation(value = "删除BOM头ID")
    private String billSequenceId;
    /**
     * 删除BOM行ID
     */
    @ApiModelProperty(value = "删除BOM行ID")
    @FieldInterpretation(value = "删除BOM行ID")
    private String componentSequenceId;
    /**
     * 删除工艺头ID
     */
    @ApiModelProperty(value = "删除工艺头ID")
    @FieldInterpretation(value = "删除工艺头ID")
    private String routingSequenceId;
    /**
     * 删除工艺行ID
     */
    @ApiModelProperty(value = "删除工艺行ID")
    @FieldInterpretation(value = "删除工艺行ID")
    private String operationSequenceId;
    /**
     * 行层工序
     */
    @ApiModelProperty(value = "行层工序")
    @FieldInterpretation(value = "行层工序")
    private String operationSeqNum;
    /**
     * 工艺路线行层部门
     */
    @ApiModelProperty(value = "工艺路线行层部门")
    @FieldInterpretation(value = "工艺路线行层部门")
    private String operationDepartmentCode;
    /**
     * 行层说明
     */
    @ApiModelProperty(value = "行层说明")
    @FieldInterpretation(value = "行层说明")
    private String description;
    /**
     * 删除状态
     */
    @ApiModelProperty(value = "删除状态")
    @FieldInterpretation(value = "删除状态")
    private String deleteStatus;
    /**
     * BOM行层组件
     */
    @ApiModelProperty(value = "BOM行层组件")
    @FieldInterpretation(value = "BOM行层组件")
    private String componentConcatSegments;
    /**
     * BOM行层序号
     */
    @ApiModelProperty(value = "BOM行层序号")
    @FieldInterpretation(value = "BOM行层序号")
    private String itemNum;
    /**
     * 生效时间
     */
    @ApiModelProperty(value = "生效时间")
    @FieldInterpretation(value = "生效时间")
    private Date effectivityDate;
    /**
     * 删除时间
     */
    @ApiModelProperty(value = "删除时间")
    @FieldInterpretation(value = "删除时间")
    private Date deleteDate;
    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间")
    @FieldInterpretation(value = "最后更新时间")
    private Date lastUpdateDate;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @FieldInterpretation(value = "版本")
    private Integer versionValue;

    @Override
    public void clean() {

    }
}
